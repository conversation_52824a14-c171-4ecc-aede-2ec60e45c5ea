/**
 * 卫星3D Tiles点云系统测试脚本
 * 用于测试API接口的基本功能
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';
const API_BASE = `${BASE_URL}/tiles`;

// 测试用的JWT Token (需要替换为实际的token)
const JWT_TOKEN = 'your_jwt_token_here';

const headers = {
  'Authorization': `Bearer ${JWT_TOKEN}`,
  'Content-Type': 'application/json'
};

async function testSatelliteTilesAPI() {
  console.log('🚀 开始测试卫星3D Tiles点云系统...\n');

  try {
    // 1. 测试获取状态
    console.log('📊 1. 测试获取点云生成状态...');
    try {
      const statusResponse = await axios.get(`${API_BASE}/status`, { headers });
      console.log('✅ 状态获取成功:', statusResponse.data);
    } catch (error) {
      console.log('⚠️  状态获取失败 (可能是首次运行):', error.response?.data || error.message);
    }
    console.log('');

    // 2. 测试获取元数据
    console.log('📋 2. 测试获取元数据...');
    try {
      const metadataResponse = await axios.get(`${API_BASE}/metadata`, { headers });
      console.log('✅ 元数据获取成功:', metadataResponse.data);
    } catch (error) {
      console.log('⚠️  元数据获取失败 (可能是首次运行):', error.response?.data || error.message);
    }
    console.log('');

    // 3. 测试获取点云数据
    console.log('🌍 3. 测试获取卫星点云数据...');
    try {
      const satellitesResponse = await axios.get(`${API_BASE}/satellites`, { headers });
      const data = satellitesResponse.data;
      console.log('✅ 点云数据获取成功:');
      console.log(`   - 生成时间: ${data.generatedAt}`);
      console.log(`   - 卫星总数: ${data.totalSatellites}`);
      console.log(`   - 计算时间: ${data.calculationTime}`);
      if (data.statistics) {
        console.log(`   - 轨道类型分布:`, data.statistics.orbitClassDistribution);
        console.log(`   - 高度范围: ${data.statistics.altitudeRange.min/1000}km - ${data.statistics.altitudeRange.max/1000}km`);
      }
      if (data.satellites && data.satellites.length > 0) {
        const sample = data.satellites[0];
        console.log(`   - 示例卫星: ${sample.name} (${sample.id})`);
        console.log(`     位置: [${sample.position.x.toFixed(2)}, ${sample.position.y.toFixed(2)}, ${sample.position.z.toFixed(2)}]`);
        console.log(`     轨道类型: ${sample.properties.orbitClass}`);
      }
    } catch (error) {
      console.log('⚠️  点云数据获取失败 (可能还未生成):', error.response?.data || error.message);
    }
    console.log('');

    // 4. 测试手动生成点云数据
    console.log('🔧 4. 测试手动生成点云数据...');
    try {
      const generateRequest = {
        forceRegenerate: false,
        colorByOrbitClass: true,
        includeStatistics: true,
        coordinateSystem: 'cartesian',
        compressionLevel: 6
      };

      console.log('   正在发送生成请求...');
      const generateResponse = await axios.post(`${API_BASE}/generate`, generateRequest, { 
        headers,
        timeout: 300000 // 5分钟超时
      });
      
      console.log('✅ 点云生成成功:', generateResponse.data);
      
      // 生成成功后，再次获取数据验证
      console.log('   验证生成结果...');
      const verifyResponse = await axios.get(`${API_BASE}/satellites`, { headers });
      const verifyData = verifyResponse.data;
      console.log(`   ✅ 验证成功: 生成了 ${verifyData.totalSatellites} 颗卫星的点云数据`);
      
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        console.log('⏰ 生成请求超时 (这是正常的，生成过程可能需要几分钟)');
      } else {
        console.log('❌ 点云生成失败:', error.response?.data || error.message);
      }
    }
    console.log('');

    // 5. 测试静态文件访问
    console.log('📁 5. 测试静态文件访问...');
    try {
      const staticResponse = await axios.get(`${BASE_URL}/public/tiles/satellites.json`);
      console.log('✅ 静态文件访问成功');
      console.log(`   文件大小: ${JSON.stringify(staticResponse.data).length} 字符`);
    } catch (error) {
      console.log('⚠️  静态文件访问失败:', error.response?.status || error.message);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }

  console.log('\n🎉 测试完成!');
}

// 运行测试
if (require.main === module) {
  testSatelliteTilesAPI();
}

module.exports = { testSatelliteTilesAPI }; 