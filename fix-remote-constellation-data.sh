#!/bin/bash

echo "=== 修复远程服务器 constellation 数据问题 ==="
echo "当前时间: $(date)"
echo ""

# 设置服务器地址（请根据实际情况修改）
SERVER_URL="http://localhost:3000"  # 替换为您的远程服务器地址
TOKEN_FILE="token.json"

# 检查 token 文件是否存在
if [[ ! -f "$TOKEN_FILE" ]]; then
    echo "❌ 错误: token.json 文件不存在，请先获取认证令牌"
    echo "   运行: ./get-token.sh 或 ./get-admin-token.sh"
    exit 1
fi

# 读取 token
TOKEN=$(cat "$TOKEN_FILE" | jq -r '.token // .access_token // .')
if [[ "$TOKEN" == "null" || -z "$TOKEN" ]]; then
    echo "❌ 错误: 无法从 token.json 中读取有效的认证令牌"
    exit 1
fi

echo "✅ 认证令牌已获取"
echo ""

echo "=== 步骤1: 检查当前卫星数据状态 ==="
SATELLITE_COUNT=$(curl -s -X POST "$SERVER_URL/api/database/query" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "SELECT COUNT(*) as total FROM satellites",
    "params": []
  }' | jq -r '.data[0].total // 0')

echo "当前数据库中卫星总数: $SATELLITE_COUNT"

if [[ "$SATELLITE_COUNT" == "0" ]]; then
    echo ""
    echo "❌ 问题确认: 远程服务器数据库中没有卫星数据"
    echo ""
    echo "=== 解决方案选择 ==="
    echo "1. 执行卫星数据增量聚合（推荐）"
    echo "2. 手动检查数据库连接和配置"
    echo ""
    
    read -p "请选择解决方案 (1 或 2): " choice
    
    case $choice in
        1)
            echo ""
            echo "=== 执行卫星数据增量聚合 ==="
            echo "正在调用增量聚合接口..."
            
            AGGREGATION_RESPONSE=$(curl -s -X POST "$SERVER_URL/local/satellite/incremental-aggregate" \
              -H "Authorization: Bearer $TOKEN" \
              -H "Content-Type: application/json" \
              -d '{"saveToDatabase": true}')
            
            echo "聚合响应:"
            echo "$AGGREGATION_RESPONSE" | jq '.'
            
            # 检查聚合是否成功
            SUCCESS=$(echo "$AGGREGATION_RESPONSE" | jq -r '.success // false')
            if [[ "$SUCCESS" == "true" ]]; then
                echo ""
                echo "✅ 数据聚合成功！"
                
                # 重新检查数据数量
                echo ""
                echo "=== 重新检查数据状态 ==="
                sleep 5  # 等待数据写入完成
                
                NEW_SATELLITE_COUNT=$(curl -s -X POST "$SERVER_URL/api/database/query" \
                  -H "Authorization: Bearer $TOKEN" \
                  -H "Content-Type: application/json" \
                  -d '{
                    "query": "SELECT COUNT(*) as total FROM satellites",
                    "params": []
                  }' | jq -r '.data[0].total // 0')
                
                echo "聚合后卫星总数: $NEW_SATELLITE_COUNT"
                
                if [[ "$NEW_SATELLITE_COUNT" -gt "0" ]]; then
                    echo ""
                    echo "=== 测试 constellation 接口 ==="
                    CONSTELLATION_RESPONSE=$(curl -s -X GET "$SERVER_URL/local/constellation/with-tle" \
                      -H "Authorization: Bearer $TOKEN" \
                      -H "Content-Type: application/json")
                    
                    echo "constellation 接口响应:"
                    echo "$CONSTELLATION_RESPONSE" | jq '.'
                    
                    TOTAL_CONSTELLATIONS=$(echo "$CONSTELLATION_RESPONSE" | jq -r '.total // 0')
                    if [[ "$TOTAL_CONSTELLATIONS" -gt "0" ]]; then
                        echo ""
                        echo "🎉 问题已解决！找到 $TOTAL_CONSTELLATIONS 个星座"
                    else
                        echo ""
                        echo "⚠️  数据聚合完成，但仍未找到具有TLE轨道信息的星座"
                        echo "   这可能是因为："
                        echo "   1. ES数据库中没有TLE轨道数据"
                        echo "   2. 数据格式不匹配"
                        echo "   3. 需要更新星座信息"
                    fi
                else
                    echo ""
                    echo "❌ 数据聚合失败：聚合后仍无数据"
                fi
            else
                echo ""
                echo "❌ 数据聚合失败，请检查错误信息"
            fi
            ;;
        2)
            echo ""
            echo "=== 手动检查指南 ==="
            echo "请按以下步骤检查："
            echo ""
            echo "1. 检查数据库连接："
            echo "   curl -X GET \"$SERVER_URL/api/database/health\" \\"
            echo "     -H \"Authorization: Bearer $TOKEN\""
            echo ""
            echo "2. 检查 PostgreSQL 服务状态："
            echo "   systemctl status postgresql"
            echo ""
            echo "3. 检查数据库配置："
            echo "   查看环境变量或配置文件中的数据库连接信息"
            echo ""
            echo "4. 检查 Elasticsearch 连接："
            echo "   确保ES数据库可以正常访问"
            echo ""
            echo "5. 运行数据库迁移："
            echo "   npm run migration:run"
            ;;
        *)
            echo "无效选择，脚本退出"
            exit 1
            ;;
    esac
else
    echo ""
    echo "✅ 数据库中有 $SATELLITE_COUNT 条卫星数据"
    echo ""
    echo "=== 检查 TLE 轨道信息数据 ==="
    
    TLE_COUNT=$(curl -s -X POST "$SERVER_URL/api/database/query" \
      -H "Authorization: Bearer $TOKEN" \
      -H "Content-Type: application/json" \
      -d '{
        "query": "SELECT COUNT(*) as total FROM satellites WHERE orbit_info IS NOT NULL AND orbit_info::text != '\''[]'\'' AND (orbit_info::text ILIKE '\''%orbital_tle%'\'' OR jsonb_path_exists(orbit_info, '\''$[*].sources[*] ? (@ == \"orbital_tle\")'\''))",
        "params": []
      }' | jq -r '.data[0].total // 0')
    
    echo "具有TLE轨道信息的卫星数量: $TLE_COUNT"
    
    if [[ "$TLE_COUNT" == "0" ]]; then
        echo ""
        echo "❌ 问题确认: 虽然有卫星数据，但没有TLE轨道信息"
        echo ""
        echo "=== 可能的解决方案 ==="
        echo "1. 重新获取TLE轨道数据"
        echo "2. 检查ES中的轨道数据索引"
        echo "3. 更新轨道信息字段格式"
        echo ""
        echo "建议运行 TLE 数据更新:"
        echo "curl -X POST \"$SERVER_URL/api/es/orbit/bulk-tle\" \\"
        echo "  -H \"Authorization: Bearer $TOKEN\" \\"
        echo "  -d '{\"noradIds\": [], \"forceUpdate\": true}'"
    else
        echo ""
        echo "✅ 有 $TLE_COUNT 条TLE轨道信息数据"
        echo ""
        echo "=== 测试 constellation 接口 ==="
        CONSTELLATION_RESPONSE=$(curl -s -X GET "$SERVER_URL/local/constellation/with-tle" \
          -H "Authorization: Bearer $TOKEN" \
          -H "Content-Type: application/json")
        
        echo "constellation 接口响应:"
        echo "$CONSTELLATION_RESPONSE" | jq '.'
        
        TOTAL_CONSTELLATIONS=$(echo "$CONSTELLATION_RESPONSE" | jq -r '.total // 0')
        if [[ "$TOTAL_CONSTELLATIONS" -gt "0" ]]; then
            echo ""
            echo "🎉 接口工作正常！找到 $TOTAL_CONSTELLATIONS 个星座"
        else
            echo ""
            echo "❌ 接口仍返回空数据，可能是星座信息处理逻辑问题"
            echo "   建议检查："
            echo "   1. constellation 字段格式"
            echo "   2. 卫星名称格式"
            echo "   3. 日志级别设置（开启 debug 日志）"
        fi
    fi
fi

echo ""
echo "=== 修复脚本完成 ===" 