/**
 * 翻译问题诊断测试脚本
 * 专门用于测试forceRetranslate和内容截断问题的修复
 */

const axios = require('axios');
const fs = require('fs');

// 配置
const config = {
  baseURL: 'http://localhost:3001',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwic3ViIjozLCJpYXQiOjE3NTAzMDQxNDMsImV4cCI6MTc1MDM5MDU0M30.o_AkTUrItH4JcKqkx3pPzOi4dKEK2XAOqmyCM_Y7VY4'
};

// 创建axios实例
const api = axios.create({
  baseURL: config.baseURL,
  headers: {
    'Authorization': `Bearer ${config.token}`,
    'Content-Type': 'application/json'
  }
});

/**
 * 测试强制重新翻译功能
 */
async function testForceRetranslate() {
  console.log('\n=== 测试强制重新翻译功能 ===');
  
  try {
    // 第一次翻译（会创建缓存）
    console.log('1. 执行第一次翻译（会创建缓存）...');
    const firstResult = await api.post('/api/es/news/translate', {
      batchSize: 3,
      maxDocs: 3,
      forceRetranslate: false,
      autoExtractThemes: true
    });
    
    console.log('第一次翻译结果:', {
      success: firstResult.data.success,
      total: firstResult.data.statistics?.total || 0,
      success_count: firstResult.data.statistics?.success || 0,
      message: firstResult.data.message
    });

    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 第二次翻译（应该使用缓存，所以很快）
    console.log('\n2. 执行第二次翻译（应该使用缓存）...');
    const secondResult = await api.post('/api/es/news/translate', {
      batchSize: 3,
      maxDocs: 3,
      forceRetranslate: false,
      autoExtractThemes: true
    });
    
    console.log('第二次翻译结果:', {
      success: secondResult.data.success,
      total: secondResult.data.statistics?.total || 0,
      success_count: secondResult.data.statistics?.success || 0,
      message: secondResult.data.message
    });

    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 第三次翻译（强制重新翻译，应该绕过缓存）
    console.log('\n3. 执行强制重新翻译（应该绕过缓存）...');
    const thirdResult = await api.post('/api/es/news/translate', {
      batchSize: 3,
      maxDocs: 3,
      forceRetranslate: true,
      autoExtractThemes: true
    });
    
    console.log('强制重新翻译结果:', {
      success: thirdResult.data.success,
      total: thirdResult.data.statistics?.total || 0,
      success_count: thirdResult.data.statistics?.success || 0,
      message: thirdResult.data.message
    });

    return {
      success: true,
      message: '强制重新翻译测试完成'
    };

  } catch (error) {
    console.error('强制重新翻译测试失败:', error.response?.data || error.message);
    return {
      success: false,
      error: error.response?.data || error.message
    };
  }
}

/**
 * 检查翻译内容质量
 */
async function checkTranslationQuality() {
  console.log('\n=== 检查翻译内容质量 ===');
  
  try {
    // 搜索一些已翻译的文档来检查质量
    const searchResult = await api.post('/api/es/news/search', {
      query: '*',
      size: 5,
      includeUntranslated: false, // 只查看已翻译的
      from: 0
    });

    if (!searchResult.data.success) {
      throw new Error('搜索失败: ' + searchResult.data.message);
    }

    const documents = searchResult.data.data.documents || [];
    console.log(`找到 ${documents.length} 个已翻译文档`);

    const qualityReport = [];

    for (let i = 0; i < documents.length; i++) {
      const doc = documents[i];
      
      const originalLength = (doc.content || '').length;
      const translatedLength = (doc.content_cn || '').length;
      const lengthRatio = originalLength > 0 ? (translatedLength / originalLength) : 0;
      
      const quality = {
        index: doc._index,
        id: doc._id,
        title: doc.title ? doc.title.substring(0, 50) + '...' : 'N/A',
        originalLength,
        translatedLength,
        lengthRatio: Math.round(lengthRatio * 100) / 100,
        hasTitle: !!doc.title_cn,
        hasSummary: !!doc.summary_cn,
        hasContent: !!doc.content_cn,
        hasThemes: !!doc.themes_cn
      };

      qualityReport.push(quality);
      
      console.log(`\n文档 ${i + 1}:`, {
        id: quality.id,
        title: quality.title,
        '原文长度': quality.originalLength,
        '译文长度': quality.translatedLength,
        '长度比例': quality.lengthRatio,
        '完整性': {
          title: quality.hasTitle,
          summary: quality.hasSummary,
          content: quality.hasContent,
          themes: quality.hasThemes
        }
      });

      // 检查是否存在截断问题
      if (quality.lengthRatio < 0.3 && quality.originalLength > 500) {
        console.log(`⚠️  文档 ${quality.id} 可能存在截断问题！长度比例仅为 ${quality.lengthRatio}`);
      }

      // 检查翻译内容中是否有错误标记
      if (doc.content_cn && (
        doc.content_cn.includes('[翻译失败') ||
        doc.content_cn.includes('[段落') ||
        doc.content_cn.includes('[内容敏感')
      )) {
        console.log(`⚠️  文档 ${quality.id} 的翻译内容包含错误标记`);
      }
    }

    // 计算总体质量指标
    const avgLengthRatio = qualityReport.reduce((sum, item) => sum + item.lengthRatio, 0) / qualityReport.length;
    const lowQualityCount = qualityReport.filter(item => item.lengthRatio < 0.3 && item.originalLength > 500).length;
    
    console.log('\n=== 质量总结 ===');
    console.log(`检查文档数: ${qualityReport.length}`);
    console.log(`平均长度比例: ${Math.round(avgLengthRatio * 100) / 100}`);
    console.log(`可能截断的文档数: ${lowQualityCount}`);
    console.log(`截断率: ${Math.round((lowQualityCount / qualityReport.length) * 100)}%`);

    return {
      success: true,
      qualityReport,
      summary: {
        totalDocs: qualityReport.length,
        avgLengthRatio,
        lowQualityCount,
        truncationRate: lowQualityCount / qualityReport.length
      }
    };

  } catch (error) {
    console.error('翻译质量检查失败:', error.response?.data || error.message);
    return {
      success: false,
      error: error.response?.data || error.message
    };
  }
}

/**
 * 测试长文本翻译
 */
async function testLongTextTranslation() {
  console.log('\n=== 测试长文本翻译 ===');
  
  try {
    // 查找一些长文本文档进行测试
    const searchResult = await api.post('/api/es/news/search', {
      query: '*',
      size: 10,
      includeUntranslated: true,
      from: 0
    });

    if (!searchResult.data.success) {
      throw new Error('搜索失败: ' + searchResult.data.message);
    }

    const documents = searchResult.data.data.documents || [];
    
    // 找到一些长文本文档
    const longDocs = documents.filter(doc => 
      (doc.content || '').length > 5000
    ).slice(0, 2);

    if (longDocs.length === 0) {
      console.log('没有找到长文本文档，跳过长文本测试');
      return { success: true, message: '无长文本文档可测试' };
    }

    console.log(`找到 ${longDocs.length} 个长文本文档进行测试`);

    for (const doc of longDocs) {
      console.log(`\n测试文档: ${doc._id}`);
      console.log(`原文长度: ${(doc.content || '').length} 字符`);
      
      // 强制重新翻译这个文档
      const translateResult = await api.post('/api/es/news/translate', {
        batchSize: 1,
        maxDocs: 1,
        forceRetranslate: true,
        specificIndexes: [doc._index],
        autoExtractThemes: true
      });

      console.log('翻译结果:', {
        success: translateResult.data.success,
        message: translateResult.data.message,
        statistics: translateResult.data.statistics
      });
    }

    return { success: true, message: '长文本翻译测试完成' };

  } catch (error) {
    console.error('长文本翻译测试失败:', error.response?.data || error.message);
    return {
      success: false,
      error: error.response?.data || error.message
    };
  }
}

/**
 * 主测试函数
 */
async function runDiagnostics() {
  console.log('开始翻译问题诊断测试...');
  console.log('测试目标: 验证forceRetranslate和内容截断问题的修复');
  
  const results = [];

  // 测试1: 强制重新翻译功能
  results.push(await testForceRetranslate());

  // 测试2: 检查翻译内容质量
  results.push(await checkTranslationQuality());

  // 测试3: 测试长文本翻译
  results.push(await testLongTextTranslation());

  // 输出最终报告
  console.log('\n=== 诊断测试总结 ===');
  const successCount = results.filter(r => r.success).length;
  console.log(`总测试数: ${results.length}`);
  console.log(`成功数: ${successCount}`);
  console.log(`失败数: ${results.length - successCount}`);

  if (successCount === results.length) {
    console.log('✅ 所有测试通过！翻译问题修复验证成功');
  } else {
    console.log('❌ 部分测试失败，需要进一步检查');
  }

  // 保存详细报告到文件
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      total: results.length,
      success: successCount,
      failed: results.length - successCount
    },
    details: results
  };

  fs.writeFileSync('translation-diagnosis-report.json', JSON.stringify(report, null, 2));
  console.log('\n详细报告已保存到: translation-diagnosis-report.json');
}

// 执行诊断
if (require.main === module) {
  runDiagnostics().catch(console.error);
}

module.exports = {
  testForceRetranslate,
  checkTranslationQuality,
  testLongTextTranslation,
  runDiagnostics
}; 