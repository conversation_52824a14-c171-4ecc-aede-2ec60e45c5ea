const { Client } = require('@elastic/elasticsearch');

const ES_URL = 'http://123.57.173.156:9200';
const ES_USERNAME = 'web_readonly';
const ES_PASSWORD = 'web@readonly4all';
const INDEX_NAME = 'orbital_tle';

async function quickVerify() {
  console.log('🔍 快速验证修复是否正确...\n');
  
  const es = new Client({
    node: ES_URL,
    auth: { username: ES_USERNAME, password: ES_PASSWORD },
  });

  // 检查几个之前"缺失"的NORAD_ID是否在ES中存在（无时间过滤）
  const testIds = [47769, 47775, 47774, 48567, 48036];
  
  console.log('检查之前被错误标记为"缺失"的NORAD_ID:');
  
  for (const id of testIds) {
    const response = await es.search({
      index: INDEX_NAME,
      size: 1,
      _source: ['satellite_name', 'norad_id', 'cospar_id', 'time'],
      body: {
        query: {
          bool: {
            must: [
              { term: { norad_id: id } },
              {
                wildcard: {
                  satellite_name: {
                    value: 'starlink*',
                    case_insensitive: true,
                  },
                },
              },
            ],
          },
        },
      },
    });

    const exists = response.hits.total.value > 0;
    if (exists) {
      const record = response.hits.hits[0]._source;
      console.log(`✅ NORAD_ID ${id}: 在TLE中存在 - ${record.satellite_name} (${record.time})`);
    } else {
      console.log(`❌ NORAD_ID ${id}: 在TLE中不存在`);
    }
  }

  // 统计总数对比
  console.log('\n📊 数据统计对比:');
  
  // 所有Starlink TLE记录数
  const allTleResponse = await es.count({
    index: INDEX_NAME,
    body: {
      query: {
        wildcard: {
          satellite_name: {
            value: 'starlink*',
            case_insensitive: true,
          },
        },
      },
    },
  });

  // 唯一NORAD_ID数量（通过聚合查询）
  const uniqueNoradResponse = await es.search({
    index: INDEX_NAME,
    size: 0,
    body: {
      query: {
        wildcard: {
          satellite_name: {
            value: 'starlink*',
            case_insensitive: true,
          },
        },
      },
      aggs: {
        unique_norad_ids: {
          cardinality: {
            field: 'norad_id'
          }
        }
      }
    },
  });

  const totalTleRecords = allTleResponse.count;
  const uniqueNoradIds = uniqueNoradResponse.aggregations.unique_norad_ids.value;

  console.log(`总TLE记录数: ${totalTleRecords.toLocaleString()}`);
  console.log(`唯一NORAD_ID数: ${uniqueNoradIds.toLocaleString()}`);
  console.log(`平均每个卫星的TLE记录数: ${(totalTleRecords / uniqueNoradIds).toFixed(1)}`);

  console.log('\n🎯 修复效果预测:');
  console.log('- 移除时间过滤后，TLE数据将包含所有历史记录');
  console.log('- 按NORAD_ID去重后，每个卫星只保留一条记录');
  console.log('- 差异文件将大幅减少，只包含真正缺失的卫星');
  console.log(`- 预计TLE数据去重后约 ${uniqueNoradIds.toLocaleString()} 条记录`);
}

quickVerify().catch(console.error); 