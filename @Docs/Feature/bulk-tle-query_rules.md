# 功能规则文档 - 批量查询卫星TLE数据

## 功能概述
批量查询卫星TLE数据API (`/orbit/bulk-tle`) 提供了灵活的卫星轨道数据查询功能，支持按指定NORAD ID列表查询特定卫星，也支持查询所有卫星的TLE数据。

## 设计思路

### 1. 功能需求
- **特定查询**: 根据NORAD ID列表查询指定卫星的TLE数据
- **全量查询**: 当NORAD ID列表为空时，返回所有卫星的TLE数据
- **数据完整性**: 确保返回每个卫星的最新TLE数据
- **性能考虑**: 限制返回数据量，避免系统负载过高

### 2. 架构设计
```
Controller Layer (API接口层)
    ↓
Service Layer (业务逻辑层)
    ↓
Elasticsearch (数据存储层)
```

### 3. 数据流设计
1. **请求验证**: 验证输入参数格式和范围
2. **查询路由**: 根据参数决定查询策略
3. **数据查询**: 执行Elasticsearch查询
4. **结果处理**: 格式化返回数据
5. **响应返回**: 统一格式返回结果

## 实现细节

### 1. API接口设计

#### 请求参数
```typescript
interface BulkNoradIdsQueryDto {
  norad_ids?: number[];  // 可选，默认为空数组
}
```

#### 响应格式
```typescript
interface BulkTleResponse {
  success: boolean;
  total: number;
  results: Array<{
    norad_id: number;
    satellite_name: string;
    cospar_id: string;
    tle_raw: string;
  }>;
}
```

### 2. 查询策略

#### 特定查询 (norad_ids非空)
- 使用 `terms` 查询匹配指定的NORAD ID列表
- 最多支持100个ID同时查询
- 按NORAD ID分组，每组返回最新数据

#### 全量查询 (norad_ids为空)
- 使用 `match_all` 查询获取所有数据
- 限制返回最多1000条记录
- 按NORAD ID分组，每组返回最新数据

### 3. 性能优化策略

#### 数据限制
- 特定查询：最多100个NORAD ID
- 全量查询：最多1000条记录
- 字段过滤：只返回必要字段

#### 查询优化
- 使用 `collapse` 功能按NORAD ID分组
- 使用 `inner_hits` 获取每组最新数据
- 使用 `_source` 过滤返回字段

#### 索引优化
- 按时间倒序排序，优先返回最新数据
- 按NORAD ID正序排序，保证结果一致性

### 4. 错误处理

#### 输入验证
- 验证NORAD ID为数字类型
- 验证数组长度不超过限制

#### 查询异常
- Elasticsearch连接异常处理
- 查询超时处理
- 数据格式异常处理

#### 日志记录
- 记录查询参数和结果统计
- 记录异常信息和堆栈跟踪
- 记录性能指标

## 配置参数

### 查询限制
```typescript
const CONFIG = {
  MAX_SPECIFIC_QUERY_IDS: 100,    // 特定查询最大ID数量
  MAX_ALL_QUERY_RESULTS: 1000,   // 全量查询最大结果数量
  QUERY_TIMEOUT: 30000,          // 查询超时时间(ms)
};
```

### 返回字段
```typescript
const REQUIRED_FIELDS = [
  'tle_raw',        // TLE原始数据
  'satellite_name', // 卫星名称
  'cospar_id',      // COSPAR ID
  'norad_id'        // NORAD ID
];
```

## 使用场景

### 1. 特定卫星查询
适用于需要获取特定卫星TLE数据的场景：
- 卫星轨道预测
- 碰撞分析
- 可见性计算

### 2. 全量数据获取
适用于需要批量处理所有卫星数据的场景：
- 数据同步
- 统计分析
- 数据备份

### 3. 系统集成
适用于与其他系统集成的场景：
- 第三方系统数据获取
- 定时任务数据更新
- 数据导出功能

## 安全考虑

### 1. 认证授权
- 需要JWT认证
- 检查用户权限
- 记录访问日志

### 2. 数据保护
- 敏感数据脱敏
- 访问频率限制
- 数据传输加密

### 3. 系统保护
- 查询结果数量限制
- 查询超时控制
- 资源使用监控

## 监控指标

### 1. 性能指标
- 查询响应时间
- 查询成功率
- 数据返回量

### 2. 业务指标
- API调用频次
- 用户使用分布
- 错误类型统计

### 3. 系统指标
- Elasticsearch负载
- 内存使用情况
- 网络传输量

## 维护说明

### 1. 数据维护
- 定期清理过期TLE数据
- 监控数据质量
- 更新索引映射

### 2. 性能调优
- 监控查询性能
- 优化查询语句
- 调整缓存策略

### 3. 功能扩展
- 支持更多查询条件
- 添加数据过滤功能
- 增加分页支持

## 版本历史

### v1.1.0 (2025-01-06)
- 新增全量查询功能
- 优化返回字段结构
- 更新API文档

### v1.0.0 (初始版本)
- 基础批量查询功能
- 支持NORAD ID列表查询
- 基本错误处理 