# 批量TLE查询API - 智能查询策略功能

## 功能概述
`/orbit/bulk-tle/all` API 提供智能查询策略的批量TLE数据查询功能，根据数据量自动选择最优查询方式，确保获取最新且完整的卫星TLE数据。

## 核心特性

### 1. 智能查询策略
- **策略1**: 数据量 < 20000条 → 按norad_id去重查询所有卫星的最新数据
- **策略2**: 数据量 ≥ 20000条 → 使用时间窗口查询
  - ≤ 10000条：直接查询
  - \> 10000条：滚动查询
- **优势**: 确保数据的完整性和时效性

### 2. 可配置时间窗口
- **查询逻辑**: 先获取time字段的最新值，然后查询[最新时间-N分钟, 最新时间]范围内的所有数据
- **时间计算**: 使用JavaScript Date对象进行精确的时间计算
- **范围查询**: 使用Elasticsearch的range查询替代term查询
- **默认配置**: 30分钟时间窗口（可通过环境变量或API调整）

### 3. NORAD ID去重查询
- **去重逻辑**: 使用composite聚合按norad_id分组，每组取time字段最新的一条记录
- **适用场景**: 当时间窗口内数据量 < 20000条时，确保获取所有卫星的最新数据
- **优势**: 避免因时间窗口限制而遗漏某些卫星的数据
- **实现方式**: 分批次处理，每批次最多10000个卫星

### 4. 时间窗口查询
- **数据量检测**: 先统计时间范围内的总记录数
- **策略选择**:
  - ≤ 10,000条: 使用直接查询（性能最优）
  - \> 10,000条: 自动切换到滚动查询（无数量限制）
- **性能保证**: 维持2-5秒的响应时间

### 5. 无数量限制
- **突破ES限制**: 通过滚动查询技术突破Elasticsearch默认的10,000条限制
- **完整数据**: 确保返回时间窗口内的所有数据，无遗漏
- **批次处理**: 大数据量时自动分批处理，每批10,000条

### 6. Swagger UI兼容
- **示例模式**: 支持`?sampleMode=true`参数，限制返回100条数据
- **避免栈溢出**: 解决Swagger UI处理大数据量时的栈溢出问题
- **双重支持**: Swagger演示和前端完整调用两种模式

## API接口

### 请求
```http
POST /orbit/bulk-tle/all?sampleMode=false
Content-Type: application/json
```

### 查询参数
- `sampleMode` (可选): 
  - `true`: 示例模式，返回前100条（适用于Swagger UI）
  - `false`或不传: 完整模式，返回全部数据（适用于前端应用）

### 响应结构
```json
// 完整模式响应 (sampleMode=false)
{
  "success": true,
  "total": 12500,                                    // 返回的记录数
  "executionTime": 3500,                             // 执行耗时（毫秒）
  "timeRangeStart": "2025-06-04T05:35:28.673Z",     // 查询开始时间
  "timeRangeEnd": "2025-06-04T06:05:28.673Z",       // 查询结束时间
  "timeRangeMinutes": 30,                            // 时间范围（分钟）
  "method": "scroll_query",                          // 查询方法: norad_dedup_query/direct_query/scroll_query
  "queryStrategy": "滚动查询",                        // 查询策略说明
  "sampleMode": false,                               // 是否为示例模式
  "results": [
    // ... 12500条记录
  ]
}

// 示例模式响应 (sampleMode=true)
{
  "success": true,
  "total": 100,                                      // 返回的记录数（限制为100条）
  "actualTotal": 12500,                              // 实际查询到的总记录数
  "executionTime": 1200,                             // 执行耗时（毫秒）
  "timeRangeStart": "2025-06-04T05:35:28.673Z",     // 查询开始时间
  "timeRangeEnd": "2025-06-04T06:05:28.673Z",       // 查询结束时间
  "timeRangeMinutes": 30,                            // 时间范围（分钟）
  "method": "norad_dedup_query",                     // 查询方法
  "queryStrategy": "按NORAD ID去重查询",              // 查询策略说明
  "sampleMode": true,                                // 是否为示例模式
  "results": [
    {
      "norad_id": 25544,
      "satellite_name": "ISS (ZARYA)",
      "cospar_id": "1998-067A",
      "epoch": "2025-01-05T16:13:27Z",
      "time": "2025-06-04T06:05:28.673Z",
      "tle_raw": "0 ISS (ZARYA)\n1 25544U 98067A..."
    }
    // ... 仅显示前100条记录
  ]
}
```

## 技术实现

### 时间计算
```typescript
// 获取最新时间
const maxTime = maxTimeResponse.aggregations.max_time.value;

// 计算配置的时间窗口前的时间
const maxTimeDate = new Date(maxTime);
const timeWindowAgo = new Date(maxTimeDate.getTime() - this.tleQueryConfig.timeWindowMinutes * 60 * 1000);
const timeWindowAgoISO = timeWindowAgo.toISOString();
```

### 查询逻辑
```typescript
// 使用range查询替代term查询
const query = {
  range: {
    time: {
      gte: timeWindowAgoISO,  // 最新时间-N分钟
      lte: maxTime            // 最新时间
    }
  }
};
```

### 排序策略
```typescript
sort: [
  { time: { order: "desc" } },    // 按时间倒序
  { norad_id: { order: "asc" } }  // 相同时间按norad_id正序
]
```

## 配置管理

### 1. 环境变量配置
在 `.env` 文件中设置：
```bash
# TLE查询配置
TLE_QUERY_TIME_WINDOW_MINUTES=30    # 时间窗口（分钟）
TLE_QUERY_MAX_RECORDS=50000         # 最大记录数
TLE_QUERY_TIMEOUT=300000            # 查询超时时间（毫秒，默认5分钟）
TLE_QUERY_SCROLL_SIZE=10000         # 滚动查询每批次大小
TLE_QUERY_SCROLL_TIMEOUT=10m        # 滚动查询超时时间

# 服务器超时配置
SERVER_TIMEOUT=300000               # 服务器请求超时（毫秒，默认5分钟）
SERVER_KEEP_ALIVE_TIMEOUT=305000    # Keep-alive超时（毫秒）
SERVER_HEADERS_TIMEOUT=306000       # Headers超时（毫秒）
```

### 2. API动态配置
```bash
# 获取当前配置
GET /orbit/tle-config

# 更新配置
PUT /orbit/tle-config
{
  "timeWindowMinutes": 30,
  "maxRecords": 50000,
  "timeout": 300000,
  "scrollSize": 10000,
  "scrollTimeout": "10m"
}
```

### 3. 配置验证规则
- **时间窗口**: 1-1440分钟（1分钟到24小时）
- **最大记录数**: 1-100000条
- **超时时间**: 1-600000毫秒（1毫秒到10分钟）
- **滚动批次大小**: 1-10000条
- **滚动超时**: 字符串格式，如'2m'、'5m'、'10m'

## 使用场景

### 1. 实时监控
- 获取最新时间窗口内的所有卫星状态更新
- 监控卫星轨道数据的实时变化
- 检测异常轨道行为
- 可根据监控需求调整时间窗口

### 2. 数据同步
- 确保不遗漏任何最新的轨道数据
- 提供可靠的数据同步机制
- 支持增量数据更新
- 可配置合适的时间窗口以平衡数据完整性和性能

### 3. 趋势分析
- 观察短时间内的数据变化趋势
- 分析卫星轨道参数的变化模式
- 提供时间序列数据支持
- 可根据分析需求调整时间窗口大小

## 性能特点

### 响应时间
- **小数据量** (≤10,000条): 2-5秒
- **大数据量** (>10,000条): 3-8秒
- **示例模式**: <1秒

### 数据完整性
- **多重验证**: 预期数量 vs 实际数量对比
- **时间分布**: 统计时间窗口内的时间点分布
- **完整性保证**: 确保获取全部符合条件的数据

### 资源优化
- **智能策略**: 根据数据量自动选择最优查询方式
- **内存管理**: 滚动查询避免大量数据同时加载
- **连接管理**: 自动清理滚动上下文

## 错误处理

### 常见错误
1. **时间字段缺失**: 返回明确的错误信息
2. **ES连接超时**: 自动重试机制
3. **数据量过大**: 自动切换到滚动查询
4. **滚动上下文失效**: 自动清理和重建

### 日志记录
- **性能日志**: 记录各阶段耗时
- **数据质量**: 记录完整性检查结果
- **错误追踪**: 详细的错误堆栈信息

## 最佳实践

### Swagger UI测试
```http
POST /orbit/bulk-tle/all?sampleMode=true
```

### 前端应用调用
```javascript
// 获取全部数据
const response = await fetch('/orbit/bulk-tle/all', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' }
});

const data = await response.json();
console.log(`获取到${data.total}条最新${data.timeRangeMinutes}分钟内的TLE数据`);
```

### 数据处理建议
1. **分页显示**: 前端可对大量数据进行分页展示
2. **缓存策略**: 可缓存结果减少重复查询
3. **增量更新**: 基于时间戳进行增量数据更新

## 版本历史
- **v1.0**: 基于epoch字段的单时间点查询
- **v2.0**: 改为基于time字段的单时间点查询
- **v3.0**: 突破ES 10000条限制，支持无限量数据
- **v4.0**: 改为10分钟时间窗口查询，提供更全面的数据覆盖
- **v5.0**: 支持可配置时间窗口，默认30分钟，可通过环境变量或API动态调整
- **v6.0**: 新增智能查询策略，当数据量<20000条时自动切换为按NORAD ID去重查询，确保获取所有卫星的最新数据 