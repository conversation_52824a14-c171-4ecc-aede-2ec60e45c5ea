# 用户权限管理功能规则文档

**功能名称**: 用户权限管理系统  
**版本**: v1.0.0  
**创建日期**: 2025-01-23  
**最后更新**: 2025-01-23  

## 功能概述

用户权限管理系统是太空大数据平台的核心安全模块，负责管理用户的身份认证、角色分配和权限控制。系统采用基于角色的访问控制（RBAC）模型，确保不同级别的用户只能访问其权限范围内的资源。

## 设计思路

### 1. 架构设计原则

- **最小权限原则**: 用户默认只拥有最基本的权限，需要时才授予更高权限
- **职责分离**: 管理员和普通用户的权限严格分离
- **安全优先**: 所有权限操作都需要经过严格的身份验证和授权
- **可扩展性**: 支持未来添加更多角色和权限类型

### 2. 角色层次结构

```
admin (管理员)
├── 用户管理权限
├── 系统配置权限
├── 数据访问权限
└── 所有普通用户权限

government (政府用户)
├── 特殊数据访问权限
├── 高级分析功能
└── 扩展API调用限制

enterprise (企业用户)
├── 高级数据访问权限
├── 批量操作权限
└── 增强API调用限制

premium (高级用户)
├── 扩展数据访问权限
├── 高级功能使用权限
└── 提升API调用限制

free (免费用户)
├── 基础数据访问权限
├── 基本功能使用权限
└── 基础API调用限制
```

## 实现细节

### 1. 数据模型

#### 用户实体 (User Entity)
```typescript
class User {
  id: number;                    // 用户唯一标识
  username: string;              // 用户名（唯一）
  password: string;              // 加密密码
  email: string;                 // 邮箱（唯一）
  role: UserRole;                // 用户角色
  apiCallsToday: number;         // 今日API调用次数
  downloadsToday: number;        // 今日下载次数
  lastApiReset: Date;            // 上次API重置时间
  avatarUrl?: string;            // 头像URL
  isActive: boolean;             // 账号是否激活
  createdAt: Date;               // 创建时间
  updatedAt: Date;               // 更新时间
  deletedAt?: Date;              // 软删除时间
}
```

#### 用户角色枚举 (UserRole Enum)
```typescript
enum UserRole {
  ADMIN = 'admin',               // 管理员
  FREE = 'free',                 // 免费用户
  PREMIUM = 'premium',           // 高级用户
  ENTERPRISE = 'enterprise',     // 企业用户
  GOVERNMENT = 'government'      // 政府用户
}
```

### 2. 权限控制机制

#### JWT认证策略
- 使用JWT令牌进行用户身份验证
- 令牌包含用户ID和用户名信息
- 每次请求时从数据库获取最新用户信息
- 支持令牌过期和刷新机制

#### 权限守卫 (Guards)

**AdminGuard**: 管理员权限守卫
```typescript
// 确保只有管理员角色的用户才能访问
canActivate(context: ExecutionContext): boolean {
  const user = request.user;
  return user && user.role === UserRole.ADMIN;
}
```

**AdminOrOwnerGuard**: 管理员或所有者权限守卫
```typescript
// 确保管理员或资源所有者才能访问
canActivate(context: ExecutionContext): boolean {
  const user = request.user;
  const targetUserId = request.params.id;
  return user && (user.role === UserRole.ADMIN || user.id === targetUserId);
}
```

### 3. API端点设计

#### 认证相关API
- `POST /auth/register` - 用户注册（公开）
- `POST /auth/login` - 用户登录（公开）
- `GET /auth/profile` - 获取当前用户信息（需认证）

#### 权限管理API
- `PUT /auth/users/:id/role` - 设置用户角色（仅管理员）
- `GET /auth/users/:id/permissions` - 获取用户权限（管理员或所有者）
- `GET /auth/users` - 获取用户列表（仅管理员）

### 4. 数据传输对象 (DTOs)

#### 权限设置DTO
```typescript
class SetUserRoleDto {
  @IsEnum(UserRole)
  role: UserRole;                // 要设置的用户角色
}
```

#### 用户权限响应DTO
```typescript
class UserPermissionResponseDto {
  id: number;                    // 用户ID
  username: string;              // 用户名
  email: string;                 // 邮箱
  role: UserRole;                // 用户角色
  isActive: boolean;             // 是否激活
  createdAt: Date;               // 创建时间
  updatedAt: Date;               // 更新时间
}
```

#### 用户列表查询DTO
```typescript
class GetUsersQueryDto {
  page?: number = 1;             // 页码
  limit?: number = 10;           // 每页数量
  role?: UserRole;               // 角色筛选
  username?: string;             // 用户名搜索
}
```

## 安全规范

### 1. 密码安全
- 使用bcrypt进行密码哈希，盐值轮数为10
- 密码最小长度6位，建议包含字母、数字和特殊字符
- 不在任何日志或响应中暴露明文密码

### 2. 权限验证
- 所有权限相关操作都需要JWT认证
- 管理员操作需要额外的AdminGuard验证
- 用户只能访问自己的资源，除非是管理员

### 3. 输入验证
- 使用class-validator进行请求参数验证
- 对所有用户输入进行严格的类型和格式检查
- 防止SQL注入和XSS攻击

### 4. 错误处理
- 统一的错误响应格式
- 不暴露敏感的系统信息
- 记录安全相关的操作日志

## 数据库设计

### 1. 用户表结构
```sql
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  username VARCHAR(100) UNIQUE NOT NULL,
  password VARCHAR(100) NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  role VARCHAR(20) DEFAULT 'free' NOT NULL,
  api_calls_today INTEGER DEFAULT 0 NOT NULL,
  downloads_today INTEGER DEFAULT 0 NOT NULL,
  last_api_reset TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
  avatar_url VARCHAR(255),
  is_active BOOLEAN DEFAULT true NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
  deleted_at TIMESTAMP WITH TIME ZONE,
  
  CONSTRAINT chk_user_role CHECK (role IN ('admin', 'free', 'premium', 'enterprise', 'government'))
);
```

### 2. 索引设计
- 用户名唯一索引：`idx_username_unique`
- 邮箱唯一索引：`idx_email_unique`
- 角色查询索引：`idx_user_role`
- 创建时间索引：`idx_created_at`

## 测试策略

### 1. 单元测试
- 认证服务方法测试
- 权限守卫逻辑测试
- DTO验证测试
- 密码加密解密测试

### 2. 集成测试
- API端点功能测试
- 权限验证流程测试
- 数据库操作测试
- JWT令牌验证测试

### 3. 端到端测试
- 完整的用户注册登录流程
- 权限设置和查询流程
- 错误处理和边界情况
- 性能和并发测试

## 部署配置

### 1. 环境变量
```bash
JWT_SECRET=your-secret-key          # JWT密钥
JWT_EXPIRES_IN=24h                  # JWT过期时间
BCRYPT_ROUNDS=10                    # 密码加密轮数
```

### 2. 数据库迁移
```bash
npm run migration:run               # 运行迁移
npm run migration:revert            # 回滚迁移
```

### 3. 默认管理员
- 用户名：admin
- 密码：admin123（生产环境需修改）
- 邮箱：<EMAIL>

## 监控和日志

### 1. 关键指标
- 用户注册数量
- 登录成功/失败率
- 权限变更操作次数
- API调用频率

### 2. 安全日志
- 登录尝试记录
- 权限变更记录
- 异常访问记录
- 系统错误记录

## 扩展计划

### 1. 短期优化
- 添加用户权限过期机制
- 实现更细粒度的权限控制
- 添加用户操作审计日志
- 支持批量用户管理

### 2. 长期规划
- 集成第三方认证（OAuth2、SAML）
- 实现多因素认证（MFA）
- 添加权限模板和继承机制
- 支持动态权限配置

## 维护指南

### 1. 常见问题
- 忘记管理员密码：通过数据库直接重置
- 用户权限异常：检查角色设置和守卫逻辑
- JWT令牌失效：检查密钥配置和过期时间

### 2. 性能优化
- 定期清理过期的JWT令牌
- 优化用户查询的数据库索引
- 实现用户信息缓存机制
- 监控API调用频率和响应时间

### 3. 安全更新
- 定期更新依赖包版本
- 审查和更新安全配置
- 监控安全漏洞和补丁
- 定期进行安全测试和评估 