# 用户审批功能设计文档

## 功能概述

用户审批功能确保新注册的用户需要经过管理员审批后才能正常使用系统，提高了系统的安全性和用户质量控制。

## 设计思路

### 1. 审批状态管理
- **PENDING**: 待审批 - 新注册用户的默认状态
- **APPROVED**: 已批准 - 管理员审批通过，可以正常使用系统
- **REJECTED**: 已拒绝 - 管理员拒绝申请，需要重新申请或联系管理员

### 2. 用户注册流程
1. 用户提交注册信息
2. 系统创建用户账户，默认状态为 `PENDING`
3. 返回注册成功信息，提示需要等待管理员审批
4. 用户无法登录，直到管理员审批通过

### 3. 管理员审批流程
1. 管理员查看待审批用户列表
2. 审核用户信息
3. 选择批准或拒绝
4. 如果拒绝，必须提供拒绝原因
5. 系统更新用户状态和相关信息

## 实现细节

### 数据库设计

#### 用户表扩展字段
```sql
-- 审批状态字段
approval_status VARCHAR(20) DEFAULT 'pending'

-- 审批时间
approved_at TIMESTAMP WITH TIME ZONE NULL

-- 审批人ID
approved_by INTEGER NULL

-- 拒绝原因
rejection_reason VARCHAR(500) NULL
```

### API接口设计

#### 1. 用户注册 (修改现有)
- **路径**: `POST /auth/register`
- **变更**: 新用户默认状态为 `pending`
- **响应**: 包含 `approvalStatus` 字段

#### 2. 用户登录 (修改现有)
- **路径**: `POST /auth/login`
- **变更**: 检查用户审批状态
- **错误响应**:
  - 待审批: "您的账户正在等待管理员审批，请耐心等待审批完成后再次尝试登录"
  - 已拒绝: "您的账户申请已被拒绝，原因：{拒绝原因}。如有疑问，请联系管理员"

#### 3. 获取待审批用户列表 (新增)
- **路径**: `GET /auth/admin/pending-users`
- **权限**: 仅管理员
- **功能**: 分页查询待审批用户，支持用户名和邮箱搜索

#### 4. 审批用户申请 (新增)
- **路径**: `PUT /auth/admin/users/:id/approval`
- **权限**: 仅管理员
- **参数**:
  ```json
  {
    "action": "approve" | "reject",
    "rejectionReason": "string" // 拒绝时必填
  }
  ```

#### 5. 获取用户审批历史 (新增)
- **路径**: `GET /auth/admin/user-approval-history`
- **权限**: 仅管理员
- **功能**: 查看所有用户的审批历史记录

### 权限控制

#### AdminGuard 管理员权限守卫
- 检查用户是否为管理员角色
- 验证管理员账户状态（已批准且激活）
- 确保只有合法管理员可以执行审批操作

### 用户体验优化

#### 1. 友好的错误信息
- 待审批用户登录时给出明确提示
- 被拒绝用户可以看到具体拒绝原因
- 引导用户联系管理员或重新申请

#### 2. 管理员操作便利性
- 支持批量查看待审批用户
- 提供搜索和分页功能
- 审批操作简单明确

## 安全考虑

### 1. 权限验证
- 所有审批相关操作都需要管理员权限
- JWT token验证确保请求合法性
- 防止普通用户绕过审批流程

### 2. 数据完整性
- 使用数据库事务确保审批操作的原子性
- 记录审批人和审批时间，便于追溯
- 防止重复审批同一用户

### 3. 输入验证
- 拒绝操作必须提供拒绝原因
- 验证用户ID的有效性
- 防止SQL注入和XSS攻击

## 向后兼容性

### 现有用户处理
- 数据库迁移时将现有用户状态设置为 `approved`
- 确保现有用户不受新功能影响
- 保持现有API的兼容性

## 测试用例

### 1. 用户注册测试
- 新用户注册后状态为 `pending`
- 注册响应包含正确的审批状态信息

### 2. 登录限制测试
- 待审批用户无法登录
- 被拒绝用户收到包含拒绝原因的错误信息
- 已批准用户可以正常登录

### 3. 管理员审批测试
- 管理员可以查看待审批用户列表
- 批准操作正确更新用户状态
- 拒绝操作需要提供拒绝原因
- 审批历史记录完整准确

### 4. 权限控制测试
- 非管理员用户无法访问审批接口
- 未登录用户被正确拒绝
- 管理员权限验证正常工作

## 监控和日志

### 1. 操作日志
- 记录所有审批操作
- 包含操作人、操作时间、操作结果
- 便于审计和问题排查

### 2. 性能监控
- 监控审批接口的响应时间
- 统计待审批用户数量
- 跟踪审批通过率

## 未来扩展

### 1. 审批工作流
- 支持多级审批
- 审批委托和代理
- 自动审批规则

### 2. 通知机制
- 邮件通知用户审批结果
- 管理员新申请提醒
- 审批状态变更通知

### 3. 审批统计
- 审批效率统计
- 用户申请趋势分析
- 拒绝原因分类统计 