# 批量TLE查询分页功能规则文档

## 功能概述
批量TLE查询API (`/orbit/bulk-tle`) 的分页功能增强，解决前端无法获取所有卫星TLE数据的问题。

## 设计目标
1. **突破数据限制**：原API限制1000条记录，无法满足获取所有卫星数据的需求
2. **保持高性能**：通过分页避免一次性加载大量数据
3. **向后兼容**：原有的特定卫星查询功能保持不变
4. **用户友好**：提供完整的分页元数据，便于前端实现分页UI

## 技术实现

### 1. 参数设计
```typescript
export class BulkNoradIdsQueryDto {
  norad_ids?: number[] = [];  // 卫星NORAD ID数组，空数组表示查询所有
  page?: number = 1;          // 页码，从1开始
  limit?: number = 1000;      // 每页记录数，最大10000
}
```

### 2. 查询逻辑
```typescript
// 当norad_ids为空时，执行分页查询
if (!noradIds || noradIds.length === 0) {
  // 1. 先获取总数（使用collapse去重）
  const countResponse = await this.elasticsearchService.search({
    index: 'orbital_tle',
    body: {
      query: { match_all: {} },
      collapse: { field: "norad_id" },
      size: 0,
      track_total_hits: true
    }
  });

  // 2. 计算分页信息
  const totalCount = countResponse.hits.total.value;
  const totalPages = Math.ceil(totalCount / limit);

  // 3. 获取分页数据
  const response = await this.elasticsearchService.search({
    index: 'orbital_tle',
    body: {
      query: { match_all: {} },
      collapse: {
        field: "norad_id",
        inner_hits: {
          name: "latest_tle",
          size: 1,
          sort: [{ epoch: { order: "desc" } }],
          _source: ["tle_raw", "satellite_name", "cospar_id", "norad_id", "epoch"]
        }
      },
      sort: [{ norad_id: { order: "asc" } }],
      from: (page - 1) * limit,
      size: limit
    }
  });
}
```

### 3. 响应结构设计

#### 分页查询响应（norad_ids为空）
```json
{
  "success": true,
  "total": 8500,              // 总记录数（去重后的卫星数量）
  "page": 1,                  // 当前页码
  "limit": 1000,              // 每页记录数
  "totalPages": 9,            // 总页数
  "currentPageCount": 1000,   // 当前页实际记录数
  "hasNextPage": true,        // 是否有下一页
  "hasPrevPage": false,       // 是否有上一页
  "results": [...]            // 数据列表
}
```

#### 特定卫星查询响应（norad_ids非空）
```json
{
  "success": true,
  "total": 3,                 // 找到的卫星数量
  "results": [...]            // 数据列表
}
```

## 性能优化策略

### 1. 数据去重
- 使用Elasticsearch的`collapse`功能按`norad_id`分组
- 每组只返回`epoch`时间最新的一条记录
- 避免同一卫星的重复数据

### 2. 字段过滤
- 使用`_source`参数只返回必要字段：`tle_raw`、`satellite_name`、`cospar_id`、`norad_id`、`epoch`
- 减少网络传输数据量

### 3. 分页限制
- 每页最大支持10000条记录，平衡性能和灵活性
- 提供合理的默认值（1000条）

### 4. 排序优化
- 使用`norad_id`进行主排序，确保分页结果的一致性
- 使用`epoch`进行时间排序，确保数据的时效性

## 前端集成指南

### 1. 获取所有数据的示例代码
```javascript
async function getAllSatelliteTLE() {
  let allData = [];
  let page = 1;
  const limit = 5000; // 根据需要调整每页大小
  
  while (true) {
    const response = await fetch('/orbit/bulk-tle', {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ norad_ids: [], page, limit })
    });
    
    const data = await response.json();
    allData.push(...data.results);
    
    console.log(`已获取第${page}页，共${data.totalPages}页`);
    
    if (!data.hasNextPage) break;
    page++;
  }
  
  return allData;
}
```

### 2. 分页UI实现建议
```javascript
function PaginationComponent({ currentPage, totalPages, onPageChange }) {
  return (
    <div className="pagination">
      <button 
        disabled={currentPage === 1}
        onClick={() => onPageChange(currentPage - 1)}
      >
        上一页
      </button>
      
      <span>第 {currentPage} 页，共 {totalPages} 页</span>
      
      <button 
        disabled={currentPage === totalPages}
        onClick={() => onPageChange(currentPage + 1)}
      >
        下一页
      </button>
    </div>
  );
}
```

## 使用场景

### 1. 前端数据表格
- 支持分页显示所有卫星TLE数据
- 用户可以翻页浏览不同的卫星信息
- 支持搜索和筛选功能

### 2. 数据导出
- 可以分批次获取所有数据进行导出
- 避免一次性加载过多数据导致的性能问题

### 3. 数据同步
- 定期同步所有卫星的最新TLE数据
- 支持增量更新和全量更新

## 错误处理

### 1. 参数验证
- `page`必须大于等于1
- `limit`必须在1-10000之间
- `norad_ids`数组最多100个元素

### 2. 异常情况
- ES查询失败时返回详细错误信息
- 超出分页范围时返回空结果
- 网络超时时提供重试机制

## 监控和日志

### 1. 性能监控
- 记录分页查询的响应时间
- 监控ES集群的负载情况
- 统计API调用频率和数据量

### 2. 日志记录
- 记录每次分页查询的参数和结果数量
- 记录异常情况和错误信息
- 便于问题排查和性能优化

## 未来扩展

### 1. 缓存机制
- 考虑对热点数据进行缓存
- 支持Redis缓存分页结果
- 减少ES查询压力

### 2. 流式传输
- 对于超大数据集，考虑实现流式传输
- 支持断点续传功能
- 提高数据传输的可靠性

### 3. 数据压缩
- 对TLE数据进行压缩传输
- 减少网络带宽占用
- 提高传输效率

## 测试策略

### 1. 单元测试
- 测试分页参数的有效性验证
- 测试ES查询逻辑的正确性
- 测试响应数据结构的完整性

### 2. 集成测试
- 测试完整的分页查询流程
- 测试分页边界情况
- 测试大数据量的性能表现

### 3. 压力测试
- 模拟高并发分页查询
- 测试系统的稳定性和性能
- 评估系统的承载能力 