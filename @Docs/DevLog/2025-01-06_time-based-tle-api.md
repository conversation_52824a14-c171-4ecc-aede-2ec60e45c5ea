# 开发日志 - 2025-01-06
## 主题：基于time字段的批量TLE查询API优化 + 突破ES 10000条限制 + 10分钟时间窗口

### 重要更新 - 突破Elasticsearch 10000条限制 🚀

#### 问题发现
用户反馈API返回的结果受到10000条限制，担心前端调用时无法获取全部数据。经检查确认：
- 代码中确实存在 `size: 10000` 的硬编码限制
- 当实际数据超过10000条时，会出现数据缺失
- 前端和Swagger都会受到这个限制

#### 解决方案
实现了智能查询策略，根据数据量自动选择最优查询方式：

1. **数据量检测**：先使用count API统计总记录数
2. **策略选择**：
   - ≤ 10000条：直接查询（速度最快）
   - \> 10000条：滚动查询（无数量限制）
3. **完整性验证**：多重检查确保数据完整

#### 技术实现
```typescript
// 1. 统计总数
const countResponse = await this.elasticsearchService.count({
  index: 'orbital_tle',
  body: { query: { term: { time: maxTime } } }
});

// 2. 选择查询策略
if (totalCount <= 10000) {
  // 直接查询
  const response = await this.search({ size: totalCount });
} else {
  // 滚动查询
  let scrollResponse = await this.search({
    scroll: '2m',
    size: 10000
  });
  // 循环获取所有批次...
}
```

#### 响应结构升级
```json
{
  "total": 12500,           // 实际返回数量
  "expectedTotal": 12500,   // 预期总数量
  "method": "scroll_query", // 查询方法
  "queryStrategy": "滚动查询" // 策略说明
}
```

---

### 重要更新 - 解决Swagger UI栈溢出问题 🛠️

#### 问题描述
用户在Swagger UI中调用 `/orbit/bulk-tle/all` API时遇到 `RangeError: Maximum call stack size exceeded` 错误。

#### 根本原因
- API返回的数据量过大（可能超过10000条记录）
- Swagger UI的语法高亮器无法处理大量数据
- 浏览器JavaScript引擎栈溢出

#### 解决方案
添加了 `sampleMode` 查询参数，专门用于Swagger UI演示：

1. **示例模式**: `?sampleMode=true`
   - 限制返回前100条数据
   - 避免Swagger UI栈溢出
   - 适合API文档演示

2. **完整模式**: 不传参数或 `?sampleMode=false`
   - 返回全部数据，无数量限制
   - 适合前端应用调用

#### 使用方法

**在Swagger UI中测试（推荐）**:
```
POST /orbit/bulk-tle/all?sampleMode=true
```

**前端应用调用**:
```javascript
// 获取全部数据
fetch('/orbit/bulk-tle/all', { method: 'POST' })

// 或明确指定完整模式
fetch('/orbit/bulk-tle/all?sampleMode=false', { method: 'POST' })
```

#### 响应结构
```json
{
  "success": true,
  "total": 100,          // 返回的记录数
  "expectedTotal": 12500, // 数据库中的总数
  "actualTotal": 12500,   // 实际可获取的数
  "sampleMode": true,     // 是否为示例模式
  "results": [...]        // 数据记录
}
```

---

### 需求背景
用户要求更新 `/orbit/bulk-tle/all` API 的实现逻辑：
- 先获取time字段是最近一次的所有结果
- 例如time最近的一次值是2025-06-04T06:05:28.673Z
- 返回所有time字段是2025-06-04T06:05:28.673Z的文档结果
- 结果中只需要返回跟该API之前返回一样的字段

### 实现方案

#### 新的查询逻辑
1. **第一步：获取time字段最大值**
   ```typescript
   const maxTimeResponse = await this.elasticsearchService.search({
     index: 'orbital_tle',
     body: {
       size: 0,
       aggs: {
         max_time: {
           max: { field: "time" }
         }
       }
     }
   });
   ```

2. **第二步：查询所有该time值的文档**
   ```typescript
   const response = await this.elasticsearchService.search({
     index: 'orbital_tle',
     body: {
       query: {
         term: { time: maxTime }
       },
       sort: [{ norad_id: { order: "asc" } }],
       size: 10000,
       _source: ["tle_raw", "satellite_name", "cospar_id", "norad_id", "epoch"]
     }
   });
   ```

#### 代码变更
- **文件**: `src/elasticsearch/services/elasticsearch.orbit.service.ts`
  - 重写 `getAllSatelliteTleData()` 方法
  - 移除复杂的composite聚合逻辑
  - 移除scroll备用方案
  - 简化为两步查询

- **文件**: `src/elasticsearch/controllers/elasticsearch.orbit.controller.ts`
  - 更新API描述信息
  - 更新响应schema
  - 移除不再使用的scroll接口

#### 性能对比

| 方案 | 查询步骤 | 预期耗时 | 复杂度 | 数据一致性 |
|------|----------|----------|--------|------------|
| 旧方案(composite) | 多批次聚合 | 3-8秒 | 高 | 按epoch去重 |
| 新方案(time-based) | 2步查询 | 2-5秒 | 低 | 同一时间点 |

#### 响应结构变化
```json
// 新增字段
{
  "latestTime": "2025-06-04T06:05:28.673Z",
  "maxTimeQueryTime": 500,
  "dataQueryTime": 3000,
  "method": "time_based"
}

// 移除字段
{
  "batchCount": 1,
  "averageTimePerBatch": 3500
}
```

### 技术优势

#### 1. 逻辑简化
- 从复杂的分批聚合简化为两次直接查询
- 不再需要处理afterKey和分页逻辑
- 去除了composite聚合的复杂性

#### 2. 数据一致性
- 确保返回的所有数据都是同一时间点采集的
- 避免了因epoch字段去重可能导致的数据不一致
- 更符合用户对"最新一批数据"的期望

#### 3. 性能提升
- 减少了ES查询的复杂度
- 减少内存使用（不需要大量聚合缓存）
- 更可靠的查询性能

#### 4. 维护性提升

---

### 最新更新 - 改为10分钟时间窗口查询 🕒

#### 需求变更
用户要求修改API逻辑，从返回"最新时间点的所有数据"改为"最新时间前10分钟内的所有数据"。

#### 实现方案
1. **时间范围计算**：
   ```typescript
   // 获取最新时间
   const maxTime = maxTimeResponse.aggregations.max_time.value;
   
   // 计算10分钟前的时间
   const maxTimeDate = new Date(maxTime);
   const tenMinutesAgo = new Date(maxTimeDate.getTime() - 10 * 60 * 1000);
   const tenMinutesAgoISO = tenMinutesAgo.toISOString();
   ```

2. **查询逻辑变更**：
   ```typescript
   // 从term查询改为range查询
   const query = {
     range: {
       time: {
         gte: tenMinutesAgoISO,  // 最新时间-10分钟
         lte: maxTime            // 最新时间
       }
     }
   };
   ```

3. **排序优化**：
   ```typescript
   sort: [
     { time: { order: "desc" } },    // 按时间倒序
     { norad_id: { order: "asc" } }  // 相同时间按norad_id正序
   ]
   ```

#### 响应结构更新
```json
{
  "timeRangeStart": "2025-06-04T05:55:28.673Z",  // 查询开始时间
  "timeRangeEnd": "2025-06-04T06:05:28.673Z",    // 查询结束时间
  "timeRangeMinutes": 10,                         // 时间范围（分钟）
  "timeDistribution": 5,                          // 10分钟内包含的不同时间点数量
  "results": [
    {
      "time": "2025-06-04T06:05:28.673Z",  // 新增time字段到返回结果
      // ... 其他字段
    }
  ]
}
```

#### 技术优势
1. **更全面的数据覆盖**：
   - 不再局限于单一时间点
   - 包含最新10分钟内的所有更新
   - 提供更完整的数据视图

2. **更好的数据连续性**：
   - 避免因网络延迟导致的数据缺失
   - 包含多个时间点的数据更新
   - 提供时间分布统计

3. **保持高性能**：
   - 仍然使用智能查询策略
   - 保持滚动查询突破10000条限制
   - 维持秒级响应时间

#### 使用场景
- **实时监控**：获取最新10分钟内的所有卫星状态更新
- **数据同步**：确保不遗漏任何最新的轨道数据
- **趋势分析**：观察短时间内的数据变化趋势
- 代码逻辑更清晰
- 错误处理更简单
- 调试更容易

### 测试验证
- [x] 编译通过，无语法错误
- [x] 类型检查通过
- [x] API文档更新正确
- [ ] 功能测试：验证能正确获取最新time的所有数据
- [ ] 性能测试：验证响应时间在预期范围内
- [ ] 数据质量测试：验证返回数据的完整性

### 部署注意事项
1. 确保ES集群中time字段存在且有正确的映射
2. 监控新API的性能表现
3. 如有必要，调整size参数以适应数据量
4. 考虑为time字段添加索引以提升查询性能

### 后续优化建议
1. 添加缓存机制，缓存最新time值
2. 考虑添加time字段的范围查询支持
3. 监控数据更新频率，优化查询策略
4. 考虑添加数据变化通知机制

### 相关文件
- `src/elasticsearch/services/elasticsearch.orbit.service.ts`
- `src/elasticsearch/controllers/elasticsearch.orbit.controller.ts`
- `@Docs/Feature/bulk-tle-performance-optimization_rules.md` 