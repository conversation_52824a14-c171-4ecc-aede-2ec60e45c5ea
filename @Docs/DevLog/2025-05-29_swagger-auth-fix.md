# 2025-05-29 Swagger JWT认证问题修复

## 问题描述
用户在Swagger文档中使用admin用户的登录token时，访问需要管理员权限的API（获取用户列表、获取用户权限信息、获取当前用户信息）仍然收到401未认证错误。

## 问题分析
通过分析发现了两个主要问题：

### 1. Swagger JWT认证配置问题
- 在`main.ts`中已经配置了JWT认证方案，名称为'JWT-auth'
- 但在`auth.controller.ts`中的`@ApiBearerAuth()`装饰器没有指定认证方案名称
- 导致Swagger无法正确关联JWT认证配置

### 2. Admin用户角色问题
- admin用户虽然已注册，但角色仍为"free"而不是"admin"
- 导致即使token有效，也无法通过管理员权限检查

## 解决方案

### 1. 修复Swagger JWT认证配置
修改`src/auth/auth.controller.ts`中所有需要JWT认证的API：
```typescript
// 修改前
@ApiBearerAuth()

// 修改后
@ApiBearerAuth('JWT-auth')
```

涉及的API：
- `PUT /auth/users/:id/role` - 设置用户角色
- `GET /auth/users/:id/permissions` - 获取用户权限信息
- `GET /auth/users` - 获取用户列表
- `GET /auth/profile` - 获取当前用户信息

### 2. 创建Admin用户管理脚本
创建了`src/scripts/create-admin-user.ts`脚本来直接在数据库中设置admin用户角色：

```typescript
import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { UserRole } from '../auth/enums/user-role.enum';
import { DataSource } from 'typeorm';
import { User } from '../entities/user.entity';

async function createAdminUser() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const dataSource = app.get(DataSource);
  
  const userRepository = dataSource.getRepository(User);
  let adminUser = await userRepository.findOne({ where: { username: 'admin' } });
  
  if (adminUser) {
    adminUser.role = UserRole.ADMIN;
    await userRepository.save(adminUser);
    console.log('admin用户角色已更新为admin');
  }
}
```

### 3. 创建测试脚本
创建了多个测试脚本来验证修复效果：

- `get-admin-token.sh` - 获取admin用户token
- `test-admin-token.sh` - 测试admin权限API
- `setup-admin-user.sh` - 设置admin用户的完整流程

## 修复结果
修复后的测试结果：

```bash
=== 测试Admin Token认证 ===
1. 测试获取当前用户信息 (GET /auth/profile) - HTTP状态码: 200 ✅
2. 测试获取用户列表 (GET /auth/users) - HTTP状态码: 200 ✅  
3. 测试获取用户权限信息 (GET /auth/users/1/permissions) - HTTP状态码: 200 ✅
```

## 经验总结

### 1. Swagger认证配置要点
- 在`main.ts`中使用`addBearerAuth()`配置JWT认证时，要指定名称
- 在控制器中使用`@ApiBearerAuth()`时，必须指定对应的认证方案名称
- 格式：`@ApiBearerAuth('JWT-auth')`

### 2. 用户角色管理
- 新注册的用户默认角色为"free"
- 需要通过数据库操作或专门的脚本来设置admin角色
- 建议在项目初始化时自动创建默认admin用户

### 3. 调试技巧
- 使用curl命令测试API可以快速定位问题
- 检查token内容和用户角色是关键步骤
- 创建测试脚本可以提高调试效率

## 后续优化建议

1. **自动化admin用户创建**：在应用启动时自动检查并创建默认admin用户
2. **角色管理API**：提供更完善的角色管理功能
3. **权限细化**：考虑实现更细粒度的权限控制
4. **测试覆盖**：添加自动化测试来防止类似问题再次发生 