# 常见问题解答 (FAQ)

## JWT认证相关问题

### Q1: 在Swagger中使用JWT token时收到401未认证错误
**问题描述**：已经在Swagger中输入了有效的JWT token，但访问需要认证的API时仍然收到401错误。

**可能原因**：
1. Token格式不正确
2. Token已过期
3. 用户角色权限不足
4. Swagger认证配置问题

**解决方案**：
1. **检查Token格式**：
   - 确保在Swagger中输入的token包含"Bearer "前缀
   - 正确格式：`Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

2. **验证Token有效性**：
   ```bash
   # 使用curl测试token是否有效
   curl -X GET http://localhost:3001/auth/profile \
     -H "Authorization: Bearer your_token_here"
   ```

3. **检查用户角色**：
   - 管理员API需要admin角色
   - 使用以下命令检查用户角色：
   ```bash
   curl -X GET http://localhost:3001/auth/profile \
     -H "Authorization: Bearer your_token_here"
   ```

4. **重新获取Token**：
   ```bash
   curl -X POST http://localhost:3001/auth/login \
     -H "Content-Type: application/json" \
     -d '{"username":"your_username","password":"your_password"}'
   ```

### Q2: Admin用户无法访问管理员API
**问题描述**：admin用户登录成功，但无法访问需要管理员权限的API。

**解决方案**：
1. **检查用户角色**：
   ```bash
   # 获取当前用户信息，查看role字段
   curl -X GET http://localhost:3001/auth/profile \
     -H "Authorization: Bearer your_token_here"
   ```

2. **设置admin角色**：
   如果role不是"admin"，使用以下方法设置：
   
   **方法1 - 使用脚本**：
   ```bash
   npx ts-node src/scripts/create-admin-user.ts
   ```
   
   **方法2 - 直接修改数据库**：
   ```sql
   UPDATE users SET role = 'admin' WHERE username = 'admin';
   ```

3. **重新登录获取新Token**：
   ```bash
   curl -X POST http://localhost:3001/auth/login \
     -H "Content-Type: application/json" \
     -d '{"username":"admin","password":"admin123"}'
   ```

### Q3: 如何创建第一个admin用户？
**问题描述**：系统中没有admin用户，无法管理其他用户权限。

**解决方案**：
1. **注册admin用户**：
   ```bash
   curl -X POST http://localhost:3001/auth/register \
     -H "Content-Type: application/json" \
     -d '{"username":"admin","password":"admin123","email":"<EMAIL>"}'
   ```

2. **设置admin角色**：
   ```bash
   npx ts-node src/scripts/create-admin-user.ts
   ```

3. **验证设置**：
   ```bash
   # 登录获取token
   curl -X POST http://localhost:3001/auth/login \
     -H "Content-Type: application/json" \
     -d '{"username":"admin","password":"admin123"}'
   
   # 测试admin权限
   curl -X GET http://localhost:3001/auth/users \
     -H "Authorization: Bearer your_token_here"
   ```

## API使用相关问题

### Q4: API调用次数限制
**问题描述**：收到API调用次数超限的错误。

**解决方案**：
1. **检查当前限制**：
   ```bash
   curl -X GET http://localhost:3001/auth/profile \
     -H "Authorization: Bearer your_token_here"
   ```
   查看`apiCallsToday`字段

2. **升级用户角色**：
   - free用户：每日100次
   - premium用户：每日1000次
   - enterprise/government用户：无限制

3. **联系管理员升级角色**

### Q5: 如何在代码中使用JWT认证？
**问题描述**：在前端或其他客户端中如何正确使用JWT token。

**解决方案**：
1. **JavaScript/TypeScript示例**：
   ```javascript
   // 登录获取token
   const loginResponse = await fetch('http://localhost:3001/auth/login', {
     method: 'POST',
     headers: {
       'Content-Type': 'application/json',
     },
     body: JSON.stringify({
       username: 'your_username',
       password: 'your_password'
     })
   });
   
   const { access_token } = await loginResponse.json();
   
   // 使用token调用API
   const apiResponse = await fetch('http://localhost:3001/auth/profile', {
     headers: {
       'Authorization': `Bearer ${access_token}`
     }
   });
   ```

2. **Python示例**：
   ```python
   import requests
   
   # 登录获取token
   login_response = requests.post('http://localhost:3001/auth/login', json={
       'username': 'your_username',
       'password': 'your_password'
   })
   
   access_token = login_response.json()['access_token']
   
   # 使用token调用API
   headers = {'Authorization': f'Bearer {access_token}'}
   api_response = requests.get('http://localhost:3001/auth/profile', headers=headers)
   ```

## 数据库相关问题

### Q6: 数据库连接失败
**问题描述**：应用启动时数据库连接失败。

**解决方案**：
1. **检查数据库配置**：
   - 确认PostgreSQL服务正在运行
   - 检查`config/env/development.env`中的数据库配置

2. **检查数据库权限**：
   - 确认数据库用户有足够权限
   - 确认数据库存在

3. **重启数据库服务**：
   ```bash
   # macOS
   brew services restart postgresql
   
   # Linux
   sudo systemctl restart postgresql
   ```

### Q7: 如何重置数据库？
**问题描述**：需要清空数据库重新开始。

**解决方案**：
1. **删除并重新创建数据库**：
   ```sql
   DROP DATABASE your_database_name;
   CREATE DATABASE your_database_name;
   ```

2. **运行迁移**：
   ```bash
   npm run migration:run
   ```

3. **创建admin用户**：
   ```bash
   npx ts-node src/scripts/create-admin-user.ts
   ```

## 开发环境问题

### Q8: 应用启动失败
**问题描述**：运行`npm run start:dev`时应用启动失败。

**解决方案**：
1. **检查依赖**：
   ```bash
   npm install
   ```

2. **检查环境配置**：
   - 确认`.env`文件存在且配置正确
   - 检查数据库连接配置

3. **查看详细错误信息**：
   ```bash
   npm run start:dev -- --verbose
   ```

4. **清理并重新构建**：
   ```bash
   npm run build
   rm -rf dist/
   npm run start:dev
   ```

### Q9: 端口被占用
**问题描述**：3001端口被其他进程占用。

**解决方案**：
1. **查找占用端口的进程**：
   ```bash
   # macOS/Linux
   lsof -i :3001
   
   # Windows
   netstat -ano | findstr :3001
   ```

2. **终止进程**：
   ```bash
   # macOS/Linux
   kill -9 <PID>
   
   # Windows
   taskkill /PID <PID> /F
   ```

3. **或者更改端口**：
   在`main.ts`中修改端口号：
   ```typescript
   await app.listen(3002); // 使用其他端口
   ```

## 联系支持

如果以上解决方案无法解决您的问题，请：

1. 查看应用日志：`logs/error.log` 和 `logs/combined.log`
2. 提供详细的错误信息和重现步骤
3. 联系开发团队获取支持

---

**最后更新时间**：2025-05-29

## 问题1: `/local/constellation/with-tle` 接口没有数据返回

### 问题描述
调用获取具有TLE轨道信息的卫星星座列表接口时，返回空数据或total为0。

### 问题原因
这个问题通常出现在远程服务器部署时，主要原因是：

1. **数据库为空**: PostgreSQL 数据库中的 `satellites` 表没有数据
2. **缺少TLE轨道信息**: 虽然有卫星数据，但没有包含 `orbital_tle` 来源的轨道信息
3. **数据库连接问题**: 无法正确连接到 PostgreSQL 数据库
4. **数据格式问题**: `orbit_info` 字段格式不符合预期

### 解决步骤

#### 步骤1: 使用诊断脚本检查问题
```bash
# 修改脚本中的服务器地址
vim debug-constellation-api.sh
# 将 SERVER_URL 改为您的远程服务器地址

# 运行诊断脚本
./debug-constellation-api.sh
```

#### 步骤2: 使用修复脚本解决问题
```bash
# 修改脚本中的服务器地址
vim fix-remote-constellation-data.sh
# 将 SERVER_URL 改为您的远程服务器地址

# 运行修复脚本
./fix-remote-constellation-data.sh
```

#### 步骤3: 手动解决方案

**如果数据库完全为空:**
```bash
# 获取认证令牌
./get-admin-token.sh

# 执行数据聚合
curl -X POST "http://your-server:3000/local/satellite/incremental-aggregate" \
  -H "Authorization: Bearer $(cat token.json | jq -r '.token')" \
  -H "Content-Type: application/json" \
  -d '{"saveToDatabase": true}'
```

**如果有数据但没有TLE信息:**
```bash
# 更新TLE轨道数据
curl -X POST "http://your-server:3000/api/es/orbit/bulk-tle" \
  -H "Authorization: Bearer $(cat token.json | jq -r '.token')" \
  -H "Content-Type: application/json" \
  -d '{"noradIds": [], "forceUpdate": true}'
```

**如果需要更新星座信息:**
```bash
# 更新星座信息
curl -X POST "http://your-server:3000/local/constellation/update" \
  -H "Authorization: Bearer $(cat token.json | jq -r '.token')" \
  -H "Content-Type: application/json"
```

### 预防措施

1. **部署后立即执行数据初始化**:
   ```bash
   # 在新服务器部署后运行
   ./fix-remote-constellation-data.sh
   ```

2. **设置定时任务自动同步数据**:
   - 使用卫星调度器自动执行增量聚合
   - 配置 `SATELLITE_SCHEDULER_ENABLED=true`

3. **监控数据状态**:
   ```bash
   # 定期检查数据状态
   curl -X GET "http://your-server:3000/api/database/health" \
     -H "Authorization: Bearer $TOKEN"
   ```

### 相关接口说明

- `GET /local/constellation/with-tle` - 获取具有TLE轨道信息的卫星星座列表
- `POST /local/satellite/incremental-aggregate` - 执行卫星数据增量聚合  
- `POST /local/constellation/update` - 更新卫星星座信息
- `POST /api/es/orbit/bulk-tle` - 批量更新TLE轨道数据
- `GET /api/database/health` - 检查数据库连接状态

### 技术细节

**查询逻辑:**
```sql
SELECT s.* 
FROM satellites s 
WHERE s.orbit_info IS NOT NULL 
  AND s.orbit_info::text != '[]'
  AND (
    s.orbit_info::text ILIKE '%orbital_tle%'
    OR jsonb_path_exists(s.orbit_info, '$[*].sources[*] ? (@ == "orbital_tle")')
  )
```

**数据格式要求:**
- `orbit_info` 必须是 JSONB 格式
- 必须包含 `sources` 数组，其中包含 `"orbital_tle"` 字符串
- `constellation` 字段格式: `[{"value": "星座名称", "sources": ["来源"]}]`

### 日志排查

如果问题仍然存在，可以检查服务器日志：

```bash
# 查看相关日志
grep -A 20 "查询具有TLE轨道信息的卫星星座" logs/combined.log
grep "数据库中卫星总数" logs/combined.log
grep "找到.*个具有TLE轨道信息的卫星记录" logs/combined.log
```

启用调试日志:
```bash
# 设置环境变量
export LOG_LEVEL=debug
# 或在应用配置中设置日志级别
``` 