/**
 * 发射场数据类型定义
 */
export interface LaunchSite {
  code: string;          // 发射场代码
  englishName: string;   // 英文名称
  chineseName: string;   // 中文名称
  country?: string;      // 所属国家（可选）
  category: 'USA' | 'China' | 'Russia' | 'Other' | 'Commercial' | 'TestSite';  // 分类
  aliases?: string[];    // 别名列表
}

/**
 * 发射场数据
 */
export const launchSites: LaunchSite[] = [
  // 美国发射场
  {
    code: 'CC',
    englishName: 'Cape Canaveral Space Force Station',
    chineseName: '卡纳维拉尔角航天发射场',
    category: 'USA',
    aliases: [
      'Cape Canaveral SFS, FL, USA',
      'CCAFS',
      'Cape Canaveral Air Force Station',
      'Cape Kennedy Air Force Station',
      'Cape Kennedy',
      '卡纳维拉尔角'
    ]
  },
  {
    code: 'KSC',
    englishName: 'Kennedy Space Center',
    chineseName: '肯尼迪航天中心',
    category: 'USA',
    aliases: [
      'Kennedy Space Center, FL, USA',
      'NASA Kennedy',
      'John <PERSON>. Kennedy Space Center'
    ]
  },
  {
    code: 'VSFB',
    englishName: 'Vandenberg Space Force Base',
    chineseName: '范登堡空军基地',
    category: 'USA',
    aliases: [
      'Vandenberg SFB, CA, USA',
      'VAFB',
      'Vandenberg Air Force Base',
      'Western Launch Site'
    ]
  },
  {
    code: 'SPAM',
    englishName: 'Spaceport America',
    chineseName: '美国航天港',
    category: 'USA',
    aliases: [
      'Spaceport America, NM, USA',
      'Sierra County Spaceport'
    ]
  },
  {
    code: 'WFF',
    englishName: 'Wallops Flight Facility',
    chineseName: '沃洛普斯飞行设施',
    category: 'USA',
    aliases: [
      'Wallops Flight Facility, Virginia, USA',
      'NASA Wallops',
      'Wallops Island'
    ]
  },

  // 中国发射场
  {
    code: 'JQ',
    englishName: 'Jiuquan Satellite Launch Center',
    chineseName: '酒泉卫星发射中心',
    category: 'China',
    aliases: [
      'JSLC',
      'Jiuquan Satellite Launch Center, People\'s Republic of China',
      '东风航天城',
      '酒泉发射场'
    ]
  },
  {
    code: 'TYSC',
    englishName: 'Taiyuan Satellite Launch Center',
    chineseName: '太原卫星发射中心',
    category: 'China',
    aliases: [
      'TSLC',
      'Taiyuan Satellite Launch Center, People\'s Republic of China',
      '太原发射场',
      'Wuzhai'
    ]
  },
  {
    code: 'XSC',
    englishName: 'Xichang Satellite Launch Center',
    chineseName: '西昌卫星发射中心',
    category: 'China',
    aliases: [
      'XSLC',
      'Xichang Satellite Launch Center, People\'s Republic of China',
      '西昌发射场'
    ]
  },
  {
    code: 'WEN',
    englishName: 'Wenchang Space Launch Site',
    chineseName: '文昌航天发射场',
    category: 'China',
    aliases: [
      'WSLC',
      'Wenchang Satellite Launch Center',
      'Wenchang Space Launch Site, People\'s Republic of China',
      '文昌发射场'
    ]
  },

  // 俄罗斯发射场
  {
    code: 'VOST',
    englishName: 'Vostochny Cosmodrome',
    chineseName: '东方航天发射场',
    category: 'Russia',
    aliases: [
      'Vostochny Cosmodrome, Russian Federation',
      'Восточный космодром'
    ]
  },
  {
    code: 'PLSK',
    englishName: 'Plesetsk Cosmodrome',
    chineseName: '普列谢茨克航天发射场',
    category: 'Russia',
    aliases: [
      'Plesetsk Cosmodrome, Russian Federation',
      'Космодром Плесецк',
      'GIK-1'
    ]
  },
  {
    code: 'BAI',
    englishName: 'Baikonur Cosmodrome',
    chineseName: '拜科努尔航天发射场',
    category: 'Russia',
    aliases: [
      'Baikonur Cosmodrome, Republic of Kazakhstan',
      'Космодром Байконур',
      'NIIP-5',
      'Tyuratam'
    ]
  },

  // 其他国家主要发射场
  {
    code: 'SHAR',
    englishName: 'Satish Dhawan Space Centre',
    chineseName: '萨迪什·达万航天中心',
    category: 'Other',
    country: '印度',
    aliases: [
      'SDSC',
      'Satish Dhawan Space Centre, India',
      'Sriharikota Range',
      'SHAR Centre'
    ]
  },
  {
    code: 'CSG',
    englishName: 'Guiana Space Centre',
    chineseName: '圭亚那航天中心',
    category: 'Other',
    country: '法属圭亚那',
    aliases: [
      'Guiana Space Centre, French Guiana',
      'Centre Spatial Guyanais',
      'Kourou'
    ]
  },
  {
    code: 'NARO',
    englishName: 'Naro Space Center',
    chineseName: '罗老宇航中心',
    category: 'Other',
    country: '韩国',
    aliases: [
      'Naro Space Center, South Korea',
      'Naro Space Port',
      '나로우주센터'
    ]
  },
  {
    code: 'TNSC',
    englishName: 'Tanegashima Space Center',
    chineseName: '种子岛航天中心',
    category: 'Other',
    country: '日本',
    aliases: [
      'Tanegashima Space Center, Japan',
      '種子島宇宙センター',
      'JAXA Tanegashima'
    ]
  },
  {
    code: 'MAHIA',
    englishName: 'Rocket Lab Launch Complex 1',
    chineseName: '火箭实验室1号发射场',
    category: 'Other',
    country: '新西兰',
    aliases: [
      'Rocket Lab Launch Complex 1, Mahia Peninsula, New Zealand',
      'LC-1',
      'Mahia Peninsula Launch Site'
    ]
  },

  // 商业航天发射场
  {
    code: 'SPXMCG',
    englishName: 'SpaceX Starbase',
    chineseName: 'SpaceX星舰基地',
    category: 'Commercial',
    aliases: [
      'SpaceX Starbase, TX, USA',
      'Boca Chica Launch Site',
      'Starbase, Texas'
    ]
  },
  {
    code: 'ARNHEM',
    englishName: 'Arnhem Space Centre',
    chineseName: '阿纳姆航天中心',
    category: 'Commercial',
    aliases: [
      'Arnhem Space Centre, Australia',
      'Equatorial Launch Australia'
    ]
  },
  {
    code: 'KOON',
    englishName: 'Koonibba Test Range',
    chineseName: '库尼巴试验场',
    category: 'Commercial',
    aliases: [
      'Koonibba Test Range, South Australia',
      'Southern Launch Koonibba'
    ]
  },

  // 主要试验场
  {
    code: 'WS',
    englishName: 'White Sands Missile Range',
    chineseName: '白沙导弹靶场',
    category: 'TestSite',
    aliases: [
      'White Sands Missile Range, NM, USA',
      'WSMR',
      'White Sands Space Harbor'
    ]
  },
  {
    code: 'KMR',
    englishName: 'Ronald Reagan Ballistic Missile Defense Test Site',
    chineseName: '罗纳德·里根弹道导弹防御试验场',
    category: 'TestSite',
    aliases: [
      'Reagan Test Site',
      'Kwajalein Missile Range',
      'RTS'
    ]
  },
  {
    code: 'WWAY',
    englishName: 'Whalers Way Orbital Launch Complex',
    chineseName: '捕鲸人之路轨道发射综合体',
    category: 'TestSite',
    aliases: [
      'Whalers Way Orbital Launch Complex, South Australia',
      'Southern Launch WWOLC'
    ]
  },

  // 法国发射场
  {
    code: 'CIEES',
    englishName: 'Centre interarmées d\'essais d\'engins spéciaux (CIEES)',
    chineseName: '法国军用特种装备试验中心',
    category: 'Other',
    country: '法属阿尔及利亚',
    aliases: [
      'CIEES',
      'Centre interarmées d\'essais d\'engins spéciaux'
    ]
  },
  {
    code: 'HAMM',
    englishName: 'Hammaguir',
    chineseName: '哈马吉尔发射场',
    category: 'Other',
    country: '法属阿尔及利亚',
    aliases: [
      'Hammaguir Launch Site'
    ]
  },
  {
    code: 'REGG',
    englishName: 'Reggane',
    chineseName: '雷加纳试验场',
    category: 'TestSite',
    country: '法属阿尔及利亚',
    aliases: [
      'Reggane Test Site'
    ]
  },

  // 意大利发射场
  {
    code: 'BROG',
    englishName: 'Broglio Space Centre (San Marco)',
    chineseName: '布罗格利奥航天中心',
    category: 'Other',
    country: '意大利',
    aliases: [
      'San Marco Platform',
      'Broglio Space Centre',
      'Malindi'
    ]
  },

  // 日本发射场（补充）
  {
    code: 'UCHI',
    englishName: 'Uchinoura Space Center',
    chineseName: '内之浦宇宙空间观测所',
    category: 'Other',
    country: '日本',
    aliases: [
      'Uchinoura Space Center, Japan',
      'USC'
    ]
  },
  {
    code: 'AKIT',
    englishName: 'Akita Rocket Range',
    chineseName: '秋田火箭发射场',
    category: 'TestSite',
    country: '日本',
    aliases: [
      'Akita Rocket Range, Japan'
    ]
  },
  {
    code: 'TAIK',
    englishName: 'Taiki Multi-Purpose Aerospace Park',
    chineseName: '大树多用途航空航天公园',
    category: 'TestSite',
    country: '日本',
    aliases: [
      'Taiki Multi-Purpose Aerospace Park, Japan',
      'Taiki, Hokkaido'
    ]
  },
  {
    code: 'SPKII',
    englishName: 'Spaceport Kii',
    chineseName: '纪伊航天港',
    category: 'Commercial',
    country: '日本',
    aliases: [
      'Spaceport Kii, Japan',
      'Kushimoto'
    ]
  },

  // 俄罗斯发射场（补充）
  {
    code: 'SVOB',
    englishName: 'Svobodny Cosmodrome',
    chineseName: '自由港航天发射场',
    category: 'Russia',
    aliases: [
      'Svobodny Cosmodrome, Russian Federation',
      'Amur Oblast'
    ]
  },
  {
    code: 'YASN',
    englishName: 'Yasny Cosmodrome',
    chineseName: '亚斯内航天发射场',
    category: 'Russia',
    aliases: [
      'Yasny Cosmodrome, Russian Federation',
      'Dombarovsky'
    ]
  },
  {
    code: 'KAPU',
    englishName: 'Kapustin Yar',
    chineseName: '卡普斯京亚尔试验场',
    category: 'Russia',
    aliases: [
      'Kapustin Yar, Russian Federation',
      'Astrakhan Oblast'
    ]
  },

  // 挪威发射场
  {
    code: 'ANDO',
    englishName: 'Andøya Space Center',
    chineseName: '安德亚航天中心',
    category: 'Other',
    country: '挪威',
    aliases: [
      'Andøya Space Center, Norway'
    ]
  },

  // 瑞典发射场
  {
    code: 'ESRA',
    englishName: 'Esrange',
    chineseName: '埃斯兰奇航天中心',
    category: 'Other',
    country: '瑞典',
    aliases: [
      'Esrange, Sweden',
      'Kiruna'
    ]
  },

  // 西班牙发射场
  {
    code: 'AREN',
    englishName: 'El Arenosillo',
    chineseName: '埃尔阿雷诺西略发射场',
    category: 'Other',
    country: '西班牙',
    aliases: [
      'El Arenosillo, Spain'
    ]
  },

  // 巴西发射场
  {
    code: 'ALCA',
    englishName: 'Alcântara Launch Center',
    chineseName: '阿尔坎塔拉发射中心',
    category: 'Other',
    country: '巴西',
    aliases: [
      'Alcântara Launch Center, Brazil',
      'Maranhão'
    ]
  },

  // 澳大利亚发射场（补充）
  {
    code: 'WOOM',
    englishName: 'Woomera Test Range',
    chineseName: '伍默拉试验场',
    category: 'TestSite',
    country: '澳大利亚',
    aliases: [
      'Woomera Test Range, Australia'
    ]
  },

  // 美国发射场（补充）
  {
    code: 'MOJA',
    englishName: 'Mojave Air and Space Port',
    chineseName: '莫哈韦航空航天港',
    category: 'Commercial',
    aliases: [
      'Mojave Air and Space Port, CA, USA',
      'Mojave Spaceport'
    ]
  },
  {
    code: 'PACS',
    englishName: 'Pacific Spaceport Complex',
    chineseName: '太平洋航天综合体',
    category: 'Commercial',
    aliases: [
      'Pacific Spaceport Complex, Alaska, USA',
      'Kodiak'
    ]
  },
  {
    code: 'MARS',
    englishName: 'Mid-Atlantic Regional Spaceport',
    chineseName: '中大西洋地区航天港',
    category: 'Commercial',
    aliases: [
      'Mid-Atlantic Regional Spaceport, VA, USA',
      'MARS'
    ]
  }
];

/**
 * 根据发射场代码获取发射场信息
 * @param code 发射场代码
 * @returns 发射场信息，如果未找到则返回undefined
 */
export function getLaunchSiteByCode(code: string): LaunchSite | undefined {
  return launchSites.find(site => site.code === code);
}

/**
 * 根据分类获取发射场列表
 * @param category 分类
 * @returns 该分类下的所有发射场
 */
export function getLaunchSitesByCategory(category: LaunchSite['category']): LaunchSite[] {
  return launchSites.filter(site => site.category === category);
}

/**
 * 获取所有发射场代码
 * @returns 所有发射场代码数组
 */
export function getAllLaunchSiteCodes(): string[] {
  return launchSites.map(site => site.code);
}

/**
 * 根据国家获取发射场列表
 * @param country 国家名称
 * @returns 该国家的所有发射场
 */
export function getLaunchSitesByCountry(country: string): LaunchSite[] {
  return launchSites.filter(site => site.country === country);
}

/**
 * 根据名称或别名查找发射场
 * @param name 发射场名称或别名
 * @returns 匹配的发射场信息，如果未找到则返回undefined
 */
export function findLaunchSiteByName(name: string): LaunchSite | undefined {
  const normalizedName = name.toLowerCase().trim();
  return launchSites.find(site => 
    site.englishName.toLowerCase() === normalizedName ||
    site.chineseName === name ||
    site.aliases?.some(alias => alias.toLowerCase() === normalizedName)
  );
}

/**
 * 模糊搜索发射场
 * @param keyword 搜索关键词
 * @returns 匹配的发射场列表
 */
export function searchLaunchSites(keyword: string): LaunchSite[] {
  const normalizedKeyword = keyword.toLowerCase().trim();
  return launchSites.filter(site => 
    site.englishName.toLowerCase().includes(normalizedKeyword) ||
    site.chineseName.includes(keyword) ||
    site.aliases?.some(alias => alias.toLowerCase().includes(normalizedKeyword))
  );
}

/**
 * 根据发射场代码或任何标识符获取其中文名称
 * @param identifier 发射场代码、英文名、中文名或别名
 * @returns 发射场的中文名称，如果未找到则返回原始输入
 */
export function getLaunchSiteChineseName(identifier: string): string {
  if (!identifier || typeof identifier !== 'string') {
    return identifier;
  }

  const normalizedInput = identifier.toLowerCase().trim();
  
  const site = launchSites.find(site => 
    site.code.toLowerCase() === normalizedInput ||
    site.englishName.toLowerCase() === normalizedInput ||
    site.chineseName === identifier.trim() ||
    site.aliases?.some(alias => alias.toLowerCase() === normalizedInput)
  );

  return site ? site.chineseName : identifier;
} 