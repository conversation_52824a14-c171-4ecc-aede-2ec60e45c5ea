const fs = require('fs');

// 读取文件
const tleContent = fs.readFileSync('starlink_tle.txt', 'utf-8').trim();
const satInfoContent = fs.readFileSync('starlink_satinfo.txt', 'utf-8').trim();
const diffContent = fs.readFileSync('diff_id.txt', 'utf-8').trim();

// 解析TLE文件中的NORAD_ID
const tleNoradIds = new Set();
const tleLines = tleContent.split('\n');
console.log(`TLE文件总行数: ${tleLines.length}`);

tleLines.forEach((line, index) => {
  const parts = line.split(',');
  if (parts.length >= 2 && parts[1]) {
    const noradId = parts[1].trim();
    if (noradId && !isNaN(noradId)) {
      tleNoradIds.add(parseInt(noradId));
    }
  }
});

console.log(`TLE文件中的NORAD_ID数量: ${tleNoradIds.size}`);

// 解析API文件中的NORAD_ID
const satInfoNoradIds = new Set();
const satInfoLines = satInfoContent.split('\n');
console.log(`API文件总行数: ${satInfoLines.length}`);

satInfoLines.forEach((line, index) => {
  const parts = line.split('|');
  if (parts.length >= 3 && parts[2]) {
    const noradId = parts[2].trim();
    if (noradId && !isNaN(noradId)) {
      satInfoNoradIds.add(parseInt(noradId));
    }
  }
});

console.log(`API文件中的NORAD_ID数量: ${satInfoNoradIds.size}`);

// 解析diff文件中的缺失NORAD_ID
const diffLines = diffContent.split('\n');
const missingNoradIds = [];
let inNoradSection = false;

diffLines.forEach(line => {
  line = line.trim();
  if (line === '缺失的 NORAD_ID:') {
    inNoradSection = true;
    return;
  }
  if (line === '' || line.startsWith('缺失的 COSPAR_ID:')) {
    inNoradSection = false;
    return;
  }
  if (inNoradSection && line && !isNaN(line)) {
    missingNoradIds.push(parseInt(line));
  }
});

console.log(`差异文件中标记为缺失的NORAD_ID数量: ${missingNoradIds.length}`);

// 检查前10个"缺失"的ID是否真的在TLE文件中存在
console.log('\n检查前10个"缺失"的NORAD_ID:');
for (let i = 0; i < Math.min(10, missingNoradIds.length); i++) {
  const id = missingNoradIds[i];
  const inTle = tleNoradIds.has(id);
  const inSatInfo = satInfoNoradIds.has(id);
  console.log(`NORAD_ID ${id}: TLE中存在=${inTle}, API中存在=${inSatInfo}`);
  
  if (inTle) {
    console.log(`  ❌ 错误！${id} 在TLE文件中存在，但被标记为缺失`);
  }
}

// 重新计算真正缺失的ID
const reallyMissingIds = [];
satInfoNoradIds.forEach(id => {
  if (!tleNoradIds.has(id)) {
    reallyMissingIds.push(id);
  }
});

console.log(`\n重新计算：真正缺失的NORAD_ID数量: ${reallyMissingIds.length}`);
console.log(`原差异文件显示缺失数量: ${missingNoradIds.length}`);

if (reallyMissingIds.length !== missingNoradIds.length) {
  console.log('❌ 差异计算有误！');
  
  // 找出错误标记的ID
  const wronglyMarked = missingNoradIds.filter(id => tleNoradIds.has(id));
  if (wronglyMarked.length > 0) {
    console.log(`\n错误标记为缺失的ID (前10个):`);
    wronglyMarked.slice(0, 10).forEach(id => {
      console.log(`  ${id} - 实际在TLE文件中存在`);
    });
  }
} else {
  console.log('✅ 差异计算正确');
} 