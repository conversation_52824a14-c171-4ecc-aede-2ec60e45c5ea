#!/bin/bash

echo "=== 诊断 /local/constellation/with-tle 接口问题 ==="
echo "当前时间: $(date)"
echo ""

# 设置服务器地址（请根据实际情况修改）
SERVER_URL="http://localhost:3000"  # 替换为您的远程服务器地址
TOKEN_FILE="token.json"

# 检查 token 文件是否存在
if [[ ! -f "$TOKEN_FILE" ]]; then
    echo "❌ 错误: token.json 文件不存在，请先获取认证令牌"
    echo "   运行: ./get-token.sh 或 ./get-admin-token.sh"
    exit 1
fi

# 读取 token
TOKEN=$(cat "$TOKEN_FILE" | jq -r '.token // .access_token // .')
if [[ "$TOKEN" == "null" || -z "$TOKEN" ]]; then
    echo "❌ 错误: 无法从 token.json 中读取有效的认证令牌"
    exit 1
fi

echo "✅ 认证令牌已获取"
echo ""

echo "=== 步骤1: 检查数据库连接状态 ==="
curl -s -X GET "$SERVER_URL/api/database/health" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.'
echo ""

echo "=== 步骤2: 检查卫星表状态 ==="
curl -s -X GET "$SERVER_URL/api/database/table-info?table_name=satellites" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.'
echo ""

echo "=== 步骤3: 检查卫星数据总数 ==="
curl -s -X POST "$SERVER_URL/api/database/query" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "SELECT COUNT(*) as total_satellites FROM satellites",
    "params": []
  }' | jq '.'
echo ""

echo "=== 步骤4: 检查具有 orbit_info 的卫星数量 ==="
curl -s -X POST "$SERVER_URL/api/database/query" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "SELECT COUNT(*) as satellites_with_orbit_info FROM satellites WHERE orbit_info IS NOT NULL AND orbit_info != '\''[]'\''",
    "params": []
  }' | jq '.'
echo ""

echo "=== 步骤5: 检查具有 TLE 数据的卫星数量 ==="
curl -s -X POST "$SERVER_URL/api/database/query" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "SELECT COUNT(*) as satellites_with_tle FROM satellites WHERE orbit_info IS NOT NULL AND orbit_info::text != '\''[]'\'' AND (orbit_info::text ILIKE '\''%orbital_tle%'\'' OR jsonb_path_exists(orbit_info, '\''$[*].sources[*] ? (@ == \"orbital_tle\")'\''))",
    "params": []
  }' | jq '.'
echo ""

echo "=== 步骤6: 查看前5条具有 TLE 数据的卫星示例 ==="
curl -s -X POST "$SERVER_URL/api/database/query" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "SELECT id, satellite_name, constellation, orbit_info FROM satellites WHERE orbit_info IS NOT NULL AND orbit_info::text != '\''[]'\'' AND (orbit_info::text ILIKE '\''%orbital_tle%'\'' OR jsonb_path_exists(orbit_info, '\''$[*].sources[*] ? (@ == \"orbital_tle\")'\'')) LIMIT 5",
    "params": []
  }' | jq '.'
echo ""

echo "=== 步骤7: 调用实际接口 ==="
echo "调用 /local/constellation/with-tle 接口..."
RESPONSE=$(curl -s -X GET "$SERVER_URL/local/constellation/with-tle" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json")

echo "接口响应:"
echo "$RESPONSE" | jq '.'

# 分析响应
TOTAL=$(echo "$RESPONSE" | jq -r '.total // 0')
if [[ "$TOTAL" == "0" ]]; then
    echo ""
    echo "❌ 问题确认: 接口返回 total=0，说明没有找到具有TLE轨道信息的卫星星座"
else
    echo ""
    echo "✅ 接口正常: 找到 $TOTAL 个星座"
fi

echo ""
echo "=== 诊断完成 ==="
echo "请查看以上输出结果，并根据问题进行相应的数据导入或修复操作" 