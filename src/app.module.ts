import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from './auth/auth.module';
import { ESModule } from './elasticsearch/elasticsearch.module';
import databaseConfig from './config/database.config';
import { OrbitAnalysisModule } from './modules/orbit-analysis.module';
import { SatelliteModule } from './modules/satellite.module';
import { AggregationTaskModule } from './modules/aggregation-task.module';
import { DatabaseModule } from './modules/database.module';
import { SatelliteTilesModule } from './modules/satellite-tiles.module';
import { NewsSchedulerModule } from './modules/news-scheduler.module';
import { SatelliteSchedulerModule } from './modules/satellite-scheduler.module';
import { SatelliteAggregationController } from './controllers/satellite-aggregation.controller';
import { APP_GUARD } from '@nestjs/core';
import { JwtAuthGuard } from './auth/guards/jwt-auth.guard';
import { UserModule } from './modules/user/user.module';
import { LLMConfigModule } from './modules/llm-config.module';
import { AppDataSource } from './data-source';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot(AppDataSource.options),
    AuthModule,
    ESModule,
    OrbitAnalysisModule,
    SatelliteModule,
    AggregationTaskModule,
    DatabaseModule,
    SatelliteTilesModule,
    NewsSchedulerModule,
    SatelliteSchedulerModule,
    UserModule,
    LLMConfigModule,
  ],
  controllers: [
    SatelliteAggregationController,
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
  ],
})
export class AppModule {} 