import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, IsEnum, Min, Max, IsBoolean } from 'class-validator';

/**
 * 支持的大模型提供商
 */
export enum LLMProvider {
  QWEN = 'qwen',
  OPENAI = 'openai',
  CLAUDE = 'claude',
  GLM = 'glm'
}

/**
 * 配置类型枚举
 */
export enum ConfigType {
  TRANSLATION = 'translation',
  THEME_EXTRACTION = 'theme_extraction'
}

/**
 * 更新大模型配置DTO
 */
export class UpdateLLMConfigDto {
  @ApiProperty({ 
    description: '配置类型',
    enum: ConfigType,
    example: ConfigType.TRANSLATION
  })
  @IsEnum(ConfigType)
  configType: ConfigType;

  @ApiPropertyOptional({ 
    description: '大模型提供商',
    enum: LLMProvider,
    example: LLMProvider.QWEN
  })
  @IsOptional()
  @IsEnum(LLMProvider)
  provider?: LLMProvider;

  @ApiPropertyOptional({ 
    description: '模型名称',
    example: 'qwen-turbo'
  })
  @IsOptional()
  @IsString()
  model?: string;

  @ApiPropertyOptional({ 
    description: 'API密钥',
    example: 'sk-1b581fd6f319419d9d0e3f2cc855c10d'
  })
  @IsOptional()
  @IsString()
  apiKey?: string;

  @ApiPropertyOptional({ 
    description: 'API基础URL',
    example: 'https://dashscope.aliyuncs.com/compatible-mode/v1'
  })
  @IsOptional()
  @IsString()
  baseURL?: string;

  @ApiPropertyOptional({ 
    description: '系统提示词',
    example: '你是一位专业翻译，特别是很擅长在航空航天领域的翻译，将以下内容准确翻译为中文，保留数字和专有名词格式。'
  })
  @IsOptional()
  @IsString()
  systemPrompt?: string;

  @ApiPropertyOptional({ 
    description: '最大令牌数',
    example: 4000,
    minimum: 100,
    maximum: 32000
  })
  @IsOptional()
  @IsNumber()
  @Min(100)
  @Max(32000)
  maxTokens?: number;

  @ApiPropertyOptional({ 
    description: '温度参数（控制随机性）',
    example: 0.1,
    minimum: 0,
    maximum: 2
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(2)
  temperature?: number;

  @ApiPropertyOptional({ 
    description: '请求超时时间（毫秒）',
    example: 30000,
    minimum: 5000,
    maximum: 300000
  })
  @IsOptional()
  @IsNumber()
  @Min(5000)
  @Max(300000)
  timeout?: number;

  @ApiPropertyOptional({ 
    description: '最大重试次数',
    example: 2,
    minimum: 0,
    maximum: 10
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(10)
  maxRetries?: number;

  @ApiPropertyOptional({ 
    description: '重试延迟时间（毫秒）',
    example: 1000,
    minimum: 100,
    maximum: 10000
  })
  @IsOptional()
  @IsNumber()
  @Min(100)
  @Max(10000)
  retryDelay?: number;

  @ApiPropertyOptional({ 
    description: '最大并发请求数',
    example: 3,
    minimum: 1,
    maximum: 10
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  maxConcurrentRequests?: number;
}

/**
 * 大模型配置响应DTO
 */
export class LLMConfigResponseDto {
  @ApiProperty({ 
    description: '配置类型',
    enum: ConfigType
  })
  configType: ConfigType;

  @ApiProperty({ 
    description: '大模型提供商',
    enum: LLMProvider
  })
  provider: LLMProvider;

  @ApiProperty({ 
    description: '模型名称'
  })
  model: string;

  @ApiProperty({ 
    description: 'API基础URL'
  })
  baseURL: string;

  @ApiProperty({ 
    description: 'API密钥（已脱敏）'
  })
  apiKey: string;

  @ApiProperty({ 
    description: '系统提示词'
  })
  systemPrompt: string;

  @ApiProperty({ 
    description: '最大令牌数'
  })
  maxTokens: number;

  @ApiProperty({ 
    description: '温度参数'
  })
  temperature: number;

  @ApiProperty({ 
    description: '请求超时时间（毫秒）'
  })
  timeout: number;

  @ApiProperty({ 
    description: '最大重试次数'
  })
  maxRetries: number;

  @ApiProperty({ 
    description: '重试延迟时间（毫秒）'
  })
  retryDelay: number;

  @ApiProperty({ 
    description: '最大并发请求数'
  })
  maxConcurrentRequests: number;

  @ApiProperty({ 
    description: '配置最后更新时间'
  })
  lastUpdated: string;
}

/**
 * 测试连接DTO
 */
export class TestConnectionDto {
  @ApiProperty({ 
    description: '配置类型',
    enum: ConfigType,
    example: ConfigType.TRANSLATION
  })
  @IsEnum(ConfigType)
  configType: ConfigType;

  @ApiPropertyOptional({ 
    description: '测试文本',
    example: 'Hello world'
  })
  @IsOptional()
  @IsString()
  testText?: string;
}

/**
 * 测试连接响应DTO
 */
export class TestConnectionResponseDto {
  @ApiProperty({ 
    description: '连接测试是否成功'
  })
  success: boolean;

  @ApiProperty({ 
    description: '响应时间（毫秒）'
  })
  responseTime: number;

  @ApiPropertyOptional({ 
    description: '测试结果'
  })
  result?: string;

  @ApiPropertyOptional({ 
    description: '错误信息'
  })
  error?: string;

  @ApiProperty({ 
    description: '测试时间'
  })
  timestamp: string;
}

/**
 * 重置配置DTO
 */
export class ResetConfigDto {
  @ApiProperty({ 
    description: '配置类型',
    enum: ConfigType,
    example: ConfigType.TRANSLATION
  })
  @IsEnum(ConfigType)
  configType: ConfigType;

  @ApiPropertyOptional({ 
    description: '是否确认重置',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  confirm?: boolean;
}

/**
 * 配置统计信息DTO
 */
export class ConfigStatsDto {
  @ApiProperty({ 
    description: '翻译配置信息'
  })
  translation: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    lastUsed: string;
  };

  @ApiProperty({ 
    description: '主题提取配置信息'
  })
  themeExtraction: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    lastUsed: string;
  };

  @ApiProperty({ 
    description: '统计时间'
  })
  timestamp: string;
} 