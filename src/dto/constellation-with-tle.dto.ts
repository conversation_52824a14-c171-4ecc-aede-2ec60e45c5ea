import { ApiProperty } from '@nestjs/swagger';

/**
 * 具有TLE轨道信息的卫星星座响应DTO
 */
export class ConstellationWithTleDto {
  @ApiProperty({
    description: '星座名称',
    example: 'Starlink'
  })
  name: string;

  @ApiProperty({
    description: '该星座下有TLE轨道信息的卫星数量',
    example: 42
  })
  satelliteCount: number;
}

/**
 * 星座查询响应DTO
 */
export class ConstellationsWithTleResponseDto {
  @ApiProperty({
    description: '星座总数',
    example: 5
  })
  total: number;

  @ApiProperty({
    type: [ConstellationWithTleDto],
    description: '星座列表'
  })
  constellations: ConstellationWithTleDto[];
} 