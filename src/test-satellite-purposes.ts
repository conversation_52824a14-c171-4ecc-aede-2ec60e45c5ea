import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DatabaseService } from './services/database.service';
import * as fs from 'fs';

// 定义卫星用途类型接口
interface SatellitePurpose {
  en: string;
  cn: string;
}

interface SatellitePurposesResult {
  success: boolean;
  purposes: SatellitePurpose[];
}

/**
 * 测试卫星用途API
 * 直接调用服务方法并输出结果
 */
async function testSatellitePurposes() {
  try {
    // 创建NestJS应用实例
    const app = await NestFactory.create(AppModule, {
      logger: ['log', 'error', 'warn'],
    });

    // 获取DatabaseService实例
    const databaseService = app.get(DatabaseService);
    
    console.log('调用服务方法测试...');
    
    // 调用getSatellitePurposes方法
    const result = await databaseService.getSatellitePurposes() as SatellitePurposesResult;
    
    // 输出结果到文件
    fs.writeFileSync(
      'api-satellite-purposes-result.json',
      JSON.stringify(result, null, 2)
    );
    
    console.log(`获取到 ${result.purposes.length} 个卫星用途`);
    console.log('结果已写入 api-satellite-purposes-result.json 文件');
    
    // 检查是否所有项目都有中文翻译
    const untranslatedItems = result.purposes.filter((item: SatellitePurpose) => 
      !item.cn || 
      !/[\u4e00-\u9fa5]/.test(item.cn)
    );
    
    if (untranslatedItems.length > 0) {
      console.log('警告: 发现未翻译的项目:');
      untranslatedItems.forEach((item: SatellitePurpose) => {
        console.log(`  - ${item.en}: "${item.cn}"`);
      });
    } else {
      console.log('所有卫星用途项均已翻译成中文，无未翻译项');
    }
    
    // 关闭应用
    await app.close();
    
  } catch (error) {
    console.error('测试过程中发生错误:', error);
  }
}

// 执行测试
testSatellitePurposes();
