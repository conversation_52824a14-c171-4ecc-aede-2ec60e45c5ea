import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

/**
 * 卫星信息实体类
 * 用于存储聚合后的卫星信息
 */
@Entity('satellites')
export class Satellite {
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * 卫星名称 JSON 格式存储多数据源的不同值
   * 数据格式: [{ value: string, sources: string[] }]
   */
  @Column({ type: 'jsonb', nullable: true })
  satellite_name: any[];

  /**
   * 卫星别名 JSON 格式存储多数据源的不同值
   * 数据格式: [{ value: string, sources: string[] }]
   */
  @Column({ type: 'jsonb', nullable: true })
  alternative_name: any[];

  /**
   * COSPAR ID JSON 格式存储多数据源的不同值
   * 数据格式: [{ value: string, sources: string[] }]
   */
  @Column({ type: 'jsonb', nullable: true })
  cospar_id: any[];

  /**
   * 注册国家 JSON 格式存储多数据源的不同值
   * 数据格式: [{ value: string, sources: string[] }]
   */
  @Column({ type: 'jsonb', nullable: true })
  country_of_registry: any[];

  /**
   * 所有者 JSON 格式存储多数据源的不同值
   * 数据格式: [{ value: string, sources: string[] }]
   */
  @Column({ type: 'jsonb', nullable: true })
  owner: any[];

  /**
   * 使用者 JSON 格式存储多数据源的不同值
   * 数据格式: [{ value: string, sources: string[] }]
   */
  @Column({ type: 'jsonb', nullable: true })
  users: any[];

  /**
   * 状态 JSON 格式存储多数据源的不同值
   * 数据格式: [{ value: string, sources: string[] }]
   */
  @Column({ type: 'jsonb', nullable: true })
  status: any[];

  /**
   * NORAD ID JSON 格式存储多数据源的不同值
   * 数据格式: [{ value: string, sources: string[] }]
   */
  @Column({ type: 'jsonb', nullable: true })
  norad_id: any[];

  /**
   * 发射信息 JSON 格式存储多数据源的不同值
   * 数据格式: [{ value: string, sources: string[] }]
   */
  @Column({ type: 'jsonb', nullable: true })
  launch_info: any[];

  /**
   * 轨道信息 JSON 格式存储多数据源的不同值
   * 数据格式: [{ value: string, sources: string[] }]
   */
  @Column({ type: 'jsonb', nullable: true })
  orbit_info: any[];

  /**
   * 质量(kg) JSON 格式存储多数据源的不同值
   * 数据格式: [{ value: string, sources: string[] }]
   */
  @Column({ type: 'jsonb', nullable: true })
  mass_kg: any[];

  /**
   * 功率(W) JSON 格式存储多数据源的不同值
   * 数据格式: [{ value: string, sources: string[] }]
   */
  @Column({ type: 'jsonb', nullable: true })
  power_watts: any[];

  /**
   * 寿命(年) JSON 格式存储多数据源的不同值
   * 数据格式: [{ value: string, sources: string[] }]
   */
  @Column({ type: 'jsonb', nullable: true })
  lifetime_years: any[];

  /**
   * 承包商 JSON 格式存储多数据源的不同值
   * 数据格式: [{ value: string, sources: string[] }]
   */
  @Column({ type: 'jsonb', nullable: true })
  contractor: any[];

  /**
   * 用途 JSON 格式存储多数据源的不同值
   * 数据格式: [{ value: string, sources: string[] }]
   */
  @Column({ type: 'jsonb', nullable: true })
  purpose: any[];

  /**
   * 详细用途 JSON 格式存储多数据源的不同值
   * 数据格式: [{ value: string, sources: string[] }]
   */
  @Column({ type: 'jsonb', nullable: true })
  detailed_purpose: any[];

  /**
   * 有效载荷 JSON 格式存储多数据源的不同值
   * 数据格式: [{ value: string, sources: string[] }]
   */
  @Column({ type: 'jsonb', nullable: true })
  payload: any[];

  /**
   * 有效载荷描述 JSON 格式存储多数据源的不同值
   * 数据格式: [{ value: string, sources: string[] }]
   */
  @Column({ type: 'jsonb', nullable: true })
  payload_description: any[];

  /**
   * 更新时间 JSON 格式存储多数据源的不同值
   * 数据格式: [{ value: string, sources: string[] }]
   */
  @Column({ type: 'jsonb', nullable: true })
  update_time: any[];

  /**
   * 星座信息 JSON 格式存储多数据源的不同值
   * 数据格式: [{ value: string, sources: string[] }]
   */
  @Column({ type: 'jsonb', nullable: true, default: '[]' })
  constellation: any[];

  /**
   * 数据来源
   */
  @Column({ type: 'jsonb', default: [] })
  _sources: string[];

  /**
   * 原始相似度信息 - 用于debug
   */
  @Column({ type: 'jsonb', nullable: true })
  _similarity_info: any;

  /**
   * 创建时间
   */
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  /**
   * 更新时间
   */
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
} 