import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

/**
 * 聚合任务实体类
 * 用于记录卫星数据聚合任务的执行状态和结果
 */
@Entity('aggregation_tasks')
export class AggregationTask {
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * 任务类型
   * - 'full': 全量聚合
   * - 'partial': 部分聚合（有limit限制）
   * - 'keyword': 关键词聚合
   * - 'incremental': 增量聚合（只聚合新的卫星数据）
   */
  @Column({ type: 'varchar', length: 50 })
  task_type: string;

  /**
   * 任务状态
   * - 'pending': 等待执行
   * - 'running': 执行中
   * - 'completed': 已完成
   * - 'failed': 失败
   */
  @Column({ type: 'varchar', length: 50 })
  status: string;

  /**
   * 任务开始时间
   */
  @Column({ type: 'timestamp' })
  start_time: Date;

  /**
   * 任务结束时间
   */
  @Column({ type: 'timestamp', nullable: true })
  end_time: Date;

  /**
   * 处理的记录数
   */
  @Column({ type: 'int', default: 0 })
  processed_records: number;

  /**
   * 完成聚合的记录数
   */
  @Column({ type: 'int', default: 0 })
  aggregated_records: number;

  /**
   * 任务参数（JSON格式）
   * 例如：{limit: 1000, keyword: 'ISS', saveToDatabase: true}
   */
  @Column({ type: 'jsonb', nullable: true })
  parameters: any;

  /**
   * 错误信息（如果任务失败）
   */
  @Column({ type: 'text', nullable: true })
  error_message: string;

  /**
   * 任务进度百分比 (0-100)
   */
  @Column({ type: 'float', default: 0 })
  progress: number;

  /**
   * 创建时间
   */
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  /**
   * 更新时间
   */
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
} 