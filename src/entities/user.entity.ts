import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, DeleteDateColumn, Unique } from 'typeorm';
import { UserRole } from '../auth/enums/user-role.enum';
import { UserApprovalStatus } from '../auth/enums/user-approval-status.enum';
import { Exclude } from 'class-transformer';

/**
 * 用户实体类
 * @description 存储用户基本信息和审批状态
 */
@Entity('users')
@Unique(['username'])
@Unique(['email'])
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 100 })
  username: string;

  @Column({ type: 'varchar', length: 100 })
  @Exclude()
  password: string;

  @Column({ type: 'varchar', length: 100 })
  email: string;

  @Column({
    type: 'varchar',
    length: 20,
    default: UserRole.FREE
  })
  role: UserRole;

  @Column({
    name: 'approval_status',
    type: 'varchar',
    length: 20,
    default: UserApprovalStatus.PENDING
  })
  approvalStatus: UserApprovalStatus;

  @Column({ 
    name: 'approved_at', 
    type: 'timestamp with time zone', 
    nullable: true,
    comment: '审批操作时间（包括批准和拒绝）'
  })
  approvedAt?: Date;

  @Column({ 
    name: 'approved_by', 
    type: 'integer', 
    nullable: true,
    comment: '执行审批操作的管理员用户ID（包括批准和拒绝）'
  })
  approvedBy?: number;

  @Column({ name: 'rejection_reason', type: 'varchar', length: 500, nullable: true })
  rejectionReason?: string;

  @Column({ name: 'api_calls_today', type: 'integer', default: 0 })
  apiCallsToday: number;

  @Column({ name: 'downloads_today', type: 'integer', default: 0 })
  downloadsToday: number;

  @Column({ name: 'last_api_reset', type: 'timestamp with time zone', default: () => 'CURRENT_TIMESTAMP' })
  lastApiReset: Date;

  @Column({ name: 'avatar_url', type: 'varchar', length: 255, nullable: true })
  avatarUrl?: string;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp with time zone' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp with time zone' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at', type: 'timestamp with time zone', nullable: true })
  deletedAt?: Date;
} 