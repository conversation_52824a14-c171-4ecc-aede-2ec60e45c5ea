import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../entities/user.entity';
import { UserRole } from '../enums/user-role.enum';
import { UserApprovalStatus } from '../enums/user-approval-status.enum';

/**
 * 管理员权限守卫
 * @description 检查当前用户是否为管理员，只有管理员才能执行特定操作
 */
@Injectable()
export class AdminGuard implements CanActivate {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // 检查是否已通过JWT认证
    if (!user || !user.id) {
      throw new ForbiddenException('未登录用户无法访问此资源');
    }

    // 检查用户是否为管理员
    if (user.role !== UserRole.ADMIN) {
      throw new ForbiddenException('只有管理员才能执行此操作');
    }

    // 检查管理员账户状态
    if (user.approvalStatus !== UserApprovalStatus.APPROVED) {
      throw new ForbiddenException('管理员账户未通过审批');
    }

    if (!user.isActive) {
      throw new ForbiddenException('管理员账户已被禁用');
    }

    // 将完整的用户信息添加到请求对象中，供后续使用
    request.adminUser = user;
    
    return true;
  }
}

/**
 * 管理员或所有者权限守卫
 * @description 确保只有管理员或资源所有者才能访问受保护的资源
 */
@Injectable()
export class AdminOrOwnerGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user: User = request.user;
    const targetUserId = parseInt(request.params.id);

    if (!user) {
      throw new ForbiddenException('未找到用户信息');
    }

    // 管理员可以访问任何资源
    if (user.role === UserRole.ADMIN) {
      return true;
    }

    // 用户只能访问自己的资源
    if (user.id === targetUserId) {
      return true;
    }

    throw new ForbiddenException('您只能访问自己的资源');
  }
} 