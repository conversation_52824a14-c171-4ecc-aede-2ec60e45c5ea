import { Injectable, ConflictException, UnauthorizedException, BadRequestException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, Like } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { User } from '../entities/user.entity';
import { RegisterDto, LoginDto } from './dto/auth.dto';
import { SetUserRoleDto, UserPermissionResponseDto, GetUsersQueryDto, UsersListResponseDto } from './dto/permission.dto';
import { UserApprovalDto, PendingUsersQueryDto, PendingUsersResponseDto, UserApprovalResponseDto, ApprovalResultResponseDto } from './dto/user-approval.dto';
import { UserRole } from './enums/user-role.enum';
import { UserApprovalStatus } from './enums/user-approval-status.enum';
import { DeleteUserResponseDto } from './dto/user-delete.dto';
import { UserService } from '../modules/user/user.service';
import { ChangePasswordDto } from './dto/change-password.dto';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly jwtService: JwtService,
    private readonly dataSource: DataSource,
    private readonly userService: UserService,
  ) {}

  /**
   * 用户注册
   * @param registerDto 注册信息
   * @returns 注册成功的用户信息（不含密码），新用户状态为待审批
   */
  async register(registerDto: RegisterDto): Promise<Omit<User, 'password'>> {
    const { username, password, email } = registerDto;

    // 验证邮箱格式
    if (!email) {
      throw new BadRequestException('电子邮箱是必填项');
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new BadRequestException('请输入有效的电子邮箱地址');
    }

    return await this.dataSource.transaction(async transactionalEntityManager => {
      // 在事务中检查用户名和邮箱是否已存在
      const [existingUserByUsername, existingUserByEmail] = await Promise.all([
        transactionalEntityManager.findOne(User, { 
          where: { username },
          withDeleted: true,
        }),
        transactionalEntityManager.findOne(User, { 
          where: { email },
          withDeleted: true,
        })
      ]);

      if (existingUserByUsername) {
        throw new ConflictException('用户名已存在');
      }

      if (existingUserByEmail) {
        throw new ConflictException('邮箱已被使用');
      }

      // 创建新用户，默认状态为待审批
      const hashedPassword = await bcrypt.hash(password, 10);
      const user = this.userRepository.create({
        username,
        password: hashedPassword,
        email,
        approvalStatus: UserApprovalStatus.PENDING, // 新用户需要管理员审批
      });

      // 保存用户
      const savedUser = await transactionalEntityManager.save(User, user);
      
      // 返回用户信息时排除密码
      const { password: _, ...result } = savedUser;
      return result;
    }).catch(error => {
      // 处理数据库唯一性约束违反错误
      if (error.code === '23505') { // PostgreSQL 唯一性约束违反的错误码
        if (error.detail?.includes('email')) {
          throw new ConflictException('邮箱已被使用');
        }
        if (error.detail?.includes('username')) {
          throw new ConflictException('用户名已存在');
        }
      }
      throw error;
    });
  }

  /**
   * 用户登录
   * @param loginDto 登录信息
   * @returns 登录成功的令牌和用户信息
   */
  async login(loginDto: LoginDto): Promise<{ access_token: string; user: Omit<User, 'password'> }> {
    const { username, password } = loginDto;

    // 查找用户
    const user = await this.userRepository.findOne({ where: { username } });
    if (!user) {
      throw new UnauthorizedException('用户名或密码错误');
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('用户名或密码错误');
    }

    // 检查用户审批状态
    if (user.approvalStatus === UserApprovalStatus.PENDING) {
      throw new UnauthorizedException('您的账户正在等待管理员审批，请耐心等待审批完成后再次尝试登录');
    }

    if (user.approvalStatus === UserApprovalStatus.REJECTED) {
      const rejectionReason = user.rejectionReason || '未提供拒绝原因';
      throw new UnauthorizedException(`您的账户申请已被拒绝，原因：${rejectionReason}。如有疑问，请联系管理员`);
    }

    // 检查用户是否激活
    if (!user.isActive) {
      throw new UnauthorizedException('您的账户已被禁用，请联系管理员');
    }

    // 生成 JWT token
    const payload = { username: user.username, sub: user.id };
    const access_token = this.jwtService.sign(payload);

    // 返回令牌和用户信息（不含密码）
    const { password: _, ...result } = user;
    return {
      access_token,
      user: result,
    };
  }

  /**
   * 设置用户角色
   * @param userId 用户ID
   * @param setUserRoleDto 角色设置信息
   * @returns 更新后的用户信息
   */
  async setUserRole(userId: number, setUserRoleDto: SetUserRoleDto): Promise<UserPermissionResponseDto> {
    const { role } = setUserRoleDto;

    // 查找目标用户
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 更新用户角色
    user.role = role;
    await this.userRepository.save(user);

    // 返回用户权限信息
    const { password: _, ...userInfo } = user;
    return userInfo;
  }

  /**
   * 根据ID获取用户信息
   * @param userId 用户ID
   * @returns 用户信息（不含密码）
   */
  async getUserById(userId: number): Promise<UserPermissionResponseDto> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    const { password: _, ...userInfo } = user;
    return userInfo;
  }

  /**
   * 获取用户权限信息
   * @param userId 用户ID
   * @returns 用户权限信息
   */
  async getUserPermissions(userId: number): Promise<UserPermissionResponseDto> {
    return this.getUserById(userId);
  }

  /**
   * 获取用户列表（分页）
   * @param queryDto 查询参数
   * @returns 用户列表和分页信息
   */
  async getUsers(queryDto: GetUsersQueryDto): Promise<UsersListResponseDto> {
    const { page = 1, limit = 10, role, username } = queryDto;

    // 构建查询条件
    const where: any = {};
    if (role) {
      where.role = role;
    }
    if (username) {
      where.username = Like(`%${username}%`);
    }

    // 执行分页查询
    const [users, total] = await this.userRepository.findAndCount({
      where,
      skip: (page - 1) * limit,
      take: limit,
      order: { createdAt: 'DESC' },
    });

    // 过滤密码字段
    const usersWithoutPassword = users.map(user => {
      const { password: _, ...userInfo } = user;
      return userInfo;
    });

    return {
      total,
      page,
      limit,
      users: usersWithoutPassword,
    };
  }

  /**
   * 检查是否为管理员
   * @param userId 用户ID
   * @returns 是否为管理员
   */
  async isAdmin(userId: number): Promise<boolean> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    return user?.role === UserRole.ADMIN || false;
  }

  /**
   * 获取待审批用户列表
   * @param queryDto 查询参数
   * @returns 待审批用户列表和分页信息
   */
  async getPendingUsers(queryDto: PendingUsersQueryDto): Promise<PendingUsersResponseDto> {
    const { page = 1, limit = 10, username, email } = queryDto;

    // 构建查询条件
    const where: any = { approvalStatus: UserApprovalStatus.PENDING };
    if (username) {
      where.username = Like(`%${username}%`);
    }
    if (email) {
      where.email = Like(`%${email}%`);
    }

    // 执行分页查询
    const [users, total] = await this.userRepository.findAndCount({
      where,
      skip: (page - 1) * limit,
      take: limit,
      order: { createdAt: 'ASC' }, // 按注册时间排序，优先显示早期申请
    });

    // 过滤密码字段并转换为响应DTO
    const usersResponse: UserApprovalResponseDto[] = users.map(user => {
      const { password: _, ...userInfo } = user;
      return userInfo as UserApprovalResponseDto;
    });

    const totalPages = Math.ceil(total / limit);

    return {
      users: usersResponse,
      total,
      page,
      limit,
      totalPages,
    };
  }

  /**
   * 管理员审批用户
   * @param userId 要审批的用户ID
   * @param approvalDto 审批操作信息
   * @param adminId 执行审批的管理员ID
   * @returns 审批结果
   */
  async approveUser(userId: number, approvalDto: UserApprovalDto, adminId: number): Promise<ApprovalResultResponseDto> {
    const { action, rejectionReason } = approvalDto;

    // 检查操作合法性
    if (action === 'reject' && !rejectionReason) {
      throw new BadRequestException('拒绝用户申请时必须提供拒绝原因');
    }

    return await this.dataSource.transaction(async transactionalEntityManager => {
      console.log('开始事务，审批用户:', userId, '操作:', action);
      
      // 查找要审批的用户
      const user = await transactionalEntityManager.findOne(User, { where: { id: userId } });
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      // 检查用户当前状态
      if (user.approvalStatus !== UserApprovalStatus.PENDING) {
        throw new BadRequestException(`用户当前状态为 ${user.approvalStatus}，无法重复审批`);
      }

      // 更新用户审批状态
      if (action === 'approve') {
        user.approvalStatus = UserApprovalStatus.APPROVED;
        user.approvedAt = new Date();
        user.approvedBy = adminId;
        user.rejectionReason = undefined; // 清除之前的拒绝原因
      } else {
        user.approvalStatus = UserApprovalStatus.REJECTED;
        user.rejectionReason = rejectionReason;
        user.approvedAt = new Date(); // 记录拒绝时间
        user.approvedBy = adminId; // 记录拒绝审批人
        console.log('设置拒绝审批信息:', {
          userId: user.id,
          approvedAt: user.approvedAt,
          approvedBy: user.approvedBy,
          rejectionReason: user.rejectionReason
        });
      }

      // 保存更新 - 使用原生SQL确保字段被正确更新
      console.log('开始执行SQL更新...');
      if (action === 'reject') {
        const result = await transactionalEntityManager.query(`
          UPDATE users 
          SET approval_status = $1, 
              approved_at = $2, 
              approved_by = $3, 
              rejection_reason = $4,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $5
        `, [
          UserApprovalStatus.REJECTED,
          new Date(),
          adminId,
          rejectionReason,
          userId
        ]);
        console.log('拒绝SQL更新结果:', result);
      } else {
        const result = await transactionalEntityManager.query(`
          UPDATE users 
          SET approval_status = $1, 
              approved_at = $2, 
              approved_by = $3, 
              rejection_reason = NULL,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $4
        `, [
          UserApprovalStatus.APPROVED,
          new Date(),
          adminId,
          userId
        ]);
        console.log('批准SQL更新结果:', result);
      }

      // 重新查询更新后的用户信息
      const updatedUserResult = await transactionalEntityManager.query(`
        SELECT id, username, email, role, approval_status, approved_at, approved_by, rejection_reason, 
               api_calls_today, downloads_today, last_api_reset, avatar_url, is_active, 
               created_at, updated_at, deleted_at
        FROM users WHERE id = $1
      `, [userId]);
      
      if (!updatedUserResult || updatedUserResult.length === 0) {
        throw new NotFoundException('更新后用户不存在');
      }
      
      const updatedUser = updatedUserResult[0];
      console.log('保存后的用户信息:', {
        id: updatedUser.id,
        approvedAt: updatedUser.approved_at,
        approvedBy: updatedUser.approved_by,
        rejectionReason: updatedUser.rejection_reason
      });

      // 返回审批结果
      const userInfo = {
        id: updatedUser.id,
        username: updatedUser.username,
        email: updatedUser.email,
        role: updatedUser.role,
        approvalStatus: updatedUser.approval_status,
        approvedAt: updatedUser.approved_at,
        approvedBy: updatedUser.approved_by,
        rejectionReason: updatedUser.rejection_reason,
        apiCallsToday: updatedUser.api_calls_today,
        downloadsToday: updatedUser.downloads_today,
        lastApiReset: updatedUser.last_api_reset,
        avatarUrl: updatedUser.avatar_url,
        isActive: updatedUser.is_active,
        createdAt: updatedUser.created_at,
        updatedAt: updatedUser.updated_at,
        deletedAt: updatedUser.deleted_at
      };

      const message = action === 'approve' ? '用户审批通过' : '用户申请已拒绝';

      return {
        success: true,
        message,
        user: userInfo as UserApprovalResponseDto,
      };
    });
  }

  /**
   * 获取所有用户的审批历史（包括已审批和待审批）
   * @param queryDto 查询参数
   * @returns 用户审批历史列表
   */
  async getUserApprovalHistory(queryDto: GetUsersQueryDto): Promise<UsersListResponseDto> {
    const { page = 1, limit = 10, role, username } = queryDto;

    // 构建查询条件
    const where: any = {};
    if (role) {
      where.role = role;
    }
    if (username) {
      where.username = Like(`%${username}%`);
    }

    // 执行分页查询
    const [users, total] = await this.userRepository.findAndCount({
      where,
      skip: (page - 1) * limit,
      take: limit,
      order: { createdAt: 'DESC' },
    });

    // 过滤密码字段
    const usersWithoutPassword = users.map(user => {
      const { password: _, ...userInfo } = user;
      return userInfo;
    });

    return {
      total,
      page,
      limit,
      users: usersWithoutPassword,
    };
  }

  /**
   * 删除用户（管理员专用）
   * @param userId 要删除的用户ID
   * @returns 删除结果
   */
  async deleteUser(userId: number): Promise<DeleteUserResponseDto> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    if (user.role === 'admin') {
      throw new ForbiddenException('不能删除管理员用户');
    }

    await this.userRepository.softDelete(userId);
    
    return {
      success: true,
      message: '用户删除成功'
    };
  }

  /**
   * 修改用户密码
   * @param userId 用户ID
   * @param changePasswordDto 密码修改信息
   * @returns 修改结果
   */
  async changePassword(userId: number, changePasswordDto: ChangePasswordDto): Promise<{ message: string }> {
    const user = await this.userService.findOne(userId);
    if (!user) {
      throw new UnauthorizedException('用户不存在');
    }

    const isPasswordValid = await bcrypt.compare(changePasswordDto.currentPassword, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('当前密码错误');
    }

    if (changePasswordDto.currentPassword === changePasswordDto.newPassword) {
      throw new BadRequestException('新密码不能与当前密码相同');
    }

    const salt = await bcrypt.genSalt();
    const hashedPassword = await bcrypt.hash(changePasswordDto.newPassword, salt);
    
    await this.userService.update(userId, { password: hashedPassword });

    return { message: '密码修改成功' };
  }
} 