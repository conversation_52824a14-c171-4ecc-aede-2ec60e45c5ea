import { IsEnum, IsOptional, IsString, IsInt, <PERSON>, <PERSON>, IsNotEmpty } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { UserApprovalStatus } from '../enums/user-approval-status.enum';
import { UserRole } from '../enums/user-role.enum';

/**
 * 用户审批操作DTO
 * @description 管理员批准或拒绝用户申请时使用
 */
export class UserApprovalDto {
  @ApiProperty({
    description: '审批操作类型',
    enum: ['approve', 'reject'],
    example: 'approve'
  })
  @IsEnum(['approve', 'reject'], { message: '审批操作只能是 approve 或 reject' })
  @IsNotEmpty({ message: '审批操作不能为空' })
  action: 'approve' | 'reject';

  @ApiPropertyOptional({
    description: '拒绝原因（拒绝时必填）',
    example: '申请信息不完整，请重新提交完整资料'
  })
  @IsOptional()
  @IsString({ message: '拒绝原因必须是字符串' })
  rejectionReason?: string;
}

/**
 * 待审批用户查询DTO
 * @description 管理员查询待审批用户列表时使用
 */
export class PendingUsersQueryDto {
  @ApiPropertyOptional({
    description: '页码',
    minimum: 1,
    default: 1,
    example: 1
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: '页码必须是整数' })
  @Min(1, { message: '页码必须大于0' })
  page?: number = 1;

  @ApiPropertyOptional({
    description: '每页数量',
    minimum: 1,
    maximum: 100,
    default: 10,
    example: 10
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: '每页数量必须是整数' })
  @Min(1, { message: '每页数量必须大于0' })
  @Max(100, { message: '每页数量不能超过100' })
  limit?: number = 10;

  @ApiPropertyOptional({
    description: '用户名筛选（模糊搜索）',
    example: 'john'
  })
  @IsOptional()
  @IsString({ message: '用户名必须是字符串' })
  username?: string;

  @ApiPropertyOptional({
    description: '邮箱筛选（模糊搜索）',
    example: '<EMAIL>'
  })
  @IsOptional()
  @IsString({ message: '邮箱必须是字符串' })
  email?: string;
}

/**
 * 用户审批信息响应DTO
 * @description 返回用户审批相关信息
 */
export class UserApprovalResponseDto {
  @ApiProperty({ description: '用户ID', example: 1 })
  id: number;

  @ApiProperty({ description: '用户名', example: 'johnsmith' })
  username: string;

  @ApiProperty({ description: '邮箱', example: '<EMAIL>' })
  email: string;

  @ApiProperty({ 
    description: '用户角色',
    enum: UserRole,
    example: UserRole.FREE
  })
  role: UserRole;

  @ApiProperty({
    description: '审批状态',
    enum: UserApprovalStatus,
    example: UserApprovalStatus.PENDING
  })
  approvalStatus: UserApprovalStatus;

  @ApiPropertyOptional({ 
    description: '审批操作时间（包括批准和拒绝）',
    example: '2024-01-15T10:30:00Z'
  })
  approvedAt?: Date;

  @ApiPropertyOptional({ 
    description: '执行审批操作的管理员用户ID（包括批准和拒绝）',
    example: 1
  })
  approvedBy?: number;

  @ApiPropertyOptional({ 
    description: '拒绝原因',
    example: '申请信息不完整'
  })
  rejectionReason?: string;

  @ApiProperty({ 
    description: '注册时间',
    example: '2024-01-15T10:00:00Z'
  })
  createdAt: Date;

  @ApiProperty({ 
    description: '是否激活',
    example: true
  })
  isActive: boolean;
}

/**
 * 待审批用户列表响应DTO
 * @description 返回待审批用户列表和分页信息
 */
export class PendingUsersResponseDto {
  @ApiProperty({ 
    description: '用户列表',
    type: [UserApprovalResponseDto]
  })
  users: UserApprovalResponseDto[];

  @ApiProperty({ description: '总数量', example: 25 })
  total: number;

  @ApiProperty({ description: '当前页码', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  limit: number;

  @ApiProperty({ description: '总页数', example: 3 })
  totalPages: number;
}

/**
 * 审批结果响应DTO
 * @description 审批操作完成后的响应
 */
export class ApprovalResultResponseDto {
  @ApiProperty({ description: '是否成功', example: true })
  success: boolean;

  @ApiProperty({ description: '操作信息', example: '用户审批成功' })
  message: string;

  @ApiProperty({ 
    description: '用户信息',
    type: UserApprovalResponseDto
  })
  user: UserApprovalResponseDto;
} 