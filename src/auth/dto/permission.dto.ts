import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { UserRole } from '../enums/user-role.enum';
import { User } from '../../entities/user.entity';

/**
 * 设置用户角色DTO
 * @description 用于管理员设置用户权限角色的请求数据
 */
export class SetUserRoleDto {
  @ApiProperty({
    description: '用户角色',
    enum: UserRole,
    example: UserRole.PREMIUM,
    enumName: 'UserRole'
  })
  @IsEnum(UserRole, { message: '请选择有效的用户角色' })
  role: UserRole;
}

/**
 * 用户权限信息响应DTO
 * @description 返回用户权限信息的数据结构
 */
export class UserPermissionResponseDto {
  @ApiProperty({ description: '用户ID', example: 1 })
  id: number;

  @ApiProperty({ description: '用户名', example: 'testuser' })
  username: string;

  @ApiProperty({ description: '邮箱', example: '<EMAIL>' })
  email: string;

  @ApiProperty({ 
    description: '用户角色',
    enum: UserRole,
    example: UserRole.PREMIUM 
  })
  role: UserRole;

  @ApiProperty({ description: '是否激活', example: true })
  isActive: boolean;

  @ApiProperty({ description: '创建时间', example: '2024-02-03T12:34:56.789Z' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间', example: '2024-02-03T12:34:56.789Z' })
  updatedAt: Date;
}

/**
 * 用户列表查询DTO
 * @description 管理员查询用户列表的请求参数
 */
export class GetUsersQueryDto {
  @ApiProperty({ 
    description: '页码', 
    example: 1,
    required: false,
    default: 1
  })
  @IsOptional()
  @IsNumber({}, { message: '页码必须是数字' })
  page?: number = 1;

  @ApiProperty({ 
    description: '每页数量', 
    example: 10,
    required: false,
    default: 10
  })
  @IsOptional()
  @IsNumber({}, { message: '每页数量必须是数字' })
  limit?: number = 10;

  @ApiProperty({ 
    description: '角色筛选',
    enum: UserRole,
    required: false
  })
  @IsOptional()
  @IsEnum(UserRole, { message: '请选择有效的用户角色' })
  role?: UserRole;

  @ApiProperty({ 
    description: '用户名搜索', 
    example: 'test',
    required: false
  })
  @IsOptional()
  @IsString({ message: '用户名必须是字符串' })
  username?: string;
}

/**
 * 用户列表响应DTO
 * @description 返回用户列表的数据结构
 */
export class UsersListResponseDto {
  @ApiProperty({ description: '总数量', example: 100 })
  total: number;

  @ApiProperty({ description: '当前页码', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  limit: number;

  @ApiProperty({ 
    description: '用户列表',
    type: [UserPermissionResponseDto]
  })
  users: UserPermissionResponseDto[];
} 