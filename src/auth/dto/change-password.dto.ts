import { IsString, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsStrongPassword } from './password.validator';

export class ChangePasswordDto {
  @ApiProperty({
    description: '当前密码',
    example: 'OldPass123!@#',
  })
  @IsString({ message: '当前密码必须是字符串' })
  @IsNotEmpty({ message: '当前密码不能为空' })
  currentPassword: string;

  @ApiProperty({
    description: '新密码',
    example: 'NewPass123!@#',
    minLength: 8,
    maxLength: 20,
  })
  @IsString({ message: '新密码必须是字符串' })
  @IsNotEmpty({ message: '新密码不能为空' })
  @IsStrongPassword({
    message: '新密码必须包含大小写字母、数字和特殊字符，长度在8-20个字符之间'
  })
  newPassword: string;
} 