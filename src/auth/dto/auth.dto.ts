import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsE<PERSON>, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsStrongPassword } from './password.validator';

/**
 * 用户注册 DTO
 */
export class RegisterDto {
  @ApiProperty({
    description: '用户名',
    example: 'testuser',
    minLength: 4,
    maxLength: 20,
  })
  @IsString({ message: '用户名必须是字符串' })
  @IsNotEmpty({ message: '用户名不能为空' })
  @MinLength(4, { message: '用户名长度不能小于4个字符' })
  @MaxLength(20, { message: '用户名长度不能超过20个字符' })
  username: string;

  @ApiProperty({
    description: '密码',
    example: 'Test123!@#',
    minLength: 8,
    maxLength: 20,
  })
  @IsString({ message: '密码必须是字符串' })
  @IsNotEmpty({ message: '密码不能为空' })
  @IsStrongPassword({
    message: '密码必须包含大小写字母、数字和特殊字符，长度在8-20个字符之间'
  })
  password: string;

  @ApiProperty({
    description: '电子邮箱',
    example: '<EMAIL>',
    required: true,
  })
  @IsEmail({}, { message: '请输入有效的电子邮箱地址' })
  @IsNotEmpty({ message: '电子邮箱不能为空' })
  email: string;
}

/**
 * 用户登录 DTO
 */
export class LoginDto {
  @ApiProperty({
    description: '用户名',
    example: 'testuser',
  })
  @IsString({ message: '用户名必须是字符串' })
  @IsNotEmpty({ message: '用户名不能为空' })
  username: string;

  @ApiProperty({
    description: '密码',
    example: 'Test123!@#',
  })
  @IsString({ message: '密码必须是字符串' })
  @IsNotEmpty({ message: '密码不能为空' })
  password: string;
} 