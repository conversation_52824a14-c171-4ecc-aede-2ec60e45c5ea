import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';
import { PasswordValidator } from '../../utils/password-validator';

export function IsStrongPassword(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isStrongPassword',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (typeof value !== 'string') return false;
          const result = PasswordValidator.validate(value);
          if (!result.isValid) {
            // 将错误信息传递给验证选项
            if (validationOptions) {
              validationOptions.message = result.errors.join(', ');
            }
            return false;
          }
          return true;
        }
      }
    });
  };
} 