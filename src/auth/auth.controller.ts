import { Controller, Post, Body, HttpCode, HttpStatus, UseGuards, Put, Get, Param, Query, ParseIntPipe, Delete } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { RegisterDto, LoginDto } from './dto/auth.dto';
import { SetUserRoleDto, UserPermissionResponseDto, GetUsersQueryDto, UsersListResponseDto } from './dto/permission.dto';
import { UserApprovalDto, PendingUsersQueryDto, PendingUsersResponseDto, ApprovalResultResponseDto } from './dto/user-approval.dto';
import { User } from '../entities/user.entity';
import { Public } from './decorators/public.decorator';
import { CurrentUser } from './decorators/current-user.decorator';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { AdminGuard, AdminOrOwnerGuard } from './guards/admin.guard';
import { DeleteUserResponseDto } from './dto/user-delete.dto';
import { ChangePasswordDto } from './dto/change-password.dto';

/**
 * 认证控制器
 * @description 处理用户注册、登录和权限管理请求
 */
@ApiTags('认证')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  /**
   * 用户注册
   * @param registerDto 注册信息
   * @returns 注册成功的用户信息
   */
  @Public()
  @Post('register')
  @ApiOperation({ summary: '用户注册', description: '创建新用户账号，注册成功后需要等待管理员审批才能登录' })
  @ApiBody({ type: RegisterDto })
  @ApiResponse({
    status: 201,
    description: '用户注册成功，等待管理员审批',
    schema: {
      example: {
        username: "testuser",
        email: "<EMAIL>",
        avatarUrl: null,
        id: 1,
        role: "free",
        approvalStatus: "pending",
        apiCallsToday: 0,
        downloadsToday: 0,
        lastApiReset: "2024-02-03T12:34:56.789Z",
        isActive: true,
        createdAt: "2024-02-03T12:34:56.789Z",
        updatedAt: "2024-02-03T12:34:56.789Z"
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: '请求参数验证失败',
    schema: {
      example: {
        statusCode: 400,
        message: ["用户名长度不能小于4个字符", "密码长度不能小于6个字符", "请输入有效的电子邮箱地址"],
        error: "Bad Request"
      }
    }
  })
  @ApiResponse({
    status: 409,
    description: '用户名或邮箱已存在',
    schema: {
      example: {
        statusCode: 409,
        message: "用户名已存在",
        error: "Conflict"
      }
    }
  })
  async register(@Body() registerDto: RegisterDto): Promise<Omit<User, 'password'>> {
    return this.authService.register(registerDto);
  }

  /**
   * 用户登录
   * @param loginDto 登录信息
   * @returns 登录成功的令牌和用户信息
   */
  @Public()
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiTags('认证')
  @ApiOperation({ summary: '用户登录', description: '使用用户名和密码登录' })
  @ApiBody({ 
    type: LoginDto,
    schema: {
      example: {
        username: "testuser",
        password: "password123"
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '登录成功',
    schema: {
      example: {
        access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        user: {
          id: 1,
          username: "testuser",
          email: "<EMAIL>",
          role: "free",
          apiCallsToday: 0,
          downloadsToday: 0,
          lastApiReset: "2024-02-03T12:34:56.789Z",
          avatarUrl: null,
          isActive: true,
          createdAt: "2024-02-03T12:34:56.789Z",
          updatedAt: "2024-02-03T12:34:56.789Z"
        }
      }
    }
  })
  @ApiResponse({
    status: 401,
    description: '用户名或密码错误，或账户未通过审批',
    schema: {
      examples: {
        wrongCredentials: {
          summary: '用户名或密码错误',
          value: {
            statusCode: 401,
            message: "用户名或密码错误",
            error: "Unauthorized"
          }
        },
        pendingApproval: {
          summary: '账户等待审批',
          value: {
            statusCode: 401,
            message: "您的账户正在等待管理员审批，请耐心等待审批完成后再次尝试登录",
            error: "Unauthorized"
          }
        },
        rejected: {
          summary: '账户申请被拒绝',
          value: {
            statusCode: 401,
            message: "您的账户申请已被拒绝，原因：申请信息不完整。如有疑问，请联系管理员",
            error: "Unauthorized"
          }
        },
        disabled: {
          summary: '账户已被禁用',
          value: {
            statusCode: 401,
            message: "您的账户已被禁用，请联系管理员",
            error: "Unauthorized"
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: '请求参数验证失败',
    schema: {
      example: {
        statusCode: 400,
        message: ["用户名不能为空", "密码不能为空"],
        error: "Bad Request"
      }
    }
  })
  async login(@Body() loginDto: LoginDto): Promise<{ access_token: string; user: Omit<User, 'password'> }> {
    return this.authService.login(loginDto);
  }

  /**
   * 设置用户角色（管理员专用）
   * @param userId 用户ID
   * @param setUserRoleDto 角色设置信息
   * @returns 更新后的用户信息
   */
  @UseGuards(JwtAuthGuard, AdminGuard)
  @Put('users/:id/role')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ 
    summary: '设置用户角色', 
    description: '管理员设置指定用户的权限角色' 
  })
  @ApiParam({
    name: 'id',
    description: '用户ID',
    type: 'number',
    example: 1
  })
  @ApiBody({ type: SetUserRoleDto })
  @ApiResponse({
    status: 200,
    description: '用户角色设置成功',
    type: UserPermissionResponseDto,
    schema: {
      example: {
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        role: "premium",
        isActive: true,
        createdAt: "2024-02-03T12:34:56.789Z",
        updatedAt: "2024-02-03T12:34:56.789Z"
      }
    }
  })
  @ApiResponse({
    status: 401,
    description: '未认证',
    schema: {
      example: {
        statusCode: 401,
        message: "Unauthorized",
        error: "Unauthorized"
      }
    }
  })
  @ApiResponse({
    status: 403,
    description: '权限不足',
    schema: {
      example: {
        statusCode: 403,
        message: "只有管理员才能执行此操作",
        error: "Forbidden"
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: '用户不存在',
    schema: {
      example: {
        statusCode: 404,
        message: "用户不存在",
        error: "Not Found"
      }
    }
  })
  async setUserRole(
    @Param('id', ParseIntPipe) userId: number,
    @Body() setUserRoleDto: SetUserRoleDto,
  ): Promise<UserPermissionResponseDto> {
    return this.authService.setUserRole(userId, setUserRoleDto);
  }

  /**
   * 获取用户权限信息
   * @param userId 用户ID
   * @param currentUser 当前登录用户
   * @returns 用户权限信息
   */
  @UseGuards(JwtAuthGuard, AdminOrOwnerGuard)
  @Get('users/:id/permissions')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ 
    summary: '获取用户权限信息', 
    description: '获取指定用户的权限信息（管理员可查看任何用户，普通用户只能查看自己）' 
  })
  @ApiParam({
    name: 'id',
    description: '用户ID',
    type: 'number',
    example: 1
  })
  @ApiResponse({
    status: 200,
    description: '获取用户权限信息成功',
    type: UserPermissionResponseDto,
    schema: {
      example: {
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        role: "premium",
        isActive: true,
        createdAt: "2024-02-03T12:34:56.789Z",
        updatedAt: "2024-02-03T12:34:56.789Z"
      }
    }
  })
  @ApiResponse({
    status: 401,
    description: '未认证',
    schema: {
      example: {
        statusCode: 401,
        message: "Unauthorized",
        error: "Unauthorized"
      }
    }
  })
  @ApiResponse({
    status: 403,
    description: '权限不足',
    schema: {
      example: {
        statusCode: 403,
        message: "您只能访问自己的资源",
        error: "Forbidden"
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: '用户不存在',
    schema: {
      example: {
        statusCode: 404,
        message: "用户不存在",
        error: "Not Found"
      }
    }
  })
  async getUserPermissions(
    @Param('id', ParseIntPipe) userId: number,
    @CurrentUser() currentUser: User,
  ): Promise<UserPermissionResponseDto> {
    return this.authService.getUserPermissions(userId);
  }

  /**
   * 获取用户列表（管理员专用）
   * @param queryDto 查询参数
   * @returns 用户列表和分页信息
   */
  @UseGuards(JwtAuthGuard, AdminGuard)
  @Get('users')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ 
    summary: '获取用户列表', 
    description: '管理员获取系统中的用户列表，支持分页和筛选' 
  })
  @ApiQuery({
    name: 'page',
    description: '页码',
    required: false,
    type: 'number',
    example: 1
  })
  @ApiQuery({
    name: 'limit',
    description: '每页数量',
    required: false,
    type: 'number',
    example: 10
  })
  @ApiQuery({
    name: 'role',
    description: '角色筛选',
    required: false,
    enum: ['admin', 'free', 'premium', 'enterprise', 'government'],
    example: 'premium'
  })
  @ApiQuery({
    name: 'username',
    description: '用户名搜索',
    required: false,
    type: 'string',
    example: 'test'
  })
  @ApiResponse({
    status: 200,
    description: '获取用户列表成功',
    type: UsersListResponseDto,
    schema: {
      example: {
        total: 100,
        page: 1,
        limit: 10,
        users: [
          {
            id: 1,
            username: "testuser1",
            email: "<EMAIL>",
            role: "premium",
            isActive: true,
            createdAt: "2024-02-03T12:34:56.789Z",
            updatedAt: "2024-02-03T12:34:56.789Z"
          },
          {
            id: 2,
            username: "testuser2",
            email: "<EMAIL>",
            role: "free",
            isActive: true,
            createdAt: "2024-02-03T12:34:56.789Z",
            updatedAt: "2024-02-03T12:34:56.789Z"
          }
        ]
      }
    }
  })
  @ApiResponse({
    status: 401,
    description: '未认证',
    schema: {
      example: {
        statusCode: 401,
        message: "Unauthorized",
        error: "Unauthorized"
      }
    }
  })
  @ApiResponse({
    status: 403,
    description: '权限不足',
    schema: {
      example: {
        statusCode: 403,
        message: "只有管理员才能执行此操作",
        error: "Forbidden"
      }
    }
  })
  async getUsers(@Query() queryDto: GetUsersQueryDto): Promise<UsersListResponseDto> {
    return this.authService.getUsers(queryDto);
  }

  /**
   * 获取当前用户信息
   * @param currentUser 当前登录用户
   * @returns 当前用户信息
   */
  @UseGuards(JwtAuthGuard)
  @Get('profile')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ 
    summary: '获取当前用户信息', 
    description: '获取当前登录用户的基本信息和权限' 
  })
  @ApiResponse({
    status: 200,
    description: '获取用户信息成功',
    type: UserPermissionResponseDto,
    schema: {
      example: {
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        role: "premium",
        isActive: true,
        createdAt: "2024-02-03T12:34:56.789Z",
        updatedAt: "2024-02-03T12:34:56.789Z"
      }
    }
  })
  @ApiResponse({
    status: 401,
    description: '未认证',
    schema: {
      example: {
        statusCode: 401,
        message: "Unauthorized",
        error: "Unauthorized"
      }
    }
  })
  async getProfile(@CurrentUser() currentUser: User): Promise<UserPermissionResponseDto> {
    const { password: _, ...userInfo } = currentUser;
    return userInfo;
  }

  /**
   * 获取待审批用户列表（管理员专用）
   * @param queryDto 查询参数
   * @returns 待审批用户列表
   */
  @UseGuards(JwtAuthGuard, AdminGuard)
  @Get('admin/pending-users')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ 
    summary: '获取待审批用户列表', 
    description: '管理员获取系统中等待审批的用户列表' 
  })
  @ApiQuery({
    name: 'page',
    description: '页码',
    required: false,
    type: 'number',
    example: 1
  })
  @ApiQuery({
    name: 'limit',
    description: '每页数量',
    required: false,
    type: 'number',
    example: 10
  })
  @ApiQuery({
    name: 'username',
    description: '用户名搜索',
    required: false,
    type: 'string',
    example: 'john'
  })
  @ApiQuery({
    name: 'email',
    description: '邮箱搜索',
    required: false,
    type: 'string',
    example: '<EMAIL>'
  })
  @ApiResponse({
    status: 200,
    description: '获取待审批用户列表成功',
    type: PendingUsersResponseDto,
    schema: {
      example: {
        users: [
          {
            id: 1,
            username: "newuser1",
            email: "<EMAIL>",
            role: "free",
            approvalStatus: "pending",
            createdAt: "2024-01-15T10:00:00Z",
            isActive: true
          }
        ],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1
      }
    }
  })
  @ApiResponse({
    status: 401,
    description: '未认证'
  })
  @ApiResponse({
    status: 403,
    description: '只有管理员才能执行此操作'
  })
  async getPendingUsers(@Query() queryDto: PendingUsersQueryDto): Promise<PendingUsersResponseDto> {
    return this.authService.getPendingUsers(queryDto);
  }

  /**
   * 审批用户申请（管理员专用）
   * @param userId 用户ID
   * @param approvalDto 审批操作信息
   * @param request 请求对象（包含管理员信息）
   * @returns 审批结果
   */
  @UseGuards(JwtAuthGuard, AdminGuard)
  @Put('admin/users/:id/approval')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ 
    summary: '审批用户申请', 
    description: '管理员批准或拒绝用户注册申请' 
  })
  @ApiParam({
    name: 'id',
    description: '待审批用户ID',
    type: 'number',
    example: 1
  })
  @ApiBody({ 
    type: UserApprovalDto,
    examples: {
      approve: {
        summary: '批准用户',
        value: {
          action: 'approve'
        }
      },
      reject: {
        summary: '拒绝用户',
        value: {
          action: 'reject',
          rejectionReason: '申请信息不完整，请重新提交完整资料'
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '用户审批成功',
    type: ApprovalResultResponseDto,
    schema: {
      example: {
        success: true,
        message: '用户审批通过',
        user: {
          id: 1,
          username: "newuser1",
          email: "<EMAIL>",
          role: "free",
          approvalStatus: "approved",
          approvedAt: "2024-01-15T10:30:00Z",
          approvedBy: 1,
          createdAt: "2024-01-15T10:00:00Z",
          isActive: true
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
    schema: {
      example: {
        statusCode: 400,
        message: '拒绝用户申请时必须提供拒绝原因',
        error: 'Bad Request'
      }
    }
  })
  @ApiResponse({
    status: 401,
    description: '未认证'
  })
  @ApiResponse({
    status: 403,
    description: '只有管理员才能执行此操作'
  })
  @ApiResponse({
    status: 404,
    description: '用户不存在'
  })
  async approveUser(
    @Param('id', ParseIntPipe) userId: number,
    @Body() approvalDto: UserApprovalDto,
    @CurrentUser() currentUser: User,
  ): Promise<ApprovalResultResponseDto> {
    return this.authService.approveUser(userId, approvalDto, currentUser.id);
  }

  /**
   * 获取用户审批历史（管理员专用）
   * @param queryDto 查询参数
   * @returns 用户审批历史列表
   */
  @UseGuards(JwtAuthGuard, AdminGuard)
  @Get('admin/user-approval-history')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ 
    summary: '获取用户审批历史', 
    description: '管理员查看所有用户的审批历史记录' 
  })
  @ApiQuery({
    name: 'page',
    description: '页码',
    required: false,
    type: 'number',
    example: 1
  })
  @ApiQuery({
    name: 'limit',
    description: '每页数量',
    required: false,
    type: 'number',
    example: 10
  })
  @ApiQuery({
    name: 'role',
    description: '角色筛选',
    required: false,
    enum: ['admin', 'free', 'premium', 'enterprise', 'government'],
    example: 'free'
  })
  @ApiQuery({
    name: 'username',
    description: '用户名搜索',
    required: false,
    type: 'string',
    example: 'user'
  })
  @ApiResponse({
    status: 200,
    description: '获取审批历史成功',
    type: UsersListResponseDto
  })
  @ApiResponse({
    status: 401,
    description: '未认证'
  })
  @ApiResponse({
    status: 403,
    description: '只有管理员才能执行此操作'
  })
  async getUserApprovalHistory(@Query() queryDto: GetUsersQueryDto): Promise<UsersListResponseDto> {
    return this.authService.getUserApprovalHistory(queryDto);
  }

  /**
   * 删除用户（管理员专用）
   * @param userId 用户ID
   * @returns 删除结果
   */
  @UseGuards(JwtAuthGuard, AdminGuard)
  @Delete('admin/users/:id/delete')
  @ApiBearerAuth('JWT-auth')
  @ApiTags('认证')
  @ApiOperation({ 
    summary: '删除用户', 
    description: '管理员删除指定用户（不能删除管理员用户）' 
  })
  @ApiParam({
    name: 'id',
    description: '要删除的用户ID',
    type: 'number',
    example: 1
  })
  @ApiResponse({
    status: 200,
    description: '用户删除成功',
    type: DeleteUserResponseDto
  })
  @ApiResponse({
    status: 401,
    description: '未认证',
    schema: {
      example: {
        statusCode: 401,
        message: "Unauthorized",
        error: "Unauthorized"
      }
    }
  })
  @ApiResponse({
    status: 403,
    description: '权限不足或尝试删除管理员',
    schema: {
      examples: {
        noPermission: {
          summary: '权限不足',
          value: {
            statusCode: 403,
            message: "只有管理员才能执行此操作",
            error: "Forbidden"
          }
        },
        deleteAdmin: {
          summary: '尝试删除管理员',
          value: {
            statusCode: 403,
            message: "不能删除管理员用户",
            error: "Forbidden"
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: '用户不存在',
    schema: {
      example: {
        statusCode: 404,
        message: "用户不存在",
        error: "Not Found"
      }
    }
  })
  async deleteUser(
    @Param('id', ParseIntPipe) userId: number,
  ): Promise<DeleteUserResponseDto> {
    return this.authService.deleteUser(userId);
  }

  /**
   * 修改用户密码
   * @param currentUser 当前用户
   * @param changePasswordDto 密码修改信息
   * @returns 修改结果
   */
  @UseGuards(JwtAuthGuard)
  @Put('change-password')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: '修改密码',
    description: '用户修改自己的密码'
  })
  @ApiBody({ type: ChangePasswordDto })
  @ApiResponse({
    status: 200,
    description: '密码修改成功',
    schema: {
      example: {
        message: '密码修改成功'
      }
    }
  })
  @ApiResponse({
    status: 401,
    description: '当前密码错误',
    schema: {
      example: {
        statusCode: 401,
        message: '当前密码错误',
        error: 'Unauthorized'
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: '新密码不能与当前密码相同',
    schema: {
      example: {
        statusCode: 400,
        message: '新密码不能与当前密码相同',
        error: 'Bad Request'
      }
    }
  })
  async changePassword(
    @CurrentUser() currentUser: User,
    @Body() changePasswordDto: ChangePasswordDto
  ): Promise<{ message: string }> {
    return this.authService.changePassword(currentUser.id, changePasswordDto);
  }
} 