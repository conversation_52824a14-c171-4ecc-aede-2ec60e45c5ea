import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthModule } from './auth.module';
import { User } from '../entities/user.entity';
import { UserRole } from './enums/user-role.enum';
import databaseConfig from '../config/database.config';

describe('用户认证测试', () => {
  let app: INestApplication;
  let authToken: string;
  
  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          load: [databaseConfig],
        }),
        TypeOrmModule.forRootAsync({
          imports: [ConfigModule],
          useFactory: (configService: ConfigService) => ({
            type: 'postgres',
            host: configService.get('DB_HOST', 'localhost'),
            port: configService.get('DB_PORT', 5432),
            username: configService.get('DB_USERNAME', 'postgres'),
            password: configService.get('DB_PASSWORD', 'postgres'),
            database: configService.get('DB_DATABASE', 'spacedata_test'),
            entities: [User],
            synchronize: true,
          }),
          inject: [ConfigService],
        }),
        AuthModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('用户注册和登录流程', () => {
    const testUser = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'Password123!',
      role: UserRole.FREE
    };

    beforeAll(async () => {
      // 先注册一个测试用户
      await request(app.getHttpServer())
        .post('/auth/register')
        .send(testUser)
        .expect(201);

      // 登录并获取token
      const response = await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          username: testUser.username,
          password: testUser.password,
        });
      authToken = response.body.data.token;
    });

    describe('POST /auth/login', () => {
      it('使用正确的用户名和密码应该成功登录', async () => {
        const response = await request(app.getHttpServer())
          .post('/auth/login')
          .send({
            username: testUser.username,
            password: testUser.password,
          })
          .expect(200);

        expect(response.body).toEqual({
          success: true,
          data: expect.objectContaining({
            token: expect.any(String),
            role: UserRole.FREE,
          }),
          error: null,
          timestamp: expect.any(String),
        });
      });

      it('使用错误的密码应该登录失败', async () => {
        const response = await request(app.getHttpServer())
          .post('/auth/login')
          .send({
            username: testUser.username,
            password: 'wrongpassword',
          })
          .expect(401);

        expect(response.body).toEqual({
          success: false,
          data: null,
          error: '用户名或密码错误',
          timestamp: expect.any(String),
        });
      });

      it('使用不存在的用户名应该登录失败', async () => {
        const response = await request(app.getHttpServer())
          .post('/auth/login')
          .send({
            username: 'nonexistentuser',
            password: testUser.password,
          })
          .expect(401);

        expect(response.body).toEqual({
          success: false,
          data: null,
          error: '用户名或密码错误',
          timestamp: expect.any(String),
        });
      });

      it('使用空的用户名或密码应该返回验证错误', async () => {
        const response = await request(app.getHttpServer())
          .post('/auth/login')
          .send({
            username: '',
            password: '',
          })
          .expect(400);

        expect(response.body).toEqual({
          success: false,
          data: null,
          error: expect.any(String),
          timestamp: expect.any(String),
        });
      });

      it('密码长度不足应该返回验证错误', async () => {
        const response = await request(app.getHttpServer())
          .post('/auth/login')
          .send({
            username: testUser.username,
            password: '123',
          })
          .expect(400);

        expect(response.body).toEqual({
          success: false,
          data: null,
          error: expect.any(String),
          timestamp: expect.any(String),
        });
      });
    });

    describe('登录后的token验证', () => {
      it('使用有效token应该能访问受保护的资源', async () => {
        await request(app.getHttpServer())
          .get('/auth/profile')
          .set('Authorization', `Bearer ${authToken}`)
          .expect(200);
      });

      it('使用无效token应该被拒绝访问', async () => {
        await request(app.getHttpServer())
          .get('/auth/profile')
          .set('Authorization', 'Bearer invalid-token')
          .expect(401);
      });

      it('不提供token应该被拒绝访问', async () => {
        await request(app.getHttpServer())
          .get('/auth/profile')
          .expect(401);
      });
    });

    describe('权限验证', () => {
      it('免费用户应该有API调用次数限制', async () => {
        const response = await request(app.getHttpServer())
          .get('/auth/limits')
          .set('Authorization', `Bearer ${authToken}`)
          .expect(200);

        expect(response.body.data).toEqual({
          apiCallsLimit: 100,
          downloadsLimit: 3,
          downloadSizeLimit: 10 * 1024 * 1024, // 10MB
        });
      });
    });
  });
}); 