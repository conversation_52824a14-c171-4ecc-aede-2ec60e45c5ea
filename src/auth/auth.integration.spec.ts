import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { createTestingApp } from '../test/test-utils';

describe('Auth Integration Tests', () => {
  let app: INestApplication;

  beforeAll(async () => {
    app = await createTestingApp();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('注册流程', () => {
    const testUser = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'Password123!',
    };

    it('应该成功注册新用户', () => {
      return request(app.getHttpServer())
        .post('/auth/register')
        .send(testUser)
        .expect(201)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data).toHaveProperty('id');
          expect(res.body.data.username).toBe(testUser.username);
          expect(res.body.data.email).toBe(testUser.email);
          expect(res.body.data).not.toHaveProperty('password');
        });
    });

    it('重复注册应该失败', () => {
      return request(app.getHttpServer())
        .post('/auth/register')
        .send(testUser)
        .expect(409)
        .expect((res) => {
          expect(res.body.success).toBe(false);
          expect(res.body.error).toBe('用户名或邮箱已存在');
        });
    });
  });

  describe('登录流程', () => {
    const loginCredentials = {
      username: 'testuser',
      password: 'Password123!',
    };

    it('使用正确凭据应该成功登录', () => {
      return request(app.getHttpServer())
        .post('/auth/login')
        .send(loginCredentials)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data).toHaveProperty('token');
          expect(typeof res.body.data.token).toBe('string');
        });
    });

    it('使用错误的密码应该登录失败', () => {
      return request(app.getHttpServer())
        .post('/auth/login')
        .send({
          ...loginCredentials,
          password: 'wrongpassword',
        })
        .expect(401)
        .expect((res) => {
          expect(res.body.success).toBe(false);
          expect(res.body.error).toBe('用户名或密码错误');
        });
    });

    it('使用不存在的用户名应该登录失败', () => {
      return request(app.getHttpServer())
        .post('/auth/login')
        .send({
          username: 'nonexistentuser',
          password: 'Password123!',
        })
        .expect(401)
        .expect((res) => {
          expect(res.body.success).toBe(false);
          expect(res.body.error).toBe('用户名或密码错误');
        });
    });
  });
}); 