import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { ConflictException, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { AuthService } from './auth.service';
import { User } from '../entities/user.entity';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { UserRole } from './enums/user-role.enum';

interface MockRepository<T> {
  findOne: jest.Mock;
  create: jest.Mock;
  save: jest.Mock;
}

describe('AuthService', () => {
  let service: AuthService;
  let jwtService: JwtService;
  let mockUserRepository: MockRepository<User>;

  beforeEach(async () => {
    mockUserRepository = {
      findOne: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(() => 'test-token'),
          },
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    jwtService = module.get<JwtService>(JwtService);
  });

  describe('register', () => {
    const validRegisterDto = {
      username: 'testuser',
      password: 'password123',
      email: '<EMAIL>',
    };

    it('should successfully register a new user', async () => {
      mockUserRepository.findOne
        .mockResolvedValueOnce(null)  // 用户名检查
        .mockResolvedValueOnce(null); // 邮箱检查

      const mockUser = {
        id: 1,
        ...validRegisterDto,
        password: 'hashedPassword',
        role: UserRole.FREE,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockUserRepository.create.mockReturnValue(mockUser);
      mockUserRepository.save.mockResolvedValue(mockUser);

      const result = await service.register(validRegisterDto);

      expect(result).toHaveProperty('id');
      expect(result).toHaveProperty('username', validRegisterDto.username);
      expect(result).toHaveProperty('email', validRegisterDto.email);
      expect(result).not.toHaveProperty('password');
      expect(mockUserRepository.findOne).toHaveBeenCalledTimes(2);
    });

    it('should throw BadRequestException if email is not provided', async () => {
      const dtoWithoutEmail = {
        username: 'testuser',
        password: 'password123',
      };

      await expect(service.register(dtoWithoutEmail as any)).rejects.toThrow(
        new BadRequestException('电子邮箱是必填项')
      );
    });

    it('should throw ConflictException with USERNAME_EXISTS code if username exists', async () => {
      mockUserRepository.findOne
        .mockResolvedValueOnce({ id: 1, ...validRegisterDto }) // 用户名存在
        .mockResolvedValueOnce(null); // 邮箱不存在

      const error = await service.register(validRegisterDto).catch(e => e);
      expect(error).toBeInstanceOf(ConflictException);
      expect(error.message).toBe('用户名已存在');
      expect(error.response.error).toBe('USERNAME_EXISTS');
    });

    it('should throw ConflictException with EMAIL_EXISTS code if email exists', async () => {
      mockUserRepository.findOne
        .mockResolvedValueOnce(null) // 用户名不存在
        .mockResolvedValueOnce({ id: 2, username: 'other', email: validRegisterDto.email }); // 邮箱存在

      const error = await service.register(validRegisterDto).catch(e => e);
      expect(error).toBeInstanceOf(ConflictException);
      expect(error.message).toBe('邮箱已被使用');
      expect(error.response.error).toBe('EMAIL_EXISTS');
    });
  });

  describe('login', () => {
    const validLoginDto = {
      username: 'testuser',
      password: 'password123',
    };

    it('should successfully login a user', async () => {
      const hashedPassword = await bcrypt.hash(validLoginDto.password, 10);
      const mockUser = {
        id: 1,
        username: validLoginDto.username,
        password: hashedPassword,
        role: UserRole.FREE,
        email: '<EMAIL>',
      };

      mockUserRepository.findOne.mockResolvedValue(mockUser);

      const result = await service.login(validLoginDto);

      expect(result).toHaveProperty('access_token', 'test-token');
      expect(result).toHaveProperty('user');
      expect(result.user).not.toHaveProperty('password');
      expect(result.user).toHaveProperty('email', mockUser.email);
    });

    it('should throw UnauthorizedException with INVALID_CREDENTIALS code if user not found', async () => {
      mockUserRepository.findOne.mockResolvedValue(null);

      const error = await service.login(validLoginDto).catch(e => e);
      expect(error).toBeInstanceOf(UnauthorizedException);
      expect(error.message).toBe('用户名或密码错误');
      expect(error.response.error).toBe('INVALID_CREDENTIALS');
    });

    it('should throw UnauthorizedException with INVALID_CREDENTIALS code if password is incorrect', async () => {
      const hashedPassword = await bcrypt.hash('differentpassword', 10);
      const mockUser = {
        id: 1,
        username: validLoginDto.username,
        password: hashedPassword,
        role: UserRole.FREE,
      };

      mockUserRepository.findOne.mockResolvedValue(mockUser);

      const error = await service.login(validLoginDto).catch(e => e);
      expect(error).toBeInstanceOf(UnauthorizedException);
      expect(error.message).toBe('用户名或密码错误');
      expect(error.response.error).toBe('INVALID_CREDENTIALS');
    });
  });
}); 