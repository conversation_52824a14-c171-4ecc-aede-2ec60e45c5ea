import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

/**
 * 添加用户审批状态相关字段的迁移
 * @description 为users表添加审批状态、审批时间、审批人和拒绝原因字段
 */
export class AddUserApprovalStatus1710900000000 implements MigrationInterface {
  name = 'AddUserApprovalStatus1710900000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 添加审批状态字段
    await queryRunner.addColumn('users', new TableColumn({
      name: 'approval_status',
      type: 'varchar',
      length: '20',
      default: "'pending'",
      comment: '用户审批状态：pending-待审批，approved-已批准，rejected-已拒绝'
    }));

    // 添加审批时间字段
    await queryRunner.addColumn('users', new TableColumn({
      name: 'approved_at',
      type: 'timestamp with time zone',
      isNullable: true,
      comment: '审批通过时间'
    }));

    // 添加审批人字段
    await queryRunner.addColumn('users', new TableColumn({
      name: 'approved_by',
      type: 'integer',
      isNullable: true,
      comment: '审批人用户ID'
    }));

    // 添加拒绝原因字段
    await queryRunner.addColumn('users', new TableColumn({
      name: 'rejection_reason',
      type: 'varchar',
      length: '500',
      isNullable: true,
      comment: '拒绝申请的原因'
    }));

    // 将现有用户的审批状态设置为已批准，确保现有用户不受影响
    await queryRunner.query(`
      UPDATE users 
      SET approval_status = 'approved', approved_at = created_at 
      WHERE approval_status = 'pending'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除添加的字段
    await queryRunner.dropColumn('users', 'rejection_reason');
    await queryRunner.dropColumn('users', 'approved_by');
    await queryRunner.dropColumn('users', 'approved_at');
    await queryRunner.dropColumn('users', 'approval_status');
  }
} 