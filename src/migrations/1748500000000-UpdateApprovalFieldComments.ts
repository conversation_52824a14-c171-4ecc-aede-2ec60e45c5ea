import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * 更新审批字段注释的迁移
 * @description 更新approved_at和approved_by字段的注释，反映这些字段也记录拒绝操作的信息
 */
export class UpdateApprovalFieldComments1748500000000 implements MigrationInterface {
  name = 'UpdateApprovalFieldComments1748500000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 更新approved_at字段注释
    await queryRunner.query(`
      COMMENT ON COLUMN users.approved_at IS '审批操作时间（包括批准和拒绝）'
    `);

    // 更新approved_by字段注释
    await queryRunner.query(`
      COMMENT ON COLUMN users.approved_by IS '执行审批操作的管理员用户ID（包括批准和拒绝）'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 恢复原来的注释
    await queryRunner.query(`
      COMMENT ON COLUMN users.approved_at IS '审批通过时间'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN users.approved_by IS '审批人用户ID'
    `);
  }
} 