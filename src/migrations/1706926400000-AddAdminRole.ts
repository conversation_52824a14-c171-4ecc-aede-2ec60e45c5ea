import { MigrationInterface, QueryRunner } from 'typeorm';
import * as bcrypt from 'bcrypt';

/**
 * 添加管理员角色支持迁移
 * @description 更新用户角色枚举以支持admin角色，并创建默认管理员用户
 */
export class AddAdminRole1706926400000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 1. 更新role字段的注释，说明支持的角色类型
    await queryRunner.query(`
      COMMENT ON COLUMN users.role IS '用户角色: admin(管理员), free(免费用户), premium(高级用户), enterprise(企业用户), government(政府用户)';
    `);

    // 2. 创建默认管理员用户（如果不存在）
    const adminExists = await queryRunner.query(`
      SELECT id FROM users WHERE username = 'admin' LIMIT 1;
    `);

    if (adminExists.length === 0) {
      // 生成管理员密码的哈希值
      const adminPassword = 'admin123'; // 在生产环境中应该使用更安全的密码
      const hashedPassword = await bcrypt.hash(adminPassword, 10);

      await queryRunner.query(`
        INSERT INTO users (username, password, email, role, is_active, created_at, updated_at)
        VALUES ('admin', $1, '<EMAIL>', 'admin', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
      `, [hashedPassword]);

      console.log('✅ 默认管理员用户已创建');
      console.log('   用户名: admin');
      console.log('   密码: admin123');
      console.log('   邮箱: <EMAIL>');
      console.log('   ⚠️  请在生产环境中修改默认密码！');
    } else {
      console.log('ℹ️  管理员用户已存在，跳过创建');
    }

    // 3. 添加角色检查约束（可选，确保只能使用有效的角色值）
    await queryRunner.query(`
      ALTER TABLE users 
      ADD CONSTRAINT chk_user_role 
      CHECK (role IN ('admin', 'free', 'premium', 'enterprise', 'government'));
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 1. 移除角色检查约束
    await queryRunner.query(`
      ALTER TABLE users DROP CONSTRAINT IF EXISTS chk_user_role;
    `);

    // 2. 移除默认管理员用户
    await queryRunner.query(`
      DELETE FROM users WHERE username = 'admin' AND email = '<EMAIL>';
    `);

    // 3. 移除字段注释
    await queryRunner.query(`
      COMMENT ON COLUMN users.role IS NULL;
    `);

    console.log('✅ 管理员角色支持已回滚');
  }
} 