import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUsersField1710754801389 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "satellites" ADD COLUMN IF NOT EXISTS "users" JSONB DEFAULT '[]'`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "satellites" DROP COLUMN IF EXISTS "users"`);
  }
} 