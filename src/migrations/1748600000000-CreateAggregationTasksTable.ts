import { MigrationInterface, QueryRunner, Table } from 'typeorm';

/**
 * 创建聚合任务表的迁移
 * 用于存储卫星数据聚合任务的执行状态和结果
 */
export class CreateAggregationTasksTable1748600000000 implements MigrationInterface {
  name = 'CreateAggregationTasksTable1748600000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'aggregation_tasks',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'task_type',
            type: 'varchar',
            length: '50',
            comment: '任务类型：full-全量聚合, partial-部分聚合, keyword-关键词聚合, incremental-增量聚合',
          },
          {
            name: 'status',
            type: 'varchar',
            length: '50',
            comment: '任务状态：pending-等待执行, running-执行中, completed-已完成, failed-失败',
          },
          {
            name: 'start_time',
            type: 'timestamp',
            comment: '任务开始时间',
          },
          {
            name: 'end_time',
            type: 'timestamp',
            isNullable: true,
            comment: '任务结束时间',
          },
          {
            name: 'processed_records',
            type: 'integer',
            default: 0,
            comment: '处理的记录数',
          },
          {
            name: 'aggregated_records',
            type: 'integer',
            default: 0,
            comment: '完成聚合的记录数',
          },
          {
            name: 'parameters',
            type: 'jsonb',
            isNullable: true,
            comment: '任务参数（JSON格式）',
          },
          {
            name: 'error_message',
            type: 'text',
            isNullable: true,
            comment: '错误信息（任务失败时）',
          },
          {
            name: 'progress',
            type: 'float',
            default: 0,
            comment: '任务进度百分比（0-100）',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            comment: '创建时间',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
            comment: '更新时间',
          },
        ],
        indices: [
          {
            name: 'IDX_aggregation_tasks_status',
            columnNames: ['status'],
          },
          {
            name: 'IDX_aggregation_tasks_task_type',
            columnNames: ['task_type'],
          },
          {
            name: 'IDX_aggregation_tasks_created_at',
            columnNames: ['created_at'],
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('aggregation_tasks');
  }
} 