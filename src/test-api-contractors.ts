import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DatabaseController } from './controllers/database.controller';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 测试发射承包商API功能
 */
async function bootstrap() {
  try {
    // 创建NestJS应用实例
    const app = await NestFactory.create(AppModule);
    
    // 获取DatabaseController实例
    const databaseController = app.get(DatabaseController);
    
    console.log('正在调用getLaunchContractors API方法...');
    
    // 直接调用控制器方法
    const result = await databaseController.getLaunchContractors();
    
    console.log(`成功获取${result.contractors.length}个发射承包商`);
    
    // 将结果保存到文件中
    const outputPath = path.join(process.cwd(), 'api-launch-contractors-result.json');
    fs.writeFileSync(outputPath, JSON.stringify(result, null, 2));
    
    console.log(`结果已写入文件: ${outputPath}`);
    
    // 关闭应用
    await app.close();
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

// 执行测试
bootstrap(); 