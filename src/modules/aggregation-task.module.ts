import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AggregationTask } from '../entities/aggregation-task.entity';
import { AggregationTaskService } from '../services/aggregation-task.service';
import { AggregationTaskController } from '../controllers/aggregation-task.controller';

/**
 * 聚合任务模块
 * 用于管理卫星数据聚合任务
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([AggregationTask]),
  ],
  controllers: [AggregationTaskController],
  providers: [AggregationTaskService],
  exports: [AggregationTaskService],
})
export class AggregationTaskModule {} 