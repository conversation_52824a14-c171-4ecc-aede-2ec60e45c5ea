import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { SatelliteTilesService } from '../services/satellite-tiles/satellite-tiles.service';
import { FilePushService } from '../services/satellite-tiles/file-push.service';
import { SatelliteTilesController } from '../controllers/satellite-tiles.controller';
import { ESModule } from '../elasticsearch/elasticsearch.module';
import { OrbitAnalysisModule } from './orbit-analysis.module';

/**
 * 卫星3D Tiles点云模块
 * 提供卫星点云数据的定时生成和API服务
 */
@Module({
  imports: [
    ScheduleModule.forRoot(), // 启用定时任务
    ESModule,                 // 导入Elasticsearch模块以获取TLE数据
    OrbitAnalysisModule,      // 导入轨道分析模块以使用OrbitCalculator
  ],
  controllers: [SatelliteTilesController],
  providers: [SatelliteTilesService, FilePushService],
  exports: [SatelliteTilesService, FilePushService],
})
export class SatelliteTilesModule {} 