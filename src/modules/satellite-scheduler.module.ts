import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { SatelliteSchedulerService } from '../services/satellite-scheduler/satellite-scheduler.service';
import { SatelliteSchedulerController } from '../controllers/satellite-scheduler.controller';
import { SatelliteModule } from './satellite.module';

/**
 * 卫星数据定时任务模块
 * 提供卫星数据增量聚合的定时任务服务
 */
@Module({
  imports: [
    ScheduleModule.forRoot(), // 启用定时任务
    SatelliteModule,          // 导入卫星模块以使用卫星服务
  ],
  controllers: [SatelliteSchedulerController],
  providers: [SatelliteSchedulerService],
  exports: [SatelliteSchedulerService],
})
export class SatelliteSchedulerModule {} 