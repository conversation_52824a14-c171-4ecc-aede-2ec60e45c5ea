import { Module } from '@nestjs/common';
import { OrbitCalculator } from '../services/orbit-calculator/OrbitCalculator';
import { PassAnalysisService } from '../services/pass-analysis/pass-analysis.service';
import { ConjunctionAnalysisService } from '../services/conjunction-analysis/conjunction-analysis.service';
import { PassAnalysisController } from '../controllers/pass-analysis.controller';
import { ConjunctionAnalysisController } from '../controllers/conjunction-analysis.controller';
import { OrbitCalculatorController } from '../controllers/orbit-calculator.controller';

@Module({
  imports: [],
  controllers: [
    OrbitCalculatorController,
    PassAnalysisController,
    ConjunctionAnalysisController
  ],
  providers: [
    {
      provide: OrbitCalculator,
      useFactory: () => new OrbitCalculator({
        wgs84: {
          earthRadius: 6378.137,     // 地球半径(km)
          mu: 398600.4418,          // 地球引力常数(km^3/s^2)
          j2: 0.00108262998905,     // J2摄动系数
          j3: -0.00000253215306,    // J3摄动系数
          j4: -0.00000161098761     // J4摄动系数
        }
      })
    },
    PassAnalysisService,
    ConjunctionAnalysisService
  ],
  exports: [
    OrbitCalculator,
    PassAnalysisService,
    ConjunctionAnalysisService
  ]
})
export class OrbitAnalysisModule {} 