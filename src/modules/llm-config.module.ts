import { Module } from '@nestjs/common';
import { LLMConfigController } from '../controllers/llm-config.controller';
import { LLMConfigService } from '../services/llm-config.service';
import { ESModule } from '../elasticsearch/elasticsearch.module';

/**
 * 大模型配置模块
 * 提供大模型配置管理功能
 */
@Module({
  imports: [ESModule], // 导入elasticsearch模块以获取TranslationService
  controllers: [LLMConfigController],
  providers: [LLMConfigService],
  exports: [LLMConfigService], // 导出服务供其他模块使用
})
export class LLMConfigModule {} 