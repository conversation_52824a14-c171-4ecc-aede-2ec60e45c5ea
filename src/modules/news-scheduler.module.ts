import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { NewsSchedulerService } from '../services/news-scheduler/news-scheduler.service';
import { NewsSchedulerController } from '../controllers/news-scheduler.controller';
import { ESModule } from '../elasticsearch/elasticsearch.module';

/**
 * 新闻处理定时任务模块
 * 提供新闻翻译和主题提取的定时任务服务
 */
@Module({
  imports: [
    ScheduleModule.forRoot(), // 启用定时任务
    ESModule,                 // 导入Elasticsearch模块以使用新闻服务
  ],
  controllers: [NewsSchedulerController],
  providers: [NewsSchedulerService],
  exports: [NewsSchedulerService],
})
export class NewsSchedulerModule {} 