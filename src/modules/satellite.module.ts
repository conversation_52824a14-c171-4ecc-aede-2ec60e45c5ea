import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Satellite } from '../entities/satellite.entity';
import { SatelliteService } from '../services/satellite.service';
import { SatelliteController } from '../controllers/satellite.controller';
import { ESModule } from '../elasticsearch/elasticsearch.module';
import { AggregationTaskModule } from './aggregation-task.module';
import { ConstellationController } from '../controllers/constellation.controller';
import { ConstellationService } from '../services/constellation.service';

/**
 * 卫星模块
 * 用于管理本地数据库中的卫星信息
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([Satellite]),
    ESModule, // 导入ES模块，以便访问ElasticsearchSatelliteService
    AggregationTaskModule, // 导入聚合任务模块，以便访问AggregationTaskService
  ],
  controllers: [SatelliteController, ConstellationController],
  providers: [SatelliteService, ConstellationService],
  exports: [SatelliteService, ConstellationService],
})
export class SatelliteModule {} 