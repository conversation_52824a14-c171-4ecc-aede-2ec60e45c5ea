import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DatabaseService } from '../services/database.service';
import { DatabaseController } from '../controllers/database.controller';

/**
 * 数据库模块
 * 提供数据库查询相关功能
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([]),
  ],
  controllers: [DatabaseController],
  providers: [DatabaseService],
  exports: [DatabaseService],
})
export class DatabaseModule {} 