import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DatabaseService } from './services/database.service';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 测试发射承包商集合获取功能
 */
async function bootstrap() {
  try {
    // 创建NestJS应用实例
    const app = await NestFactory.create(AppModule);
    
    // 获取DatabaseService实例
    const databaseService = app.get(DatabaseService);
    
    console.log('正在调用getLaunchContractors方法...');
    
    // 调用getLaunchContractors方法
    const result = await databaseService.getLaunchContractors();
    
    console.log(`成功获取${result.contractors.length}个发射承包商`);
    
    // 将结果保存到文件中
    const outputPath = path.join(process.cwd(), 'launch-contractors-result.json');
    fs.writeFileSync(outputPath, JSON.stringify(result, null, 2));
    
    console.log(`结果已写入文件: ${outputPath}`);
    
    // 关闭应用
    await app.close();
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

// 执行测试
bootstrap(); 