import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { AuthService } from '../auth/auth.service';
import { UserRole } from '../auth/enums/user-role.enum';
import { DataSource } from 'typeorm';
import { User } from '../entities/user.entity';

/**
 * 创建或更新admin用户的脚本
 */
async function createAdminUser() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const dataSource = app.get(DataSource);
  
  try {
    console.log('开始创建/更新admin用户...');
    
    // 查找现有的admin用户
    const userRepository = dataSource.getRepository(User);
    let adminUser = await userRepository.findOne({ where: { username: 'admin' } });
    
    if (adminUser) {
      console.log('找到现有admin用户，更新角色为admin...');
      adminUser.role = UserRole.ADMIN;
      await userRepository.save(adminUser);
      console.log('admin用户角色已更新为admin');
    } else {
      console.log('admin用户不存在，请先注册admin用户');
      console.log('运行以下命令注册admin用户：');
      console.log('curl -X POST http://localhost:3001/auth/register -H "Content-Type: application/json" -d \'{"username":"admin","password":"admin123","email":"<EMAIL>"}\'');
    }
    
    // 显示当前admin用户信息
    adminUser = await userRepository.findOne({ where: { username: 'admin' } });
    if (adminUser) {
      const { password, ...userInfo } = adminUser;
      console.log('当前admin用户信息：', userInfo);
    }
    
  } catch (error) {
    console.error('创建admin用户时出错：', error);
  } finally {
    await app.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  createAdminUser();
} 