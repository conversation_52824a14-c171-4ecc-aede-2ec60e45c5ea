/**
 * 碎片搜索响应接口
 */
export interface DebrisSearchResponse {
  /**
   * 总记录数
   */
  total: number;
  
  /**
   * 当前页码
   */
  page: number;
  
  /**
   * 每页记录数
   */
  size: number;
  
  /**
   * 碎片列表
   */
  items: DebrisItemWithMatch[];
}

/**
 * 带匹配信息的碎片项
 */
export interface DebrisItemWithMatch extends DebrisItem {
  /**
   * 匹配分数
   */
  match_score: number;
  
  /**
   * 数据来源索引
   */
  source_index?: string;
  
  /**
   * 字段来源记录
   * 当字段值在不同索引中不同时，记录使用的字段来自哪个索引
   */
  source_fields?: Record<string, string>;
  
  /**
   * 字段值记录
   * 存储字段在不同索引中的不同值及其来源
   */
  field_values?: Record<string, Array<{value: any, source: string}>>;
  
  /**
   * 匹配字段
   */
  matched_fields?: {
    /**
     * 关键词匹配
     */
    keyword?: boolean;
    
    /**
     * NORAD ID匹配
     */
    norad_id?: boolean;
    
    /**
     * COSPAR ID匹配
     */
    cospar_id?: boolean;
    
    /**
     * 名称匹配
     */
    name?: boolean;
    
    /**
     * 国家匹配
     */
    country?: boolean;
    
    /**
     * 对象类别匹配
     */
    object_class?: boolean;
    
    /**
     * 任务匹配
     */
    mission?: boolean;
    
    /**
     * 轨道周期范围匹配
     */
    period_minutes_range?: boolean;
    
    /**
     * 轨道倾角范围匹配
     */
    incl_degrees_range?: boolean;
    
    /**
     * 远地点高度范围匹配
     */
    apogee_km_range?: boolean;
    
    /**
     * 近地点高度范围匹配
     */
    perigee_km_range?: boolean;
  };
  
  /**
   * 匹配字段描述
   */
  matched_fields_description?: Array<{
    /**
     * 匹配字段名称
     */
    field: string;
    
    /**
     * 匹配级别
     */
    matchLevel: string;
    
    /**
     * 匹配分数
     */
    score: number;
  }>;
  
  /**
   * 匹配字段数量
   */
  matched_fields_count?: number;
}

/**
 * 碎片信息项
 */
export interface DebrisItem {
  /**
   * 文档ID
   */
  _id: string;
  
  /**
   * NORAD ID
   */
  norad_id?: number;
  
  /**
   * COSPAR ID
   */
  cospar_id?: string;
  
  /**
   * 碎片名称
   */
  name?: string;
  
  /**
   * 所属国家
   */
  country?: string;
  
  /**
   * 发射日期
   */
  launch_date?: string;
  
  /**
   * 发射场
   */
  launch_site?: string;
  
  /**
   * 雷达散射截面大小
   */
  rcs_size?: string;
  
  /**
   * 坠毁日期
   */
  decay?: string;
  
  /**
   * 首次观测时间
   */
  first_epoch?: string;
  
  /**
   * 对象类型
   */
  object_class?: string;
  
  /**
   * 任务名称
   */
  mission?: string;
  
  /**
   * 其他属性
   */
  [key: string]: any;
} 