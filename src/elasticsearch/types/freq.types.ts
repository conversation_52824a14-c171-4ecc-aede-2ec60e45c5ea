import { ApiProperty } from '@nestjs/swagger';

/**
 * 卫星频率信息文档
 */
export class FreqDocument {
  @ApiProperty({ description: '卫星名称' })
  sat_name: string;

  @ApiProperty({ description: '卫星编号', required: false })
  norad_id?: number;

  @ApiProperty({ description: '国际卫星标识符', required: false })
  cospar_id?: string;

  @ApiProperty({ description: '频率（MHz）' })
  frequency: number;

  @ApiProperty({ description: '带宽（MHz）' })
  bandwidth: number;

  @ApiProperty({ description: '极化方式', required: false })
  polarization?: string;

  @ApiProperty({ description: '业务类型', required: false })
  service?: string;

  @ApiProperty({ description: '传输方向', required: false })
  direction?: string;

  @ApiProperty({ description: '发射特性', required: false })
  emission?: string;

  @ApiProperty({ description: '功率（dBW）', required: false })
  power?: number;

  @ApiProperty({ description: '波束名称', required: false })
  beam_name?: string;

  @ApiProperty({ description: '状态', required: false })
  status?: string;

  @ApiProperty({ description: '发布日期', required: false })
  date_of_publication?: string;

  @ApiProperty({ description: '备注', required: false })
  remarks?: string;
}

/**
 * 卫星频率信息查询响应
 */
export class FreqSearchResponse {
  @ApiProperty({ description: '总记录数' })
  total: number;

  @ApiProperty({ description: '频率信息列表', type: [FreqDocument] })
  data: FreqDocument[];
} 