/**
 * ES 文档中的漏洞数据类型
 */
export interface ESLoopholeDocument {
  cveMetadata: {
    cveId: string;
    assignerShortName: string;
    dateReserved: string;
    datePublished: string;
    dateUpdated: string;
  };
  containers: {
    cna: {
      descriptions: Array<{
        value: string;
      }>;
      affected: Array<{
        vendor: string;
        product: string;
        versions: Array<{
          version: string;
          status: string;
        }>;
      }>;
      problemTypes: Array<{
        descriptions: Array<{
          description: string;
          type: string;
        }>;
      }>;
      references: Array<{
        url: string;
        tags: string[];
      }>;
      metrics?: Array<{
        severity: string;
      }>;
      solutions?: Array<{
        value: string;
      }>;
    };
    adp?: Array<{
      title: string;
      references: Array<{
        url: string;
        tags: string[];
      }>;
    }>;
  };
}

/**
 * 漏洞数据类型（API 响应格式）
 */
export interface LoopholeDocument {
  descriptions: string;
  affected: string;
  severity: string;
  cve_id: string;
  published_date: string;
  patch_status: string;
  patch_version: string | null;
  references: string[];
  mitigation: string;
}

/**
 * 漏洞查询响应类型
 */
export interface LoopholeSearchResponse {
  total: number;
  hits: Array<LoopholeDocument & { _id: string }>;
} 