/**
 * 新闻文档类型
 * 定义ES中新闻数据的字段结构
 */
export interface NewsDocument {
  title: string;
  summary: string;
  content: string;
  title_cn?: string;
  summary_cn?: string;
  content_cn?: string;
  themes_cn?: string;      // 中文主题词，逗号分隔
  published_at?: string;
  source?: string;
  author?: string;
  url?: string;
  info_source?: string;    // 内容来源URL
  content_fetched?: boolean;     // 是否已尝试爬取内容
  content_fetch_time?: string;   // 内容爬取时间
  content_fetch_success?: boolean; // 内容爬取是否成功
  _id?: string;
  _index?: string;
  [key: string]: any; // 允许其他字段
}

/**
 * 失败文档记录
 */
export interface FailedDocument {
  index: string;       // 索引名称
  id: string;          // 文档ID
  reason: string;      // 失败原因
  timestamp: Date;     // 失败时间
  retryCount?: number; // 重试次数
  skipped?: boolean;   // 是否是跳过的文档
}

/**
 * 翻译统计结果类型
 */
export interface TranslationStatistics {
  total: number;        // 总处理文档数
  success: number;      // 成功翻译文档数
  failed: number;       // 失败文档数
  skipped: number;      // 已翻译过跳过的文档数
  indexCount: number;   // 处理的索引数量
  indexes: string[];    // 处理的索引列表
  startTime: Date;      // 开始时间
  endTime?: Date;       // 结束时间
  elapsedTime?: number; // 耗时(毫秒)
  failedDocuments?: FailedDocument[]; // 失败文档记录
  skippedDocuments?: FailedDocument[]; // 跳过的文档记录
  // 主题提取相关统计
  themeExtraction?: {
    total: number;      // 尝试提取主题词的文档数
    success: number;    // 成功提取主题词的文档数
    failed: number;     // 主题提取失败的文档数
    skipped: number;    // 跳过主题提取的文档数
  };
}

/**
 * 分页参数类型
 */
export interface PaginationParams {
  page?: number;
  size?: number;
}

/**
 * 搜索参数类型
 */
export interface SearchParams extends PaginationParams {
  keywords?: string;
  dateFrom?: string;
  dateTo?: string;
  source?: string;
  sortBy?: 'published_at' | 'score';
  sortOrder?: 'asc' | 'desc';
}

/**
 * 新闻列表返回类型
 */
export interface NewsListResult {
  data: NewsDocument[];
  total: number;
  page: number;
  size: number;
  totalPages: number;
}

/**
 * 大模型模式枚举
 */
export enum LLMMode {
  DEFAULT = 'default',
  HIGH_QUALITY = 'high_quality',
  FAST = 'fast'
}

/**
 * 翻译请求参数类型
 */
export interface TranslateNewsParams {
  batchSize?: number;           // 每批处理的文档数量，默认10
  maxDocs?: number;             // 最大处理文档数，默认不限制
  forceRetranslate?: boolean;   // 是否强制重新翻译已翻译的内容
  forceRefetchContent?: boolean; // 是否强制重新爬取内容，默认false只爬取未爬取过的
  specificIndexes?: string[];   // 指定要处理的索引，默认处理所有news_*
  llmMode?: LLMMode;           // 大模型模式选择
  customModel?: string;        // 自定义模型名称
  autoExtractThemes?: boolean; // 是否自动提取主题词和类别，默认true
  documentId?: string;         // 指定要翻译的特定文档ID，如果指定则只翻译该文档
}

/**
 * 主题提取请求参数类型
 */
export interface ExtractThemesParams {
  batchSize?: number;           // 每批处理的文档数量，默认10
  maxDocs?: number;             // 最大处理文档数，默认不限制
  forceReextract?: boolean;     // 是否强制重新提取已有主题词的文档
  specificIndexes?: string[];   // 指定要处理的索引，默认处理所有news_*
  llmMode?: LLMMode;           // 大模型模式选择
  customModel?: string;        // 自定义模型名称
}

/**
 * 翻译结果类型
 */
export interface TranslationResponse {
  success: boolean;
  message: string;
  statistics: TranslationStatistics;
  timestamp?: string;
}

/**
 * 主题提取结果类型
 */
export interface ThemeExtractionResponse {
  success: boolean;
  message: string;
  statistics: TranslationStatistics;
  timestamp?: string;
}

/**
 * 热门主题参数类型
 */
export interface HotThemesParams {
  topN?: number;               // 返回前N个热门主题
  minCount?: number;           // 主题词最小出现次数
  specificIndexes?: string[];  // 指定要处理的索引，默认处理所有news_*
}

/**
 * 热门主题类型
 */
export interface HotTheme {
  theme: string;
  count: number;
}

/**
 * 热门主题结果类型
 */
export interface HotThemesResponse {
  success: boolean;
  message: string;
  data: {
    themes: HotTheme[];
    total: number;
    indexCount: number;
    indexes: string[];
    processedDocs: number;
  };
  timestamp?: string;
}