import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsInt, Min } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * 星座查询 DTO
 */
export class ConstellationQueryDto {
  @ApiProperty({
    description: '查询关键词（在星座名称、公司名称等字段中进行模糊匹配）',
    required: false,
    example: 'Starlink'
  })
  @IsString()
  @IsOptional()
  keyword?: string;

  @ApiProperty({
    description: '目标名称（星座名称）',
    required: false,
    example: 'Starlink'
  })
  @IsString()
  @IsOptional()
  targetName?: string;

  @ApiProperty({
    description: '目标编号',
    required: false,
    example: 'STARLINK-1234'
  })
  @IsString()
  @IsOptional()
  targetId?: string;

  @ApiProperty({
    description: '所属国家（支持中英文，如：中国、USA、Russia等）',
    required: false,
    example: 'USA'
  })
  @IsString()
  @IsOptional()
  country?: string;

  @ApiProperty({
    description: '所属机构（如：SpaceX、OneWeb、中国航天科技集团等）',
    required: false,
    example: 'SpaceX'
  })
  @IsString()
  @IsOptional()
  organization?: string;

  @ApiProperty({
    description: '星座用途（支持中英文，如：通信/Communication、导航/Navigation、地球观测/Earth Observation、互联网/Internet等）',
    required: false,
    example: '互联网'
  })
  @IsString()
  @IsOptional()
  purpose?: string;

  @ApiProperty({
    description: '页码',
    required: false,
    default: 1,
    minimum: 1,
    example: 1
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: '每页数量',
    required: false,
    default: 10,
    minimum: 1,
    example: 10
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number = 10;
}

/**
 * 星座数据源枚举
 */
export enum ConstellationDataSource {
  NEWSPACE = 'constell_newspace',
  N2YO = 'constell_n2yo'
}

/**
 * 星座查询响应接口
 */
export interface ConstellationSearchResponse {
  total: number;
  page: number;
  limit: number;
  hits: Array<{
    id: string;
    constellation_name: string;
    company: string;
    satellites: Array<{
      norad_id: string;
      name: string;
    }>;
    match_score: number;
    matched_fields?: {
      targetName: boolean;
      targetId?: boolean;
      organization: boolean;
      country: boolean;
      purpose: boolean;
    };
    matched_fields_description?: Array<{
      field: string;
      matchLevel: string;
      score: number;
    }>;
    sources: {
      [key: string]: {
        source: ConstellationDataSource;
        original_data?: any;
      };
    };
  }>;
}

/**
 * 星座卫星 DTO
 */
export class ConstellationSatelliteDto {
  @ApiProperty({
    description: 'NORAD ID',
    example: '45123'
  })
  norad_id!: string;

  @ApiProperty({
    description: '卫星名称',
    example: 'STARLINK-1234'
  })
  name!: string;
}

/**
 * 星座匹配字段 DTO
 */
export class ConstellationMatchedFieldsDto {
  @ApiProperty({
    description: '目标名称是否匹配',
    example: true
  })
  targetName!: boolean;

  @ApiProperty({
    description: '目标ID是否匹配',
    example: false
  })
  targetId?: boolean;

  @ApiProperty({
    description: '组织是否匹配',
    example: true
  })
  organization!: boolean;

  @ApiProperty({
    description: '国家是否匹配',
    example: false
  })
  country!: boolean;

  @ApiProperty({
    description: '用途是否匹配',
    example: false
  })
  purpose!: boolean;
}

/**
 * 星座匹配描述 DTO
 */
export class ConstellationMatchDescriptionDto {
  @ApiProperty({
    description: '字段名称',
    example: 'targetName'
  })
  field!: string;

  @ApiProperty({
    description: '匹配级别',
    example: '完全匹配'
  })
  matchLevel!: string;

  @ApiProperty({
    description: '匹配分数',
    example: 1.0
  })
  score!: number;
}

/**
 * 星座查询结果项 DTO
 */
export class ConstellationHitDto {
  @ApiProperty({
    description: '星座ID',
    example: '123456'
  })
  id!: string;

  @ApiProperty({
    description: '星座名称',
    example: 'Starlink'
  })
  constellation_name!: string;

  @ApiProperty({
    description: '所属公司',
    example: 'SpaceX'
  })
  company!: string;

  @ApiProperty({
    description: '卫星列表',
    type: 'array',
    isArray: true
  })
  satellites!: ConstellationSatelliteDto[];

  @ApiProperty({
    description: '匹配分数',
    example: 0.95
  })
  match_score!: number;

  @ApiProperty({
    description: '匹配字段',
    type: 'object',
    additionalProperties: false
  })
  matched_fields?: ConstellationMatchedFieldsDto;

  @ApiProperty({
    description: '匹配字段描述',
    type: 'array',
    isArray: true
  })
  matched_fields_description?: ConstellationMatchDescriptionDto[];

  @ApiProperty({
    description: '数据源信息',
    type: 'object',
    additionalProperties: true
  })
  sources!: Record<string, ConstellationSourceDto>;
}

/**
 * 星座查询响应 DTO（用于 Swagger 文档）
 */
export class ConstellationSearchResponseDto {
  @ApiProperty({
    description: '总记录数',
    example: 10
  })
  total!: number;

  @ApiProperty({
    description: '当前页码',
    example: 1
  })
  page!: number;

  @ApiProperty({
    description: '每页记录数',
    example: 10
  })
  limit!: number;

  @ApiProperty({
    description: '查询结果列表',
    type: 'array',
    isArray: true
  })
  hits!: ConstellationHitDto[];
}

/**
 * 星座数据源 DTO
 */
export class ConstellationSourceDto {
  @ApiProperty({
    description: '数据源类型',
    enum: ConstellationDataSource,
    example: 'constell_newspace'
  })
  source!: ConstellationDataSource;
}

/**
 * 星座名称集合响应 DTO
 */
export class ConstellationNamesResponseDto {
  @ApiProperty({
    description: '星座名称集合',
    type: [String],
    example: ['Starlink', 'OneWeb', 'Kuiper']
  })
  constellationNames: string[];

  @ApiProperty({
    description: '星座数量',
    type: Number,
    example: 3
  })
  count: number;
}

/**
 * 星座所属机构集合响应 DTO
 */
export class ConstellationOrganizationsResponseDto {
  @ApiProperty({
    description: '星座所属机构集合',
    type: [String],
    example: ['SpaceX', 'OneWeb', 'Amazon', 'Boeing', 'Lockheed Martin']
  })
  organizations: string[];

  @ApiProperty({
    description: '机构数量',
    type: Number,
    example: 5
  })
  count: number;
}

/**
 * 星座用途集合响应 DTO
 */
export class ConstellationPurposesResponseDto {
  @ApiProperty({
    description: '星座用途集合',
    type: [String],
    example: ['通信', 'Communication', '导航', 'Navigation', '地球观测', 'Earth Observation']
  })
  purposes: string[];

  @ApiProperty({
    description: '用途数量',
    type: Number,
    example: 6
  })
  count: number;
} 