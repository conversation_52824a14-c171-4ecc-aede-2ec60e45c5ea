/**
 * 关联分数类型定义
 */
export interface Score {
  cosparMatch: number;
  timeScore: number;
  nameScore: number;
}

/**
 * 关联结果类型定义
 */
export interface CorrelationResult {
  event_id: string;
  confidence: number;
}

/**
 * 碎片事件关联结果类型定义
 */
export interface EventCorrelationResult {
  debris_list: string[];
}

/**
 * 评分权重配置
 */
export interface ScoringWeights {
  cosparWeight: number;
  timeWeight: number;
  nameWeight: number;
}

/**
 * 关联配置参数
 */
export interface CorrelationConfig {
  timeWindowYears: number;
  minNameSimilarity: number;
  weights: ScoringWeights;
} 