/**
 * 卫星文档接口
 */
export interface SatelliteDocument {
  info_source: string;
  update_time: string;
  satellite_name: string;
  alternative_name: string;
  country_of_registry: string;
  owner: string;
  status: string;
  norad_id: number;
  source: string;
  orbit_info: {
    decay_date: string | null;
    deployed_date: string | null;
    period_minutes?: number;
    inclination_degrees?: number;
    apogee_km?: number;
    perigee_km?: number;
    eccentricity?: number;
    arg_of_pericenter?: number;
    mean_anomaly?: number;
    mean_motion?: number;
  };
  launch_info: {
    launch_date: string | null;
    launch_site?: string;
    launch_vehicle?: string;
  };
}

/**
 * 带来源的值接口
 */
export interface SourcedValue {
  value: any;
  sources: string[];
}

/**
 * 聚合后的卫星信息接口
 */
export interface AggregatedSatelliteInfo {
  // 基本信息
  satellite_name: SourcedValue[];
  alternative_name: SourcedValue[];
  cospar_id: SourcedValue[];
  country_of_registry: SourcedValue[];
  owner: SourcedValue[];
  status: SourcedValue[];
  norad_id: SourcedValue[];
  
  // 发射信息
  launch_info: SourcedValue[];
  
  // 轨道信息
  orbit_info: SourcedValue[];
  
  // 物理特性
  mass_kg?: SourcedValue[];
  power_watts?: SourcedValue[];
  lifetime_years?: SourcedValue[];
  contractor?: SourcedValue[];
  
  // 任务信息
  purpose?: SourcedValue[];
  detailed_purpose?: SourcedValue[];
  
  // 技术信息
  payload?: SourcedValue[];
  payload_description?: SourcedValue[];
  
  // 元数据
  update_time: SourcedValue[];
  _sources: string[];
}

/**
 * 卫星信息查询响应接口
 */
export interface SatelliteSearchResponse {
  total: number;
  page: number;
  limit: number;
  hits: (AggregatedSatelliteInfo & {
    _score?: number;
    _similarity_info?: {
      total_score: number;
      normalized_score: number;
      index?: string;
      indices?: string[];
      fields: Record<string, number>;
    };
  })[];
} 