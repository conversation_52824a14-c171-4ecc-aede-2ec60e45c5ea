import { Injectable, Logger } from '@nestjs/common';
import { ElasticsearchService } from '@nestjs/elasticsearch';
import { ElasticsearchBaseService } from './elasticsearch.base.service';
import { LaunchQueryDto, LaunchStatus, SortDirection } from '../dto/launch-query.dto';
import { LaunchCosparQueryDto } from '../dto/launch-cospar-query.dto';
import { LaunchSite, launchSites } from '../../../data/launchSites';
import { LaunchSiteWikiQueryDto } from '../dto/launch-site-wiki-query.dto';
import { findLaunchSiteByName, getLaunchSiteChineseName } from '../../../data/launchSites';

/**
 * 发射信息文档类型
 */
interface LaunchDocument {
  _id: string;
  _index: string;
  site_name?: string;
  [key: string]: any;  // 允许其他字段
}

/**
 * Elasticsearch发射信息服务
 */
@Injectable()
export class ElasticsearchLaunchService extends ElasticsearchBaseService {
  protected readonly logger = new Logger(ElasticsearchLaunchService.name);
  private readonly launchIndexPatterns = ['launch_jonathan', 'launch_spacenow*'];
  private readonly gunterIndex = 'launch_gunter';
  private readonly wikiLaunchSitesIndex = 'launchsites_wiki';
  private readonly serviceProviderIndex = 'orgs_jonathan';

  constructor(protected readonly elasticsearchService: ElasticsearchService) {
    super(elasticsearchService);
  }

  /**
   * 获取所有匹配的发射信息索引
   * @param status 发射状态
   * @returns 匹配的索引名称数组
   */
  private async getLaunchIndices(status?: LaunchStatus): Promise<string[]> {
    try {
      // 如果是历史发射、发射成功或发射失败状态，只返回launch_jonathan索引
      if (status === LaunchStatus.HISTORY || status === LaunchStatus.SUCCESS || status === LaunchStatus.FAILURE) {
        return ['launch_jonathan'];
      }

      // 获取所有索引
      const indices = await this.elasticsearchService.cat.indices({
        format: 'json'
      });

      // 过滤出匹配模式的索引
      const matchedIndices = indices
        .map((index: any) => index.index)
        .filter((indexName: string) => {
          // 检查索引名称是否匹配任一模式
          return this.launchIndexPatterns.some(pattern => {
            // 如果模式以*结尾，则检查索引名称是否以模式前缀开头
            if (pattern.endsWith('*')) {
              const prefix = pattern.slice(0, -1);
              return indexName.startsWith(prefix);
            }
            // 否则进行精确匹配
            return indexName === pattern;
          });
        });

      this.logger.debug(`找到 ${matchedIndices.length} 个匹配的发射信息索引: ${matchedIndices.join(', ')}`);
      return matchedIndices;
    } catch (error) {
      this.logger.error(`获取发射信息索引失败: ${error.message}`, error.stack);
      // 如果出错，返回默认索引模式
      return this.launchIndexPatterns;
    }
  }

  /**
   * 将发射场名称转换为中文名称
   * @param siteName 发射场名称（可以是代码、英文名、别名等）
   * @returns 中文名称，如果找不到对应的发射场则返回原始名称
   */
  private convertToChineseName(siteName: string | undefined): string {
    if (!siteName || typeof siteName !== 'string') {
      return '';  // 如果没有发射场名称，返回空字符串
    }

    const normalizedInput = siteName.toLowerCase().trim();
    
    // 尝试查找匹配的发射场
    const site = launchSites.find(site => 
      site.code.toLowerCase() === normalizedInput ||
      site.englishName.toLowerCase() === normalizedInput ||
      site.chineseName === siteName.trim() ||
      site.aliases?.some(alias => alias.toLowerCase() === normalizedInput)
    );

    return site ? site.chineseName : siteName;
  }

  /**
   * 搜索发射信息
   * @param query 发射信息查询参数
   * @returns 发射信息搜索结果
   */
  async searchLaunchInfo(query: LaunchQueryDto): Promise<any> {
    try {
      this.logger.debug(`搜索发射信息，参数: ${JSON.stringify(query)}`);

      const { page = 1, limit = 10 } = query;
      const from = (page - 1) * limit;

      // 构建查询条件
      const must: any[] = [];

      // 发射时间范围（需要将北京时间转换为UTC时间）
      if (query.launchDateStart || query.launchDateEnd) {
        const rangeQuery: any = {
          range: {
            launch_date: {}
          }
        };

        if (query.launchDateStart) {
          // 将北京时间转换为UTC时间
          const utcStartDate = this.convertBeijingToUTC(query.launchDateStart);
          this.logger.debug(`时间转换: 北京时间起始日期 ${query.launchDateStart} -> UTC时间 ${utcStartDate}`);
          rangeQuery.range.launch_date.gte = utcStartDate;
        }

        if (query.launchDateEnd) {
          // 将北京时间转换为UTC时间，并设置为当天的结束时间（23:59:59）
          const utcEndDate = this.convertBeijingToUTC(query.launchDateEnd, true);
          this.logger.debug(`时间转换: 北京时间结束日期 ${query.launchDateEnd} -> UTC时间 ${utcEndDate}`);
          rangeQuery.range.launch_date.lte = utcEndDate;
        }

        must.push(rangeQuery);
      }

      // 火箭型号
      if (query.rocketName) {
        must.push({
          term: {
            "rocket_name.keyword": query.rocketName
          }
        });
      }

      // 发射场
      if (query.siteName) {
        const siteIdentifiers = this.getAllLaunchSiteIdentifiers(query.siteName);
        
        // 使用bool查询匹配任何一个可能的发射场标识符
        must.push({
          bool: {
            should: siteIdentifiers.map(name => ({
              term: {
                "site_name.keyword": name
              }
            })),
            minimum_should_match: 1
          }
        });
        
        this.logger.debug(`发射场查询将匹配以下任意值: ${siteIdentifiers.join(', ')}`);
      }

      // 服务商
      if (query.provider) {
        must.push({
          term: {
            "provider.keyword": query.provider
          }
        });
      }

      // 任务状态
      if (query.status) {
        // 获取匹配的索引
        const indices = await this.getLaunchIndices(query.status);
        this.logger.debug(`使用索引: ${indices.join(', ')}`);

        // 如果是历史发射、发射成功或发射失败状态，使用launch_code字段匹配
        if (query.status === LaunchStatus.HISTORY || 
            query.status === LaunchStatus.SUCCESS || 
            query.status === LaunchStatus.FAILURE) {
          
          // 如果是历史发射，不需要添加额外的查询条件
          if (query.status !== LaunchStatus.HISTORY) {
            // 发射成功匹配'S'，发射失败匹配'F'
            const statusChar = query.status === LaunchStatus.SUCCESS ? 'S' : 'F';
            must.push({
              script: {
                script: {
                  source: "doc['launch_code.keyword'].value != null && doc['launch_code.keyword'].value.length() >= 2 && doc['launch_code.keyword'].value.substring(1, 2) == params.status",
                  params: {
                    status: statusChar
                  }
                }
              }
            });
          }

          // 执行查询
          const response = await this.elasticsearchService.search({
            index: indices,
            track_total_hits: true, // 确保返回准确的总记录数
            body: {
              query: {
                bool: {
                  must
                }
              },
              sort: [
                { launch_date: { order: SortDirection.DESC } } // 固定按发射时间降序排序
              ],
              from,
              size: limit
            }
          });

          // 处理结果
          const hits = response.hits?.hits || [];
          const total = typeof response.hits.total === 'number'
            ? response.hits.total
            : (response.hits.total as any).value || 0;

          const documents = hits.map(hit => {
            const source = hit._source as LaunchDocument;
            const { _id, _index, ...restSource } = source;
            return {
              ...restSource,
              // 将发射场名称转换为中文名称
              site_name: this.convertToChineseName(source.site_name)
            } as LaunchDocument;
          });

          return {
            total,
            page,
            limit,
            results: documents
          };
        }

        // 对于其他状态，使用原有的状态匹配逻辑
        const statusValues = this.mapStatusToDbValues(query.status);
        must.push({
          bool: {
            should: statusValues.map(value => ({
              term: {
                "status.keyword": value
              }
            })),
            minimum_should_match: 1
          }
        });

        // 如果没有任何查询条件，则匹配所有文档
        if (must.length === 0) {
          must.push({ match_all: {} });
        }

        // 根据用户参数和状态确定排序方式
        let sortOrder = query.sortDirection || SortDirection.DESC; // 默认使用用户指定的排序方向，如果未指定则默认降序

        // 如果是即将发射，则按时间升序排序（最早的排在前面），除非用户明确指定了排序方向
        if (query.status === LaunchStatus.UPCOMING && !query.sortDirection) {
          sortOrder = SortDirection.ASC;
        }

        this.logger.debug(`排序方向: ${sortOrder}, 状态: ${query.status}, 用户指定排序: ${query.sortDirection || '未指定'}`);

        // 执行查询
        const response = await this.elasticsearchService.search({
          index: indices,
          track_total_hits: true, // 确保返回准确的总记录数
          body: {
            query: {
              bool: {
                must
              }
            },
            sort: [
              { launch_date: { order: sortOrder } }
            ],
            from,
            size: limit
          }
        });

        // 处理结果
        const hits = response.hits?.hits || [];
        const total = typeof response.hits.total === 'number'
          ? response.hits.total
          : (response.hits.total as any).value || 0;

        const documents = hits.map(hit => {
          const source = hit._source as LaunchDocument;
          const { _id, _index, ...restSource } = source;
          return {
            ...restSource,
            // 将发射场名称转换为中文名称
            site_name: this.convertToChineseName(source.site_name)
          } as LaunchDocument;
        });

        return {
          total,
          page,
          limit,
          results: documents
        };
      }

      // 如果没有指定状态，则使用默认查询逻辑
      const indices = await this.getLaunchIndices();
      this.logger.debug(`使用索引: ${indices.join(', ')}`);

      // 如果没有任何查询条件，则匹配所有文档
      if (must.length === 0) {
        must.push({ match_all: {} });
      }

      // 执行查询
      const response = await this.elasticsearchService.search({
        index: indices,
        track_total_hits: true,
        body: {
          query: {
            bool: {
              must
            }
          },
          sort: [
            { launch_date: { order: query.sortDirection || SortDirection.DESC } }
          ],
          from,
          size: limit
        }
      });

      // 处理结果
      const hits = response.hits?.hits || [];
      const total = typeof response.hits.total === 'number'
        ? response.hits.total
        : (response.hits.total as any).value || 0;

      const documents = hits.map(hit => {
        const source = hit._source as LaunchDocument;
        const { _id, _index, ...restSource } = source;
        return {
          ...restSource,
          // 将发射场名称转换为中文名称
          site_name: this.convertToChineseName(source.site_name)
        } as LaunchDocument;
      });

      return {
        total,
        page,
        limit,
        results: documents
      };
    } catch (error) {
      this.logger.error(`搜索发射信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取火箭名称集合
   * @returns 火箭名称集合
   */
  async getRocketNames(): Promise<string[]> {
    try {
      this.logger.debug('获取火箭名称集合');

      // 获取匹配的索引，不传入状态参数以获取所有索引
      const indices = await this.getLaunchIndices();
      this.logger.debug(`使用索引: ${indices.join(', ')}`);

      // 使用聚合查询获取所有不同的火箭名称
      const response = await this.elasticsearchService.search({
        index: indices,
        size: 0, // 不需要返回文档，只需要聚合结果
        body: {
          aggs: {
            rocket_names: {
              terms: {
                field: 'rocket_name.keyword',
                size: 1000, // 获取足够多的火箭名称
                order: {
                  _key: 'asc' // 按名称字母顺序排序
                }
              }
            }
          }
        }
      });

      // 提取聚合结果
      const buckets = (response.aggregations?.rocket_names as any)?.buckets || [];

      // 提取火箭名称并过滤掉空值
      const rocketNames = buckets
        .map((bucket: any) => bucket.key)
        .filter((name: string) => name && name.trim() !== '' &&
                                 name.toLowerCase() !== 'null' &&
                                 name.toLowerCase() !== 'undefined' &&
                                 name.toLowerCase() !== 'none');

      this.logger.debug(`获取到 ${rocketNames.length} 个火箭名称`);
      return rocketNames;
    } catch (error) {
      this.logger.error(`获取火箭名称集合失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取发射场名称集合
   * @returns 发射场名称集合
   */
  async getSiteNames(): Promise<string[]> {
    try {
      this.logger.debug('获取发射场名称集合');

      // 获取匹配的索引，不传入状态参数以获取所有索引
      const indices = await this.getLaunchIndices();
      this.logger.debug(`使用索引: ${indices.join(', ')}`);

      // 使用聚合查询获取所有不同的发射场名称
      const response = await this.elasticsearchService.search({
        index: indices,
        size: 0, // 不需要返回文档，只需要聚合结果
        body: {
          aggs: {
            site_names: {
              terms: {
                field: 'site_name.keyword',
                size: 1000, // 获取足够多的发射场名称
                order: {
                  _key: 'asc' // 按名称字母顺序排序
                }
              }
            }
          }
        }
      });

      // 提取聚合结果
      const buckets = (response.aggregations?.site_names as any)?.buckets || [];

      // 提取发射场名称并过滤掉空值
      const siteNames = buckets
        .map((bucket: any) => bucket.key)
        .filter((name: string) => name && name.trim() !== '' &&
                                 name.toLowerCase() !== 'null' &&
                                 name.toLowerCase() !== 'undefined' &&
                                 name.toLowerCase() !== 'none');

      this.logger.debug(`获取到 ${siteNames.length} 个发射场名称`);
      return siteNames;
    } catch (error) {
      this.logger.error(`获取发射场名称集合失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取服务商名称集合
   * @returns 服务商名称集合
   */
  async getProviders(): Promise<string[]> {
    try {
      this.logger.debug('获取服务商名称集合');

      // 获取匹配的索引，不传入状态参数以获取所有索引
      const indices = await this.getLaunchIndices();
      this.logger.debug(`使用索引: ${indices.join(', ')}`);

      // 使用聚合查询获取所有不同的服务商名称
      const response = await this.elasticsearchService.search({
        index: indices,
        size: 0, // 不需要返回文档，只需要聚合结果
        body: {
          aggs: {
            providers: {
              terms: {
                field: 'provider.keyword',
                size: 1000, // 获取足够多的服务商名称
                order: {
                  _key: 'asc' // 按名称字母顺序排序
                }
              }
            }
          }
        }
      });

      // 提取聚合结果
      const buckets = (response.aggregations?.providers as any)?.buckets || [];

      // 提取服务商名称并过滤掉空值
      const providers = buckets
        .map((bucket: any) => bucket.key)
        .filter((name: string) => name && name.trim() !== '' &&
                                 name.toLowerCase() !== 'null' &&
                                 name.toLowerCase() !== 'undefined' &&
                                 name.toLowerCase() !== 'none');

      this.logger.debug(`获取到 ${providers.length} 个服务商名称`);
      return providers;
    } catch (error) {
      this.logger.error(`获取服务商名称集合失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 将北京时间转换为UTC时间
   * @param beijingDateStr 北京时间日期字符串（格式：YYYY-MM-DD）
   * @param isEndOfDay 是否设置为当天的结束时间（23:59:59）
   * @returns UTC时间日期字符串
   */
  private convertBeijingToUTC(beijingDateStr: string, isEndOfDay: boolean = false): string {
    try {
      // 解析北京时间日期字符串
      const [year, month, day] = beijingDateStr.split('-').map(Number);

      // 创建北京时间的Date对象
      // 如果是结束日期，设置为当天的23:59:59，否则设置为00:00:00
      const beijingDate = new Date(year, month - 1, day, isEndOfDay ? 23 : 0, isEndOfDay ? 59 : 0, isEndOfDay ? 59 : 0);

      // 北京时间比UTC时间快8小时，所以需要减去8小时
      const utcTimestamp = beijingDate.getTime() - 8 * 60 * 60 * 1000;
      const utcDate = new Date(utcTimestamp);

      // 格式化为ISO字符串并截取日期部分
      const utcDateStr = utcDate.toISOString();

      return utcDateStr;
    } catch (error) {
      this.logger.error(`时间转换失败: ${error.message}`, error.stack);
      // 如果转换失败，返回原始日期字符串
      return beijingDateStr;
    }
  }

  /**
   * 将用户友好的状态值映射到数据库中的状态值
   * @param status 用户友好的状态值
   * @returns 数据库中对应的状态值数组
   */
  private mapStatusToDbValues(status: LaunchStatus): string[] {
    switch (status) {
      case LaunchStatus.UPCOMING:
        return [
          'To Be Confirmed',
          'Go for Launch',
          'To Be Determined',
          'On Hold'
        ];
      case LaunchStatus.IN_FLIGHT:
        return [
          'Launch in Flight'
        ];
      case LaunchStatus.SUCCESS:
        return [
          'Launch Successful',
          'Payload Deployed',
          '1', // Success
        ];
      case LaunchStatus.FAILURE:
        return [
          'Launch Failure',
          'Launch was a Partial Failure',
          '0', // Failure
          '2', // Partial Failure
          '3', // In-Flight Abort (Crewed)
          '-1' // Not Set
        ];
      case LaunchStatus.HISTORY:
        // 历史发射 = 发射成功 + 发射失败
        return [
          // 发射成功
          'Launch Successful',
          'Payload Deployed',
          '1', // Success
          // 发射失败
          'Launch Failure',
          'Launch was a Partial Failure',
          '0', // Failure
          '2', // Partial Failure
          '3', // In-Flight Abort (Crewed)
          '-1' // Not Set
        ];
      default:
        return [];
    }
  }

  /**
   * 通过COSPAR ID查询发射信息
   * @param query COSPAR ID查询参数
   * @returns 发射信息
   */
  async searchLaunchByCosparId(query: LaunchCosparQueryDto): Promise<any> {
    try {
      this.logger.debug(`通过COSPAR ID查询发射信息，参数: ${JSON.stringify(query)}`);

      // 执行查询
      const response = await this.elasticsearchService.search({
        index: this.gunterIndex,
        body: {
          query: {
            term: {
              "cospar_launch": query.cospar_launch
            }
          }
        }
      });

      // 处理结果
      const hits = response.hits?.hits || [];
      const total = typeof response.hits.total === 'number'
        ? response.hits.total
        : (response.hits.total as any).value || 0;

      const documents = hits.map(hit => {
        const source = hit._source || {};
        return {
          _id: hit._id,
          _index: hit._index,
          ...source
        };
      });

      return {
        total,
        results: documents
      };
    } catch (error) {
      this.logger.error(`通过COSPAR ID查询发射信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取wiki发射场名称集合
   * @returns wiki发射场名称集合
   */
  async getWikiSiteNames(): Promise<string[]> {
    try {
      this.logger.debug('获取wiki发射场名称集合');

      // 使用聚合查询获取所有不同的发射场名称
      const response = await this.elasticsearchService.search({
        index: this.wikiLaunchSitesIndex,
        size: 0, // 不需要返回文档，只需要聚合结果
        body: {
          aggs: {
            site_names: {
              terms: {
                field: 'site_name.keyword',
                size: 1000, // 获取足够多的发射场名称
                order: {
                  _key: 'asc' // 按名称字母顺序排序
                }
              }
            }
          }
        }
      });

      // 提取聚合结果
      const buckets = (response.aggregations?.site_names as any)?.buckets || [];

      // 提取发射场名称并过滤掉空值
      const siteNames = buckets
        .map((bucket: any) => bucket.key)
        .filter((name: string) => name && name.trim() !== '' &&
                                 name.toLowerCase() !== 'null' &&
                                 name.toLowerCase() !== 'undefined' &&
                                 name.toLowerCase() !== 'none');

      this.logger.debug(`获取到 ${siteNames.length} 个wiki发射场名称`);
      return siteNames;
    } catch (error) {
      this.logger.error(`获取wiki发射场名称集合失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取所有发射场信息（包含中文名称、位置和ES文档ID）
   * @returns 所有发射场的信息数组，包含中文名称、位置和ES文档ID
   */
  async getAllLaunchSitesInfo(): Promise<Array<{ english_name: string; chinese_name: string; location: string; _id: string }>> {
    try {
      this.logger.debug('获取所有发射场信息');

      // 查询所有发射场文档，只返回site_name和location字段
      const response = await this.elasticsearchService.search({
        index: this.wikiLaunchSitesIndex,
        size: 10000, // 获取所有发射场信息
        body: {
          query: {
            match_all: {} // 匹配所有文档
          },
          _source: ['site_name', 'location'] // 只返回这两个字段
        }
      });

      const hits = response.hits.hits;
      this.logger.debug(`从ES获取到 ${hits.length} 条发射场信息`);

      // 处理结果，返回英文名称和对应的中文名称
      const launchSitesInfo: Array<{ english_name: string; chinese_name: string; location: string; _id: string }> = [];
      
      hits.forEach((hit: any) => {
        const source = hit._source;
        const documentId = hit._id; // 获取ES文档的_id字段
        
        if (!source || !source.site_name) {
          return; // 跳过没有site_name的记录
        }

        // site_name可能是字符串或数组，需要分别处理
        const siteNames = Array.isArray(source.site_name) ? source.site_name : [source.site_name];
        
        // 为每个site_name创建一个记录
        siteNames.forEach((siteName: any) => {
          if (siteName && typeof siteName === 'string' && siteName.trim() !== '') {
            const englishName = siteName.trim();
            // 将英文发射场名称转换为中文名称
            const chineseName = getLaunchSiteChineseName(englishName);
            
            // 检查是否已经存在相同的英文名称，避免重复
            const existingEntry = launchSitesInfo.find(item => item.english_name === englishName);
            if (!existingEntry) {
              launchSitesInfo.push({
                english_name: englishName,
                chinese_name: chineseName,
                location: source.location || '', // 如果没有location字段，返回空字符串
                _id: documentId // 添加ES文档ID
              });
            }
          }
        });
      });

      this.logger.debug(`处理后得到 ${launchSitesInfo.length} 条有效发射场信息`);
      return launchSitesInfo;
    } catch (error) {
      this.logger.error(`获取所有发射场信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 根据用户输入的名称或代码获取所有可能的发射场名称表示形式
   * 这个方法会找出发射场的代码、英文名、中文名和所有别名
   * @param nameOrCode 用户输入的发射场名称或代码
   * @returns 该发射场的所有可能表示形式数组，如果未找到则返回原始输入值的数组
   */
  private getAllLaunchSiteIdentifiers(nameOrCode: string): string[] {
    if (!nameOrCode || typeof nameOrCode !== 'string') {
      return [nameOrCode]; // 返回原始输入，以便ES查询可以正常进行
    }

    const normalizedInput = nameOrCode.toLowerCase().trim();
    
    // 尝试查找匹配的发射场
    for (const site of launchSites) {
      // 检查是否匹配代码、英文名、中文名或别名中的任何一个
      if (
        site.code.toLowerCase() === normalizedInput ||
        site.englishName.toLowerCase() === normalizedInput ||
        site.chineseName === nameOrCode.trim() ||
        site.aliases?.some(alias => alias.toLowerCase() === normalizedInput)
      ) {
        // 找到匹配的发射场，收集所有可能的标识符
        const identifiers: string[] = [
          site.code,              // 代码
          site.englishName,       // 英文名
          site.chineseName        // 中文名
        ];
        
        // 添加所有别名
        if (site.aliases && site.aliases.length > 0) {
          identifiers.push(...site.aliases);
        }
        
        this.logger.debug(`发射场 "${nameOrCode}" 解析为以下可能的标识符: ${identifiers.join(', ')}`);
        return identifiers.filter(id => id && id.trim() !== ''); // 过滤掉空值
      }
    }
    
    // 如果没有找到匹配的发射场，返回原始输入
    this.logger.warn(`无法解析发射场名称或代码: "${nameOrCode}". 将使用原始输入进行查询.`);
    return [nameOrCode];
  }

  /**
   * 查询发射场wiki信息
   * @param query 查询参数
   * @returns 匹配的发射场信息
   */
  async searchLaunchSiteWiki(query: LaunchSiteWikiQueryDto): Promise<any> {
    try {
      this.logger.debug(`查询发射场wiki信息: ${JSON.stringify(query)}`);

      // 验证查询参数
      if (!query.siteName && !query._id) {
        this.logger.warn('查询参数无效：siteName和_id至少需要提供一个');
        return [];
      }

      let esQuery: any;

      // 如果提供了_id，优先使用_id进行精确查询
      if (query._id) {
        this.logger.debug(`使用ES文档ID进行精确查询: ${query._id}`);
        
        try {
          const response = await this.elasticsearchService.get({
            index: this.wikiLaunchSitesIndex,
            id: query._id
          });

          if (response.found) {
            this.logger.debug(`通过_id查询到发射场wiki信息`);
            return [{ ...(response._source as any), _id: response._id }];
          } else {
            this.logger.debug(`未找到_id为 ${query._id} 的发射场信息`);
            return [];
          }
        } catch (error: any) {
          if (error.statusCode === 404) {
            this.logger.debug(`_id ${query._id} 对应的文档不存在`);
            return [];
          }
          throw error;
        }
      }

      // 如果没有提供_id，使用siteName进行查询
      if (query.siteName) {
        // 根据输入的发射场名称查找对应的发射场信息
        const launchSite = findLaunchSiteByName(query.siteName);
        if (!launchSite) {
          this.logger.debug(`未找到匹配的发射场信息: ${query.siteName}`);
          return [];
        }

        // 获取该发射场的所有可能名称
        const siteIdentifiers = [
          launchSite.code,
          launchSite.englishName,
          launchSite.chineseName,
          ...(launchSite.aliases || [])
        ];

        this.logger.debug(`发射场匹配标识符: ${siteIdentifiers.join(', ')}`);

        // 构建ES查询
        const response = await this.elasticsearchService.search({
          index: this.wikiLaunchSitesIndex,
          body: {
            query: {
              bool: {
                should: siteIdentifiers.map(name => ({
                  term: {
                    "site_name.keyword": name
                  }
                })),
                minimum_should_match: 1
              }
            }
          }
        });

        const hits = response.hits.hits.map((hit: any) => ({ ...hit._source, _id: hit._id }));
        this.logger.debug(`查询到 ${hits.length} 条发射场wiki信息`);
        return hits;
      }

      return [];
    } catch (error) {
      this.logger.error(`查询发射场wiki信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 通过关键词查询发射服务商信息
   * @param keyword 查询关键词
   * @returns 匹配的服务商信息
   */
  async searchServiceProviderByKeyword(keyword: string): Promise<any> {
    try {
      this.logger.debug(`通过关键词查询发射服务商信息: ${keyword}`);
      
      // 创建查询条件，精确匹配多个字段中的任意一个
      const query = {
        bool: {
          should: [
            { term: { "code.keyword": keyword } },
            { term: { "ucode.keyword": keyword } },
            { term: { "shortname.keyword": keyword } },
            { term: { "name.keyword": keyword } },
            { term: { "short_ename.keyword": keyword } },
            { term: { "ename.keyword": keyword } },
            { term: { "uname.keyword": keyword } }
          ],
          minimum_should_match: 1 // 至少匹配一个条件
        }
      };
      
      // 执行查询
      const response = await this.elasticsearchService.search({
        index: this.serviceProviderIndex,
        body: {
          query,
          size: 10 // 限制返回结果数量
        }
      });
      
      // 处理结果
      const hits = response.hits?.hits || [];
      const total = typeof response.hits.total === 'number'
        ? response.hits.total
        : (response.hits.total as any).value || 0;
      
      const providers = hits.map(hit => ({
        _id: hit._id,
        ...(hit._source as object)
      }));
      
      this.logger.debug(`查询到 ${providers.length} 个匹配的发射服务商`);
      return {
        total,
        results: providers
      };
    } catch (error) {
      this.logger.error(`查询发射服务商信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}
