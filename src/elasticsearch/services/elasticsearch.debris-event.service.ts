import { Injectable, Logger, InternalServerErrorException } from '@nestjs/common';
import { ElasticsearchService as NestElasticsearchService } from '@nestjs/elasticsearch';
import { ElasticsearchBaseService } from './elasticsearch.base.service';
import { DebrisEventQueryDto } from '../dto/debris-event-query.dto';
import { SearchResponse, SearchTotalHits } from '@elastic/elasticsearch/lib/api/types';
import { EventDocument, DebrisEventSearchResponse } from '../types/debris-event.types';

/**
 * 碎片事件查询服务
 * 提供碎片事件数据的查询和聚合功能
 */
@Injectable()
export class ElasticsearchDebrisEventService extends ElasticsearchBaseService {
  protected readonly logger = new Logger(ElasticsearchDebrisEventService.name);
  private readonly debrisEventIndex = 'event_discos';

  constructor(protected readonly elasticsearchService: NestElasticsearchService) {
    super(elasticsearchService);
  }

  /**
   * 查询碎片事件
   * @param queryDto 查询条件
   * @returns 碎片事件列表
   */
  async searchDebrisEvents(queryDto: DebrisEventQueryDto): Promise<DebrisEventSearchResponse> {
    try {
      this.logger.debug(`搜索碎片事件，参数: ${JSON.stringify(queryDto)}`);
      
      const { _id, piece, start_date, end_date, keyword } = queryDto;
      
      // 构建查询条件
      const must: any[] = [];
      
      // ID精确匹配
      if (_id) {
        must.push({ term: { _id } });
      }
      
      // 碎片标识精确匹配
      if (piece) {
        must.push({ 
          term: { 
            "piece.keyword": piece 
          }
        });
      }
      
      // 时间范围查询
      if (start_date || end_date) {
        const range: any = { sdate: {} };
        if (start_date) {
          range.sdate.gte = start_date;
        }
        if (end_date) {
          range.sdate.lte = end_date;
        }
        must.push({ range });
      }
      
      // 关键词全文搜索
      if (keyword) {
        must.push({
          multi_match: {
            query: keyword,
            fields: ['*'],
            type: 'phrase_prefix'
          }
        });
      }
      
      // 构建查询体
      const query = {
        bool: {
          must: must.length > 0 ? must : [{ match_all: {} }]
        }
      };

      this.logger.debug(`碎片事件查询条件: ${JSON.stringify(query)}`);
      
      // 并行查询两个索引
      const [discosResults, jonathanResults] = await Promise.all([
        this.elasticsearchService.search<EventDocument>({
          index: 'event_discos',
          query
        }),
        this.elasticsearchService.search<EventDocument>({
          index: 'event_jonathan',
          query
        })
      ]);

      // 计算置信度的函数
      const calculateConfidence = (hit: any, queryDto: DebrisEventQueryDto): number => {
        let confidence = 0;
        const source = hit._source as EventDocument;

        // 碎片标识匹配得分（权重：0.4）
        if (queryDto.piece && source.piece === queryDto.piece) {
          confidence += 0.4;
        }

        // 时间匹配得分（权重：0.3）
        if (queryDto.start_date || queryDto.end_date) {
          const eventDate = new Date(source.sdate).getTime();
          const startDate = queryDto.start_date ? new Date(queryDto.start_date).getTime() : 0;
          const endDate = queryDto.end_date ? new Date(queryDto.end_date).getTime() : Date.now();
          
          if (eventDate >= startDate && eventDate <= endDate) {
            confidence += 0.3;
          }
        } else {
          // 如果没有指定时间范围，默认给一半时间匹配分数
          confidence += 0.15;
        }

        // 关键词匹配得分（权重：0.3）
        if (queryDto.keyword) {
          const keywordLower = queryDto.keyword.toLowerCase();
          // 使用类型安全的方式检查字段
          const fieldsToCheck: (keyof EventDocument)[] = ['name', 'event_type', 'description', 'object_class'];
          const availableFields = fieldsToCheck.filter(field => 
            field in source && typeof source[field] === 'string'
          );
          
          let keywordMatchCount = 0;
          for (const field of availableFields) {
            const value = source[field];
            if (typeof value === 'string' && value.toLowerCase().includes(keywordLower)) {
              keywordMatchCount++;
            }
          }
          
          if (keywordMatchCount > 0) {
            confidence += (0.3 * keywordMatchCount / availableFields.length);
          }
        } else {
          // 如果没有指定关键词，默认给一半关键词匹配分数
          confidence += 0.15;
        }

        // 添加基础分数（0.1）确保有最小置信度
        confidence += 0.1;

        // 确保置信度不超过1
        return Math.min(confidence, 1);
      };

      // 合并结果并添加来源标记和置信度
      const hits = [
        ...(discosResults.hits?.hits || []).map(hit => ({
          _id: hit._id,
          _source: hit._source,
          source: 'discos',
          confidence: calculateConfidence(hit, queryDto)
        })),
        ...(jonathanResults.hits?.hits || []).map(hit => ({
          _id: hit._id,
          _source: hit._source,
          source: 'jonathan',
          confidence: calculateConfidence(hit, queryDto)
        }))
      ];

      // 计算总数
      const total = 
        (discosResults.hits?.total 
          ? (typeof discosResults.hits.total === 'number' 
            ? discosResults.hits.total 
            : discosResults.hits.total.value || 0)
          : 0) +
        (jonathanResults.hits?.total
          ? (typeof jonathanResults.hits.total === 'number'
            ? jonathanResults.hits.total
            : jonathanResults.hits.total.value || 0)
          : 0);

      // 按置信度降序排序
      hits.sort((a, b) => b.confidence - a.confidence);

      // 格式化返回结果
      return {
        total,
        hits: hits.map(hit => {
          const source = hit._source as EventDocument;
          return {
            _id: hit._id || '',  // 确保_id不为undefined
            source: hit.source,
            confidence: hit.confidence,
            piece: source.piece,
            sdate: source.sdate,
            event_type: source.event_type,
            name: source.name,
            cataloguedFragments: source.cataloguedFragments,
            object_class: source.object_class
          };
        })
      };
    } catch (error) {
      this.logger.error(`搜索碎片事件失败: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`搜索碎片事件失败: ${error.message}`);
    }
  }

  /**
   * 获取碎片事件详情
   * @param id 碎片事件ID
   * @returns 碎片事件详情
   */
  async getDebrisEventById(id: string): Promise<any> {
    try {
      const result = await this.elasticsearchService.search({
        index: this.debrisEventIndex,
        body: {
          query: {
            term: {
              _id: id,
            },
          },
        },
      });
      
      if (result.hits.hits.length === 0) {
        return null;
      }
      
      const hit = result.hits.hits[0];
      const source = hit._source as Record<string, any>;
      return {
        ...source,
        _id: hit._id,
      };
    } catch (error) {
      this.logger.error(`获取碎片事件详情失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取碎片事件统计信息
   * @returns 碎片事件统计信息
   */
  async getDebrisEventStats(): Promise<any> {
    try {
      const result = await this.elasticsearchService.search({
        index: this.debrisEventIndex,
        body: {
          size: 0,
          aggs: {
            event_types: {
              terms: {
                field: 'event_type.keyword',
                size: 10,
              },
            },
            event_by_year: {
              date_histogram: {
                field: 'event_time',
                calendar_interval: 'year',
                format: 'yyyy',
              },
            },
          },
        },
      });
      
      const eventTypes = result.aggregations && 'event_types' in result.aggregations 
        ? (result.aggregations.event_types as any).buckets || [] 
        : [];
      const eventByYear = result.aggregations && 'event_by_year' in result.aggregations 
        ? (result.aggregations.event_by_year as any).buckets || [] 
        : [];
      
      return {
        event_types: eventTypes.map((bucket: any) => ({
          type: bucket.key,
          count: bucket.doc_count,
        })),
        event_by_year: eventByYear.map((bucket: any) => ({
          year: bucket.key_as_string,
          count: bucket.doc_count,
        })),
      };
    } catch (error) {
      this.logger.error(`获取碎片事件统计信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 构建碎片事件查询条件
   * @param filters 过滤条件
   * @returns 查询条件数组
   * @private
   */
  private buildDebrisEventQueryConditions(filters: any): any[] {
    const must = [];
    
    // 添加事件类型过滤
    if (filters.event_type) {
      must.push({
        match: {
          event_type: filters.event_type,
        },
      });
    }
    
    // 添加时间范围过滤
    if (filters.start_date || filters.end_date) {
      const rangeQuery: any = {};
      
      if (filters.start_date) {
        rangeQuery.gte = this.normalizeDate(filters.start_date);
      }
      
      if (filters.end_date) {
        rangeQuery.lte = this.normalizeDate(filters.end_date);
      }
      
      must.push({
        range: {
          event_time: rangeQuery,
        },
      });
    }
    
    // 添加关键词搜索
    if (filters.keyword) {
      must.push({
        multi_match: {
          query: filters.keyword,
          fields: ['description', 'event_type'],
          type: 'best_fields',
          fuzziness: 'AUTO',
        },
      });
    }
    
    // 添加碎片ID过滤
    if (filters.debris_id) {
      must.push({
        term: {
          debris_ids: filters.debris_id,
        },
      });
    }
    
    return must.length ? must : [{ match_all: {} }];
  }

  /**
   * 标准化日期格式
   * @param date 日期字符串
   * @returns 标准化后的日期字符串
   * @protected
   */
  protected normalizeDate(date: string): string | null {
    if (!date) return null;
    
    try {
      const dateObj = new Date(date);
      return dateObj.toISOString();
    } catch (error) {
      this.logger.warn(`日期格式化失败: ${date}`);
      return date;
    }
  }
} 