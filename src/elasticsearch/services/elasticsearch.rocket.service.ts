import { Injectable, Logger } from '@nestjs/common';
import { ElasticsearchService } from '@nestjs/elasticsearch';
import { ElasticsearchBaseService } from './elasticsearch.base.service';
import { RocketQueryDto } from '../dto/rocket-query.dto';

/**
 * Elasticsearch火箭信息服务
 */
@Injectable()
export class ElasticsearchRocketService extends ElasticsearchBaseService {
  protected readonly logger = new Logger(ElasticsearchRocketService.name);
  private readonly rocketIndices = ['veh_discos', 'veh_jonathan'];

  constructor(protected readonly elasticsearchService: ElasticsearchService) {
    super(elasticsearchService);
  }

  /**
   * 获取火箭信息索引
   * @returns 火箭信息索引列表
   */
  private async getRocketIndices(): Promise<string[]> {
    try {
      // 获取所有索引
      const response = await this.elasticsearchService.cat.indices({
        format: 'json'
      });

      // 过滤出匹配的索引
      const matchedIndices = response
        .filter(index => index.index && this.rocketIndices.includes(index.index))
        .map(index => index.index as string)
        .filter(index => index !== undefined);

      this.logger.debug(`找到 ${matchedIndices.length} 个匹配的火箭信息索引: ${matchedIndices.join(', ')}`);
      return matchedIndices;
    } catch (error: any) {
      this.logger.error(`获取火箭信息索引失败: ${error.message}`, error.stack);
      // 如果出错，返回默认索引
      return this.rocketIndices;
    }
  }

  /**
   * 搜索火箭信息
   * @param query 火箭信息查询参数
   * @returns 火箭信息搜索结果
   */
  async searchRocketInfo(query: RocketQueryDto): Promise<any> {
    try {
      this.logger.debug(`搜索火箭信息，参数: ${JSON.stringify(query)}`);

      const { page = 1, limit = 10, rocketName } = query;
      const from = (page - 1) * limit;

      // 构建查询条件
      const must: any[] = [];

      // 火箭型号查询（不区分大小写的精确匹配）
      if (rocketName) {
        // 只进行精确匹配查询
        must.push({
          bool: {
            should: [
              {
                term: {
                  "rocket_name.keyword": {
                    value: rocketName,
                    case_insensitive: true
                  }
                }
              },
              {
                term: {
                  "lv_variant.keyword": {
                    value: rocketName,
                    case_insensitive: true
                  }
                }
              }
            ],
            minimum_should_match: 1
          }
        });

        // 添加日志以便调试
        this.logger.debug(`火箭型号查询条件: ${JSON.stringify(must)}`);
      }

      // 获取匹配的索引
      const indices = await this.getRocketIndices();
      this.logger.debug(`使用索引: ${indices.join(', ')}`);

      // 如果没有查询条件，则返回所有结果
      const finalQuery = must.length > 0
        ? { bool: { must } }
        : { match_all: {} };

      // 构建完整的查询请求
      const searchRequest = {
        index: indices,
        track_total_hits: true, // 确保返回准确的总记录数
        body: {
          query: finalQuery,
          from,
          size: limit
        }
      };

      // 记录完整的查询请求，便于调试
      this.logger.debug(`完整的查询请求: ${JSON.stringify(searchRequest)}`);

      // 执行查询
      const response = await this.elasticsearchService.search(searchRequest);

      // 处理结果
      const hits = response.hits?.hits || [];
      const total = typeof response.hits.total === 'number'
        ? response.hits.total
        : (response.hits.total as any).value || 0;

      // 记录查询结果，便于调试
      this.logger.debug(`查询结果总数: ${total}`);
      if (hits.length > 0) {
        this.logger.debug(`第一个结果: ${JSON.stringify(hits[0])}`);
      } else {
        this.logger.debug('没有找到匹配的结果');
      }

      // 提取所有非空字段
      const documents = hits.map(hit => {
        const source = hit._source || {};
        const nonEmptyFields: Record<string, any> = {};

        // 遍历所有字段，只保留非空字段
        Object.entries(source).forEach(([key, value]) => {
          if (value !== null && value !== undefined && value !== '') {
            nonEmptyFields[key] = value;
          }
        });

        return {
          _id: hit._id,
          _index: hit._index,
          ...nonEmptyFields
        };
      });

      return {
        total,
        page,
        limit,
        hits: documents
      };
    } catch (error: any) {
      this.logger.error(`搜索火箭信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}
