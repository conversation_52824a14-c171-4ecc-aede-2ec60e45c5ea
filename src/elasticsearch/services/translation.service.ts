import { Injectable, Logger } from '@nestjs/common';
import OpenAI from 'openai';
import { 
  TranslationConfig, 
  ThemeExtractionConfig, 
  getTranslationConfig, 
  getThemeExtractionConfig,
  mergeConfig,
  validateLLMConfig
} from '../../../config/llm.config';

/**
 * 翻译服务类
 * 负责与Qwen大模型API交互，进行文本翻译和主题提取
 * 支持可配置的模型选择，默认使用qwen-turbo模型
 */
@Injectable()
export class TranslationService {
  private readonly logger = new Logger(TranslationService.name);
  private openai: OpenAI;
  private translationCache = new Map<string, string>();
  private apiCallCount = 0;
  private apiTotalTime = 0;

  // 配置属性
  private translationConfig: TranslationConfig;
  private themeExtractionConfig: ThemeExtractionConfig;

  // 并发控制
  private activeRequests = 0;
  private readonly requestQueue: Array<() => Promise<void>> = [];

  // 文本分段配置
  private readonly paragraphSeparators = ["\n\n", "\n", ". ", "! ", "? "]; // 段落分隔符

  // 重试配置
  private consecutiveFailures = 0; // 当前连续失败次数（仅用于日志记录）
  
  // 失败统计
  private failureStats = {
    contentFilter: 0,
    timeout: 0,
    rateLimit: 0,
    networkError: 0,
    other: 0,
    totalAttempts: 0,
    successfulProcessing: 0
  };

  // 敏感词及替换映射，用于在发送给模型前降低被拒绝概率
  private readonly sensitiveReplacements: Array<{ pattern: RegExp; replacement: string }> = [
    // 英文敏感词替换为较温和表达
    { pattern: /\bterrorist(s)?\b/gi, replacement: 'extremist $1' },
    { pattern: /\bterrorism\b/gi, replacement: 'extremism' },
    { pattern: /\bexplosive(s)?\b/gi, replacement: 'explosive material' },
    { pattern: /\bbomb(s)?\b/gi, replacement: 'explosive device$1' },
    { pattern: /\bnuclear weapon(s)?\b/gi, replacement: 'special weapon$1' },
    { pattern: /\bmissile(s)?\b/gi, replacement: 'projectile$1' },
    // 中文敏感词
    { pattern: /炸弹/g, replacement: '爆炸装置' },
    { pattern: /核武器/g, replacement: '特殊武器' },
    { pattern: /恐怖分子/g, replacement: '极端分子' },
    { pattern: /恐怖主义/g, replacement: '极端主义' }
  ];

  constructor() {
    this.translationConfig = this.loadTranslationConfig();
    this.themeExtractionConfig = this.loadThemeExtractionConfig();
    
    this.openai = new OpenAI({
      apiKey: this.translationConfig.apiKey,
      baseURL: this.translationConfig.baseURL
    });

    this.logger.log(`翻译服务已初始化 - 翻译模型: ${this.translationConfig.model}, 主题提取模型: ${this.themeExtractionConfig.model}`);
  }

  /**
   * 智能选择翻译配置
   * 根据文档长度自动选择最适合的配置
   * @param textLength 文本长度
   * @returns 配置模式
   */
  private selectOptimalConfig(textLength: number): 'default' | 'high_quality' | 'fast' | 'ultra_long' {
    if (textLength > 150000) {
      // 超过150K字符，使用ultra_long模式并提醒需要特殊处理
      this.logger.warn(`检测到极长文档 (${textLength}字符)，使用ultra_long模式，可能需要较长处理时间`);
      return 'ultra_long';
    } else if (textLength > 100000) {
      // 100K-150K字符，使用ultra_long模式并启用特殊分段策略
      this.logger.warn(`检测到超长文档 (${textLength}字符)，自动使用ultra_long模式`);
      return 'ultra_long';
    } else if (textLength > 40000) {
      // 40K-100K字符，使用ultra_long模式
      this.logger.debug(`检测到长文档 (${textLength}字符)，使用ultra_long模式`);
      return 'ultra_long';
    } else if (textLength > 25000) {
      // 25K-40K字符，使用high_quality模式
      this.logger.debug(`检测到中等长度文档 (${textLength}字符)，使用high_quality模式`);
      return 'high_quality';
    } else if (textLength < 10000) {
      // 小于10K字符，使用fast模式
      return 'fast';
    } else {
      // 10K-25K字符，使用默认模式
      return 'default';
    }
  }

  /**
   * 临时切换到最适合的配置
   * @param textLength 文本长度
   * @returns 原始配置，用于恢复
   */
  private switchToOptimalConfig(textLength: number): TranslationConfig {
    const originalConfig = { ...this.translationConfig };
    const optimalMode = this.selectOptimalConfig(textLength);
    
    if (optimalMode !== 'default') {
      const newConfig = getTranslationConfig(optimalMode);
      this.updateTranslationConfig(newConfig);
      this.logger.log(`已临时切换到${optimalMode}模式，原模型: ${originalConfig.model} -> 新模型: ${newConfig.model}`);
    }
    
    return originalConfig;
  }

  /**
   * 恢复原始配置
   * @param originalConfig 原始配置
   */
  private restoreOriginalConfig(originalConfig: TranslationConfig): void {
    this.updateTranslationConfig(originalConfig);
    this.logger.debug(`已恢复原始配置，模型: ${originalConfig.model}`);
  }

  /**
   * 加载翻译配置
   * @returns 翻译配置
   */
  private loadTranslationConfig(): TranslationConfig {
    const mode = (process.env.TRANSLATION_MODE as 'default' | 'high_quality' | 'fast') || 'default';
    let config = getTranslationConfig(mode);

    // 从环境变量覆盖配置
    const envOverrides: Partial<TranslationConfig> = {};
    
    if (process.env.TRANSLATION_MODEL) {
      envOverrides.model = process.env.TRANSLATION_MODEL;
    }
    
    if (process.env.QWEN_API_KEY) {
      envOverrides.apiKey = process.env.QWEN_API_KEY;
    }
    
    if (process.env.QWEN_BASE_URL) {
      envOverrides.baseURL = process.env.QWEN_BASE_URL;
    }
    
    if (process.env.TRANSLATION_MAX_CONCURRENT) {
      envOverrides.maxConcurrentRequests = parseInt(process.env.TRANSLATION_MAX_CONCURRENT);
    }
    
    if (process.env.TRANSLATION_TIMEOUT) {
      envOverrides.timeout = parseInt(process.env.TRANSLATION_TIMEOUT);
    }
    
    if (process.env.TRANSLATION_MAX_RETRIES) {
      envOverrides.maxRetries = parseInt(process.env.TRANSLATION_MAX_RETRIES);
    }

    config = mergeConfig(config, envOverrides);
    
    this.logger.log(`加载翻译配置 - 模式: ${mode}, 模型: ${config.model}`);
    return config;
  }

  /**
   * 加载主题提取配置
   * @returns 主题提取配置
   */
  private loadThemeExtractionConfig(): ThemeExtractionConfig {
    const mode = (process.env.THEME_EXTRACTION_MODE as 'default' | 'high_quality' | 'fast') || 'default';
    let config = getThemeExtractionConfig(mode);

    // 从环境变量覆盖配置
    const envOverrides: Partial<ThemeExtractionConfig> = {};
    
    if (process.env.THEME_EXTRACTION_MODEL) {
      envOverrides.model = process.env.THEME_EXTRACTION_MODEL;
    }
    
    if (process.env.QWEN_API_KEY) {
      envOverrides.apiKey = process.env.QWEN_API_KEY;
    }
    
    if (process.env.QWEN_BASE_URL) {
      envOverrides.baseURL = process.env.QWEN_BASE_URL;
    }
    
    if (process.env.THEME_EXTRACTION_MAX_CONCURRENT) {
      envOverrides.maxConcurrentRequests = parseInt(process.env.THEME_EXTRACTION_MAX_CONCURRENT);
    }
    
    if (process.env.THEME_EXTRACTION_TIMEOUT) {
      envOverrides.timeout = parseInt(process.env.THEME_EXTRACTION_TIMEOUT);
    }
    
    if (process.env.THEME_EXTRACTION_MAX_RETRIES) {
      envOverrides.maxRetries = parseInt(process.env.THEME_EXTRACTION_MAX_RETRIES);
    }

    config = mergeConfig(config, envOverrides);
    
    this.logger.log(`加载主题提取配置 - 模式: ${mode}, 模型: ${config.model}`);
    return config;
  }

  /**
   * 获取当前翻译配置
   * @returns 翻译配置
   */
  getTranslationConfig(): TranslationConfig {
    return { ...this.translationConfig };
  }

  /**
   * 获取当前主题提取配置
   * @returns 主题提取配置
   */
  getThemeExtractionConfig(): ThemeExtractionConfig {
    return { ...this.themeExtractionConfig };
  }

  /**
   * 动态更新翻译配置
   * @param newConfig 新的配置
   */
  updateTranslationConfig(newConfig: Partial<TranslationConfig>): void {
    this.translationConfig = mergeConfig(this.translationConfig, newConfig);
    validateLLMConfig(this.translationConfig);
    
    // 如果API配置发生变化，重新初始化OpenAI客户端
    if (newConfig.apiKey || newConfig.baseURL) {
      this.openai = new OpenAI({
        apiKey: this.translationConfig.apiKey,
        baseURL: this.translationConfig.baseURL
      });
    }
    
    this.logger.log(`翻译配置已更新 - 模型: ${this.translationConfig.model}`);
  }

  /**
   * 动态更新主题提取配置
   * @param newConfig 新的配置
   */
  updateThemeExtractionConfig(newConfig: Partial<ThemeExtractionConfig>): void {
    this.themeExtractionConfig = mergeConfig(this.themeExtractionConfig, newConfig);
    validateLLMConfig(this.themeExtractionConfig);
    this.logger.log(`主题提取配置已更新 - 模型: ${this.themeExtractionConfig.model}`);
  }

  /**
   * 在发送到大模型前，对文本进行敏感词智能替换，降低拒绝概率
   * @param text 原始文本
   */
  private sanitizeSensitiveWords(text: string): string {
    let sanitized = text;
    for (const { pattern, replacement } of this.sensitiveReplacements) {
      sanitized = sanitized.replace(pattern, replacement);
    }
    return sanitized;
  }

  /**
   * 翻译文本 - 增强版错误处理
   * @param text 待翻译文本
   * @param retryCount 当前重试次数
   * @param isSegment 是否是分段调用，如果是则不再进行分段处理
   * @param forceRetranslate 是否强制重新翻译（绕过缓存）
   * @returns 翻译后的文本
   * @throws 如果熔断器已触发，抛出异常
   */
  async translateText(text: string, retryCount = 0, isSegment = false, forceRetranslate = false): Promise<string> {
    if (!text || text.trim() === '') {
      return '';
    }

    // 检查文本长度，如果超过100K字符则直接跳过翻译
    const ULTRA_LONG_THRESHOLD = 100000; // 100K字符阈值
    
    if (!isSegment && text.length > ULTRA_LONG_THRESHOLD) {
      this.logger.warn(`单个文本过长 (${text.length}字符，超过${ULTRA_LONG_THRESHOLD}字符阈值)，跳过翻译`);
      return ''; // 返回空字符串，表示跳过翻译
    }

    this.failureStats.totalAttempts++;

    // 敏感词处理（只在非分段时处理）
    let inputText = text;
    if (!isSegment) {
      inputText = this.sanitizeSensitiveWords(text);
    }

    // 缓存逻辑：forceRetranslate模式下绕过缓存
    const cacheKey = this.generateCacheKey(inputText);
    if (!forceRetranslate && this.translationCache.has(cacheKey)) {
      this.apiCallCount++;
      this.logger.debug(`命中翻译缓存 - 长度: ${inputText.length}`);
      return this.translationCache.get(cacheKey) || '';
    }

    // 长文本处理
    if (!isSegment && inputText.length > this.translationConfig.maxTextLength) {
      // 对于超长文本，translateLongText内部会自动选择最优配置
      return this.translateLongText(inputText, forceRetranslate);
    }

    // 对于单个文本，也检查是否需要使用更好的配置
    let originalConfig: TranslationConfig | null = null;
    if (!isSegment && inputText.length > 20000) {
      originalConfig = this.switchToOptimalConfig(inputText.length);
    }

    // 并发控制
    return this.executeWithConcurrencyControl(async () => {
      try {
        this.apiCallCount++;
        const startTime = Date.now();

        // 预处理文本
        const processedText = this.preprocessContent(inputText);

        const completion = await this.openai.chat.completions.create({
          model: this.translationConfig.model,
          messages: [
            { role: 'system', content: this.translationConfig.systemPrompt },
            { role: 'user', content: processedText }
          ],
          max_tokens: this.translationConfig.maxTokens,
          temperature: this.translationConfig.temperature
        }, {
          timeout: this.translationConfig.timeout
        });

        const translatedText = completion.choices[0]?.message?.content || '';
        const elapsed = Date.now() - startTime;
        this.apiTotalTime += elapsed;

        this.logger.debug(`翻译API调用完成，耗时: ${elapsed}ms，总调用次数: ${this.apiCallCount}`);

        // 缓存翻译结果
        this.translationCache.set(cacheKey, translatedText);

        // 重置连续失败计数
        this.consecutiveFailures = 0;
        
        // 统计成功处理
        this.failureStats.successfulProcessing++;

        // 恢复原始配置（如果之前切换过）
        if (originalConfig) {
          this.restoreOriginalConfig(originalConfig);
        }

        return translatedText;
      } catch (error: any) {
        // 恢复原始配置（如果之前切换过）
        if (originalConfig) {
          this.restoreOriginalConfig(originalConfig);
        }
        
        // 增加连续失败计数（仅用于日志记录）
        this.consecutiveFailures++;
        
        // 记录详细的错误信息
        this.logger.error(`翻译失败 - 模型: ${this.translationConfig.model}, 重试次数: ${retryCount}, 错误: ${error.message}`);

        // 处理内容不适当错误 - 增强版
        if (error.message.includes('inappropriate content') || 
            error.message.includes('content_filter') || 
            error.message.includes('content policy') ||
            error.message.includes('safety') ||
            error.message.includes('harmful')) {
          
          // 统计内容过滤错误
          this.failureStats.contentFilter++;
          
          this.logger.warn(`检测到内容过滤错误，错误信息: ${error.message}, 当前重试次数=${retryCount}, 最大重试次数=${this.translationConfig.maxRetries}`);

          // 如果重试次数大于等于最大重试次数，直接跳过文档
          if (retryCount >= (this.translationConfig.maxRetries || 2)) {
            this.logger.error(`内容过滤错误超过最大重试次数(${this.translationConfig.maxRetries})，当前重试次数=${retryCount}，跳过该文档`);
            this.logger.error(`原始文本前100字符: ${inputText.substring(0, 100)}...`);
            throw new Error(`SKIP_DOCUMENT:内容过滤错误超过最大重试次数 - ${error.message}`);
          }

          // 使用智能预处理重试
          this.logger.warn(`使用智能预处理重试，重试次数: ${retryCount + 1}`);
          
          // 分析原始文本
          const preprocessingResult = this.intelligentPreprocessing(inputText);
          
          this.logger.debug(`智能预处理结果: 策略=${preprocessingResult.strategy}, 敏感词=${preprocessingResult.analysis.original.sensitiveWords.length}个, 风险等级=${preprocessingResult.analysis.original.riskLevel}, 敏感类别=[${preprocessingResult.analysis.original.categories.join(', ')}]`);
          
          // 记录预处理前后的变化
          const originalLength = inputText.length;
          const processedLength = preprocessingResult.processedText.length;
          this.logger.debug(`文本预处理: 原长度=${originalLength}, 处理后长度=${processedLength}, 压缩率=${Math.round((1 - processedLength/originalLength) * 100)}%`);
          
          // 如果需要人工审核，记录详细信息
          if (preprocessingResult.needsManualReview) {
            this.logger.warn(`文档需要人工审核: 敏感类别=${preprocessingResult.analysis.original.categories.join(', ')}, 敏感词数量=${preprocessingResult.analysis.original.sensitiveWords.length}`);
            this.logger.debug(`检测到的敏感词: ${preprocessingResult.analysis.original.sensitiveWords.slice(0, 10).join(', ')}${preprocessingResult.analysis.original.sensitiveWords.length > 10 ? '...' : ''}`);
          }

          return this.translateText(preprocessingResult.processedText, retryCount + 1, isSegment, forceRetranslate);
        }

        // 处理超时错误
        if (error.message.includes('timed out') || error.message.includes('timeout')) {
          // 统计超时错误
          this.failureStats.timeout++;
          
          if (retryCount < (this.translationConfig.maxRetries || 2)) {
            // 指数退避重试
            const waitTime = Math.pow(2, retryCount) * (this.translationConfig.retryDelay || 1000);
            this.logger.warn(`翻译请求超时，${waitTime}ms后重试，重试次数: ${retryCount + 1} - 模型: ${this.translationConfig.model}`);

            await new Promise(resolve => setTimeout(resolve, waitTime));
            return this.translateText(inputText, retryCount + 1, isSegment, forceRetranslate);
          } else {
            // 超过最大重试次数，返回特殊标记以跳过该文档
            this.logger.error(`翻译超时错误超过最大重试次数(${this.translationConfig.maxRetries})，跳过该文档`);
            throw new Error(`SKIP_DOCUMENT:翻译超时错误超过最大重试次数`);
          }
        }

        // 处理API限制错误
        if (error.message.includes('rate limit') || error.message.includes('429')) {
          // 统计API限制错误
          this.failureStats.rateLimit++;
          
          if (retryCount < (this.translationConfig.maxRetries || 2)) {
            // 更长的等待时间
            const waitTime = (retryCount + 1) * 5000; // 5秒、10秒、15秒...
            this.logger.warn(`API限制错误，${waitTime}ms后重试，重试次数: ${retryCount + 1} - 模型: ${this.translationConfig.model}`);

            await new Promise(resolve => setTimeout(resolve, waitTime));
            return this.translateText(inputText, retryCount + 1, isSegment, forceRetranslate);
          } else {
            this.logger.error(`API限制错误超过最大重试次数(${this.translationConfig.maxRetries})，跳过该文档`);
            throw new Error(`SKIP_DOCUMENT:API限制错误超过最大重试次数`);
          }
        }

        // 处理网络错误
        if (error.message.includes('network') || 
            error.message.includes('connection') || 
            error.message.includes('ENOTFOUND') ||
            error.message.includes('ECONNRESET') ||
            error.message.includes('socket')) {
          this.failureStats.networkError++;
          this.logger.error(`网络连接错误 - 模型: ${this.translationConfig.model}: ${error.message}`);
        } else {
          // 其他未分类错误
          this.failureStats.other++;
        }

        // 处理其他错误
        this.logger.error(`翻译失败 - 模型: ${this.translationConfig.model}: ${error.message}`, error.stack);
        throw new Error(`翻译失败: ${error.message}`);
      }
    });
  }

  /**
   * 批量翻译文本
   * @param texts 待翻译文本数组
   * @returns 翻译后的文本数组
   */
  async translateBatch(texts: string[]): Promise<string[]> {
    const results = [];
    for (const text of texts) {
      try {
        const translated = await this.translateText(text);
        results.push(translated);
      } catch (error: any) {
        this.logger.error(`批量翻译出错: ${error.message}`);
        results.push(''); // 翻译失败返回空字符串
      }
    }
    return results;
  }

  /**
   * 生成缓存键
   * @param text 文本
   * @returns 缓存键
   */
  private generateCacheKey(text: string): string {
    // 简单哈希函数用于缓存
    return text.trim().toLowerCase();
  }

  /**
   * 获取API调用统计信息
   */
  getAPIStats() {
    return {
      callCount: this.apiCallCount,
      totalTime: this.apiTotalTime,
      averageTime: this.apiCallCount > 0 ? this.apiTotalTime / this.apiCallCount : 0,
      cacheSize: this.translationCache.size
    };
  }

  /**
   * 获取详细的失败统计信息
   */
  getFailureStats() {
    const totalFailures = this.failureStats.contentFilter + 
                         this.failureStats.timeout + 
                         this.failureStats.rateLimit + 
                         this.failureStats.networkError + 
                         this.failureStats.other;
    
    const successRate = this.failureStats.totalAttempts > 0 ? 
                       (this.failureStats.successfulProcessing / this.failureStats.totalAttempts * 100).toFixed(2) : 
                       '0.00';
    
    return {
      // 基础统计
      totalAttempts: this.failureStats.totalAttempts,
      successfulProcessing: this.failureStats.successfulProcessing,
      totalFailures: totalFailures,
      successRate: parseFloat(successRate),
      
      // 失败分类统计
      failures: {
        contentFilter: {
          count: this.failureStats.contentFilter,
          percentage: totalFailures > 0 ? (this.failureStats.contentFilter / totalFailures * 100).toFixed(2) : '0.00'
        },
        timeout: {
          count: this.failureStats.timeout,
          percentage: totalFailures > 0 ? (this.failureStats.timeout / totalFailures * 100).toFixed(2) : '0.00'
        },
        rateLimit: {
          count: this.failureStats.rateLimit,
          percentage: totalFailures > 0 ? (this.failureStats.rateLimit / totalFailures * 100).toFixed(2) : '0.00'
        },
        networkError: {
          count: this.failureStats.networkError,
          percentage: totalFailures > 0 ? (this.failureStats.networkError / totalFailures * 100).toFixed(2) : '0.00'
        },
        other: {
          count: this.failureStats.other,
          percentage: totalFailures > 0 ? (this.failureStats.other / totalFailures * 100).toFixed(2) : '0.00'
        }
      },
      
      // 性能指标
      consecutiveFailures: this.consecutiveFailures,
      
      // 建议
      recommendations: this.generateRecommendations()
    };
  }

  /**
   * 根据失败统计生成优化建议
   */
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    const stats = this.failureStats;
    const totalFailures = stats.contentFilter + stats.timeout + stats.rateLimit + stats.networkError + stats.other;
    
    if (totalFailures === 0) {
      recommendations.push('系统运行正常，无需特殊优化');
      return recommendations;
    }
    
    // 内容过滤问题
    if (stats.contentFilter > totalFailures * 0.3) {
      recommendations.push('内容过滤失败率较高，建议：');
      recommendations.push('  - 加强敏感内容预处理');
      recommendations.push('  - 检查新闻来源的内容质量');
      recommendations.push('  - 考虑调整内容处理策略');
    }
    
    // 超时问题
    if (stats.timeout > totalFailures * 0.2) {
      recommendations.push('超时问题频发，建议：');
      recommendations.push('  - 增加请求超时时间');
      recommendations.push('  - 减少批次大小');
      recommendations.push('  - 检查网络连接稳定性');
    }
    
    // API限制问题
    if (stats.rateLimit > totalFailures * 0.2) {
      recommendations.push('API限制频繁触发，建议：');
      recommendations.push('  - 减少并发请求数');
      recommendations.push('  - 增加请求间隔时间');
      recommendations.push('  - 考虑升级API套餐');
    }
    
    // 网络问题
    if (stats.networkError > totalFailures * 0.1) {
      recommendations.push('网络连接不稳定，建议：');
      recommendations.push('  - 检查网络环境');
      recommendations.push('  - 增加重试机制');
      recommendations.push('  - 考虑使用备用网络');
    }
    
    return recommendations;
  }

  /**
   * 重置失败统计
   */
  resetFailureStats() {
    this.failureStats = {
      contentFilter: 0,
      timeout: 0,
      rateLimit: 0,
      networkError: 0,
      other: 0,
      totalAttempts: 0,
      successfulProcessing: 0
    };
    this.consecutiveFailures = 0;
    this.logger.log('失败统计已重置');
  }

  /**
   * 清除翻译缓存
   */
  clearCache() {
    const cacheSize = this.translationCache.size;
    this.translationCache.clear();
    this.logger.log(`清除了${cacheSize}条翻译缓存`);
  }

  /**
   * 并发控制执行器
   * @param task 要执行的任务
   * @returns 任务结果
   */
  private async executeWithConcurrencyControl<T>(task: () => Promise<T>): Promise<T> {
    // 如果当前活跃请求数小于最大并发数，直接执行
    if (this.activeRequests < (this.translationConfig.maxConcurrentRequests || 3)) {
      this.activeRequests++;
      try {
        return await task();
      } finally {
        this.activeRequests--;
        // 处理队列中的下一个任务
        this.processNextQueuedTask();
      }
    }

    // 如果并发数已达到上限，将任务加入队列
    return new Promise<T>((resolve, reject) => {
      this.requestQueue.push(async () => {
        try {
          const result = await task();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      this.logger.debug(`当前并发请求数: ${this.activeRequests}, 队列中的请求数: ${this.requestQueue.length}`);
    });
  }

  /**
   * 处理队列中的下一个任务
   */
  private processNextQueuedTask() {
    if (this.requestQueue.length > 0 && this.activeRequests < (this.translationConfig.maxConcurrentRequests || 3)) {
      const nextTask = this.requestQueue.shift();
      if (nextTask) {
        this.activeRequests++;
        nextTask().finally(() => {
          this.activeRequests--;
          this.processNextQueuedTask();
        });
      }
    }
  }

    /**
   * 分段处理长文本 - 增强版，确保最大程度翻译
   * @param text 长文本
   * @param forceRetranslate 是否强制重新翻译（绕过缓存）
   * @returns 翻译后的文本
   */
  private async translateLongText(text: string, forceRetranslate = false): Promise<string> {
    // 检查文本长度，如果超过100K字符则直接返回空字符串
    const ULTRA_LONG_THRESHOLD = 100000; // 100K字符阈值
    
    if (text.length > ULTRA_LONG_THRESHOLD) {
      this.logger.warn(`长文本过长 (${text.length}字符，超过${ULTRA_LONG_THRESHOLD}字符阈值)，跳过翻译`);
      return ''; // 返回空字符串，表示跳过翻译
    }
    
    // 智能配置选择：对于超长文档，自动切换到最适合的配置
    const originalConfig = this.switchToOptimalConfig(text.length);
    
    try {
      // 对于超长文档，使用更保守的分段策略
      const isUltraLong = text.length > 50000; // 调整阈值，50K以上使用保守策略
      
      // 将长文本分成段落，并记录分割信息
      const segmentInfos = this.splitTextIntoSegments(text, isUltraLong);
      this.logger.debug(`长文本分段处理: 原文长度=${text.length}, 共${segmentInfos.length}段, 超长模式=${isUltraLong}`);

    const translatedSegments: string[] = [];
    let successCount = 0;
    let failureCount = 0;

    // 逐段翻译
    for (let i = 0; i < segmentInfos.length; i++) {
      const segmentInfo = segmentInfos[i];
      const segment = segmentInfo.segment;

      if (segment.trim()) {
        try {
          this.logger.debug(`翻译第${i+1}/${segmentInfos.length}段，长度: ${segment.length}, 分割类型: ${segmentInfo.separatorType}`);
          // 传入 isSegment = true，表示这是分段调用，避免无限递归
          const translated = await this.translateText(segment, 0, true, forceRetranslate);

          // 增强的翻译结果质量检查
          if (this.isValidTranslation(translated, segment)) {
            // 翻译成功，添加翻译结果和原有的分隔符
            translatedSegments.push(translated + segmentInfo.separator);
            successCount++;
            this.logger.debug(`段落${i+1}翻译成功，原长度: ${segment.length}, 译文长度: ${translated.length}`);
          } else {
            this.logger.warn(`段落${i+1}翻译质量不佳，尝试重新翻译`);

            // 尝试重新翻译一次
            try {
              const retranslated = await this.translateText(segment, 0, true, true); // 强制重新翻译
              if (this.isValidTranslation(retranslated, segment)) {
                translatedSegments.push(retranslated + segmentInfo.separator);
                successCount++;
                this.logger.debug(`段落${i+1}重新翻译成功`);
              } else {
                this.logger.warn(`段落${i+1}重新翻译仍然失败，尝试细分翻译`);
                const finegrainedResult = await this.attemptFinegrainedTranslation(segment, segmentInfo.separator);
                translatedSegments.push(finegrainedResult);
                failureCount++;
              }
            } catch (retryError) {
              this.logger.warn(`段落${i+1}重新翻译出错: ${retryError.message}，尝试细分翻译`);
              const finegrainedResult = await this.attemptFinegrainedTranslation(segment, segmentInfo.separator);
              translatedSegments.push(finegrainedResult);
              failureCount++;
            }
          }

          // 添加短暂延迟，避免API过载
          if (i < segmentInfos.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 200));
          }
        } catch (error: any) {
          // 如果单段翻译失败，尝试更细粒度的分割翻译
          this.logger.error(`段落${i+1}翻译失败: ${error.message}`);
          
          // 根据错误类型决定处理方式
          if (error.message.includes('SKIP_DOCUMENT')) {
            // 如果是跳过文档的错误，尝试细分翻译
            this.logger.warn(`段落${i+1}内容敏感，尝试细分翻译`);
            const finegrainedResult = await this.attemptFinegrainedTranslation(segment, segmentInfo.separator);
            translatedSegments.push(finegrainedResult);
          } else {
            // 其他错误，也尝试细分翻译
            this.logger.warn(`段落${i+1}翻译失败，尝试细分翻译`);
            const finegrainedResult = await this.attemptFinegrainedTranslation(segment, segmentInfo.separator);
            translatedSegments.push(finegrainedResult);
          }
          failureCount++;
        }
      } else {
        // 空段落，仅保留分隔符
        translatedSegments.push(segmentInfo.separator);
      }
    }

    // 记录翻译统计
    this.logger.log(`长文本翻译完成: 成功${successCount}段, 失败${failureCount}段, 总计${segmentInfos.length}段`);
    
    // 如果失败率过高，记录警告
    const failureRate = failureCount / segmentInfos.length;
    if (failureRate > 0.3) {
      this.logger.warn(`长文本翻译失败率过高: ${(failureRate * 100).toFixed(1)}%, 建议检查内容质量`);
    }

    // 合并翻译结果，保留原有的换行格式
    const result = translatedSegments.join('');
    
    // 最终质量检查
    const originalLength = text.length;
    const translatedLength = result.length;
    const lengthRatio = translatedLength / originalLength;
    
    this.logger.debug(`长文本翻译质量检查: 原文${originalLength}字符 -> 译文${translatedLength}字符, 比例: ${(lengthRatio * 100).toFixed(1)}%`);
    
      // 如果翻译结果明显过短，记录警告
      if (lengthRatio < 0.3 && originalLength > 500) {
        this.logger.warn(`翻译结果可能不完整: 原文${originalLength}字符, 译文${translatedLength}字符, 比例过低`);
      }

      // 恢复原始配置
      this.restoreOriginalConfig(originalConfig);

      return result;
    } catch (error) {
      // 长文本翻译失败，恢复原始配置后重新抛出错误
      this.restoreOriginalConfig(originalConfig);
      throw error;
    }
  }

  /**
   * 尝试对失败的段落进行更细粒度的分割翻译
   * @param segment 失败的段落
   * @param originalSeparator 原始分隔符
   * @returns 翻译结果（部分成功的翻译+保留的原文）
   */
  private async attemptFinegrainedTranslation(segment: string, originalSeparator: string): Promise<string> {
    this.logger.debug(`开始细粒度翻译，段落长度: ${segment.length}`);
    
    // 如果段落太短，直接保留原文
    if (segment.length < 100) {
      this.logger.debug(`段落太短，直接保留原文`);
      return segment + originalSeparator;
    }
    
    // 使用更激进的分割策略，按句子分割
    const sentences = this.splitIntoSentences(segment);
    const translatedParts: string[] = [];
    let successCount = 0;
    let failureCount = 0;
    
    for (let j = 0; j < sentences.length; j++) {
      const sentence = sentences[j].trim();
      
      if (sentence.length < 10) {
        // 太短的句子直接保留
        translatedParts.push(sentence);
        continue;
      }
      
      try {
        // 尝试翻译单个句子，不重试
        const translated = await this.translateText(sentence, 0, true, false);
        if (translated && translated.trim() !== '' && translated !== sentence) {
          translatedParts.push(translated);
          successCount++;
        } else {
          // 翻译结果为空或与原文相同，保留原文
          translatedParts.push(sentence);
          failureCount++;
        }
      } catch (sentenceError: any) {
        // 单句翻译失败，保留原文
        this.logger.debug(`句子翻译失败，保留原文: ${sentence.substring(0, 50)}...`);
        translatedParts.push(sentence);
        failureCount++;
      }
      
      // 添加短暂延迟，避免API过载
      if (j < sentences.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    const result = translatedParts.join(' ') + originalSeparator;
    
    this.logger.debug(`细粒度翻译完成: 成功${successCount}句, 失败${failureCount}句, 总计${sentences.length}句`);
    
    return result;
  }

  /**
   * 将文本分割为句子
   * @param text 要分割的文本
   * @returns 句子数组
   */
  private splitIntoSentences(text: string): string[] {
    // 使用多种句子分隔符
    const sentenceEnders = [
      '. ', '! ', '? ', '。', '！', '？',
      '.\n', '!\n', '?\n', '。\n', '！\n', '？\n'
    ];
    
    let sentences: string[] = [text];
    
    // 逐步按不同分隔符分割
    for (const ender of sentenceEnders) {
      const newSentences: string[] = [];
      for (const sentence of sentences) {
        if (sentence.includes(ender)) {
          const parts = sentence.split(ender);
          for (let i = 0; i < parts.length; i++) {
            if (i < parts.length - 1) {
              // 不是最后一部分，添加分隔符
              newSentences.push(parts[i] + ender.trim());
            } else {
              // 最后一部分，如果不为空则添加
              if (parts[i].trim()) {
                newSentences.push(parts[i]);
              }
            }
          }
        } else {
          newSentences.push(sentence);
        }
      }
      sentences = newSentences.filter(s => s.trim().length > 0);
    }
    
    // 如果句子太多，按长度合并相邻的短句
    if (sentences.length > 20) {
      const mergedSentences: string[] = [];
      let currentGroup = '';
      
      for (const sentence of sentences) {
        if (currentGroup.length + sentence.length < 200) {
          currentGroup += (currentGroup ? ' ' : '') + sentence;
        } else {
          if (currentGroup) {
            mergedSentences.push(currentGroup);
          }
          currentGroup = sentence;
        }
      }
      
      if (currentGroup) {
        mergedSentences.push(currentGroup);
      }
      
      sentences = mergedSentences;
    }
    
    return sentences;
  }

  /**
   * 专门翻译标题的方法，确保标题翻译的准确性
   * @param title 原标题
   * @param forceRetranslate 是否强制重新翻译
   * @returns 翻译后的标题
   */
  private async translateTitleOnly(title: string, forceRetranslate = false): Promise<string> {
    if (!title || title.trim() === '') {
      return '';
    }

    // 缓存逻辑
    const cacheKey = 'title_' + this.generateCacheKey(title);
    if (!forceRetranslate && this.translationCache.has(cacheKey)) {
      this.logger.debug(`命中标题翻译缓存`);
      return this.translationCache.get(cacheKey) || '';
    }

    // 专门的标题翻译提示词
    const titleSystemPrompt = `你是一位专业的翻译专家，专门负责翻译新闻标题。

翻译要求：
1. 只翻译提供的标题文本，不要添加任何额外内容
2. 保持标题的简洁性，通常不超过50个中文字符
3. 如果原标题是问句，翻译后也应该是问句
4. 不要在翻译中包含时间、日期、具体数字等详细信息，除非它们是标题的核心部分
5. 直接输出翻译结果，不要包含任何解释或格式化

示例：
原文：Rocket launch today: Is there a rocket launch and what time?
译文：今日火箭发射：是否有火箭发射以及发射时间？

现在请翻译以下标题：`;

    try {
      const completion = await this.openai.chat.completions.create({
        model: this.translationConfig.model,
        messages: [
          { role: 'system', content: titleSystemPrompt },
          { role: 'user', content: title }
        ],
        max_tokens: 200, // 标题翻译不需要太多token
        temperature: 0.1 // 更低的温度确保一致性
      }, {
        timeout: this.translationConfig.timeout
      });

      const translatedTitle = completion.choices[0]?.message?.content?.trim() || '';

      // 验证标题翻译质量
      if (this.validateTitleTranslation(translatedTitle, title)) {
        // 缓存结果
        this.translationCache.set(cacheKey, translatedTitle);
        this.logger.debug(`标题翻译成功: "${title}" -> "${translatedTitle}"`);
        return translatedTitle;
      } else {
        this.logger.warn(`标题翻译质量不佳，返回原标题: "${translatedTitle}"`);
        return title; // 返回原标题而不是质量不佳的翻译
      }
    } catch (error: any) {
      this.logger.error(`标题翻译失败: ${error.message}`);
      return title; // 翻译失败时返回原标题
    }
  }

  /**
   * 验证标题翻译的质量
   * @param translatedTitle 翻译后的标题
   * @param originalTitle 原标题
   * @returns 是否为有效的标题翻译
   */
  private validateTitleTranslation(translatedTitle: string, originalTitle: string): boolean {
    if (!translatedTitle || translatedTitle.trim() === '') {
      return false;
    }

    // 1. 长度检查：标题不应该太长
    if (translatedTitle.length > 100) {
      this.logger.debug(`标题翻译过长: ${translatedTitle.length}字符`);
      return false;
    }

    // 2. 格式检查：不应该包含换行符或列表格式
    if (translatedTitle.includes('\n') || translatedTitle.includes('：') && translatedTitle.split('：').length > 2) {
      this.logger.debug(`标题翻译包含格式化内容`);
      return false;
    }

    // 3. 检查是否包含明显的详细信息（时间表、列表等）
    const detailPatterns = [
      /\d{1,2}:\d{2}/, // 时间格式
      /\d+月\d+日/, // 日期格式
      /发射时间表|具体时间|详细信息/, // 具体信息指示词
      /以下|如下|包括|分别|列表/, // 列表指示词
      /第一|第二|第三|首先|其次|最后/, // 序号词
    ];

    for (const pattern of detailPatterns) {
      if (pattern.test(translatedTitle)) {
        this.logger.debug(`标题翻译包含详细信息: ${pattern}`);
        return false;
      }
    }

    // 4. 检查中文字符比例
    const chineseCharCount = (translatedTitle.match(/[\u4e00-\u9fff]/g) || []).length;
    const totalCharCount = translatedTitle.length;
    const chineseRatio = chineseCharCount / totalCharCount;

    if (chineseRatio < 0.5) {
      this.logger.debug(`标题翻译中文比例过低: ${chineseRatio.toFixed(2)}`);
      return false;
    }

    // 5. 检查是否与原标题相同（可能翻译失败）
    if (translatedTitle === originalTitle) {
      this.logger.debug(`标题翻译与原文相同，可能翻译失败`);
      return false;
    }

    return true;
  }

  /**
   * 检查翻译结果的质量
   * @param translated 翻译结果
   * @param original 原文
   * @returns 是否为有效翻译
   */
  private isValidTranslation(translated: string, original: string): boolean {
    if (!translated || translated.trim() === '') {
      return false;
    }

    // 检查翻译结果是否主要是中文
    const chineseCharCount = (translated.match(/[\u4e00-\u9fff]/g) || []).length;
    const totalCharCount = translated.length;
    const chineseRatio = chineseCharCount / totalCharCount;

    // 如果中文字符比例太低（小于30%），可能翻译失败
    if (chineseRatio < 0.3) {
      this.logger.debug(`翻译质量检查失败：中文字符比例过低 ${chineseRatio.toFixed(2)}`);
      return false;
    }

    // 检查是否包含大段未翻译的英文（连续超过100个英文字符）
    const longEnglishPattern = /[a-zA-Z\s]{100,}/g;
    const longEnglishMatches = translated.match(longEnglishPattern);
    if (longEnglishMatches && longEnglishMatches.length > 0) {
      this.logger.debug(`翻译质量检查失败：包含大段未翻译英文，共${longEnglishMatches.length}处`);
      return false;
    }

    // 检查翻译长度是否合理（不应该比原文短太多或长太多）
    const lengthRatio = translated.length / original.length;
    if (lengthRatio < 0.3 || lengthRatio > 3.0) {
      this.logger.debug(`翻译质量检查失败：长度比例异常 ${lengthRatio.toFixed(2)}`);
      return false;
    }

    return true;
  }

  /**
   * 验证组合翻译结果的质量，特别是字段分离
   * @param result 翻译结果
   * @param originalTitle 原标题
   * @param originalSummary 原摘要
   * @param originalContent 原内容
   * @returns 验证结果
   */
  private validateCombinedTranslationResult(
    result: any,
    originalTitle: string,
    originalSummary: string,
    originalContent: string
  ): { isValid: boolean; reason?: string } {

    // 检查标题翻译质量
    if (result.title_cn) {
      // 标题不应该太长（超过100个字符可能包含了额外内容）
      if (result.title_cn.length > 100) {
        return { isValid: false, reason: '标题翻译过长，可能包含额外内容' };
      }

      // 标题不应该包含换行符或列表格式
      if (result.title_cn.includes('\n') || result.title_cn.includes('：') && result.title_cn.split('：').length > 2) {
        return { isValid: false, reason: '标题翻译包含格式化内容' };
      }

      // 检查标题是否包含明显的时间表或详细信息
      const detailPatterns = [
        /\d{1,2}:\d{2}/, // 时间格式
        /\d+月\d+日/, // 日期格式
        /发射时间|发射窗口|具体时间/, // 具体时间信息
        /以下|如下|包括|分别/, // 列表指示词
      ];

      for (const pattern of detailPatterns) {
        if (pattern.test(result.title_cn)) {
          return { isValid: false, reason: '标题翻译包含详细信息或时间表' };
        }
      }
    }

    // 检查内容翻译质量
    if (result.content_cn && originalContent) {
      if (!this.isValidTranslation(result.content_cn, originalContent)) {
        return { isValid: false, reason: '内容翻译质量不佳' };
      }
    }

    return { isValid: true };
  }

  /**
   * 将文本分成段落，并记录分割信息以便后续重组时保留原有格式
   * @param text 要分段的文本
   * @param isUltraLong 是否为超长文档，超长文档使用更保守的分段策略
   * @returns 包含分段内容和分割信息的对象数组
   */
  private splitTextIntoSegments(text: string, isUltraLong: boolean = false): Array<{segment: string, separator: string, separatorType: string}> {
    const segments: Array<{segment: string, separator: string, separatorType: string}> = [];
    let remainingText = text;
    
    // 对于超长文档，使用更小的分段长度以确保稳定性
    const maxLength = isUltraLong ? 
      Math.min(this.translationConfig.maxTextLength * 0.7, 30000) : 
      this.translationConfig.maxTextLength;

    while (remainingText.length > 0) {
      if (remainingText.length <= maxLength) {
        segments.push({
          segment: remainingText,
          separator: '',
          separatorType: 'end'
        });
        break;
      }

      // 改进的分割策略：按段落 -> 句子 -> 最大长度
      let splitIndex = -1;
      let bestSeparator = '';
      let separatorType = '';
      let actualSeparator = '';

      // 1. 首先尝试按段落分割（双换行符）
      const paragraphIndex = remainingText.lastIndexOf('\n\n', maxLength);
      if (paragraphIndex > maxLength * (isUltraLong ? 0.3 : 0.5)) { // 超长文档使用更保守的分割标准
        splitIndex = paragraphIndex;
        bestSeparator = '段落';
        separatorType = 'paragraph';
        actualSeparator = '\n\n';
      }

      // 2. 如果段落分割不理想，尝试按句子分割
      if (splitIndex <= 0) {
        for (const separator of ['. ', '! ', '? ', '。', '！', '？']) {
          const sentenceIndex = remainingText.lastIndexOf(separator, maxLength);
          if (sentenceIndex > splitIndex && sentenceIndex > maxLength * (isUltraLong ? 0.2 : 0.3)) {
            splitIndex = sentenceIndex + separator.length;
            bestSeparator = '句子';
            separatorType = 'sentence';
            actualSeparator = '';
          }
        }
      }

      // 3. 如果句子分割也不理想，尝试按单行分割
      if (splitIndex <= 0) {
        const lineIndex = remainingText.lastIndexOf('\n', maxLength);
        if (lineIndex > maxLength * (isUltraLong ? 0.2 : 0.3)) {
          splitIndex = lineIndex;
          bestSeparator = '行';
          separatorType = 'line';
          actualSeparator = '\n';
        }
      }

      // 4. 如果没有找到合适的分隔符，按词汇边界分割
      if (splitIndex <= 0) {
        const wordBoundaryIndex = remainingText.lastIndexOf(' ', maxLength);
        if (wordBoundaryIndex > maxLength * (isUltraLong ? 0.6 : 0.8)) {
          splitIndex = wordBoundaryIndex;
          bestSeparator = '词汇';
          separatorType = 'word';
          actualSeparator = ' ';
        }
      }

      // 5. 最后兜底：强制在最大长度处分割
      if (splitIndex <= 0) {
        splitIndex = maxLength;
        bestSeparator = '强制';
        separatorType = 'force';
        actualSeparator = '';
      }

      this.logger.debug(`文本分段: 长度=${splitIndex}, 分割方式=${bestSeparator}, 剩余=${remainingText.length - splitIndex}`);

      const segmentContent = remainingText.substring(0, splitIndex);
      segments.push({
        segment: segmentContent,
        separator: actualSeparator,
        separatorType: separatorType
      });

      // 跳过分隔符
      if (separatorType === 'paragraph') {
        remainingText = remainingText.substring(splitIndex + 2);
      } else if (separatorType === 'line') {
        remainingText = remainingText.substring(splitIndex + 1);
      } else if (separatorType === 'word') {
        remainingText = remainingText.substring(splitIndex + 1);
      } else {
        remainingText = remainingText.substring(splitIndex);
      }
    }

    return segments;
  }

  /**
   * 预处理文本，移除可能触发内容过滤的内容
   * @param text 原始文本
   * @returns 处理后的文本
   */
  private removeAdvertisements(text: string): string {
    // 基于常见广告关键词和格式的简单启发式过滤
    const adPatterns = [
      /^\s*广告[:：]/i,
      /^\s*Sponsored[:：]?/i,
      /^\s*Advertisement[:：]?/i,
      /点击(这里|链接)查看全文/i,
      /如果您无法正常浏览此邮件/i,
      /免责声明[:：]/i,
      /本文(来源|来自)/i,
      /更多精彩内容请关注/i,
      /All rights reserved/i,
      /版权所有/i
    ];

    // 将文本按行切分，过滤广告行
    const cleanedLines = text.split(/\n+/).filter(line => {
      return !adPatterns.some(pattern => pattern.test(line.trim()));
    });

    // 过滤掉过短且疑似广告的最后一段
    if (cleanedLines.length > 0) {
      const lastLine = cleanedLines[cleanedLines.length - 1].trim();
      if (lastLine.length < 30 && /\b(http|www\.|\.com|\.cn)\b/i.test(lastLine)) {
        cleanedLines.pop();
      }
    }

    return cleanedLines.join('\n').trim();
  }

  // 修改 preprocessContent 调用广告去除
  private preprocessContent(text: string): string {
    let processed = this.removeAdvertisements(text);
    // 原有预处理逻辑
    processed = processed
      // 移除HTML标签
      .replace(/<[^>]+>/g, ' ')
      // 移除URL
      .replace(/https?:\/\/\S+/g, '[URL]')
      // 移除邮箱地址
      .replace(/\S+@\S+\.\S+/g, '[EMAIL]')
      // 移除多余空白
      .replace(/\s{2,}/g, ' ')
      .trim();

    return processed;
  }

  /**
   * 应用更严格的预处理，在基本预处理失败后使用
   * @param text 原始文本
   * @returns 处理后的文本
   */
  private applyStricterPreprocessing(text: string): string {
    // 先应用基本预处理
    let processedText = this.preprocessContent(text);

    // 更严格的处理
    processedText = processedText
      // 移除所有数字序列
      .replace(/(\d{4,})/g, '[NUMBER]')
      // 移除所有特殊字符
      .replace(/[^\w\s.,;:\-()\[\]{}"']/g, ' ')
      // 移除可能的敏感内容 - 更全面的敏感词汇
      .replace(/\b(missile|nuclear|classified|confidential|secret|intelligence|spy|surveillance|hack|breach|leak|sensitive|restricted|clearance|covert|operation|agent|agency|infiltrate|sabotage|conspiracy|whistleblower)\b/gi, '[SENSITIVE]')
      // 将连续的多个空格替换为单个空格
      .replace(/\s+/g, ' ')
      // 将文本分成短句子，每个句子不超过100个字符
      .split(/[.!?]\s+/)
      .map(sentence => sentence.trim().substring(0, 100))
      .join('. ')
      .trim();

    // 如果文本还是很长，截取前1000个字符
    if (processedText.length > 1000) {
      processedText = processedText.substring(0, 1000) + '...';
    }

    return processedText;
  }

  /**
   * 提取中文文本的主题词
   * @param title 文章标题
   * @param content 文章内容
   * @param retryCount 当前重试次数
   * @returns 主题词数组，逗号分隔的字符串
   */
  async extractThemes(title: string, content: string, retryCount = 0): Promise<string> {
    if ((!title || title.trim() === '') && (!content || content.trim() === '')) {
      return '';
    }

    // 构建用于提取主题的文本
    let textForThemeExtraction = '';
    if (title && title.trim() !== '') {
      textForThemeExtraction += `标题：${title}\n\n`;
    }
    if (content && content.trim() !== '') {
      // 如果内容太长，只取前2000个字符用于主题提取
      const maxContentLength = 2000;
      const truncatedContent = content.length > maxContentLength
        ? content.substring(0, maxContentLength) + '...'
        : content;
      textForThemeExtraction += `内容：${truncatedContent}`;
    }

    // 检查缓存
    const cacheKey = 'theme_' + this.generateCacheKey(textForThemeExtraction);
    if (this.translationCache.has(cacheKey)) {
      this.logger.debug(`使用缓存的主题提取结果: ${cacheKey.substring(0, 30)}...`);
      return this.translationCache.get(cacheKey) || '';
    }

    // 并发控制
    return this.executeWithConcurrencyControl(async () => {
      try {
        this.apiCallCount++;
        const startTime = Date.now();

        const completion = await this.openai.chat.completions.create({
          model: this.themeExtractionConfig.model,
          messages: [
            { role: 'system', content: this.themeExtractionConfig.systemPrompt },
            { role: 'user', content: textForThemeExtraction }
          ],
          max_tokens: this.themeExtractionConfig.maxTokens,
          temperature: this.themeExtractionConfig.temperature
        }, {
          timeout: this.themeExtractionConfig.timeout
        });

        const themes = completion.choices[0]?.message?.content || '';
        const elapsed = Date.now() - startTime;
        this.apiTotalTime += elapsed;

        this.logger.debug(`主题提取API调用完成，耗时: ${elapsed}ms，总调用次数: ${this.apiCallCount}`);

        // 缓存提取结果
        this.translationCache.set(cacheKey, themes);

        // 重置连续失败计数
        this.consecutiveFailures = 0;

        return themes;
      } catch (error: any) {
        // 增加连续失败计数（仅用于日志记录）
        this.consecutiveFailures++;
        this.logger.warn(`主题提取服务连续失败${this.consecutiveFailures}次 - 模型: ${this.themeExtractionConfig.model}`);

        // 处理内容不适当错误
        if (error.message.includes('inappropriate content')) {
          if (retryCount < (this.themeExtractionConfig.maxRetries || 2)) {
            this.logger.warn(`检测到不适当内容，尝试进一步处理后重试，重试次数: ${retryCount + 1}`);
            // 更强力的内容处理
            const furtherProcessedTitle = this.applyStricterPreprocessing(title);
            const furtherProcessedContent = this.applyStricterPreprocessing(content);
            return this.extractThemes(furtherProcessedTitle, furtherProcessedContent, retryCount + 1);
          } else {
            // 超过最大重试次数，返回特殊标记以跳过该文档
            this.logger.error(`主题提取内容不适当错误超过最大重试次数(${this.themeExtractionConfig.maxRetries})，跳过该文档`);
            throw new Error(`SKIP_DOCUMENT:主题提取内容不适当错误超过最大重试次数`);
          }
        }

        // 处理超时错误
        if (error.message.includes('timed out')) {
          if (retryCount < (this.themeExtractionConfig.maxRetries || 2)) {
            // 指数退避重试
            const waitTime = Math.pow(2, retryCount) * (this.themeExtractionConfig.retryDelay || 1000);
            this.logger.warn(`主题提取请求超时，${waitTime}ms后重试，重试次数: ${retryCount + 1} - 模型: ${this.themeExtractionConfig.model}`);

            await new Promise(resolve => setTimeout(resolve, waitTime));
            return this.extractThemes(title, content, retryCount + 1);
          } else {
            // 超过最大重试次数，返回特殊标记以跳过该文档
            this.logger.error(`主题提取超时错误超过最大重试次数(${this.themeExtractionConfig.maxRetries})，跳过该文档`);
            throw new Error(`SKIP_DOCUMENT:主题提取超时错误超过最大重试次数`);
          }
        }

        this.logger.error(`主题提取失败 - 模型: ${this.themeExtractionConfig.model}: ${error.message}`, error.stack);
        throw new Error(`主题提取失败: ${error.message}`);
      }
    });
  }

  /**
   * 一次性完成翻译和主题提取（优化版本，减少API调用次数）
   * @param title 原始标题
   * @param summary 原始摘要
   * @param content 原始内容
   * @param retryCount 当前重试次数
   * @returns 包含翻译结果和主题词的对象
   */
  async translateAndExtractInOneCall(
    title: string, 
    summary: string, 
    content: string, 
    retryCount = 0,
    forceRetranslate = false
  ): Promise<{
    title_cn: string;
    summary_cn: string;
    content_cn: string;
    themes_cn: string;
  }> {
    if ((!title || title.trim() === '') && 
        (!summary || summary.trim() === '') && 
        (!content || content.trim() === '')) {
      return {
        title_cn: '',
        summary_cn: '',
        content_cn: '',
        themes_cn: ''
      };
    }

    // 检查content长度，如果超过100K字符则跳过翻译
    const contentLength = content?.length || 0;
    const ULTRA_LONG_THRESHOLD = 100000; // 100K字符阈值
    
    if (contentLength > ULTRA_LONG_THRESHOLD) {
      this.logger.warn(`文档content过长 (${contentLength}字符，超过${ULTRA_LONG_THRESHOLD}字符阈值)，跳过翻译并清空已有翻译`);
      return {
        title_cn: '', // 清空标题翻译
        summary_cn: '', // 清空摘要翻译
        content_cn: '', // 清空内容翻译
        themes_cn: ''   // 清空主题词
      };
    }

    // 计算组合后的总长度
    const totalLength = (title?.length || 0) + (summary?.length || 0) + (content?.length || 0);
    
    // 智能配置选择：对于超长文档，自动切换到最适合的配置
    const originalConfig = this.switchToOptimalConfig(totalLength);
    
    // 更保守的策略：如果内容太长或总长度超过限制，使用分段翻译
    const shouldUseSeparateTranslation = content && (
      content.length > this.translationConfig.maxTextLength * 0.6 ||  // 内容长度超过60%限制
      totalLength > this.translationConfig.maxTextLength * 0.8        // 总长度超过80%限制
    );
    
    if (shouldUseSeparateTranslation) {
      this.logger.debug(`内容过长(content: ${content?.length}, total: ${totalLength}字符)，使用分段翻译处理`);
      
      try {
        // 分别翻译各部分，避免字段混淆
        const titleCn = title ? await this.translateTitleOnly(title, forceRetranslate) : '';
        const summaryCn = summary ? await this.translateText(summary, 0, true, forceRetranslate) : '';
        const contentCn = await this.translateLongText(content, forceRetranslate);
        
        // 使用翻译后的中文内容提取主题词
        const themes = await this.extractThemes(titleCn, contentCn);
        
        const result = {
          title_cn: titleCn,
          summary_cn: summaryCn,
          content_cn: contentCn,
          themes_cn: themes
        };
        
        // 恢复原始配置
        this.restoreOriginalConfig(originalConfig);
        
        return result;
      } catch (error) {
        // 分段翻译失败，恢复原始配置后重新抛出错误
        this.restoreOriginalConfig(originalConfig);
        throw error;
      }
    }

    // 构建输入文本 - 对于较短的内容，使用一次性翻译
    let inputText = '';
    if (title && title.trim() !== '') {
      inputText += `标题：${title}\n\n`;
    }
    if (summary && summary.trim() !== '') {
      inputText += `摘要：${summary}\n\n`;
    }
    if (content && content.trim() !== '') {
      // 不再截断内容，使用完整内容
      inputText += `内容：${content}`;
    }

    // 应用敏感词预处理
    const processedInput = this.sanitizeSensitiveWords(inputText);

    // 检查缓存（forceRetranslate模式下绕过缓存）
    const cacheKey = 'combined_' + this.generateCacheKey(processedInput);
    if (!forceRetranslate && this.translationCache.has(cacheKey)) {
      this.logger.debug(`命中组合翻译缓存: ${cacheKey.substring(0, 30)}...`);
      const cachedResult = this.translationCache.get(cacheKey);
      if (cachedResult) {
        try {
          return JSON.parse(cachedResult);
        } catch (parseError) {
          this.logger.warn(`缓存结果JSON解析失败，重新调用API`);
        }
      }
    }

    // 组合任务的系统提示词（改进版，强调字段分离）
    const combinedSystemPrompt = `你是一位专业的翻译和文本分析专家，特别擅长航空航天领域。

输入格式说明：
- 输入文本会按照"标题：xxx"、"摘要：xxx"、"内容：xxx"的格式提供
- 你需要分别翻译每个部分，绝对不能混淆各部分的内容

翻译要求：
1. **标题翻译(title_cn)**：
   - 只翻译"标题："后面的部分，保持简洁准确
   - 标题翻译必须简短，通常不超过50个中文字符
   - 如果原标题是问句，翻译后也应该是问句
   - 绝对不要在标题中包含任何列表、时间表、详细信息或内容摘要
   - 标题翻译应该只反映文章的主题，不包含具体细节
   
2. **摘要翻译(summary_cn)**：
   - 只翻译"摘要："后面的部分
   - 保持摘要的特点：简明扼要
   
3. **内容翻译(content_cn)**：
   - 只翻译"内容："后面的部分
   - 保留原文的所有换行符、段落格式和缩进空格
   - 保留数字、时间、专有名词的格式
   
4. **主题提取(themes_cn)**：
   - 基于整篇文章提取不超过5个中文主题词
   - 如果是科普类型添加"科普"，如果是军事类型添加"军事"

**重要警告**：绝对不要将一个字段的内容混入到另一个字段中！

严格按照以下JSON格式返回，不要有其他内容：
{"title_cn":"仅标题翻译","summary_cn":"仅摘要翻译","content_cn":"仅内容翻译","themes_cn":"主题词1,主题词2,主题词3"}`;

    // 并发控制
    return this.executeWithConcurrencyControl(async () => {
      try {
        this.apiCallCount++;
        const startTime = Date.now();

        // 预处理文本
        const finalProcessedText = this.preprocessContent(processedInput);

        const completion = await this.openai.chat.completions.create({
          model: this.translationConfig.model,
          messages: [
            { role: 'system', content: combinedSystemPrompt },
            { role: 'user', content: finalProcessedText }
          ],
          // 使用配置的maxTokens，根据模型限制设置
          max_tokens: this.translationConfig.maxTokens || 7000,
          temperature: this.translationConfig.temperature
        }, {
          timeout: this.translationConfig.timeout
        });

        const responseContent = completion.choices[0]?.message?.content || '';
        const elapsed = Date.now() - startTime;
        this.apiTotalTime += elapsed;

        this.logger.debug(`组合翻译API调用完成，耗时: ${elapsed}ms，总调用次数: ${this.apiCallCount}`);

        // 解析JSON响应
        let result;
        try {
          // 改进的JSON清理和解析逻辑
          let cleanedResponse = responseContent.trim();
          
          // 移除各种可能的markdown代码块标记
          cleanedResponse = cleanedResponse.replace(/^```(?:json|javascript|js)?\s*/i, '');
          cleanedResponse = cleanedResponse.replace(/\s*```$/i, '');
          
          // 移除可能的前后文本，只保留JSON部分
          const jsonStartPattern = /\{[\s\S]*?"title_cn"[\s\S]*?\}/;
          const jsonMatch = cleanedResponse.match(jsonStartPattern);
          
          if (jsonMatch) {
            cleanedResponse = jsonMatch[0];
          } else {
            // 如果没有找到完整的JSON，尝试查找JSON对象的开始和结束
            const startIndex = cleanedResponse.indexOf('{');
            const lastIndex = cleanedResponse.lastIndexOf('}');
            
            if (startIndex !== -1 && lastIndex !== -1 && lastIndex > startIndex) {
              cleanedResponse = cleanedResponse.substring(startIndex, lastIndex + 1);
            }
          }
          
          // 尝试解析JSON
          result = JSON.parse(cleanedResponse);
          
          // 验证必要字段
          if (!result.title_cn && !result.summary_cn && !result.content_cn) {
            throw new Error('响应中缺少翻译字段');
          }
          
          // 确保所有字段都存在并进行质量验证
          result = {
            title_cn: this.validateAndCleanTitle(result.title_cn || '', title),
            summary_cn: this.validateAndCleanField(result.summary_cn || '', summary, 'summary'),
            content_cn: this.validateAndCleanField(result.content_cn || '', content, 'content'),
            themes_cn: result.themes_cn || ''
          };
          
          this.logger.debug(`JSON解析成功，提取字段: title_cn=${!!result.title_cn}, summary_cn=${!!result.summary_cn}, content_cn=${!!result.content_cn}, themes_cn=${!!result.themes_cn}`);
          
        } catch (parseError) {
          this.logger.error(`JSON解析失败，原始响应: ${responseContent}`);
          this.logger.debug(`解析错误详情: ${parseError.message}`);
          
          // 如果JSON解析失败，尝试从响应中提取内容
          const lines = responseContent.split('\n');
          const extractedTitle = this.extractFieldFromResponse(lines, 'title_cn') || title;
          const extractedSummary = this.extractFieldFromResponse(lines, 'summary_cn') || summary;
          const extractedContent = this.extractFieldFromResponse(lines, 'content_cn') || content;
          const extractedThemes = this.extractFieldFromResponse(lines, 'themes_cn') || '';
          
          result = {
            title_cn: this.validateAndCleanTitle(extractedTitle, title),
            summary_cn: this.validateAndCleanField(extractedSummary, summary, 'summary'),
            content_cn: this.validateAndCleanField(extractedContent, content, 'content'),
            themes_cn: extractedThemes
          };
          
          this.logger.warn(`使用备用解析方法，提取结果: ${JSON.stringify(result)}`);
        }

        // 验证翻译结果质量，特别是字段分离
        const validationResult = this.validateCombinedTranslationResult(result, title, summary, content);
        if (!validationResult.isValid) {
          this.logger.warn(`组合翻译结果验证失败: ${validationResult.reason}，尝试分段翻译`);

          // 如果验证失败，回退到分段翻译
          const titleCn = title ? await this.translateTitleOnly(title, forceRetranslate) : '';
          const summaryCn = summary ? await this.translateText(summary, 0, true, forceRetranslate) : '';
          const contentCn = await this.translateLongText(content, forceRetranslate);

          // 使用翻译后的中文内容提取主题词
          const themes = await this.extractThemes(titleCn, contentCn);

          const fallbackResult = {
            title_cn: titleCn,
            summary_cn: summaryCn,
            content_cn: contentCn,
            themes_cn: themes
          };

          // 恢复原始配置
          this.restoreOriginalConfig(originalConfig);

          this.logger.debug(`分段翻译完成 - 标题长度: ${titleCn?.length || 0}, 内容长度: ${contentCn?.length || 0}`);
          return fallbackResult;
        }

        // 缓存结果
        this.translationCache.set(cacheKey, JSON.stringify(result));

        // 重置连续失败计数
        this.consecutiveFailures = 0;
        this.failureStats.successfulProcessing++;

        // 恢复原始配置
        this.restoreOriginalConfig(originalConfig);

        this.logger.debug(`组合翻译成功 - 模型: ${this.translationConfig.model}, 标题长度: ${result.title_cn?.length || 0}, 内容长度: ${result.content_cn?.length || 0}, 主题数: ${result.themes_cn?.split(',').length || 0}`);

        return result;
      } catch (error: any) {
        // 恢复原始配置（在错误处理前）
        this.restoreOriginalConfig(originalConfig);
        
        // 增加连续失败计数
        this.consecutiveFailures++;
        
        this.logger.error(`组合翻译失败 - 模型: ${this.translationConfig.model}, 重试次数: ${retryCount}, 错误: ${error.message}`);

        // 处理内容不适当错误
        if (error.message.includes('inappropriate content') || 
            error.message.includes('content_filter') || 
            error.message.includes('content policy') ||
            error.message.includes('safety') ||
            error.message.includes('harmful')) {
          
          this.failureStats.contentFilter++;
          
          if (retryCount >= (this.translationConfig.maxRetries || 2)) {
            this.logger.error(`组合翻译内容过滤错误超过最大重试次数(${this.translationConfig.maxRetries})，跳过该文档`);
            throw new Error(`SKIP_DOCUMENT:组合翻译内容过滤错误超过最大重试次数 - ${error.message}`);
          }

          this.logger.warn(`使用智能预处理重试组合翻译，重试次数: ${retryCount + 1}`);
          const preprocessingResult = this.intelligentPreprocessing(inputText);
          
          // 重新构建输入
          const retryTitle = this.extractOriginalField(preprocessingResult.processedText, '标题');
          const retrySummary = this.extractOriginalField(preprocessingResult.processedText, '摘要');
          const retryContent = this.extractOriginalField(preprocessingResult.processedText, '内容');
          
          return this.translateAndExtractInOneCall(retryTitle, retrySummary, retryContent, retryCount + 1);
        }

        // 处理超时错误
        if (error.message.includes('timed out') || error.message.includes('timeout')) {
          this.failureStats.timeout++;
          
          if (retryCount < (this.translationConfig.maxRetries || 2)) {
            const waitTime = Math.pow(2, retryCount) * (this.translationConfig.retryDelay || 1000);
            this.logger.warn(`组合翻译请求超时，${waitTime}ms后重试，重试次数: ${retryCount + 1}`);

            await new Promise(resolve => setTimeout(resolve, waitTime));
            return this.translateAndExtractInOneCall(title, summary, content, retryCount + 1);
          } else {
            this.logger.error(`组合翻译超时错误超过最大重试次数，跳过该文档`);
            throw new Error(`SKIP_DOCUMENT:组合翻译超时错误超过最大重试次数`);
          }
        }

        // 处理API限制错误
        if (error.message.includes('rate limit') || error.message.includes('429')) {
          this.failureStats.rateLimit++;
          
          if (retryCount < (this.translationConfig.maxRetries || 2)) {
            const waitTime = (retryCount + 1) * 5000;
            this.logger.warn(`组合翻译API限制错误，${waitTime}ms后重试，重试次数: ${retryCount + 1}`);

            await new Promise(resolve => setTimeout(resolve, waitTime));
            return this.translateAndExtractInOneCall(title, summary, content, retryCount + 1);
          } else {
            this.logger.error(`组合翻译API限制错误超过最大重试次数，跳过该文档`);
            throw new Error(`SKIP_DOCUMENT:组合翻译API限制错误超过最大重试次数`);
          }
        }

        // 处理网络错误
        if (error.message.includes('network') || 
            error.message.includes('connection') || 
            error.message.includes('ENOTFOUND') ||
            error.message.includes('ECONNRESET') ||
            error.message.includes('socket')) {
          this.failureStats.networkError++;
        } else {
          this.failureStats.other++;
        }

        this.logger.error(`组合翻译失败: ${error.message}`, error.stack);
        throw new Error(`组合翻译失败: ${error.message}`);
      }
    });
  }

  /**
   * 从响应文本中提取指定字段的值
   * @param lines 响应文本行数组
   * @param fieldName 字段名
   * @returns 提取的值
   */
  private extractFieldFromResponse(lines: string[], fieldName: string): string {
    // 尝试多种模式来提取字段值
    const patterns = [
      // 标准JSON格式: "field_name": "value"
      new RegExp(`"${fieldName}"\\s*:\\s*"([^"]*)"`, 'i'),
      // 带转义的JSON格式: "field_name": "value with \"quotes\""
      new RegExp(`"${fieldName}"\\s*:\\s*"([^"\\\\]*(?:\\\\.[^"\\\\]*)*)"`, 'i'),
      // 无引号格式: field_name: value
      new RegExp(`${fieldName}\\s*:\\s*([^,\\n}]+)`, 'i'),
      // 中文标签格式: 标题：value
      new RegExp(`${this.getChineseLabel(fieldName)}：([^\\n]+)`, 'i')
    ];
    
    for (const line of lines) {
      if (line.includes(fieldName) || line.includes(this.getChineseLabel(fieldName))) {
        for (const pattern of patterns) {
          const match = line.match(pattern);
          if (match && match[1]) {
            // 清理提取的值
            let value = match[1].trim();
            // 移除可能的尾部逗号或其他符号
            value = value.replace(/[,}]$/, '');
            // 处理转义字符
            value = value.replace(/\\"/g, '"').replace(/\\n/g, '\n');
            return value;
          }
        }
      }
    }
    return '';
  }

  /**
   * 获取字段的中文标签
   * @param fieldName 字段名
   * @returns 中文标签
   */
  private getChineseLabel(fieldName: string): string {
    const labelMap: { [key: string]: string } = {
      'title_cn': '标题',
      'summary_cn': '摘要', 
      'content_cn': '内容',
      'themes_cn': '主题词'
    };
    return labelMap[fieldName] || fieldName;
  }

  /**
   * 验证和清理标题翻译字段
   * @param translatedTitle 翻译后的标题
   * @param originalTitle 原始标题
   * @returns 清理后的标题
   */
  private validateAndCleanTitle(translatedTitle: string, originalTitle: string): string {
    if (!translatedTitle || translatedTitle.trim() === '') {
      this.logger.warn('标题翻译为空，返回原标题');
      return originalTitle;
    }

    let cleaned = translatedTitle.trim();

    // 0. 首先检查是否包含大段内容（比如列表、时间表等）
    const hasListContent = /(?:\n.*){3,}|(?:\d+\.\s+)|(?:-\s+.*\n)|(?:Launch Time:|Mission:|Status:|Location:)/i.test(cleaned);
    if (hasListContent) {
      this.logger.warn('标题翻译包含列表或大段内容，截取第一行作为标题');
      const firstLine = cleaned.split('\n')[0].trim();
      if (firstLine && firstLine.length > 5) {
        cleaned = firstLine;
      } else {
        return originalTitle;
      }
    }

    // 1. 检查是否包含原标题内容（可能是解析错误）
    if (cleaned.includes(originalTitle)) {
      this.logger.warn('标题翻译包含原文，尝试提取纯翻译部分');
      // 尝试移除原标题内容
      cleaned = cleaned.replace(originalTitle, '').trim();
      // 移除可能的分隔符
      cleaned = cleaned.replace(/^[：:：\-\s]+/, '').replace(/[：:：\-\s]+$/, '');
    }

    // 2. 更严格的长度检查（标题不应该过长）
    const maxTitleLength = Math.max(originalTitle.length * 3, 150); // 更严格的限制
    if (cleaned.length > maxTitleLength) {
      this.logger.warn(`标题翻译过长(${cleaned.length}字符)，可能包含其他内容，强制截取`);
      // 截取到第一个句号或合理长度
      const sentenceEnd = cleaned.search(/[。！？.!?]/);
      if (sentenceEnd > 0 && sentenceEnd < maxTitleLength) {
        cleaned = cleaned.substring(0, sentenceEnd).trim();
      } else {
        cleaned = cleaned.substring(0, maxTitleLength).trim();
      }
    }

    // 3. 检查是否包含明显的非标题内容（增强版）
    const nonTitlePatterns = [
      /摘要[:：]/i,
      /内容[:：]/i,
      /主题词[:：]/i,
      /\n\n/,  // 多行内容
      /^['"「『]/,  // 引用开始
      /['"」』]$/,   // 引用结束
      /\d+\.\s+/,  // 数字列表
      /-\s+.*:/,   // 破折号列表项
      /Launch Time:|Mission:|Status:|Location:/i,  // 发射信息字段
      /UTC|AM|PM/i,  // 时间标识
      /Pad\s+\d+/i,  // 发射台
      /Scheduled|Under Review|Postponed/i,  // 状态词
      /Please note/i,  // 说明性文字
      /today.*schedule/i  // 时间表内容
    ];

    for (const pattern of nonTitlePatterns) {
      if (pattern.test(cleaned)) {
        this.logger.warn(`标题翻译包含非标题内容(${pattern})，截取到分隔符前或使用原标题`);
        const match = cleaned.match(pattern);
        if (match && match.index !== undefined && match.index > 10) {
          cleaned = cleaned.substring(0, match.index).trim();
        } else {
          // 如果在开头就发现非标题内容，直接返回原标题
          return originalTitle;
        }
        break;
      }
    }

    // 4. 检查是否为单一问句（标题特征检查）
    const isQuestion = /^[^.!。！]*[?？]$/.test(originalTitle.trim());
    if (isQuestion && !cleaned.includes('?') && !cleaned.includes('？')) {
      // 原标题是问句但翻译后不是，可能混入了其他内容
      this.logger.warn('原标题是问句但翻译后不包含问号，可能混入其他内容');
      if (cleaned.length > originalTitle.length * 2) {
        return originalTitle;
      }
    }

    // 5. 最终检查：如果清理后内容太短或为空，返回原标题
    if (!cleaned || cleaned.length < 3) {
      this.logger.warn('标题翻译清理后过短，返回原标题');
      return originalTitle;
    }

    // 6. 检查是否仍然包含英文（可能翻译失败）
    const englishRatio = (cleaned.match(/[a-zA-Z]/g) || []).length / cleaned.length;
    if (englishRatio > 0.8 && cleaned !== originalTitle) {
      this.logger.warn('标题翻译包含过多英文，可能翻译失败，返回原标题');
      return originalTitle;
    }

    this.logger.debug(`标题验证通过: "${cleaned}"`);
    return cleaned;
  }

  /**
   * 验证和清理其他字段翻译
   * @param translatedField 翻译后的字段内容
   * @param originalField 原始字段内容
   * @param fieldType 字段类型
   * @returns 清理后的字段内容
   */
  private validateAndCleanField(translatedField: string, originalField: string, fieldType: string): string {
    if (!translatedField || translatedField.trim() === '') {
      this.logger.warn(`${fieldType}翻译为空，返回原文`);
      return originalField;
    }

    let cleaned = translatedField.trim();

    // 1. 基本长度检查
    const lengthRatio = cleaned.length / originalField.length;
    
    // 2. 检查是否包含过多英文（翻译失败的信号）
    const englishChars = (cleaned.match(/[a-zA-Z]/g) || []).length;
    const totalChars = cleaned.length;
    const englishRatio = englishChars / totalChars;

    // 如果英文比例超过60%且与原文不同，可能是翻译失败
    if (englishRatio > 0.6 && cleaned !== originalField && originalField.length > 100) {
      this.logger.warn(`${fieldType}翻译包含过多英文(${(englishRatio * 100).toFixed(1)}%)，可能翻译失败`);
      // 不直接返回原文，而是保留翻译结果，让用户知道存在问题
    }

    // 3. 检查长度比例是否合理
    if (lengthRatio < 0.1 && originalField.length > 200) {
      this.logger.warn(`${fieldType}翻译过短(比例${(lengthRatio * 100).toFixed(1)}%)，可能不完整`);
    } else if (lengthRatio > 5 && originalField.length > 100) {
      this.logger.warn(`${fieldType}翻译过长(比例${(lengthRatio * 100).toFixed(1)}%)，可能包含额外内容`);
    }

    this.logger.debug(`${fieldType}验证通过，长度比例: ${(lengthRatio * 100).toFixed(1)}%, 英文比例: ${(englishRatio * 100).toFixed(1)}%`);
    return cleaned;
  }

  /**
   * 从预处理后的文本中提取原始字段
   * @param processedText 预处理后的文本
   * @param fieldLabel 字段标签（如"标题"、"摘要"、"内容"）
   * @returns 提取的原始字段值
   */
  private extractOriginalField(processedText: string, fieldLabel: string): string {
    const regex = new RegExp(`${fieldLabel}：([\\s\\S]*?)(?=\\n\\n|$)`, 'i');
    const match = processedText.match(regex);
    return match ? match[1].trim() : '';
  }

  /**
   * 检测文本中的潜在敏感内容 - 增强版
   * @param text 要检测的文本
   * @returns 检测结果
   */
  private analyzeSensitiveContent(text: string): {
    hasSensitiveContent: boolean;
    sensitiveWords: string[];
    categories: string[];
    riskLevel: 'low' | 'medium' | 'high';
  } {
    const sensitivePatterns = {
      // 安全相关 - 扩展词库
      security: /\b(terrorism|terrorist|bomb|explosive|attack|kill|weapon|suicide|violent|war|conflict|military|missile|nuclear|assassination|threat|hostage|extremist|radical|jihad|massacre|combat|battlefield|warfare|fighter|bomber|destroyer|battleship|tank|artillery|grenade|rifle|pistol|ammunition|soldier|troops|combat|invasion|occupation|siege|raid|assault|warfare|defense|offensive|strike|launch|target|enemy|hostile|aggression|counterattack)\b/gi,
      
      // 毒品相关
      drugs: /\b(drug|cocaine|heroin|marijuana|cannabis|meth|amphetamine|opium|narcotic|substance abuse|overdose|smuggling|trafficking|dealer|addict|rehabilitation|withdrawal)\b/gi,
      
      // 成人内容
      adult: /\b(sex|porn|nude|naked|adult|explicit|xxx|erotic|obscene|prostitute|brothel|strip|sexual|intimacy|seduction)\b/gi,
      
      // 政治敏感 - 扩展
      political: /\b(genocide|torture|dictatorship|oppression|censorship|propaganda|corruption|riot|protest|revolution|rebellion|uprising|coup|regime|authoritarian|totalitarian|persecution|discrimination|violation|human rights|freedom|democracy|election fraud|suppression)\b/gi,
      
      // 情报和机密 - 扩展
      intelligence: /\b(missile|nuclear|classified|confidential|secret|intelligence|spy|surveillance|hack|breach|leak|sensitive|restricted|clearance|covert|operation|agent|agency|infiltrate|sabotage|conspiracy|whistleblower|espionage|reconnaissance|undercover|mole|double agent|black ops|cyber attack|data breach|national security|top secret|state secrets)\b/gi,
      
      // 暴力相关 - 扩展
      violence: /\b(death|murder|torture|abuse|violence|blood|injury|harm|damage|destroy|destruction|killing|slaughter|execution|brutality|cruelty|savage|vicious|ruthless|merciless)\b/gi,
      
      // 新增：网络安全和黑客
      cybersecurity: /\b(hacker|hacking|malware|virus|trojan|phishing|ransomware|ddos|cyber|breach|vulnerability|exploit|penetration|backdoor|rootkit|keylogger|botnet|dark web|anonymous|zero day)\b/gi,
      
      // 新增：恐怖主义相关术语
      terrorism_extended: /\b(isis|al.?qaeda|taliban|suicide bomber|car bomb|ied|improvised explosive|terror attack|mass shooting|school shooting|public attack|lone wolf|radicalization|indoctrination)\b/gi,
      
      // 新增：军事技术
      military_tech: /\b(drone|uav|guided missile|ballistic|intercontinental|submarine|aircraft carrier|stealth|radar|sonar|satellite surveillance|gps jamming|electronic warfare|signal intelligence)\b/gi
    };

    const sensitiveWords: string[] = [];
    const categories: string[] = [];
    let totalMatches = 0;

    Object.entries(sensitivePatterns).forEach(([category, pattern]) => {
      const matches = text.match(pattern);
      if (matches) {
        sensitiveWords.push(...matches);
        categories.push(category);
        totalMatches += matches.length;
      }
    });

    // 增强的风险等级计算
    let riskLevel: 'low' | 'medium' | 'high' = 'low';
    
    // 高风险条件更严格
    if (totalMatches > 15 || 
        categories.includes('terrorism_extended') || 
        categories.includes('cybersecurity') || 
        (categories.includes('security') && categories.includes('intelligence')) ||
        (categories.includes('violence') && totalMatches > 8)) {
      riskLevel = 'high';
    } else if (totalMatches > 8 || 
               categories.length > 3 || 
               categories.includes('security') || 
               categories.includes('intelligence') ||
               categories.includes('military_tech')) {
      riskLevel = 'medium';
    }

    return {
      hasSensitiveContent: sensitiveWords.length > 0,
      sensitiveWords: [...new Set(sensitiveWords)], // 去重
      categories: [...new Set(categories)], // 去重
      riskLevel
    };
  }

  /**
   * 智能预处理文本，根据内容分析选择合适的处理策略
   * @param text 原始文本
   * @param title 文章标题（可选，用于上下文判断）
   * @returns 处理后的文本和处理信息
   */
  private intelligentPreprocessing(text: string, title?: string): {
    processedText: string;
    strategy: string;
    analysis: any;
    needsManualReview: boolean;
  } {
    // 分析敏感内容
    const analysis = this.analyzeSensitiveContent(text);
    const titleAnalysis = title ? this.analyzeSensitiveContent(title) : null;

    let processedText = text;
    let strategy = 'none';
    let needsManualReview = false;

    // 判断是否是新闻类文本
    const isNewsContent = /\b(报道|新闻|发布|宣布|消息|据.*报告|according to|reported|announced|news|press|media)\b/i.test(text + (title || ''));
    
    // 判断是否是技术或科学文本
    const isTechnicalContent = /\b(技术|科学|研究|实验|数据|algorithm|technology|science|research|satellite|space|orbit)\b/i.test(text + (title || ''));

    if (analysis.riskLevel === 'high') {
      // 高风险内容需要更激进的处理
      if (isNewsContent || isTechnicalContent) {
        // 对于新闻或技术内容，使用温和的替换策略
        processedText = this.applyContextualPreprocessing(text, 'news_technical');
        strategy = 'contextual_news_technical';
      } else {
        // 对于其他高风险内容，使用严格处理
        processedText = this.applyStricterPreprocessing(text);
        strategy = 'strict';
        needsManualReview = true;
      }
    } else if (analysis.riskLevel === 'medium') {
      // 中等风险使用基础预处理
      processedText = this.preprocessContent(text);
      strategy = 'basic';
    } else if (analysis.hasSensitiveContent) {
      // 低风险但有敏感词，使用轻度处理
      processedText = this.applyLightPreprocessing(text);
      strategy = 'light';
    }

    return {
      processedText,
      strategy,
      analysis: {
        original: analysis,
        title: titleAnalysis,
        isNewsContent,
        isTechnicalContent
      },
      needsManualReview
    };
  }

  /**
   * 针对新闻和技术内容的上下文预处理
   * @param text 原始文本
   * @param contentType 内容类型
   * @returns 处理后的文本
   */
  private applyContextualPreprocessing(text: string, contentType: 'news_technical' | 'general'): string {
    let processedText = text;

    if (contentType === 'news_technical') {
      // 对于新闻和技术内容，保留更多信息，只替换明显敏感的词汇
      processedText = processedText
        // 保留军事技术术语，但替换暴力词汇
        .replace(/\b(kill|murder|destroy|attack|violence)\b/gi, '[ACTION]')
        // 保留核技术等术语，但替换威胁性描述
        .replace(/\b(threat|danger|risk|hazard)\b/gi, '[CONCERN]')
        // 保留情报相关术语在技术上下文中
        .replace(/\b(classified|secret|confidential)(?!\s+(information|data|technology))/gi, '[RESTRICTED]')
        // 移除明显的敏感政治内容
        .replace(/\b(terrorism|terrorist|extremist|radical)\b/gi, '[GROUP]');
    } else {
      // 对于一般内容使用标准预处理
      processedText = this.preprocessContent(text);
    }

    return processedText.trim();
  }

  /**
   * 轻度预处理，只处理最明显的敏感内容
   * @param text 原始文本
   * @returns 处理后的文本
   */
  private applyLightPreprocessing(text: string): string {
    return text
      // 只替换最明显的暴力词汇
      .replace(/\b(terrorism|terrorist|bomb|explosive|kill|murder|suicide)\b/gi, '[REDACTED]')
      // 替换明显的成人内容
      .replace(/\b(porn|nude|explicit|xxx|obscene)\b/gi, '[FILTERED]')
      // 移除URL和邮箱
      .replace(/https?:\/\/\S+/g, '[URL]')
      .replace(/\S+@\S+\.\S+/g, '[EMAIL]')
      .trim();
  }
}