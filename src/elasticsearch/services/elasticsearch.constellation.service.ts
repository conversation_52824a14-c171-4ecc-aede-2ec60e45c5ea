import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { ElasticsearchService as NestElasticsearchService } from '@nestjs/elasticsearch';
import { ElasticsearchBaseService } from './elasticsearch.base.service';
import { 
  ConstellationQueryDto, 
  ConstellationSearchResponse, 
  ConstellationNamesResponseDto, 
  ConstellationOrganizationsResponseDto,
  ConstellationDataSource,
  ConstellationPurposesResponseDto
} from '../types/constellation.types';
import { SearchTotalHits } from '@elastic/elasticsearch/lib/api/types';

/**
 * 星座信息查询服务
 * 处理星座信息的查询、聚合和处理
 */
@Injectable()
export class ElasticsearchConstellationService extends ElasticsearchBaseService {
  protected readonly logger = new Logger(ElasticsearchConstellationService.name);
  private readonly constellationIndex = 'constellation_info';

  constructor(
    elasticsearchService: NestElasticsearchService
  ) {
    super(elasticsearchService);
  }

  /**
   * 搜索星座信息
   * @param queryDto 查询参数
   * @returns 查询结果
   */
  async searchConstellationInfo(queryDto: ConstellationQueryDto): Promise<ConstellationSearchResponse> {
    try {
      this.logger.debug(`搜索星座信息，参数: ${JSON.stringify(queryDto)}`);
      
      const { page = 1, limit = 10, keyword, targetName, targetId, country, organization, purpose } = queryDto;
      
      // 构建查询条件
      const must: any[] = [];
      const should: any[] = [];
      
      // 处理目标名称查询 - 必须匹配
      if (targetName) {
        this.logger.debug(`使用目标名称查询: "${targetName}"`);
        must.push({
          bool: {
            should: [
              {
                match_phrase: {
                  constellation_name: targetName
                }
              },
              {
                match_phrase: {
                  company_constellation: targetName
                }
              }
            ],
            minimum_should_match: 1
          }
        });
      }
      
      // 处理关键词查询
      if (keyword) {
        should.push({
          multi_match: {
            query: keyword,
            fields: ['constellation_name^3', 'company_constellation^2', 'constellation_info'],
            type: 'best_fields' as any,
            fuzziness: 'AUTO'
          }
        });
      }
      
      // 处理目标编号查询
      if (targetId) {
        should.push({
          match: {
            id: {
              query: targetId
            }
          }
        });
      }
      
      // 处理所属机构查询
      if (organization) {
        should.push({
          bool: {
            should: [
              {
                wildcard: {
                  'company_constellation': {
                    value: `*${organization.toLowerCase()}*`,
                    case_insensitive: true
                  }
                }
              },
              {
                wildcard: {
                  'company': {
                    value: `*${organization.toLowerCase()}*`,
                    case_insensitive: true
                  }
                }
              },
              {
                wildcard: {
                  'owner_info.owner': {
                    value: `*${organization.toLowerCase()}*`,
                    case_insensitive: true
                  }
                }
              }
            ],
            minimum_should_match: 1
          }
        });
      }
      
      // 处理所属国家查询
      if (country) {
        // 创建国家名称的同义词映射
        const countryVariants = this.getCountryVariants(country);
        
        // 使用should查询匹配任一变体
        const countryQueries = countryVariants.map(variant => ({
          match: {
            'owner_info.country': {
              query: variant,
              fuzziness: 'AUTO'
            }
          }
        }));
        
        should.push({
          bool: {
            should: countryQueries,
            minimum_should_match: 1
          }
        });
      }
      
      // 处理星座用途查询
      if (purpose) {
        // 创建用途的同义词映射
        const purposeVariants = this.getPurposeVariants(purpose);
        
        // 使用should查询匹配任一变体
        const purposeQueries = purposeVariants.map(variant => ({
          multi_match: {
            query: variant,
            fields: ['purpose^2', 'applications'],
            type: 'best_fields',
            fuzziness: 'AUTO'
          }
        }));
        
        should.push({
          bool: {
            should: purposeQueries,
            minimum_should_match: 1
          }
        });
      }
      
      // 构建最终查询
      const query: any = {
        bool: {
          must,
          should,
          minimum_should_match: should.length > 0 ? 1 : 0
        }
      };
      
      // 执行查询 - 只查询constell_newspace索引
      const response = await this.elasticsearchService.search({
        index: ['constell_newspace'], // 只查询constell_newspace索引
        query,
        size: limit,
        from: (page - 1) * limit,
        sort: [
          { _score: { order: 'desc' } }
        ],
        _source: true // 确保返回所有字段
      });
      
      // 提取命中结果
      const hits = response.hits.hits;
      const total = typeof response.hits.total === 'number' 
        ? response.hits.total 
        : (response.hits.total as SearchTotalHits).value;
      
      // 处理每个命中结果，计算匹配字段
      const processedHits = hits.map(hit => {
        const source = hit._source as any;
        
        // 计算匹配字段
        const matchedFields = this.calculateMatchedFields([{
          index: hit._index,
          id: hit._id,
          score: hit._score,
          source: source
        }], queryDto);
        
        // 创建匹配字段描述
        const matchedFieldsDescription = this.createMatchFieldsDescription(matchedFields);
        const matchedFieldsCount = Object.values(matchedFields).filter(Boolean).length;
        
        // 返回原始文档加上匹配信息
        return {
          ...source,
          id: source.id || hit._id,
          match_score: hit._score,
          matched_fields: matchedFields,
          matched_fields_description: matchedFieldsDescription,
          matched_fields_count: matchedFieldsCount
        };
      });
      
      // 按匹配字段数量和匹配分数排序
      const sortedHits = processedHits.sort((a, b) => {
        if (b.matched_fields_count !== a.matched_fields_count) {
          return b.matched_fields_count - a.matched_fields_count;
        }
        return b.match_score - a.match_score;
      });
      
      // 构造返回结果
      const result: ConstellationSearchResponse = {
        total,
        page,
        limit,
        hits: sortedHits
      };
      
      return result;
    } catch (error) {
      this.logger.error(`搜索星座信息时出错: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`无法搜索星座信息: ${error.message}`);
    }
  }

  /**
   * 创建匹配字段描述
   * @param matchedFields 匹配字段对象
   * @returns 匹配字段描述数组
   */
  private createMatchFieldsDescription(matchedFields: any): Array<{field: string, matchLevel: string, score: number}> {
    const descriptions: Array<{field: string, matchLevel: string, score: number}> = [];
    
    // 添加关键词匹配描述
    if (matchedFields.keyword) {
      descriptions.push({
        field: 'keyword',
        matchLevel: '关键词匹配',
        score: 0.9
      });
    }
    
    if (matchedFields.targetName) {
      descriptions.push({
        field: 'targetName',
        matchLevel: '完全匹配',
        score: 1.0
      });
    }
    
    if (matchedFields.targetId) {
      descriptions.push({
        field: 'targetId',
        matchLevel: '完全匹配',
        score: 1.0
      });
    }
    
    if (matchedFields.organization) {
      descriptions.push({
        field: 'organization',
        matchLevel: '组织匹配',
        score: 0.8
      });
    }
    
    if (matchedFields.country) {
      descriptions.push({
        field: 'country',
        matchLevel: '国家匹配',
        score: 0.7
      });
    }
    
    if (matchedFields.purpose) {
      descriptions.push({
        field: 'purpose',
        matchLevel: '用途匹配',
        score: 0.6
      });
    }
    
    return descriptions;
  }

  /**
   * 获取星座名称集合
   * @returns 星座名称集合
   */
  async getConstellationNames(): Promise<ConstellationNamesResponseDto> {
    try {
      this.logger.debug('获取星座名称集合');
      
      const [newspaceResponse, n2yoResponse] = await Promise.all([
        this.elasticsearchService.search({
          index: 'constell_newspace',
          size: 1000,
          _source: ['constellation_name'],
          query: {
            exists: { field: 'constellation_name' }
          }
        }),
        this.elasticsearchService.search({
          index: 'constell_n2yo',
          size: 1000,
          _source: ['constellation_name'],
          query: {
            exists: { field: 'constellation_name' }
          }
        })
      ]);
      
      const newspaceNames = this.extractNames(newspaceResponse.hits.hits);
      const n2yoNames = this.extractNames(n2yoResponse.hits.hits);
      
      const allNames = [...new Set([...newspaceNames, ...n2yoNames])].sort();
      
      return {
        constellationNames: allNames,
        count: allNames.length
      };
    } catch (error) {
      this.logger.error(`获取星座名称集合时出错: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`无法获取星座名称集合: ${error.message}`);
    }
  }

  /**
   * 获取星座所属机构集合
   * @returns 星座所属机构集合
   */
  async getConstellationOrganizations(): Promise<ConstellationOrganizationsResponseDto> {
    try {
      this.logger.debug('获取星座所属机构集合');
      
      const [newspaceResponse, n2yoResponse] = await Promise.all([
        this.elasticsearchService.search({
          index: 'constell_newspace',
          size: 1000,
          _source: ['company_constellation', 'owner_info.owner'],
          query: {
            bool: {
              should: [
                { exists: { field: 'company_constellation' } },
                { exists: { field: 'owner_info.owner' } }
              ],
              minimum_should_match: 1
            }
          }
        }),
        this.elasticsearchService.search({
          index: 'constell_n2yo',
          size: 1000,
          _source: ['company'],
          query: {
            exists: { field: 'company' }
          }
        })
      ]);
      
      this.logger.debug(`从newspace索引找到 ${newspaceResponse.hits.hits.length} 条记录`);
      this.logger.debug(`从n2yo索引找到 ${n2yoResponse.hits.hits.length} 条记录`);
      
      // 从字符串中提取括号前的部分作为组织名称
      const extractOrgName = (fullName: string): string => {
        if (!fullName) return '';
        // 提取括号前的部分并去除首尾空格
        const match = fullName.match(/^(.*?)(\s*\(|$)/);
        return match ? match[1].trim() : fullName.trim();
      };
      
      // 从newspace索引中提取组织名称
      const newspaceOrgs: string[] = [];
      
      newspaceResponse.hits.hits.forEach(hit => {
        const source: any = hit._source;
        
        // 尝试从company_constellation直接提取
        if (typeof source.company_constellation === 'string') {
          const orgName = extractOrgName(source.company_constellation);
          if (orgName && orgName.length > 0) {
            newspaceOrgs.push(orgName);
          }
        } 
        // 尝试从company_constellation.company提取
        else if (source.company_constellation && typeof source.company_constellation.company === 'string') {
          const orgName = extractOrgName(source.company_constellation.company);
          if (orgName && orgName.length > 0) {
            newspaceOrgs.push(orgName);
          }
        }
        
        // 尝试从owner_info.owner提取
        if (source.owner_info && typeof source.owner_info.owner === 'string') {
          const orgName = extractOrgName(source.owner_info.owner);
          if (orgName && orgName.length > 0) {
            newspaceOrgs.push(orgName);
          }
        }
      });
      
      this.logger.debug(`从newspace索引提取了 ${newspaceOrgs.length} 个组织名称`);
      
      // 从n2yo索引中提取组织名称
      const n2yoOrgs: string[] = n2yoResponse.hits.hits
        .map(hit => {
          const source: any = hit._source;
          if (typeof source.company === 'string') {
            return extractOrgName(source.company);
          }
          return '';
        })
        .filter(org => org && org.length > 0);
      
      this.logger.debug(`从n2yo索引提取了 ${n2yoOrgs.length} 个组织名称`);
      
      // 合并两个索引的组织，去重并排序
      const allOrgs: string[] = [...new Set([...newspaceOrgs, ...n2yoOrgs])]
        .filter(org => org.length > 0)
        .sort();
      
      this.logger.debug(`找到 ${allOrgs.length} 个独特组织：${JSON.stringify(allOrgs.slice(0, 10))}...`);
      
      return {
        organizations: allOrgs,
        count: allOrgs.length
      };
    } catch (error) {
      this.logger.error(`获取星座所属机构集合时出错: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`无法获取星座所属机构集合: ${error.message}`);
    }
  }

  /**
   * 获取星座用途集合
   * @returns 星座用途集合
   */
  async getConstellationPurposes(): Promise<ConstellationPurposesResponseDto> {
    try {
      this.logger.debug('获取星座用途集合');
      
      const [newspaceResponse, n2yoResponse] = await Promise.all([
        this.elasticsearchService.search({
          index: 'constell_newspace',
          size: 1000,
          _source: ['purpose', 'applications'],
          query: {
            bool: {
              should: [
                { exists: { field: 'purpose' } },
                { exists: { field: 'applications' } }
              ],
              minimum_should_match: 1
            }
          }
        }),
        this.elasticsearchService.search({
          index: 'constell_n2yo',
          size: 1000,
          _source: ['purpose'],
          query: {
            exists: { field: 'purpose' }
          }
        })
      ]);
      
      this.logger.debug(`从newspace索引找到 ${newspaceResponse.hits.hits.length} 条记录`);
      this.logger.debug(`从n2yo索引找到 ${n2yoResponse.hits.hits.length} 条记录`);
      
      // 从newspace索引中提取用途
      const newspacePurposes: string[] = [];
      
      newspaceResponse.hits.hits.forEach(hit => {
        const source: any = hit._source;
        
        // 尝试从purpose字段提取
        if (source.purpose) {
          if (typeof source.purpose === 'string') {
            // 处理逗号分隔的多个用途
            const purposeValues = source.purpose.split(',').map((p: string) => p.trim());
            purposeValues.forEach((p: string) => {
              if (p && p.length > 0) {
                newspacePurposes.push(p);
              }
            });
          } else if (Array.isArray(source.purpose)) {
            source.purpose.forEach((p: string) => {
              if (p && p.length > 0) {
                newspacePurposes.push(p);
              }
            });
          }
        }
        
        // 尝试从applications字段提取
        if (source.applications) {
          if (typeof source.applications === 'string') {
            // 处理逗号分隔的多个用途
            const appValues = source.applications.split(',').map((p: string) => p.trim());
            appValues.forEach((p: string) => {
              if (p && p.length > 0) {
                newspacePurposes.push(p);
              }
            });
          } else if (Array.isArray(source.applications)) {
            source.applications.forEach((p: string) => {
              if (p && p.length > 0) {
                newspacePurposes.push(p);
              }
            });
          }
        }
      });
      
      this.logger.debug(`从newspace索引提取了 ${newspacePurposes.length} 个用途`);
      
      // 从n2yo索引中提取用途
      const n2yoPurposes: string[] = [];
      
      n2yoResponse.hits.hits.forEach(hit => {
        const source: any = hit._source;
        
        if (source.purpose) {
          if (typeof source.purpose === 'string') {
            // 处理逗号分隔的多个用途
            const purposeValues = source.purpose.split(',').map((p: string) => p.trim());
            purposeValues.forEach((p: string) => {
              if (p && p.length > 0) {
                n2yoPurposes.push(p);
              }
            });
          } else if (Array.isArray(source.purpose)) {
            source.purpose.forEach((p: string) => {
              if (p && p.length > 0) {
                n2yoPurposes.push(p);
              }
            });
          }
        }
      });
      
      this.logger.debug(`从n2yo索引提取了 ${n2yoPurposes.length} 个用途`);
      
      // 添加我们已知的通用用途（从getPurposeVariants方法中提取）
      const commonPurposes = [
        '通信', 'Communication', 'Telecommunications', 'Comms', '电信',
        '导航', 'Navigation', 'Positioning', 'GPS', 'GNSS', '定位',
        '地球观测', 'Earth Observation', 'Remote Sensing', 'EO', '遥感',
        '科学研究', 'Scientific Research', 'Science', 'Research', '科研',
        '技术验证', 'Technology Demonstration', 'Tech Demo', '技术演示',
        '互联网', 'Internet', '网络', 'Web', 'Net'
      ];
      
      // 合并所有用途，去重并排序
      const allPurposes: string[] = [...new Set([...newspacePurposes, ...n2yoPurposes, ...commonPurposes])]
        .filter(purpose => purpose && purpose.length > 0)
        .sort();
      
      this.logger.debug(`找到 ${allPurposes.length} 个独特用途：${JSON.stringify(allPurposes.slice(0, 10))}...`);
      
      return {
        purposes: allPurposes,
        count: allPurposes.length
      };
    } catch (error) {
      this.logger.error(`获取星座用途集合时出错: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`无法获取星座用途集合: ${error.message}`);
    }
  }

  /**
   * 从查询结果中提取名称
   * @param hits 查询结果
   * @returns 名称数组
   */
  private extractNames(hits: any[]): string[] {
    return hits
      .map(hit => {
        const source: any = hit._source;
        return source?.constellation_name;
      })
      .filter(name => name && typeof name === 'string');
  }

  /**
   * 计算匹配字段
   * @param hits 命中结果数组
   * @param queryDto 查询参数
   * @returns 匹配字段对象
   */
  private calculateMatchedFields(hits: any[], queryDto: ConstellationQueryDto): any {
    const { keyword, targetName, targetId, country, organization, purpose } = queryDto;
    const matchedFields: any = {};
    
    // 检查关键词匹配
    if (keyword) {
      // 检查关键词是否匹配星座名称、公司名称等
      matchedFields.keyword = hits.some(hit => {
        const source = hit.source;
        return (
          this.checkFieldMatch(source, 'constellation_name', keyword) ||
          this.checkFieldMatch(source, 'company_constellation', keyword) ||
          this.checkFieldMatch(source, 'company', keyword) ||
          this.checkFieldMatch(source, 'owner_info.owner', keyword) ||
          this.checkFieldMatch(source, 'purpose', keyword)
        );
      });
    }
    
    // 检查目标名称匹配
    if (targetName) {
      matchedFields.targetName = hits.some(hit => 
        this.checkFieldMatch(hit.source, 'constellation_name', targetName)
      );
    }
    
    // 检查目标ID匹配
    if (targetId) {
      matchedFields.targetId = hits.some(hit => 
        Array.isArray(hit.source.satellites) && 
        hit.source.satellites.some((sat: any) => sat.norad_id === targetId)
      );
    }
    
    // 检查组织匹配 - 添加对 owner_info.owner 的检查
    if (organization) {
      matchedFields.organization = hits.some(hit => 
        this.checkFieldMatch(hit.source, 'company_constellation', organization) ||
        this.checkFieldMatch(hit.source, 'company', organization) ||
        this.checkFieldMatch(hit.source, 'owner_info.owner', organization)
      );
    }
    
    // 检查国家匹配
    if (country) {
      matchedFields.country = hits.some(hit => 
        this.checkFieldMatch(hit.source, 'owner_info.country', country) ||
        this.checkFieldMatch(hit.source, 'country', country)
      );
    }
    
    // 检查用途匹配
    if (purpose) {
      matchedFields.purpose = hits.some(hit => 
        this.checkFieldMatch(hit.source, 'purpose', purpose) ||
        this.checkFieldMatch(hit.source, 'applications', purpose)
      );
    }
    
    return matchedFields;
  }

  /**
   * 检查字段是否匹配
   * @param source 源数据
   * @param fieldPath 字段路径
   * @param value 要匹配的值
   * @returns 是否匹配
   */
  private checkFieldMatch(source: any, fieldPath: string, value: string): boolean {
    const fieldValue = this.getNestedValue(source, fieldPath);
    
    if (fieldValue === undefined || fieldValue === null) {
      return false;
    }
    
    // 将字段值转换为字符串进行比较
    const fieldValueStr = String(fieldValue).toLowerCase();
    const valueStr = value.toLowerCase();
    
    // 检查完全匹配
    if (fieldValueStr === valueStr) {
      return true;
    }
    
    // 检查包含关系（模糊匹配）
    if (fieldValueStr.includes(valueStr) || valueStr.includes(fieldValueStr)) {
      return true;
    }
    
    // 检查同义词匹配（整体匹配）
    if (this.checkSynonyms(fieldValueStr, valueStr)) {
      return true;
    }
    
    // 如果字段值是逗号分隔的列表，检查每个部分是否匹配
    if (fieldValueStr.includes(',')) {
      const values = fieldValueStr.split(',').map(v => v.trim());
      
      // 检查列表中的每个值是否与查询值匹配（包括同义词匹配）
      return values.some(v => {
        // 直接匹配
        if (v === valueStr || v.includes(valueStr) || valueStr.includes(v)) {
          return true;
        }
        
        // 同义词匹配
        return this.checkSynonyms(v, valueStr);
      });
    }
    
    return false;
  }

  /**
   * 检查两个词是否是同义词
   * @param word1 第一个词
   * @param word2 第二个词
   * @returns 是否是同义词
   */
  private checkSynonyms(word1: string, word2: string): boolean {
    // 用途同义词映射
    const purposeSynonyms: Record<string, string[]> = {
      'internet': ['互联网', '网络', 'web', 'net', 'www'],
      'communication': ['通信', '通讯', 'comms', '电信', 'telecommunications'],
      'navigation': ['导航', '定位', 'positioning', 'gps', 'gnss'],
      'observation': ['观测', '观察', '监测', 'monitoring', 'earth observation', '地球观测', '遥感', 'remote sensing'],
      'research': ['研究', '科研', 'scientific', 'science', '科学'],
      'direct-to-cell': ['直接到手机', '卫星直连手机', '卫星到手机', 'satellite-to-cellphone', 'direct-to-device'],
      'relay': ['中继', '数据中继', 'data relay', 'orbital relay'],
    };
    
    // 国家同义词映射
    const countrySynonyms: Record<string, string[]> = {
      'us': ['usa', 'united states', 'america', '美国', '美利坚'],
      'china': ['prc', 'people\'s republic of china', '中国', '中华人民共和国'],
      'russia': ['russian federation', 'rf', '俄罗斯', '俄罗斯联邦'],
      'uk': ['united kingdom', 'great britain', 'england', '英国', '大不列颠'],
      'japan': ['jp', '日本'],
      // 添加更多国家映射...
    };
    
    // 检查用途同义词匹配
    if (purposeSynonyms[word1] && purposeSynonyms[word1].includes(word2)) {
      return true;
    }
    
    // 检查国家同义词匹配
    if (countrySynonyms[word1] && countrySynonyms[word1].includes(word2)) {
      return true;
    }
    
    return false;
  }

  /**
   * 获取国家名称的不同表示形式
   * @param country 国家名称
   * @returns 国家名称的不同表示形式数组
   */
  private getCountryVariants(country: string): string[] {
    // 国家名称同义词映射
    const countryMap: Record<string, string[]> = {
      'USA': ['United States', 'United States of America', 'US', 'America', '美国'],
      'China': ['PRC', 'People\'s Republic of China', '中国', '中华人民共和国'],
      'Russia': ['Russian Federation', 'RF', '俄罗斯', '俄罗斯联邦'],
      'UK': ['United Kingdom', 'Great Britain', 'England', '英国', '大不列颠'],
      'Japan': ['JP', '日本'],
      // 添加更多国家映射...
    };
    
    // 查找输入国家的所有变体
    for (const [key, variants] of Object.entries(countryMap)) {
      if (key.toLowerCase() === country.toLowerCase() || 
          variants.some(v => v.toLowerCase() === country.toLowerCase())) {
        return [key, ...variants];
      }
    }
    
    // 如果没有找到映射，返回原始输入
    return [country];
  }

  /**
   * 获取用途的不同表示形式
   * @param purpose 用途名称
   * @returns 用途的不同表示形式数组
   */
  private getPurposeVariants(purpose: string): string[] {
    // 用途同义词映射
    const purposeMap: Record<string, string[]> = {
      '通信': ['Communication', 'Telecommunications', 'Comms', '电信'],
      '导航': ['Navigation', 'Positioning', 'GPS', 'GNSS', '定位'],
      '地球观测': ['Earth Observation', 'Remote Sensing', 'EO', '遥感'],
      '科学研究': ['Scientific Research', 'Science', 'Research', '科研'],
      '技术验证': ['Technology Demonstration', 'Tech Demo', '技术演示'],
      // 添加更多用途映射...
    };
    
    // 查找输入用途的所有变体
    for (const [key, variants] of Object.entries(purposeMap)) {
      if (key.toLowerCase() === purpose.toLowerCase() || 
          variants.some(v => v.toLowerCase() === purpose.toLowerCase())) {
        return [key, ...variants];
      }
    }
    
    // 如果没有找到映射，返回原始输入
    return [purpose];
  }

  /**
   * 合并字段值，处理来自不同索引的相同字段
   * @param hits 命中结果数组
   * @param fieldPath 字段路径
   * @param alternateFieldPath 备选字段路径
   * @returns 合并后的字段值
   */
  private mergeFieldValues(hits: any[], fieldPath: string, alternateFieldPath?: string): any {
    const values = new Map<string, {value: any, sources: string[]}>();
    
    hits.forEach(hit => {
      // 获取字段值
      let value = this.getNestedValue(hit.source, fieldPath);
      
      // 如果主字段没有值，尝试备选字段
      if (value === undefined && alternateFieldPath) {
        value = this.getNestedValue(hit.source, alternateFieldPath);
      }
      
      if (value !== undefined) {
        const valueStr = typeof value === 'object' ? JSON.stringify(value) : String(value);
        
        if (!values.has(valueStr)) {
          values.set(valueStr, {value, sources: [hit.index]});
        } else {
          const existing = values.get(valueStr);
          if (existing && !existing.sources.includes(hit.index)) {
            existing.sources.push(hit.index);
          }
        }
      }
    });
    
    // 如果只有一个值，直接返回
    if (values.size === 1) {
      return Array.from(values.values())[0].value;
    }
    
    // 如果有多个值，返回带索引来源的对象
    if (values.size > 1) {
      return Array.from(values.entries()).map(([_, {value, sources}]) => ({
        value,
        sources
      }));
    }
    
    // 没有值则返回undefined
    return undefined;
  }

  /**
   * 获取嵌套对象的值
   * @param obj 对象
   * @param path 路径，如 'a.b.c'
   * @returns 值或undefined
   */
  private getNestedValue(obj: any, path: string): any {
    if (!obj) return undefined;
    
    const parts = path.split('.');
    let current = obj;
    
    for (const part of parts) {
      if (current === undefined || current === null) {
        return undefined;
      }
      current = current[part];
    }
    
    return current;
  }

  /**
   * 搜索星座信息
   * @param query 星座查询参数
   * @returns 星座搜索结果
   */
  async searchConstellations(query: any): Promise<any> {
    try {
      const { page = 1, limit = 10, sort, ...filters } = query;
      const sortField = sort?.field || 'name';
      const sortOrder = sort?.order || 'asc';
      
      // 构建查询条件
      const must = this.buildConstellationQueryConditions(filters);
      
      // 执行查询
      const searchResult = await this.elasticsearchService.search({
        index: this.constellationIndex,
        body: {
          query: {
            bool: {
              must,
            },
          },
          sort: [
            {
              [sortField]: {
                order: sortOrder,
              },
            },
          ],
          from: (page - 1) * limit,
          size: limit,
        },
      });
      
      // 处理结果
      const total = typeof searchResult.hits.total === 'number' 
        ? searchResult.hits.total 
        : (searchResult.hits.total as SearchTotalHits).value;
      
      const items = searchResult.hits.hits.map((hit: any) => ({
        ...hit._source,
        _id: hit._id,
      }));
      
      return {
        total,
        page,
        size: limit,
        items,
      };
    } catch (error) {
      this.logger.error(`搜索星座信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取星座详情
   * @param id 星座ID
   * @returns 星座详情
   */
  async getConstellationById(id: string): Promise<any> {
    try {
      const result = await this.elasticsearchService.search({
        index: this.constellationIndex,
        body: {
          query: {
            term: {
              _id: id,
            },
          },
        },
      });
      
      if (result.hits.hits.length === 0) {
        return null;
      }
      
      const hit = result.hits.hits[0];
      const source = hit._source as Record<string, any>;
      return {
        ...source,
        _id: hit._id,
      };
    } catch (error) {
      this.logger.error(`获取星座详情失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取星座成员
   * @param constellationId 星座ID
   * @returns 星座成员列表
   */
  async getConstellationMembers(constellationId: string): Promise<any[]> {
    try {
      // 查询卫星索引，获取属于该星座的卫星
      const result = await this.elasticsearchService.search({
        index: 'satellite_info',
        body: {
          query: {
            term: {
              constellation_id: constellationId,
            },
          },
          size: 1000, // 限制返回数量
        },
      });
      
      return result.hits.hits.map((hit: any) => {
        const source = hit._source as Record<string, any>;
        return {
          ...source,
          _id: hit._id,
        };
      });
    } catch (error) {
      this.logger.error(`获取星座成员失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 构建星座查询条件
   * @param filters 过滤条件
   * @returns 查询条件数组
   * @private
   */
  private buildConstellationQueryConditions(filters: any): any[] {
    const must = [];
    
    // 添加名称过滤
    if (filters.name) {
      must.push({
        match: {
          name: {
            query: filters.name,
            fuzziness: 'AUTO',
          },
        },
      });
    }
    
    // 添加运营商过滤
    if (filters.operator) {
      must.push({
        match: {
          operator: {
            query: filters.operator,
            fuzziness: 'AUTO',
          },
        },
      });
    }
    
    // 添加国家过滤
    if (filters.country) {
      // 创建国家名称的同义词映射
      const countryVariants = this.getCountryVariants(filters.country);
      
      // 使用should查询匹配任一变体
      const countryQueries = countryVariants.map(variant => ({
        match: {
          'owner_info.country': {
            query: variant,
            fuzziness: 'AUTO'
          }
        }
      }));
      
      must.push({
        bool: {
          should: countryQueries,
          minimum_should_match: 1
        }
      });
    }
    
    // 添加类型过滤
    if (filters.type) {
      must.push({
        match: {
          type: filters.type,
        },
      });
    }
    
    return must.length ? must : [{ match_all: {} }];
  }

  /**
   * 过滤constell_n2yo索引中相同constellation_name的文档，只保留_id时间戳最新的那一个
   * @param hits 命中结果数组
   * @returns 过滤后的命中结果数组
   */
  private filterLatestN2YOHits(hits: any[]): any[] {
    // 按索引分组
    const hitsByIndex: Record<string, any[]> = {};
    
    hits.forEach(hit => {
      const index = hit.index;
      if (!hitsByIndex[index]) {
        hitsByIndex[index] = [];
      }
      hitsByIndex[index].push(hit);
    });
    
    // 处理constell_n2yo索引
    if (hitsByIndex['constell_n2yo']) {
      // 提取时间戳并排序
      const n2yoHits = hitsByIndex['constell_n2yo'];
      
      // 按_id中的时间戳部分排序（假设_id格式为：时间戳_其他信息）
      n2yoHits.sort((a, b) => {
        const timestampA = this.extractTimestampFromId(a.id);
        const timestampB = this.extractTimestampFromId(b.id);
        return timestampB - timestampA; // 降序排列，最新的在前
      });
      
      // 只保留第一个（最新的）
      hitsByIndex['constell_n2yo'] = [n2yoHits[0]];
    }
    
    // 合并所有索引的结果
    return Object.values(hitsByIndex).flat();
  }
  
  /**
   * 从_id中提取时间戳部分
   * @param id _id字段值
   * @returns 时间戳数值
   */
  private extractTimestampFromId(id: string): number {
    // 假设_id格式为：时间戳_其他信息
    // 或者其他可以提取时间信息的格式
    const timestampPart = id.split('_')[0];
    return parseInt(timestampPart, 10) || 0; // 如果无法解析，返回0
  }

  // ... 其他辅助方法 ...
} 