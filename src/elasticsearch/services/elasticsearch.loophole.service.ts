import { Injectable, Logger } from '@nestjs/common';
import { ElasticsearchBaseService } from './elasticsearch.base.service';
import { LoopholeQueryDto } from '../dto/loophole-query.dto';
import { LoopholeSearchResponse, LoopholeDocument, ESLoopholeDocument } from '../types/loophole.types';
import { SearchTotalHits } from '@elastic/elasticsearch/lib/api/types';

/**
 * Elasticsearch漏洞查询服务
 * 提供漏洞信息的查询功能
 */
@Injectable()
export class ElasticsearchLoopholeService {
  private readonly logger = new Logger(ElasticsearchLoopholeService.name);
  private readonly loopholeIndex = 'cve';

  constructor(private readonly esBaseService: ElasticsearchBaseService) {}

  /**
   * 搜索漏洞信息
   * @param query 漏洞查询参数
   * @returns 漏洞搜索结果
   */
  async searchLoopholeInfo(query: LoopholeQueryDto): Promise<LoopholeSearchResponse> {
    try {
      this.logger.debug(`搜索漏洞信息，参数: ${JSON.stringify(query)}`);
      
      // 构建查询条件
      const must: any[] = [];
      
      // ID精确匹配
      if (query._id) {
        must.push({ 
          term: { 
            "cveMetadata.cveId.keyword": query._id 
          } 
        });
      }
      
      // 关键词搜索
      if (query.keywords) {
        must.push({
          multi_match: {
            query: query.keywords,
            fields: [
              "cveMetadata.cveId^3",
              "containers.cna.descriptions.value^2",
              "containers.cna.affected.vendor",
              "containers.cna.affected.product",
              "containers.cna.problemTypes.descriptions.description"
            ],
            type: "best_fields",
            fuzziness: "AUTO"
          }
        });
      }
      
      // 如果没有任何查询条件，则匹配所有文档
      if (must.length === 0) {
        must.push({ match_all: {} });
      }
      
      // 执行查询
      const searchResult = await this.esBaseService.search<ESLoopholeDocument>({
        index: this.loopholeIndex,
        body: {
          query: {
            bool: {
              must
            }
          },
          sort: [
            { "cveMetadata.datePublished": { order: "desc" } }
          ],
          size: 100
        }
      });
      
      // 处理结果
      const total = typeof searchResult.hits.total === 'number' 
        ? searchResult.hits.total 
        : (searchResult.hits.total as SearchTotalHits).value;
      
      const hits = searchResult.hits.hits.map(hit => {
        const source = hit._source as ESLoopholeDocument;
        const hitId = hit._id || '';
        
        // 转换为API响应格式
        const loopholeDoc: LoopholeDocument = {
          descriptions: source.containers.cna.descriptions?.[0]?.value || '',
          affected: this.formatAffected(source.containers.cna.affected),
          severity: this.getSeverity(source),
          cve_id: source.cveMetadata.cveId,
          published_date: source.cveMetadata.datePublished,
          patch_status: this.getPatchStatus(source),
          patch_version: this.getPatchVersion(source),
          references: source.containers.cna.references?.map(ref => ref.url) || [],
          mitigation: source.containers.cna.solutions?.[0]?.value || ''
        };
        
        return {
          ...loopholeDoc,
          _id: hitId
        };
      });
      
      return {
        total,
        hits: hits.filter(hit => hit._id !== '') as Array<LoopholeDocument & { _id: string }>
      };
    } catch (error) {
      this.logger.error(`搜索漏洞信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }
  
  /**
   * 格式化受影响组件信息
   * @param affected 受影响组件数组
   * @returns 格式化后的字符串
   * @private
   */
  private formatAffected(affected: ESLoopholeDocument['containers']['cna']['affected']): string {
    if (!affected || affected.length === 0) {
      return '';
    }
    
    return affected.map(item => {
      const versions = item.versions?.map(v => v.version).join(', ') || '';
      return `${item.vendor} ${item.product} ${versions}`;
    }).join('; ');
  }
  
  /**
   * 获取漏洞严重程度
   * @param source 漏洞文档
   * @returns 严重程度
   * @private
   */
  private getSeverity(source: ESLoopholeDocument): string {
    const metrics = source.containers.cna.metrics;
    if (metrics && metrics.length > 0) {
      return metrics[0].severity || '未知';
    }
    
    // 尝试从问题类型描述中提取严重程度
    const problemTypes = source.containers.cna.problemTypes;
    if (problemTypes && problemTypes.length > 0) {
      const descriptions = problemTypes[0].descriptions;
      if (descriptions && descriptions.length > 0) {
        const desc = descriptions[0].description.toLowerCase();
        if (desc.includes('critical')) return '严重';
        if (desc.includes('high')) return '高';
        if (desc.includes('medium')) return '中';
        if (desc.includes('low')) return '低';
      }
    }
    
    return '未知';
  }
  
  /**
   * 获取补丁状态
   * @param source 漏洞文档
   * @returns 补丁状态
   * @private
   */
  private getPatchStatus(source: ESLoopholeDocument): string {
    const solutions = source.containers.cna.solutions;
    if (solutions && solutions.length > 0 && solutions[0].value) {
      const solutionText = solutions[0].value.toLowerCase();
      if (solutionText.includes('patch') || solutionText.includes('update') || solutionText.includes('upgrade')) {
        return '已修复';
      }
    }
    
    return '未修复';
  }
  
  /**
   * 获取补丁版本
   * @param source 漏洞文档
   * @returns 补丁版本
   * @private
   */
  private getPatchVersion(source: ESLoopholeDocument): string | null {
    const solutions = source.containers.cna.solutions;
    if (solutions && solutions.length > 0 && solutions[0].value) {
      const solutionText = solutions[0].value;
      
      // 尝试从解决方案中提取版本号
      const versionMatch = solutionText.match(/version\s+(\d+\.\d+(\.\d+)*)/i);
      if (versionMatch) {
        return versionMatch[1];
      }
      
      // 尝试提取其他格式的版本号
      const otherVersionMatch = solutionText.match(/(\d+\.\d+(\.\d+)*)/);
      if (otherVersionMatch) {
        return otherVersionMatch[1];
      }
    }
    
    return null;
  }
} 