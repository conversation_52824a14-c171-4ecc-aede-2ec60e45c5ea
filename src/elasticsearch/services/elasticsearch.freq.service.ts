import { Injectable, Logger } from '@nestjs/common';
import { ElasticsearchBaseService } from './elasticsearch.base.service';
import { FreqQueryDto } from '../dto/freq-query.dto';
import { SearchTotalHits } from '@elastic/elasticsearch/lib/api/types';

/**
 * Elasticsearch频率查询服务
 * 提供频率信息的查询功能
 */
@Injectable()
export class ElasticsearchFreqService {
  private readonly logger = new Logger(ElasticsearchFreqService.name);
  private readonly freqIndex = 'freq_info';

  constructor(private readonly esBaseService: ElasticsearchBaseService) {}

  /**
   * 搜索频率信息
   * @param query 频率查询参数
   * @returns 频率搜索结果
   */
  async searchFrequencies(query: FreqQueryDto & {
    freq_min?: number;
    freq_max?: number;
    service_type?: string;
    country?: string;
    limit?: number;
    page?: number;
  }): Promise<any> {
    try {
      const { page = 1, limit = 10, ...filters } = query;
      
      // 构建查询条件
      const must = this.buildFreqQueryConditions(filters);
      
      // 执行查询
      const searchResult = await this.esBaseService.search({
        index: this.freqIndex,
        body: {
          query: {
            bool: {
              must,
            },
          },
          sort: [
            {
              freq: {
                order: 'asc',
              },
            },
          ],
          from: (page - 1) * limit,
          size: limit,
        },
      });
      
      // 处理结果
      const total = typeof searchResult.hits.total === 'number' 
        ? searchResult.hits.total 
        : (searchResult.hits.total as SearchTotalHits).value;
      
      const items = searchResult.hits.hits.map((hit: any) => ({
        ...hit._source,
        _id: hit._id,
      }));
      
      return {
        total,
        page,
        size: limit,
        items,
      };
    } catch (error) {
      this.logger.error(`搜索频率信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取服务类型列表
   * @returns 服务类型列表
   */
  async getServiceTypes(): Promise<string[]> {
    try {
      const result = await this.esBaseService.search({
        index: this.freqIndex,
        body: {
          size: 0,
          aggs: {
            service_types: {
              terms: {
                field: 'service_type.keyword',
                size: 1000,
              },
            },
          },
        },
      });
      
      const serviceTypes = result.aggregations && 'service_types' in result.aggregations 
        ? (result.aggregations.service_types as any).buckets || [] 
        : [];
      return serviceTypes.map((bucket: any) => bucket.key);
    } catch (error) {
      this.logger.error(`获取服务类型列表失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取国家列表
   * @returns 国家列表
   */
  async getCountries(): Promise<string[]> {
    try {
      const result = await this.esBaseService.search({
        index: this.freqIndex,
        body: {
          size: 0,
          aggs: {
            countries: {
              terms: {
                field: 'country.keyword',
                size: 1000,
              },
            },
          },
        },
      });
      
      const countries = result.aggregations && 'countries' in result.aggregations 
        ? (result.aggregations.countries as any).buckets || [] 
        : [];
      return countries.map((bucket: any) => bucket.key);
    } catch (error) {
      this.logger.error(`获取国家列表失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取卫星列表
   * @returns 卫星列表
   */
  async getSatellites(): Promise<string[]> {
    try {
      const result = await this.esBaseService.search({
        index: this.freqIndex,
        body: {
          size: 0,
          aggs: {
            satellites: {
              terms: {
                field: 'sat_name.keyword',
                size: 1000,
              },
            },
          },
        },
      });
      
      const satellites = result.aggregations && 'satellites' in result.aggregations 
        ? (result.aggregations.satellites as any).buckets || [] 
        : [];
      return satellites.map((bucket: any) => bucket.key);
    } catch (error) {
      this.logger.error(`获取卫星列表失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 构建频率查询条件
   * @param filters 过滤条件
   * @returns 查询条件数组
   * @private
   */
  private buildFreqQueryConditions(filters: Partial<FreqQueryDto> & {
    freq_min?: number;
    freq_max?: number;
    service_type?: string;
    country?: string;
  }): any[] {
    const must = [];
    
    // 添加卫星名称过滤
    if (filters.sat_name) {
      must.push({
        match: {
          sat_name: {
            query: filters.sat_name,
            fuzziness: 'AUTO',
          },
        },
      });
    }
    
    // 添加频率范围过滤
    if (filters.freq_min || filters.freq_max) {
      const rangeQuery: any = {};
      
      if (filters.freq_min) {
        rangeQuery.gte = filters.freq_min;
      }
      
      if (filters.freq_max) {
        rangeQuery.lte = filters.freq_max;
      }
      
      must.push({
        range: {
          freq: rangeQuery,
        },
      });
    }
    
    // 添加服务类型过滤
    if (filters.service_type) {
      must.push({
        match: {
          service_type: filters.service_type,
        },
      });
    }
    
    // 添加国家过滤
    if (filters.country) {
      must.push({
        match: {
          country: filters.country,
        },
      });
    }
    
    return must.length ? must : [{ match_all: {} }];
  }
} 