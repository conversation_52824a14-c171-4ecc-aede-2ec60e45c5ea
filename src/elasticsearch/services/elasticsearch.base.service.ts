import { Injectable, Logger } from '@nestjs/common';
import { ElasticsearchService as NestElasticsearchService } from '@nestjs/elasticsearch';
import { SearchResponse } from '@elastic/elasticsearch/lib/api/types';

/**
 * Elasticsearch基础服务类
 * 提供共享功能和通用方法
 */
@Injectable()
export class ElasticsearchBaseService {
  protected readonly logger = new Logger(this.constructor.name);

  constructor(
    protected readonly elasticsearchService: NestElasticsearchService
  ) {
    this.testConnection();
  }

  /**
   * 测试Elasticsearch连接
   */
  private async testConnection() {
    try {
      const info = await this.elasticsearchService.info();
      this.logger.log(`成功连接到Elasticsearch: ${info.name}`);
    } catch (error) {
      this.logger.error(`无法连接到Elasticsearch: ${error.message}`, error.stack);
    }
  }

  /**
   * 执行搜索查询
   * @param params 搜索参数
   * @returns 搜索结果
   */
  async search<T = any>(params: {
    index: string;
    body: any;
  }): Promise<SearchResponse<T>> {
    try {
      return await this.elasticsearchService.search(params);
    } catch (error) {
      this.logger.error(`搜索失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 标准化日期字符串
   * @param dateStr 日期字符串
   * @returns 标准化后的日期字符串或null
   */
  protected normalizeDate(dateStr: string | null | undefined): string | null {
    if (!dateStr) return null;
    
    // 尝试解析日期
    try {
      // 处理常见的日期格式
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return null;
      
      // 返回ISO格式的日期字符串
      return date.toISOString().split('T')[0];
    } catch (error) {
      this.logger.warn(`无法解析日期: ${dateStr}`);
      return null;
    }
  }

  /**
   * 计算字符串相似度
   * @param str1 字符串1
   * @param str2 字符串2
   * @returns 相似度分数 (0-1)
   */
  protected calculateStringSimilarity(str1: string, str2: string): number {
    if (!str1 || !str2) return 0;
    
    const s1 = this.normalizeString(str1);
    const s2 = this.normalizeString(str2);
    
    if (s1 === s2) return 1;
    
    // 计算编辑距离
    const m = s1.length;
    const n = s2.length;
    const dp: number[][] = Array(m + 1).fill(0).map(() => Array(n + 1).fill(0));
    
    for (let i = 0; i <= m; i++) dp[i][0] = i;
    for (let j = 0; j <= n; j++) dp[0][j] = j;
    
    for (let i = 1; i <= m; i++) {
      for (let j = 1; j <= n; j++) {
        if (s1[i - 1] === s2[j - 1]) {
          dp[i][j] = dp[i - 1][j - 1];
        } else {
          dp[i][j] = Math.min(
            dp[i - 1][j] + 1,    // 删除
            dp[i][j - 1] + 1,    // 插入
            dp[i - 1][j - 1] + 1 // 替换
          );
        }
      }
    }
    
    return 1 - dp[m][n] / Math.max(m, n);
  }

  /**
   * 标准化字符串用于比较
   * @param str 输入字符串
   * @returns 标准化后的字符串
   */
  protected normalizeString(str: string): string {
    return str
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '')
      .trim();
  }
} 