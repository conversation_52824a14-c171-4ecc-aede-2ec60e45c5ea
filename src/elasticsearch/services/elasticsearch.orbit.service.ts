import { Injectable, Logger } from '@nestjs/common';
import { ElasticsearchService as NestElasticsearchService } from '@nestjs/elasticsearch';
import { ElasticsearchBaseService } from './elasticsearch.base.service';
import { OrbitQueryDto, OrbitType } from '../dto/orbit-query.dto';
import { SearchTotalHits } from '@elastic/elasticsearch/lib/api/types';
import { 
  TleQueryConfig, 
  getTleQueryConfig, 
  validateTleQueryConfig,
  mergeTleQueryConfig 
} from '../../../config/tle-query.config';

/**
 * 轨道数据查询服务
 * 处理轨道数据的查询和处理
 */
@Injectable()
export class ElasticsearchOrbitService extends ElasticsearchBaseService {
  protected readonly logger = new Logger(ElasticsearchOrbitService.name);
  private readonly orbitIndex = 'orbit_info';
  private tleQueryConfig: TleQueryConfig;

  constructor(
    elasticsearchService: NestElasticsearchService
  ) {
    super(elasticsearchService);
    
    // 加载TLE查询配置
    this.tleQueryConfig = getTleQueryConfig();
    
    // 验证配置
    try {
      validateTleQueryConfig(this.tleQueryConfig);
      this.logger.log(`TLE查询配置加载完成: 时间窗口=${this.tleQueryConfig.timeWindowMinutes}分钟, 最大记录数=${this.tleQueryConfig.maxRecords}`);
    } catch (error) {
      this.logger.error(`TLE查询配置验证失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 根据高度确定轨道类型
   * @param altitude 高度（千米）
   * @returns 轨道类型
   */
  private getOrbitType(altitude: number): OrbitType {
    if (altitude < 2000) {
      return OrbitType.LEO; // 低地轨道
    } else if (altitude < 35786) {
      return OrbitType.MEO; // 中地轨道
    } else {
      return OrbitType.GEO; // 地球同步轨道
    }
  }

  /**
   * 搜索轨道信息
   * @param queryDto 查询参数
   * @returns 查询结果
   */
  async searchOrbitInfo(queryDto: OrbitQueryDto) {
    try {
      const { 
        page = 1, 
        limit = 10, 
        satellite_name, 
        fuzzy_match = false, 
        orbit_type, 
        min_altitude, 
        max_altitude, 
        min_inclination, 
        max_inclination, 
        min_raan, 
        max_raan, 
        norad_id, 
        cospar_id 
      } = queryDto;

      this.logger.log('开始轨道信息查询...');
      this.logger.debug(`查询参数: ${JSON.stringify(queryDto)}`);

      let response = null;
      
      // 按照优先级顺序查询: norad_id > cospar_id > satellite_name

      // 1. 首先尝试使用 norad_id 查询
      if (norad_id !== undefined) {
        this.logger.debug(`尝试使用norad_id(${norad_id})查询`);
        
        response = await this.elasticsearchService.search({
          index: 'orbital_tle',
          body: {
            query: {
              term: {
                norad_id: norad_id
              }
            },
            sort: [
              { 
                epoch: { 
                  order: 'desc' 
                } 
              },
              { 
                _score: { 
                  order: 'desc' 
                } 
              }
            ]
          },
          // 只返回最新的一条记录
          size: 1,
          from: 0
        });
        
        if (response.hits.hits.length > 0) {
          this.logger.debug(`通过norad_id(${norad_id})查找到${response.hits.hits.length}条结果`);
          // 找到结果，直接处理并返回
          return this.processSearchResults(response);
        }
      }
      
      // 2. 如果norad_id查询无结果，尝试使用cospar_id查询
      if (cospar_id) {
        this.logger.debug(`尝试使用cospar_id(${cospar_id})查询`);
        
        response = await this.elasticsearchService.search({
          index: 'orbital_tle',
          body: {
            query: {
              match_phrase: {
                cospar_id: cospar_id
              }
            },
            sort: [
              { 
                epoch: { 
                  order: 'desc' 
                } 
              },
              { 
                _score: { 
                  order: 'desc' 
                } 
              }
            ]
          },
          // 只返回最新的一条记录
          size: 1,
          from: 0
        });
        
        if (response.hits.hits.length > 0) {
          this.logger.debug(`通过cospar_id(${cospar_id})查找到${response.hits.hits.length}条结果`);
          // 找到结果，直接处理并返回
          return this.processSearchResults(response);
        }
      }
      
      // 3. 如果cospar_id查询无结果，尝试使用satellite_name查询
      if (satellite_name) {
        this.logger.debug(`尝试使用satellite_name(${satellite_name})查询`);
        
        let query;
        if (fuzzy_match) {
          query = {
            query_string: {
              query: `*${satellite_name}*`,
              fields: ["satellite_name"]
            }
          };
        } else {
          // 精确匹配：使用 match_phrase 查询确保完全匹配
          query = {
            match_phrase: {
              satellite_name: satellite_name
            }
          };
        }
        
        response = await this.elasticsearchService.search({
          index: 'orbital_tle',
          body: {
            query: query,
            sort: [
              { 
                epoch: { 
                  order: 'desc' 
                } 
              },
              { 
                _score: { 
                  order: 'desc' 
                } 
              }
            ]
          },
          // 只返回最新的一条记录
          size: 1,
          from: 0
        });
        
        if (response.hits.hits.length > 0) {
          this.logger.debug(`通过satellite_name(${satellite_name})查找到${response.hits.hits.length}条结果`);
          // 找到结果，直接处理并返回
          return this.processSearchResults(response);
        }
      }
      
      // 4. 检查是否有轨道参数查询条件
      const hasOrbitParams = orbit_type || 
        min_altitude !== undefined || max_altitude !== undefined ||
        min_inclination !== undefined || max_inclination !== undefined ||
        min_raan !== undefined || max_raan !== undefined;
      
      // 只有当用户明确提供了轨道参数时才执行轨道参数查询
      if (!hasOrbitParams) {
        this.logger.debug(`没有找到匹配的轨道数据，且未提供轨道参数，返回空结果`);
        // 返回空结果
        return {
          total: 0,
          hits: []
        };
      }
      
      this.logger.debug(`使用轨道参数查询`);
      
      const must = [];
      
      // 处理轨道类型查询
      if (orbit_type) {
        let altitudeRange: Record<string, number> = {};
        
        switch (orbit_type) {
          case OrbitType.LEO:
            altitudeRange = { lt: 2000 };
            break;
          case OrbitType.MEO:
            altitudeRange = { gte: 2000, lt: 35786 };
            break;
          case OrbitType.GEO:
            altitudeRange = { gte: 35786 };
            break;
        }
        
        must.push({
          range: {
            'orbital_elements.arg_alt_km': altitudeRange
          }
        });
      }

      // 处理高度范围查询
      if (min_altitude !== undefined || max_altitude !== undefined) {
        const altitudeRange: Record<string, number> = {};
        
        if (min_altitude !== undefined) {
          altitudeRange.gte = min_altitude;
        }
        
        if (max_altitude !== undefined) {
          altitudeRange.lte = max_altitude;
        }
        
        must.push({
          range: {
            'orbital_elements.arg_alt_km': altitudeRange
          }
        });
      }

      // 处理倾角范围查询
      if (min_inclination !== undefined || max_inclination !== undefined) {
        const inclinationRange: Record<string, number> = {};
        
        if (min_inclination !== undefined) {
          inclinationRange.gte = min_inclination;
        }
        
        if (max_inclination !== undefined) {
          inclinationRange.lte = max_inclination;
        }
        
        must.push({
          range: {
            'orbital_elements.inc_deg': inclinationRange
          }
        });
      }

      // 处理升交点赤经范围查询
      if (min_raan !== undefined || max_raan !== undefined) {
        const raanRange: Record<string, number> = {};
        
        if (min_raan !== undefined) {
          raanRange.gte = min_raan;
        }
        
        if (max_raan !== undefined) {
          raanRange.lte = max_raan;
        }
        
        must.push({
          range: {
            'orbital_elements.raan_deg': raanRange
          }
        });
      }
      
      // 构建查询
      const query = must.length > 0 
        ? { bool: { must } } 
        : { match_all: {} };
      
      // 执行查询  
      response = await this.elasticsearchService.search({
        index: 'orbital_tle',
        body: {
          query: query,
          sort: [
            { 
              epoch: { 
                order: 'desc' 
              } 
            },
            { 
              _score: { 
                order: 'desc' 
              } 
            }
          ]
        },
        size: limit,
        from: (page - 1) * limit
      });
      
      // 处理并返回结果
      return this.processSearchResults(response);
      
    } catch (error) {
      this.logger.error(`轨道信息查询出错: ${error.message}`, error.stack);
      this.logger.error(`查询参数: ${JSON.stringify(queryDto)}`);
      if (error.meta && error.meta.body) {
        this.logger.error(`ES错误详情: ${JSON.stringify(error.meta.body)}`);
      }
      throw new Error(`轨道信息查询失败: ${error.message}`);
    }
  }
  
  /**
   * 处理搜索结果
   * @param response Elasticsearch响应
   * @returns 处理后的结果
   */
  private processSearchResults(response: any) {
    // 记录查询结果数量
    const resultCount = response.hits.hits.length;
    this.logger.debug(`查询结果数量: ${resultCount}`);
    
    if (resultCount > 0) {
      // 记录第一条结果的关键信息，用于验证排序是否正确
      const firstHit = response.hits.hits[0]._source as any;
      this.logger.debug(`首条结果: norad_id=${firstHit?.norad_id || 'N/A'}, cospar_id=${firstHit?.cospar_id || 'N/A'}, satellite_name=${firstHit?.satellite_name || 'N/A'}, epoch=${firstHit?.epoch || 'N/A'}`);
    } else {
      this.logger.warn(`未找到匹配结果`);
    }

    // 处理查询结果
    const hits = response.hits.hits.map((hit: any) => {
      const source = hit._source;
      
      return {
        satellite_name: source.satellite_name,
        norad_id: source.norad_id,
        cospar_id: source.cospar_id,
        tle_raw: source.tle_raw,
        time: source.time,
        epoch: source.epoch,
        // 添加轨道计算相关字段
        mean_motion_dot: source.mean_motion_dot,
        mean_motion_ddot: source.mean_motion_ddot,
        drag_term: source.drag_term,
        orbital_elements: {
          inc_deg: source.orbital_elements?.inc_deg,
          raan_deg: source.orbital_elements?.raan_deg,
          ecc: source.orbital_elements?.ecc,
          arg_peri_deg: source.orbital_elements?.arg_peri_deg,
          mean_anom_deg: source.orbital_elements?.mean_anom_deg,
          day_laps: source.orbital_elements?.day_laps,
          orbit_num: source.orbital_elements?.orbit_num,
          orbital_period_min: source.orbital_elements?.orbital_period_min,
          sema_km: source.orbital_elements?.sema_km,
          arg_alt_km: source.orbital_elements?.arg_alt_km,
          arg_apo_deg: source.orbital_elements?.arg_apo_deg,
          launch_time: source.orbital_elements?.launch_time
        }
      };
    });

    // 返回结果
    const total = typeof response.hits.total === 'number' 
      ? response.hits.total 
      : (response.hits.total?.value || 0);
    
    return {
      total,
      hits
    };
  }

  /**
   * 搜索轨道信息
   * @param query 轨道查询参数
   * @returns 轨道搜索结果
   */
  async searchOrbits(query: any): Promise<any> {
    try {
      const { page = 1, limit = 10, sort, ...filters } = query;
      const sortField = sort?.field || 'epoch';
      const sortOrder = sort?.order || 'desc';
      
      // 构建查询条件
      const must = this.buildOrbitQueryConditions(filters);
      
      // 执行查询
      const searchResult = await this.elasticsearchService.search({
        index: this.orbitIndex,
        body: {
          query: {
            bool: {
              must,
            },
          },
          sort: [
            {
              [sortField]: {
                order: sortOrder,
              },
            },
          ],
          from: (page - 1) * limit,
          size: limit,
        },
      });
      
      // 处理结果
      const total = typeof searchResult.hits.total === 'number' 
        ? searchResult.hits.total 
        : (searchResult.hits.total as SearchTotalHits).value;
      
      const items = searchResult.hits.hits.map((hit: any) => ({
        ...hit._source,
        _id: hit._id,
      }));
      
      return {
        total,
        page,
        size: limit,
        items,
      };
    } catch (error) {
      this.logger.error(`搜索轨道信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取轨道历史
   * @param noradId NORAD ID
   * @param startDate 开始日期
   * @param endDate 结束日期
   * @returns 轨道历史数据
   */
  async getOrbitHistory(noradId: number, startDate?: string, endDate?: string): Promise<any[]> {
    try {
      const must = [];
      
      // 添加NORAD ID过滤
      must.push({
        term: {
          norad_id: noradId,
        },
      });
      
      // 添加时间范围过滤
      if (startDate || endDate) {
        const rangeQuery: any = {};
        
        if (startDate) {
          rangeQuery.gte = this.normalizeDate(startDate);
        }
        
        if (endDate) {
          rangeQuery.lte = this.normalizeDate(endDate);
        }
        
        must.push({
          range: {
            epoch: rangeQuery,
          },
        });
      }
      
      // 执行查询
      const searchResult = await this.elasticsearchService.search({
        index: this.orbitIndex,
        body: {
          query: {
            bool: {
              must,
            },
          },
          sort: [
            {
              epoch: {
                order: 'asc',
              },
            },
          ],
          size: 1000, // 限制返回数量
        },
      });
      
      // 处理结果
      return searchResult.hits.hits.map((hit: any) => {
        const source = hit._source as Record<string, any>;
        return {
          ...source,
          _id: hit._id,
        };
      });
    } catch (error) {
      this.logger.error(`获取轨道历史失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 构建轨道查询条件
   * @param filters 过滤条件
   * @returns 查询条件数组
   * @private
   */
  private buildOrbitQueryConditions(filters: any): any[] {
    const must = [];
    
    // 添加NORAD ID过滤
    if (filters.norad_id) {
      must.push({
        term: {
          norad_id: filters.norad_id,
        },
      });
    }
    
    // 添加时间范围过滤
    if (filters.start_date || filters.end_date) {
      const rangeQuery: any = {};
      
      if (filters.start_date) {
        rangeQuery.gte = this.normalizeDate(filters.start_date);
      }
      
      if (filters.end_date) {
        rangeQuery.lte = this.normalizeDate(filters.end_date);
      }
      
      must.push({
        range: {
          epoch: rangeQuery,
        },
      });
    }
    
    // 添加轨道类型过滤
    if (filters.orbit_type) {
      must.push({
        match: {
          orbit_type: filters.orbit_type,
        },
      });
    }
    
    return must.length ? must : [{ match_all: {} }];
  }

  /**
   * 标准化日期格式
   * @param date 日期字符串
   * @returns 标准化后的日期字符串
   * @protected
   */
  protected normalizeDate(date: string): string | null {
    if (!date) return null;
    
    try {
      // 尝试标准化日期格式
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) {
        throw new Error('无效的日期格式');
      }
      return dateObj.toISOString();
    } catch (error) {
      this.logger.warn(`日期格式化失败: ${date}, ${error.message}`);
      return null;
    }
  }

  /**
   * 批量查询卫星norad_id获取TLE数据
   * @param noradIds 卫星NORAD ID数组，如果为空则返回所有卫星TLE数据
   * @param page 页码，从1开始，仅在noradIds为空时生效
   * @param limit 每页记录数，最大10000，仅在noradIds为空时生效
   * @param useOneTimeQuery 是否使用一次性查询，仅在noradIds为空时生效，默认false
   * @returns 每个卫星的最新TLE数据（按epoch时间最新）
   */
  async getBulkSatelliteTleByNoradIds(
    noradIds: number[], 
    page: number = 1, 
    limit: number = 1000, 
    useOneTimeQuery: boolean = false
  ): Promise<any> {
    try {
      // 如果noradIds为空或未定义，查询所有卫星TLE数据
      if (!noradIds || noradIds.length === 0) {
        
        // 如果启用一次性查询，直接调用新方法
        if (useOneTimeQuery) {
          this.logger.log(`🚀 使用一次性查询模式获取所有卫星TLE数据`);
          return await this.getAllSatelliteTleData();
        }
        
        this.logger.log(`开始高性能查询所有卫星的TLE数据，第${page}页，每页${limit}条`);
        this.logger.warn(`💡 提示: 如需一次性获取所有数据，请使用 /orbit/bulk-tle/all 接口或设置 useOneTimeQuery=true`);
        
        // 限制每页最大记录数
        const maxLimit = 10000;
        if (limit > maxLimit) {
          this.logger.warn(`每页记录数(${limit})超过最大限制(${maxLimit})，将调整为${maxLimit}`);
          limit = maxLimit;
        }

        // 使用composite聚合进行高性能分页查询
        // 计算after_key用于分页（composite聚合使用after_key而不是from/size）
        let afterKey = null;
        if (page > 1) {
          // 对于非第一页，我们需要计算after_key
          // 这里使用一个简化的方法：基于页码计算大概的norad_id起始值
          const estimatedStartNoradId = (page - 1) * limit;
          afterKey = { norad_id: estimatedStartNoradId };
        }

        // 并行执行：获取总数 + 获取当前页数据
        const [totalCountPromise, dataPromise] = await Promise.all([
          // 获取总的不重复norad_id数量（可以缓存这个结果）
          this.elasticsearchService.search({
            index: 'orbital_tle',
            body: {
              size: 0,
              aggs: {
                unique_count: {
                  cardinality: {
                    field: "norad_id",
                    precision_threshold: 10000 // 提高精度
                  }
                }
              }
            }
          }),

          // 使用composite聚合获取分页数据 - 一次查询完成去重+分页+获取最新数据
          this.elasticsearchService.search({
            index: 'orbital_tle',
            body: {
              size: 0,
              aggs: {
                norad_pagination: {
                  composite: {
                    size: limit,
                    sources: [
                      {
                        norad_id: {
                          terms: {
                            field: "norad_id",
                            order: "asc"
                          }
                        }
                      }
                    ],
                    ...(afterKey && { after: afterKey })
                  },
                  aggs: {
                    latest_tle: {
                      top_hits: {
                        size: 1,
                        sort: [{ epoch: { order: "desc" } }],
                        _source: ["tle_raw", "satellite_name", "cospar_id", "norad_id", "epoch", "time", "constellation_name", "orbital_elements"]
                      }
                    }
                  }
                }
              }
            }
          })
        ]);

        // 处理结果
        const totalCount = (totalCountPromise.aggregations?.unique_count as any)?.value || 0;
        const totalPages = Math.ceil(totalCount / limit);

        const compositeResult = dataPromise.aggregations?.norad_pagination as any;
        const buckets = compositeResult?.buckets || [];
        const nextAfterKey = compositeResult?.after_key;

        this.logger.debug(`高性能查询完成: 总数${totalCount}，当前页${buckets.length}条记录`);

        // 提取每个bucket中的最新TLE数据
        const results = buckets.map((bucket: any) => {
          const latestHit = bucket.latest_tle?.hits?.hits?.[0];
          if (latestHit && latestHit._source) {
            const source = latestHit._source;
            this.logger.debug(`norad_id ${source.norad_id} 最新epoch: ${source.epoch}`);
            return source;
          }
          return null;
        }).filter(Boolean);

        const result = {
          success: true,
          total: totalCount,
          page: page,
          limit: limit,
          totalPages: totalPages,
          currentPageCount: results.length,
          hasNextPage: !!nextAfterKey && page < totalPages,
          hasPrevPage: page > 1,
          results: results,
          // 添加composite聚合的分页信息（用于前端优化）
          _pagination: {
            afterKey: nextAfterKey,
            method: 'composite'
          },
          // 添加性能提示
          _performance: {
            message: "如需一次性获取所有数据，请使用 POST /orbit/bulk-tle/all 接口",
            estimatedTotalTime: `约 ${Math.ceil(totalPages * 0.5)} 秒（分页方式）`,
            recommendedApproach: "一次性获取接口可在几秒内返回全部数据"
          }
        };

        this.logger.log(`高性能查询成功: 第${page}页返回${result.currentPageCount}条去重的卫星TLE数据`);
        return result;
      }
      
      this.logger.log(`开始批量查询${noradIds.length}个特定卫星的TLE数据`);
      
      // 限制最大查询数量，避免过大的请求
      const maxIdsCount = 100;
      if (noradIds.length > maxIdsCount) {
        this.logger.warn(`请求的卫星数量(${noradIds.length})超过最大限制(${maxIdsCount})，将只处理前${maxIdsCount}个`);
        noradIds = noradIds.slice(0, maxIdsCount);
      }
      
      // 对于特定卫星查询，直接使用优化的collapse查询
      const response = await this.elasticsearchService.search({
        index: 'orbital_tle',
        preference: "_local", // 优先使用本地分片
        timeout: `${this.tleQueryConfig.timeout}ms`, // 使用配置的超时时间
        body: {
          query: {
            terms: {
              norad_id: noradIds
            }
          },
          // 使用collapse确保每个norad_id只返回最新的一条记录
          collapse: {
            field: "norad_id",
            inner_hits: {
              name: "latest_tle",
              size: 1,
              sort: [{ epoch: { order: "desc" } }],
              _source: ["tle_raw", "satellite_name", "cospar_id", "norad_id", "epoch", "time", "constellation_name", "orbital_elements"]
            }
          },
          sort: [
            { norad_id: { order: "asc" } }
          ],
          size: noradIds.length,
          _source: ["tle_raw", "satellite_name", "cospar_id", "norad_id", "epoch", "time", "constellation_name", "orbital_elements"]
        }
      });
      
      const totalHits = (response.hits.total as SearchTotalHits).value || 0;
      this.logger.debug(`特定卫星查询: 找到${totalHits}条记录，返回${response.hits.hits.length}条去重记录`);
      
      // 处理结果，优先使用inner_hits中的最新数据
      const results = response.hits.hits.map(hit => {
        if (hit.inner_hits && hit.inner_hits.latest_tle && 
            hit.inner_hits.latest_tle.hits.hits.length > 0) {
          return hit.inner_hits.latest_tle.hits.hits[0]._source;
        }
        return hit._source;
      });

      const result = {
        success: true,
        total: response.hits.hits.length,
        results: results
      };
      
      // 检查查询结果覆盖情况
      const foundNoradIds = new Set(result.results.map((item: any) => item.norad_id));
      const missingNoradIds = noradIds.filter(id => !foundNoradIds.has(id));
      
      if (missingNoradIds.length > 0) {
        this.logger.warn(`未找到以下${missingNoradIds.length}个卫星的TLE数据: ${missingNoradIds.join(', ')}`);
      }
      
      this.logger.log(`特定卫星查询成功: 返回${result.total}条去重的卫星TLE数据`);
      return result;
    } catch (error) {
      this.logger.error(`批量查询卫星TLE数据失败: ${error.message}`, error.stack);
      throw new Error(`批量查询卫星TLE数据失败: ${error.message}`);
    }
  }

  /**
   * 一次性获取所有卫星的最新TLE数据（秒级返回）
   * 基于time字段获取最新10分钟内的数据：先找到time字段的最大值，然后返回最新时间前10分钟内的所有文档
   * 使用滚动查询突破ES 10000条的限制，确保能获取所有数据
   * @param sampleMode 是否为示例模式，用于限制Swagger UI中的返回数据量
   * @returns 最新10分钟内所有卫星的TLE数据
   */
  async getAllSatelliteTleData(sampleMode: boolean = false): Promise<any> {
    try {
      this.logger.log(`🚀 开始一次性获取所有卫星TLE数据（基于最新time字段前${this.tleQueryConfig.timeWindowMinutes}分钟）`);
      const startTime = Date.now();
      
      // 第一步：获取time字段的最大值
      this.logger.log(`📊 开始获取time字段的最大值...`);
      const maxTimeStartTime = Date.now();
      
      const maxTimeResponse = await this.elasticsearchService.search({
        index: 'orbital_tle',
        timeout: `${this.tleQueryConfig.timeout}ms`,
        body: {
          size: 0,
          aggs: {
            max_time: {
              max: {
                field: "time"
              }
            }
          }
        }
      });
      
      const maxTime = (maxTimeResponse.aggregations?.max_time as any)?.value;
      if (!maxTime) {
        throw new Error('未找到time字段的最大值');
      }
      
      // 计算配置的时间窗口前的时间
      const maxTimeDate = new Date(maxTime);
      const timeWindowAgo = new Date(maxTimeDate.getTime() - this.tleQueryConfig.timeWindowMinutes * 60 * 1000);
      const timeWindowAgoISO = timeWindowAgo.toISOString();
      
      const maxTimeQueryTime = Date.now() - maxTimeStartTime;
      this.logger.log(`📊 找到最新的time值: ${maxTime}，${this.tleQueryConfig.timeWindowMinutes}分钟前时间: ${timeWindowAgoISO}，耗时${maxTimeQueryTime}ms`);
      
      // 第二步：先查询总数，确定是否需要使用滚动查询
      this.logger.log(`📊 开始统计满足条件的总记录数...`);
      const countResponse = await this.elasticsearchService.count({
        index: 'orbital_tle',
        body: {
          query: {
            range: {
              time: {
                gte: timeWindowAgoISO,
                lte: maxTime
              }
            }
          }
        }
      });
      
      const totalCount = countResponse.count;
      this.logger.log(`📊 统计完成: 共找到${totalCount}条time在[${timeWindowAgoISO}, ${maxTime}]范围内的记录`);
      
      // 第三步：根据数据量选择查询策略
      const dataStartTime = Date.now();
      let results: any[] = [];
      let queryMethod = '';
      
      if (totalCount < 20000) {
        // 数据量小于2万条，改用按norad_id去重的方式查询所有数据
        this.logger.log(`📥 数据量${totalCount} < 20000，使用按norad_id去重查询所有最新数据...`);
        
        // 使用composite聚合按norad_id去重，获取每个卫星的最新数据
        let allResults: any[] = [];
        let afterKey = null;
        let batchCount = 0;
        const maxBatches = 50; // 最多50批次，避免无限循环
        
        while (batchCount < maxBatches) {
          const response: any = await this.elasticsearchService.search({
            index: 'orbital_tle',
            timeout: `${this.tleQueryConfig.timeout}ms`,
            body: {
              size: 0,
              aggs: {
                satellites_by_norad: {
                  composite: {
                    size: this.tleQueryConfig.scrollSize,
                    sources: [
                      {
                        norad_id: {
                          terms: {
                            field: "norad_id",
                            order: "asc"
                          }
                        }
                      }
                    ],
                    ...(afterKey && { after: afterKey })
                  },
                  aggs: {
                    latest_data: {
                      top_hits: {
                        size: 1,
                        sort: [{ time: { order: "desc" } }],
                        _source: ["tle_raw", "satellite_name", "cospar_id", "norad_id", "epoch", "time", "constellation_name", "orbital_elements"]
                      }
                    }
                  }
                }
              }
            }
          });
          
          const buckets = response.aggregations?.satellites_by_norad?.buckets || [];
          if (buckets.length === 0) {
            break;
          }
          
          // 提取每个bucket中的最新数据
          const batchResults = buckets.map((bucket: any) => {
            const latestHit = bucket.latest_data?.hits?.hits?.[0];
            return latestHit?._source;
          }).filter(Boolean);
          
          allResults = allResults.concat(batchResults);
          afterKey = response.aggregations?.satellites_by_norad?.after_key;
          batchCount++;
          
          this.logger.log(`📥 第${batchCount}批: 获取${batchResults.length}个卫星的最新数据，累计${allResults.length}个卫星`);
          
          if (!afterKey) {
            break;
          }
        }
        
        results = allResults;
        queryMethod = 'norad_dedup_query';
        this.logger.log(`📥 按norad_id去重查询完成: 总共${batchCount}批次，获取${results.length}个不重复卫星的最新数据`);
        
      } else if (totalCount <= 10000) {
        // 数据量小于等于10000，使用常规查询
        this.logger.log(`📥 数据量${totalCount} <= 10000，使用常规查询...`);
        
        const response = await this.elasticsearchService.search({
          index: 'orbital_tle',
          timeout: `${this.tleQueryConfig.timeout}ms`,
          body: {
            query: {
              range: {
                time: {
                  gte: timeWindowAgoISO,
                  lte: maxTime
                }
              }
            },
            sort: [{ time: { order: "desc" } }, { norad_id: { order: "asc" } }],
            size: totalCount, // 使用精确的数量
            _source: ["tle_raw", "satellite_name", "cospar_id", "norad_id", "epoch", "time", "constellation_name", "orbital_elements"]
          }
        });
        
        results = response.hits.hits.map(hit => hit._source);
        queryMethod = 'direct_query';
        
      } else {
        // 数据量大于10000，使用滚动查询
        this.logger.log(`📥 数据量${totalCount} > 10000，使用滚动查询获取所有数据...`);
        
        // 初始化滚动查询
        let scrollResponse = await this.elasticsearchService.search({
          index: 'orbital_tle',
          scroll: this.tleQueryConfig.scrollTimeout,
          timeout: `${this.tleQueryConfig.timeout}ms`,
          body: {
            query: {
              range: {
                time: {
                  gte: timeWindowAgoISO,
                  lte: maxTime
                }
              }
            },
            sort: [{ time: { order: "desc" } }, { norad_id: { order: "asc" } }],
            size: this.tleQueryConfig.scrollSize,
            _source: ["tle_raw", "satellite_name", "cospar_id", "norad_id", "epoch", "time", "constellation_name", "orbital_elements"]
          }
        });
        
        // 收集第一批数据
        results = scrollResponse.hits.hits.map(hit => hit._source);
        let scrollId = scrollResponse._scroll_id;
        
        this.logger.log(`📥 第1批: 获取${results.length}条记录`);
        
        // 继续滚动获取剩余数据
        let batchCount = 1;
        while (scrollResponse.hits.hits.length > 0) {
          scrollResponse = await this.elasticsearchService.scroll({
            scroll_id: scrollId!,
            scroll: this.tleQueryConfig.scrollTimeout
          });
          
          if (scrollResponse.hits.hits.length === 0) {
            break;
          }
          
          const batchResults = scrollResponse.hits.hits.map(hit => hit._source);
          results = results.concat(batchResults);
          scrollId = scrollResponse._scroll_id;
          batchCount++;
          
          this.logger.log(`📥 第${batchCount}批: 获取${batchResults.length}条记录，累计${results.length}条`);
        }
        
        // 清理滚动上下文
        if (scrollId) {
          try {
            await this.elasticsearchService.clearScroll({
              scroll_id: scrollId
            });
            this.logger.log(`🧹 已清理滚动上下文`);
          } catch (clearError) {
            this.logger.warn(`清理滚动上下文失败: ${clearError.message}`);
          }
        }
        
        this.logger.log(`📥 滚动查询完成: 总共${batchCount}批次，获取${results.length}条记录`);
        queryMethod = 'scroll_query';
      }
      
      const dataQueryTime = Date.now() - dataStartTime;
      const totalTime = Date.now() - startTime;
      
      // 验证数据完整性 - 确保获取了所有数据
      if (results.length !== totalCount) {
        this.logger.warn(`⚠️ 数据完整性检查: 预期${totalCount}条，实际获取${results.length}条`);
      } else {
        this.logger.log(`✅ 数据完整性验证: 成功获取所有${totalCount}条记录`);
      }
      
      // 统计时间分布
      const timeDistribution = new Map<string, number>();
      results.forEach((item: any) => {
        const timeKey = item.time;
        timeDistribution.set(timeKey, (timeDistribution.get(timeKey) || 0) + 1);
      });
      
      this.logger.log(`📊 时间分布统计: 共${timeDistribution.size}个不同的时间点`);
      
      // 示例模式处理：为了避免Swagger UI栈溢出，限制返回数据量
      let finalResults = results;
      let isSampleMode = false;
      
      if (sampleMode && results.length > 100) {
        finalResults = results.slice(0, 100);
        isSampleMode = true;
        this.logger.log(`📋 示例模式激活: 限制返回前100条数据（总共${results.length}条可用）`);
      }
      
      const result = {
        success: true,
        total: finalResults.length,
        ...(isSampleMode && { actualTotal: results.length }), // 仅在示例模式下显示实际总数
        executionTime: totalTime,
        timeRangeStart: timeWindowAgoISO,
        timeRangeEnd: maxTime,
        timeRangeMinutes: this.tleQueryConfig.timeWindowMinutes,
        method: queryMethod,
        queryStrategy: this.getQueryStrategyDescription(queryMethod),
        sampleMode: isSampleMode,
        results: finalResults
      };
      
      this.logger.log(`🎉 获取完成: 找到${result.total}条time在[${timeWindowAgoISO}, ${maxTime}]范围内的记录，总耗时${totalTime}ms (${(totalTime/1000).toFixed(1)}秒)`);
      
      return result;
      
    } catch (error) {
      this.logger.error(`获取所有卫星TLE数据失败: ${error.message}`, error.stack);
      throw new Error(`获取所有卫星TLE数据失败: ${error.message}`);
    }
  }

  /**
   * 获取当前TLE查询配置
   * @returns TLE查询配置
   */
  getTleQueryConfig(): TleQueryConfig {
    return { ...this.tleQueryConfig };
  }

  /**
   * 动态更新TLE查询配置
   * @param newConfig 新的配置
   */
  updateTleQueryConfig(newConfig: Partial<TleQueryConfig>): void {
    this.tleQueryConfig = mergeTleQueryConfig(this.tleQueryConfig, newConfig);
    validateTleQueryConfig(this.tleQueryConfig);
    
    this.logger.log(`TLE查询配置已更新: 时间窗口=${this.tleQueryConfig.timeWindowMinutes}分钟, 最大记录数=${this.tleQueryConfig.maxRecords}`);
  }

  /**
   * 获取查询策略描述
   * @param method 查询方法
   * @returns 查询策略描述
   */
  private getQueryStrategyDescription(method: string): string {
    switch (method) {
      case 'norad_dedup_query':
        return '按NORAD ID去重查询';
      case 'direct_query':
        return '单次查询';
      case 'scroll_query':
        return '滚动查询';
      default:
        return '未知查询策略';
    }
  }
}