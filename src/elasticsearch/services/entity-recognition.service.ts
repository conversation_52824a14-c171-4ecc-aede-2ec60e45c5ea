import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { EntityRecognitionResult, EntityType } from '../types/news.types';
import { launchSites } from '../../../data/launchSites';

/**
 * 实体名称集合
 */
interface EntityCollections {
  satellites: string[];
  constellations: string[];
  launchSites: string[];
  rockets: string[];
  providers: string[];
}

/**
 * 预定义的实体名称（作为备用）
 */
const FALLBACK_ENTITIES = {
  satellites: [
    'Starlink', 'Hubble Space Telescope', 'International Space Station', 'ISS',
    'James Webb Space Telescope', 'JWST', 'Galileo', 'GPS', 'Iridium'
  ],
  constellations: [
    'Starlink', 'OneWeb', 'Kuiper', 'Galileo', 'GPS', 'GLONASS', 'BeiDou'
  ],
  rockets: [
    'Falcon 9', 'Falcon Heavy', 'Atlas V', 'Delta IV', 'Ariane 5', 'Soyuz',
    'Long March', 'New Shepard', 'Electron'
  ],
  providers: [
    'SpaceX', 'Blue Origin', 'NASA', 'ESA', 'Roscosmos', 'CNSA', 'ISRO',
    'ULA', 'Rocket Lab', 'Virgin Galactic'
  ]
};

/**
 * 实体识别服务
 * 负责从新闻内容中识别和提取航天相关实体
 */
@Injectable()
export class EntityRecognitionService {
  private readonly logger = new Logger(EntityRecognitionService.name);
  private entityCollections: EntityCollections | null = null;
  private isInitialized = false;
  private initializationPromise: Promise<void> | null = null;

  constructor(private readonly httpService: HttpService) {
    // 配置HttpService的基础URL
    this.httpService.axiosRef.defaults.baseURL = 'http://localhost:3000';
  }

  /**
   * 初始化实体名称集合
   * 延迟初始化，在第一次使用时加载
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this.loadEntityCollections();
    await this.initializationPromise;
    this.isInitialized = true;
  }

  /**
   * 加载所有实体名称集合
   */
  private async loadEntityCollections(): Promise<void> {
    this.logger.log('开始加载实体名称集合...');
    
    try {
      const [satellites, constellations, launchSitesFromApi, rockets, providers] = await Promise.allSettled([
        this.loadSatelliteNames(),
        this.loadConstellationNames(),
        this.loadLaunchSiteNamesFromApi(),
        this.loadRocketNames(),
        this.loadProviders()
      ]);

      // 处理卫星名称
      const satelliteNames = satellites.status === 'fulfilled' && satellites.value.length > 0
        ? satellites.value
        : FALLBACK_ENTITIES.satellites;
      if (satellites.status === 'rejected') {
        this.logger.warn(`加载卫星名称失败: ${satellites.reason}，使用预定义列表`);
      } else if (satellites.value.length === 0) {
        this.logger.warn(`API返回的卫星名称为空，使用预定义列表`);
      }

      // 处理星座名称
      const constellationNames = constellations.status === 'fulfilled' && constellations.value.length > 0
        ? constellations.value
        : FALLBACK_ENTITIES.constellations;
      if (constellations.status === 'rejected') {
        this.logger.warn(`加载星座名称失败: ${constellations.reason}，使用预定义列表`);
      } else if (constellations.value.length === 0) {
        this.logger.warn(`API返回的星座名称为空，使用预定义列表`);
      }

      // 处理发射场名称（合并本地数据和API数据）
      const launchSiteNamesFromLocal = launchSites.map(site => site.englishName);
      const launchSiteNamesFromApiResult = launchSitesFromApi.status === 'fulfilled' ? launchSitesFromApi.value : [];
      if (launchSitesFromApi.status === 'rejected') {
        this.logger.warn(`从API加载发射场名称失败: ${launchSitesFromApi.reason}`);
      }
      const allLaunchSiteNames = [...new Set([...launchSiteNamesFromLocal, ...launchSiteNamesFromApiResult])];

      // 处理火箭名称
      const rocketNames = rockets.status === 'fulfilled' && rockets.value.length > 0
        ? rockets.value
        : FALLBACK_ENTITIES.rockets;
      if (rockets.status === 'rejected') {
        this.logger.warn(`加载火箭名称失败: ${rockets.reason}，使用预定义列表`);
      } else if (rockets.value.length === 0) {
        this.logger.warn(`API返回的火箭名称为空，使用预定义列表`);
      }

      // 处理发射服务商名称
      const providerNames = providers.status === 'fulfilled' && providers.value.length > 0
        ? providers.value
        : FALLBACK_ENTITIES.providers;
      if (providers.status === 'rejected') {
        this.logger.warn(`加载发射服务商名称失败: ${providers.reason}，使用预定义列表`);
      } else if (providers.value.length === 0) {
        this.logger.warn(`API返回的发射服务商名称为空，使用预定义列表`);
      }

      this.entityCollections = {
        satellites: satelliteNames,
        constellations: constellationNames,
        launchSites: allLaunchSiteNames,
        rockets: rocketNames,
        providers: providerNames
      };

      this.logger.log(`实体名称集合加载完成: 卫星${satelliteNames.length}个, 星座${constellationNames.length}个, 发射场${allLaunchSiteNames.length}个, 火箭${rocketNames.length}个, 服务商${providerNames.length}个`);
    } catch (error) {
      this.logger.error(`加载实体名称集合失败: ${error.message}`, error.stack);
      // 即使加载失败，也要设置一个空的集合，避免后续调用出错
      this.entityCollections = {
        satellites: [],
        constellations: [],
        launchSites: [],
        rockets: [],
        providers: []
      };
    }
  }

  /**
   * 加载卫星名称
   */
  private async loadSatelliteNames(): Promise<string[]> {
    try {
      const response = await firstValueFrom(
        this.httpService.get('/satellite/names', {
          headers: {
            'Authorization': `Bearer ${process.env.JWT_TOKEN || ''}`
          },
          timeout: 10000 // 10秒超时
        })
      );
      return response.data || FALLBACK_ENTITIES.satellites;
    } catch (error) {
      this.logger.warn(`加载卫星名称失败: ${error.message || '未知错误'}，将使用预定义列表`);
      return FALLBACK_ENTITIES.satellites;
    }
  }

  /**
   * 加载星座名称
   */
  private async loadConstellationNames(): Promise<string[]> {
    try {
      const response = await firstValueFrom(
        this.httpService.get('/constellation/names', {
          headers: {
            'Authorization': `Bearer ${process.env.JWT_TOKEN || ''}`
          },
          timeout: 10000
        })
      );
      return response.data?.constellationNames || FALLBACK_ENTITIES.constellations;
    } catch (error) {
      this.logger.warn(`加载星座名称失败: ${error.message || '未知错误'}，将使用预定义列表`);
      return FALLBACK_ENTITIES.constellations;
    }
  }

  /**
   * 从API加载发射场名称
   */
  private async loadLaunchSiteNamesFromApi(): Promise<string[]> {
    try {
      const response = await firstValueFrom(
        this.httpService.get('/api/es/launch/site-names', {
          headers: {
            'Authorization': `Bearer ${process.env.JWT_TOKEN || ''}`
          },
          timeout: 10000
        })
      );
      return response.data?.data || [];
    } catch (error) {
      this.logger.warn(`从API加载发射场名称失败: ${error.message || '未知错误'}，将使用本地数据`);
      return [];
    }
  }

  /**
   * 加载火箭名称
   */
  private async loadRocketNames(): Promise<string[]> {
    try {
      const response = await firstValueFrom(
        this.httpService.get('/api/es/launch/rocket-names', {
          headers: {
            'Authorization': `Bearer ${process.env.JWT_TOKEN || ''}`
          },
          timeout: 10000
        })
      );
      return response.data?.data || FALLBACK_ENTITIES.rockets;
    } catch (error) {
      this.logger.warn(`加载火箭名称失败: ${error.message || '未知错误'}，将使用预定义列表`);
      return FALLBACK_ENTITIES.rockets;
    }
  }

  /**
   * 加载发射服务商名称
   */
  private async loadProviders(): Promise<string[]> {
    try {
      const response = await firstValueFrom(
        this.httpService.get('/api/es/launch/providers', {
          headers: {
            'Authorization': `Bearer ${process.env.JWT_TOKEN || ''}`
          },
          timeout: 10000
        })
      );
      return response.data?.data || FALLBACK_ENTITIES.providers;
    } catch (error) {
      this.logger.warn(`加载发射服务商名称失败: ${error.message || '未知错误'}，将使用预定义列表`);
      return FALLBACK_ENTITIES.providers;
    }
  }

  /**
   * 从文本中识别实体
   * @param content 要识别的文本内容
   * @param contentCn 对应的中文翻译内容（用于获取中文翻译）
   * @returns 识别到的实体结果
   */
  async recognizeEntities(content: string, contentCn?: string): Promise<EntityRecognitionResult> {
    // 确保服务已初始化
    await this.initialize();

    if (!content || !this.entityCollections) {
      return {
        satellites: [],
        constellations: [],
        launch_sites: [],
        rockets: [],
        providers: []
      };
    }

    const result: EntityRecognitionResult = {
      satellites: this.extractEntities(content, this.entityCollections.satellites, contentCn),
      constellations: this.extractEntities(content, this.entityCollections.constellations, contentCn),
      launch_sites: this.extractEntities(content, this.entityCollections.launchSites, contentCn),
      rockets: this.extractEntities(content, this.entityCollections.rockets, contentCn),
      providers: this.extractEntities(content, this.entityCollections.providers, contentCn)
    };

    const totalEntities = Object.values(result).reduce((sum, entities) => sum + entities.length, 0);
    this.logger.debug(`从文本中识别到 ${totalEntities} 个实体`);

    return result;
  }

  /**
   * 从文本中提取特定类型的实体
   * @param content 原文内容
   * @param entityNames 实体名称集合
   * @param contentCn 中文翻译内容
   * @returns 识别到的实体列表（格式：英文原名（中文翻译））
   */
  private extractEntities(content: string, entityNames: string[], contentCn?: string): string[] {
    const foundEntities: string[] = [];
    const contentLower = content.toLowerCase();

    for (const entityName of entityNames) {
      if (!entityName || entityName.length < 2) continue;

      // 使用正则表达式进行全词匹配
      const regex = new RegExp(`\\b${this.escapeRegex(entityName)}\\b`, 'gi');
      const matches = content.match(regex);

      if (matches && matches.length > 0) {
        // 获取中文翻译
        const chineseTranslation = this.findChineseTranslation(entityName, contentCn);
        const entityWithTranslation = chineseTranslation 
          ? `${entityName}（${chineseTranslation}）`
          : entityName;

        if (!foundEntities.includes(entityWithTranslation)) {
          foundEntities.push(entityWithTranslation);
        }
      }
    }

    return foundEntities;
  }

  /**
   * 转义正则表达式特殊字符
   */
  private escapeRegex(text: string): string {
    return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * 从中文翻译内容中查找实体的中文翻译
   * @param englishName 英文实体名称
   * @param contentCn 中文翻译内容
   * @returns 中文翻译，如果未找到则返回null
   */
  private findChineseTranslation(englishName: string, contentCn?: string): string | null {
    if (!contentCn) return null;

    // 这里可以实现更复杂的翻译匹配逻辑
    // 目前简单返回null，让调用方使用大模型翻译
    return null;
  }

  /**
   * 重新加载实体名称集合
   * 用于定期更新实体数据
   */
  async reloadEntityCollections(): Promise<void> {
    this.logger.log('重新加载实体名称集合...');
    this.isInitialized = false;
    this.initializationPromise = null;
    await this.initialize();
  }

  /**
   * 获取实体统计信息
   */
  getEntityStatistics(): any {
    if (!this.entityCollections) {
      return null;
    }

    return {
      satellites: this.entityCollections.satellites.length,
      constellations: this.entityCollections.constellations.length,
      launchSites: this.entityCollections.launchSites.length,
      rockets: this.entityCollections.rockets.length,
      providers: this.entityCollections.providers.length,
      total: Object.values(this.entityCollections).reduce((sum, entities) => sum + entities.length, 0)
    };
  }
}
