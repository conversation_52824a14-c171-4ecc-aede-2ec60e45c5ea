import { Injectable, Logger } from '@nestjs/common';
import { ElasticsearchService } from '@nestjs/elasticsearch';
import { ElasticsearchBaseService } from './elasticsearch.base.service';
import { DebrisToEventQueryDto, EventToDebrisQueryDto } from '../dto/debris-event-correlation.dto';
import { CorrelationResultDto, EventCorrelationResultDto, MatchScores, DebrisCorrelationResult } from '../dto/correlation-response.dto';
import { EventDocument, DebrisDocument, SearchResponse } from '../types/elasticsearch.types';
import { CorrelationResponseDto } from '../dto/correlation-response.dto';
import * as levenshtein from 'fast-levenshtein';

/**
 * Elasticsearch相关性服务
 * 提供碎片与事件的相关性分析功能
 */
@Injectable()
export class ElasticsearchCorrelationService {
  private readonly logger = new Logger(ElasticsearchCorrelationService.name);
  private readonly config = {
    timeWindowYears: 5,
    minNameSimilarity: 3,
    weights: {
      cosparWeight: 0.7,
      timeWeight: 0.15,
      nameWeight: 0.15,
    },
  };

  constructor(
    private readonly elasticsearchService: ElasticsearchService,
    private readonly esBaseService: ElasticsearchBaseService
  ) {}

  /**
   * 计算COSPAR ID匹配分数
   * @param cosparId1 碎片ID
   * @param cosparId2 事件碎片标识
   * @returns 匹配分数 (0-1)，如果不匹配则返回0
   */
  private calculateCosparMatch(cosparId1: string, cosparId2: string): number {
    this.logger.debug(`计算COSPAR匹配度: ${cosparId1} 和 ${cosparId2}`);

    // 检查输入参数
    if (!cosparId1 || !cosparId2) {
      this.logger.debug('一个或两个COSPAR ID为空');
      return 0;
    }

    // 如果完全匹配
    if (cosparId1 === cosparId2) {
      this.logger.debug('COSPAR ID完全匹配');
      return 1;
    }

    // 提取年份和序号部分
    const pattern = /^(\d{4})-(\d{3})([A-Z]*)$/;
    
    try {
      const match1 = cosparId1.match(pattern);
      const match2 = cosparId2.match(pattern);

      if (!match1 || !match2) {
        this.logger.debug(`无效的COSPAR ID格式: ${!match1 ? cosparId1 : cosparId2}`);
        return 0;
      }

      // 年份必须匹配，否则返回0
      if (match1[1] !== match2[1]) {
        this.logger.debug(`年份不匹配: ${match1[1]} vs ${match2[1]}`);
        return 0;
      }
      
      // 序号部分前三位数字必须匹配，否则返回0
      if (match1[2] !== match2[2]) {
        this.logger.debug(`序号不匹配: ${match1[2]} vs ${match2[2]}`);
        return 0;
      }
      
      // 年份匹配得0.5分
      const yearMatch = 0.5;
      this.logger.debug(`年份匹配分数: ${yearMatch}`);
      
      // 序号部分匹配得0.4分
      const numberMatch = 0.4;
      this.logger.debug(`序号匹配分数: ${numberMatch}`);
      
      // 字母部分匹配得0.1分
      let letterMatch = 0;
      if (match1[3] && match2[3]) {
        // 如果字母部分的第一个字母相同，得0.1分
        letterMatch = match1[3].charAt(0) === match2[3].charAt(0) ? 0.1 : 0;
        this.logger.debug(`字母匹配分数: ${letterMatch}`);
      }

      const totalScore = yearMatch + numberMatch + letterMatch;
      this.logger.debug(`COSPAR总匹配分数: ${totalScore}`);
      
      return totalScore;
    } catch (error) {
      this.logger.error(`COSPAR ID匹配出错: ${error}`);
      return 0;
    }
  }

  /**
   * 计算时间匹配分数
   * @param eventDate 事件时间
   * @param firstEpoch 首次纪元时间
   * @returns 匹配分数 (0-1)
   */
  private calculateTimeScore(eventDate: string, firstEpoch: string): number {
    const eventTime = new Date(eventDate).getTime();
    const epochTime = new Date(firstEpoch).getTime();

    // 如果首次纪元时间在事件时间之后
    if (epochTime >= eventTime) {
      // 计算时间差（年）
      const yearDiff = (epochTime - eventTime) / (365 * 24 * 60 * 60 * 1000);
      
      // 如果时间差在5年内，给满分
      if (yearDiff <= 5) {
        return 1;
      }
      
      // 如果时间差在5-10年内，按比例给分
      if (yearDiff <= 10) {
        return 1 - (yearDiff - 5) / 5;
      }
    }
    
    return 0;
  }

  /**
   * 计算名称相似度分数
   * @param objectClass 碎片类型
   * @returns 匹配分数 (0-1)
   */
  private calculateNameScore(objectClass: string | undefined): number {
    // 如果没有碎片类型信息，返回默认分数
    if (!objectClass) {
      return 0.2;
    }
    
    // 根据碎片类型评分
    if (objectClass === 'Payload Fragmentation Debris') {
      return 0.8;
    } else if (objectClass.includes('Debris')) {
      return 0.5;
    }
    return 0.2;
  }

  /**
   * 计算综合置信度
   * @param scores 各维度分数
   * @returns 置信度 (0-1)
   */
  private calculateConfidence(scores: any): number {
    const { cosparMatch, timeScore, nameScore } = scores;
    const { cosparWeight, timeWeight, nameWeight } = this.config.weights;
    
    const result = (
      cosparMatch * cosparWeight +
      timeScore * timeWeight +
      nameScore * nameWeight
    );
    
    this.logger.debug(`置信度计算:
      cosparMatch: ${cosparMatch} * ${cosparWeight} = ${cosparMatch * cosparWeight}
      timeScore: ${timeScore} * ${timeWeight} = ${timeScore * timeWeight}
      nameScore: ${nameScore} * ${nameWeight} = ${nameScore * nameWeight}
      总计: ${result}
    `);
    
    return result;
  }

  /**
   * 查询碎片信息关联的事件
   * @param query 查询参数
   * @returns 关联事件列表及置信度
   */
  async findRelatedEvents(query: DebrisToEventQueryDto): Promise<CorrelationResponseDto> {
    const { cospar_id, first_epoch, name, min_confidence } = query;
    
    this.logger.debug(`搜索与碎片相关的事件:
      cospar_id: ${cospar_id}
      first_epoch: ${first_epoch || '未提供'}
      name: ${name || '未提供'}
      min_confidence: ${min_confidence}
    `);

    try {
      // 构建ES查询 - 查询两个索引
      const discosQuery = {
        index: 'event_discos',
        body: {
          size: 1000,
          query: {
            match_all: {}
          }
        }
      };

      const jonathanQuery = {
        index: 'event_jonathan',
        body: {
          size: 1000,
          query: {
            match_all: {}
          }
        }
      };

      this.logger.debug('Elasticsearch查询:', {
        discos: JSON.stringify(discosQuery, null, 2),
        jonathan: JSON.stringify(jonathanQuery, null, 2)
      });

      // 并行查询两个索引
      const [discosResult, jonathanResult] = await Promise.all([
        this.elasticsearchService.search<SearchResponse<EventDocument>>(discosQuery),
        this.elasticsearchService.search<SearchResponse<EventDocument>>(jonathanQuery)
      ]);
      
      this.logger.debug(`在discos中找到 ${discosResult.hits?.hits?.length || 0} 个潜在匹配`);
      this.logger.debug(`在jonathan中找到 ${jonathanResult.hits?.hits?.length || 0} 个潜在匹配`);

      const correlations: CorrelationResultDto[] = [];

      // 处理 discos 搜索结果
      const discosHits = discosResult.hits?.hits || [];
      for (const hit of discosHits) {
        const source = hit._source as EventDocument | undefined;
        if (!source || !hit._id) {
          this.logger.debug(`跳过discos结果 ${hit._id || 'unknown'} - 无源数据或ID`);
          continue;
        }

        this.logger.debug(`处理discos结果 ${hit._id}:`, JSON.stringify(source, null, 2));

        // 计算COSPAR ID匹配分数
        const cosparMatch = this.calculateCosparMatch(cospar_id, source.piece);
        
        // 如果COSPAR ID不匹配，跳过此结果
        if (cosparMatch === 0) {
          this.logger.debug(`跳过discos结果 ${hit._id} - COSPAR ID不匹配`);
          continue;
        }

        const scores: any = {
          cosparMatch,
          timeScore: first_epoch ? this.calculateTimeScore(source.sdate, first_epoch) : 0.5, // 如果没有提供first_epoch，给予中等分数
          nameScore: this.calculateNameScore(source.object_class),
        };

        this.logger.debug(`discos ${hit._id} 的分数:`, scores);

        const confidence = this.calculateConfidence(scores);
        this.logger.debug(`discos ${hit._id} 的置信度: ${confidence}`);

        if (confidence >= min_confidence) {
          correlations.push({
            event_id: hit._id,
            confidence,
            scores: {
              cosparMatch: scores.cosparMatch,
              timeScore: scores.timeScore,
              nameScore: scores.nameScore,
            },
            event_details: {
              ...source,
              source: 'discos' // 添加数据来源标识
            },
          });
        }
      }

      // 处理 jonathan 搜索结果
      const jonathanHits = jonathanResult.hits?.hits || [];
      for (const hit of jonathanHits) {
        const source = hit._source as EventDocument | undefined;
        if (!source || !hit._id) {
          this.logger.debug(`跳过jonathan结果 ${hit._id || 'unknown'} - 无源数据或ID`);
          continue;
        }

        this.logger.debug(`处理jonathan结果 ${hit._id}:`, JSON.stringify(source, null, 2));

        // 计算COSPAR ID匹配分数
        const cosparMatch = this.calculateCosparMatch(cospar_id, source.piece);
        
        // 如果COSPAR ID不匹配，跳过此结果
        if (cosparMatch === 0) {
          this.logger.debug(`跳过jonathan结果 ${hit._id} - COSPAR ID不匹配`);
          continue;
        }

        const scores: any = {
          cosparMatch,
          timeScore: first_epoch ? this.calculateTimeScore(source.sdate, first_epoch) : 0.5, // 如果没有提供first_epoch，给予中等分数
          nameScore: this.calculateNameScore(source.object_class),
        };

        this.logger.debug(`jonathan ${hit._id} 的分数:`, scores);

        const confidence = this.calculateConfidence(scores);
        this.logger.debug(`jonathan ${hit._id} 的置信度: ${confidence}`);

        if (confidence >= min_confidence) {
          correlations.push({
            event_id: hit._id,
            confidence,
            scores: {
              cosparMatch: scores.cosparMatch,
              timeScore: scores.timeScore,
              nameScore: scores.nameScore,
            },
            event_details: {
              ...source,
              source: 'jonathan' // 添加数据来源标识
            },
          });
        }
      }

      // 按置信度排序
      correlations.sort((a, b) => b.confidence - a.confidence);

      this.logger.debug(`找到 ${correlations.length} 个相关事件`);
      return {
        total: correlations.length,
        hits: correlations
      };
    } catch (error) {
      this.logger.error(`搜索相关事件出错: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 查询事件关联的碎片
   * @param query 查询参数
   * @returns 关联碎片列表及置信度
   */
  async findRelatedDebris(query: EventToDebrisQueryDto): Promise<EventCorrelationResultDto> {
    const { piece, sdate, event_type, min_confidence } = query;
    
    this.logger.debug(`搜索与事件相关的碎片:
      piece: ${piece}
      sdate: ${sdate}
      event_type: ${event_type}
      min_confidence: ${min_confidence}
    `);

    try {
      // 构建ES查询
      const searchQuery = {
        index: 'debris_info',
        body: {
          size: 1000,
          query: {
            bool: {
              should: [
                // 匹配COSPAR ID
                {
                  match: {
                    cospar_id: {
                      query: piece,
                      boost: 3.0 // 提高权重
                    }
                  }
                },
                // 匹配名称（模糊）
                {
                  match: {
                    name: {
                      query: piece,
                      fuzziness: "AUTO",
                      boost: 1.5
                    }
                  }
                }
              ],
              minimum_should_match: 1
            }
          }
        }
      };

      this.logger.debug(`碎片搜索查询: ${JSON.stringify(searchQuery, null, 2)}`);
      
      // 执行搜索
      const searchResult = await this.elasticsearchService.search<SearchResponse<DebrisDocument>>(searchQuery);
      
      this.logger.debug(`找到 ${searchResult.hits?.hits?.length || 0} 个潜在匹配的碎片`);
      
      const correlations: DebrisCorrelationResult[] = [];
      
      // 处理搜索结果
      const hits = searchResult.hits?.hits || [];
      for (const hit of hits) {
        const source = hit._source as DebrisDocument | undefined;
        if (!source || !hit._id) {
          this.logger.debug(`跳过碎片 ${hit._id || 'unknown'} - 无源数据或ID`);
          continue;
        }
        
        this.logger.debug(`处理碎片 ${hit._id}:`, JSON.stringify(source, null, 2));
        
        // 计算各维度分数
        const scores: any = {
          cosparMatch: this.calculateCosparMatch(source.cospar_id, piece),
          timeScore: this.calculateTimeScore(sdate, source.first_epoch),
          nameScore: this.calculateNameScore(source.object_class),
        };
        
        this.logger.debug(`碎片 ${hit._id} 的分数:`, scores);
        
        // 计算综合置信度
        const confidence = this.calculateConfidence(scores);
        this.logger.debug(`碎片 ${hit._id} 的置信度: ${confidence}`);
        
        // 如果置信度达到阈值，添加到结果中
        if (confidence >= min_confidence) {
          correlations.push({
            debris_id: hit._id,
            confidence,
            scores: {
              cosparMatch: scores.cosparMatch,
              timeScore: scores.timeScore,
              nameScore: scores.nameScore,
            },
            debris_details: source
          });
        }
      }
      
      // 按置信度排序
      correlations.sort((a, b) => b.confidence - a.confidence);
      
      this.logger.debug(`找到 ${correlations.length} 个相关碎片`);
      return {
        debris_list: correlations
      };
    } catch (error) {
      this.logger.error(`搜索相关碎片出错: ${error.message}`, error.stack);
      throw error;
    }
  }
} 