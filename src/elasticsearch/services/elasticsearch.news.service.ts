import { Injectable } from '@nestjs/common';
import { ElasticsearchBaseService } from './elasticsearch.base.service';
import { ElasticsearchService } from '@nestjs/elasticsearch';
import { ExtractThemesParams, FailedDocument, HotTheme, HotThemesParams, NewsDocument, NewsListResult, TranslateNewsParams, TranslationStatistics } from '../types/news.types';
import { TranslationService } from './translation.service';
import { NewsListQueryDto, KeywordMatchType } from '../dto/news-list-query.dto';
// 使用 require 以避免类型声明缺失导致的编译错误
// eslint-disable-next-line @typescript-eslint/no-var-requires
const cheerio = require('cheerio');

/**
 * Elasticsearch新闻服务类
 * 负责处理ES中news_*索引的新闻数据
 */
@Injectable()
export class ElasticsearchNewsService extends ElasticsearchBaseService {
  // 继承ElasticsearchBaseService中的logger实例

  // 存储最后处理时间
  private lastProcessedTimes = new Map<string, Date>();

  // 存储失败文档记录
  private failedDocuments: FailedDocument[] = [];

  // 失败文档最大存储数量
  private readonly maxFailedDocuments = 1000;

  constructor(
    protected readonly elasticsearchService: ElasticsearchService,
    private readonly translationService: TranslationService
  ) {
    super(elasticsearchService);
  }

  /**
   * 获取所有新闻索引
   * @returns 新闻索引列表
   */
  async getNewsIndices(): Promise<string[]> {
    try {
      const response = await this.elasticsearchService.cat.indices({ format: 'json' });
      const allIndices = response.map(index => index.index).filter(Boolean) as string[];
      const newsIndices = allIndices.filter(index =>
        typeof index === 'string' && index.startsWith('news_')
      );

      this.logger.log(`找到${newsIndices.length}个新闻索引`);
      return newsIndices;
    } catch (error: any) {
      this.logger.error(`获取新闻索引失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 查询未翻译的新闻数据
   * @param index 索引名称
   * @param batchSize 批次大小
   * @param forceRetranslate 是否强制重新翻译
   * @returns 新闻数据列表
   */
  async getUntranslatedNews(
    index: string,
    batchSize: number = 10,
    forceRetranslate: boolean = false
  ): Promise<NewsDocument[]> {
    try {
      this.logger.log(`获取未翻译的新闻，索引=${index}, 批次大小=${batchSize}, 强制重新翻译=${forceRetranslate}`);

      let query: any;

      if (forceRetranslate) {
        // 查询所有文档，不考虑是否已翻译
        this.logger.debug(`强制重新翻译模式，获取所有文档`);
        query = {
          match_all: {}
        };
      } else {
        // 简化查询条件：只查询真正未翻译的文档
        this.logger.debug(`标准模式，获取真正未翻译的文档`);
        query = {
          bool: {
            must: [
              // 必须满足：所有必要的中文字段都不存在或为空
              {
                bool: {
                  should: [
                    // 情况1：完全没有中文字段
                    {
                      bool: {
                        must_not: [
                          { exists: { field: "title_cn" } },
                          { exists: { field: "summary_cn" } },
                          { exists: { field: "content_cn" } }
                        ]
                      }
                    },
                    // 情况2：有中文字段但为空字符串
                    {
                      bool: {
                        must: [
                          { exists: { field: "title_cn" } },
                          { exists: { field: "summary_cn" } },
                          { exists: { field: "content_cn" } },
                          { term: { "title_cn.keyword": "" } },
                          { term: { "summary_cn.keyword": "" } },
                          { term: { "content_cn.keyword": "" } }
                        ]
                      }
                    }
                  ],
                  minimum_should_match: 1
                }
              }
            ],
            must_not: [
              // 排除翻译失败次数过多的文档（避免无限重试）
              {
                bool: {
                  must: [
                    { exists: { field: "translation_failed_count" } },
                    { range: { translation_failed_count: { gte: 3 } } }
                  ]
                }
              },
              // 排除最近刚尝试翻译失败的文档（1小时内）
              {
                bool: {
                  must: [
                    { exists: { field: "last_translation_attempt" } },
                    { 
                      range: { 
                        last_translation_attempt: { 
                          gte: "now-1h" 
                        } 
                      } 
                    },
                    { exists: { field: "translation_failed_count" } },
                    { range: { translation_failed_count: { gt: 0 } } }
                  ]
                }
              }
            ]
          }
        };
      }

      const response = await this.elasticsearchService.search<NewsDocument>({
        index,
        body: {
          query,
          size: batchSize,
          // 添加排序，优先处理较新的文档
          sort: [
            { "publish_date.year": { order: "desc" } },
            { "publish_date.month": { order: "desc" } },
            { "publish_date.day": { order: "desc" } }
          ]
        }
      });

      const hits = response.hits?.hits || [];
      const documents = hits.map(hit => ({
        _id: hit._id,
        _index: hit._index,
        ...hit._source
      })) as NewsDocument[];

      this.logger.log(`从索引${index}获取了${documents.length}条未翻译的新闻`);
      if (documents.length > 0) {
        this.logger.debug(`第一个文档ID: ${documents[0]._id}`);
      } else {
        this.logger.log(`索引${index}没有需要翻译的文档`);
      }
      return documents;
    } catch (error: any) {
      this.logger.error(`查询未翻译新闻失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 更新新闻文档（添加翻译结果）
   * @param index 索引名称
   * @param id 文档ID
   * @param translations 翻译结果
   * @returns 更新是否成功
   */
  async updateNewsWithTranslation(
    index: string,
    id: string,
    translations: { title_cn?: string; summary_cn?: string; content_cn?: string }
  ): Promise<boolean> {
    try {
      await this.elasticsearchService.update({
        index,
        id,
        body: {
          doc: translations
        }
      });

      return true;
    } catch (error: any) {
      this.logger.error(`更新新闻文档翻译失败, index=${index}, id=${id}: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 更新新闻文档的主题词
   * @param index 索引名称
   * @param id 文档ID
   * @param themes 主题词字符串，逗号分隔
   * @returns 更新是否成功
   */
  async updateNewsWithThemes(
    index: string,
    id: string,
    themes: string
  ): Promise<boolean> {
    try {
      await this.elasticsearchService.update({
        index,
        id,
        body: {
          doc: {
            themes_cn: themes
          }
        }
      });

      return true;
    } catch (error: any) {
      this.logger.error(`更新新闻文档主题词失败, index=${index}, id=${id}: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 记录翻译失败信息
   * @param index 索引名称
   * @param id 文档ID
   * @param errorMessage 错误信息
   */
  private async recordTranslationFailure(
    index: string,
    id: string,
    errorMessage: string
  ): Promise<void> {
    try {
      // 获取当前失败次数
      const doc = await this.elasticsearchService.get({
        index,
        id
      }).catch(() => null);

      const currentFailedCount = (doc as any)?._source?.translation_failed_count || 0;

      await this.elasticsearchService.update({
        index,
        id,
        body: {
          doc: {
            translation_failed_count: currentFailedCount + 1,
            last_translation_attempt: new Date().toISOString(),
            last_translation_error: errorMessage
          }
        }
      });

      this.logger.debug(`记录翻译失败信息: ${index}/${id}, 失败次数: ${currentFailedCount + 1}`);
    } catch (error: any) {
      this.logger.error(`记录翻译失败信息失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取需要提取主题词的新闻文档（包括未翻译的英文文档）
   * @param index 索引名称
   * @param batchSize 批次大小
   * @param forceReextract 是否强制重新提取主题词
   * @returns 新闻文档列表
   */
  async getNewsForThemeExtraction(
    index: string,
    batchSize: number = 10,
    forceReextract: boolean = false
  ): Promise<NewsDocument[]> {
    try {
      let query: any;

      if (forceReextract) {
        // 强制重新提取：查询所有有标题和内容的文档（中文或英文）
        query = {
          bool: {
            should: [
              // 有中文翻译的文档
              {
                bool: {
                  must: [
                    { exists: { field: "title_cn" } },
                    { exists: { field: "content_cn" } }
                  ]
                }
              },
              // 没有中文翻译但有英文原文的文档
              {
                bool: {
                  must: [
                    { exists: { field: "title" } },
                    { exists: { field: "content" } }
                  ],
                  must_not: [
                    { exists: { field: "title_cn" } }
                  ]
                }
              }
            ],
            minimum_should_match: 1
          }
        };
      } else {
        // 正常提取：查询未提取主题词的文档（中文或英文）
        query = {
          bool: {
            should: [
              // 有中文翻译但未提取主题词的文档
              {
                bool: {
                  must: [
                    { exists: { field: "title_cn" } },
                    { exists: { field: "content_cn" } }
                  ],
                  must_not: [
                    { exists: { field: "themes_cn" } }
                  ]
                }
              },
              // 没有中文翻译且未提取主题词的英文文档
              {
                bool: {
                  must: [
                    { exists: { field: "title" } },
                    { exists: { field: "content" } }
                  ],
                  must_not: [
                    { exists: { field: "title_cn" } },
                    { exists: { field: "themes_cn" } }
                  ]
                }
              }
            ],
            minimum_should_match: 1
          }
        };
      }

      const response = await this.elasticsearchService.search<NewsDocument>({
        index,
        body: {
          query,
          size: batchSize
        }
      });

      const hits = response.hits?.hits || [];
      const documents = hits.map(hit => ({
        _id: hit._id,
        _index: hit._index,
        ...hit._source
      })) as NewsDocument[];

      this.logger.debug(`从索引${index}获取了${documents.length}条需要提取主题词的新闻（包括英文文档）`);
      return documents;
    } catch (error: any) {
      this.logger.error(`查询需要提取主题词的新闻失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 翻译新闻数据
   * @param params 翻译参数
   * @returns 翻译统计信息
   */
  async translateNews(params: TranslateNewsParams): Promise<TranslationStatistics> {
    const {
      batchSize = 10,
      maxDocs = 0,
      forceRetranslate = false,
      forceRefetchContent = false,
      specificIndexes,
      llmMode,
      customModel,
      autoExtractThemes = true,
      documentId
    } = params;

    // 根据参数动态调整翻译服务配置
    if (llmMode || customModel) {
      const configUpdates: any = {};
      
      if (customModel) {
        configUpdates.model = customModel;
        this.logger.log(`使用自定义模型: ${customModel}`);
      } else if (llmMode) {
        // 根据模式设置模型和相关参数
        switch (llmMode) {
          case 'high_quality':
            configUpdates.model = 'qwen-max-latest';
            configUpdates.temperature = 0.05;
            configUpdates.maxConcurrentRequests = 2;
            break;
          case 'fast':
            configUpdates.model = 'qwen-turbo';
            configUpdates.temperature = 0.2;
            configUpdates.maxConcurrentRequests = 5;
            break;
          default: // 'default'
            configUpdates.model = 'qwen-turbo';
            configUpdates.temperature = 0.1;
            configUpdates.maxConcurrentRequests = 3;
            break;
        }
        this.logger.log(`使用模式: ${llmMode}, 模型: ${configUpdates.model}`);
      }
      
      this.translationService.updateTranslationConfig(configUpdates);
    }

    let statistics: TranslationStatistics = {
      total: 0,
      success: 0,
      failed: 0,
      skipped: 0,
      indexCount: 0,
      indexes: [],
      startTime: new Date(),
      themeExtraction: autoExtractThemes ? {
        total: 0,
        success: 0,
        failed: 0,
        skipped: 0
      } : undefined
    };

    try {
      // 如果指定了特定文档ID，则只处理该文档
      if (documentId) {
        this.logger.log(`指定文档ID模式，处理文档: ${documentId}`);
        
        // 验证必须指定索引
        if (!specificIndexes || specificIndexes.length === 0) {
          throw new Error('使用documentId参数时必须指定specificIndexes参数来指定文档所在的索引');
        }

        // 处理特定文档ID
        return await this.translateSpecificDocument(documentId, specificIndexes, forceRetranslate, forceRefetchContent, autoExtractThemes);
      }

      // 原有的批量处理逻辑
      // 获取新闻索引
      let newsIndices = await this.getNewsIndices();

      // 如果指定了特定索引，则只处理这些索引
      if (specificIndexes && specificIndexes.length > 0) {
        newsIndices = newsIndices.filter(index =>
          specificIndexes.includes(index) ||
          specificIndexes.some(pattern => {
            // 支持使用通配符匹配索引
            if (pattern.includes('*')) {
              const regexPattern = pattern.replace(/\*/g, '.*');
              return new RegExp(`^${regexPattern}$`).test(index);
            }
            return index === pattern;
          })
        );
      }

      statistics.indexCount = newsIndices.length;
      statistics.indexes = newsIndices;

      this.logger.log(`开始翻译${newsIndices.length}个新闻索引的数据，批次大小: ${batchSize}, 强制重翻译: ${forceRetranslate}, 强制重新爬取: ${forceRefetchContent}, 自动提取主题词: ${autoExtractThemes}`);

      // 处理每个索引
      for (const index of newsIndices) {
        let processedCount = 0;
        let hasMore = true;

        while (hasMore) {
          // 获取需要翻译的文档
          const newsDocuments = await this.getUntranslatedNews(index, batchSize, forceRetranslate);

          if (newsDocuments.length === 0) {
            this.logger.log(`索引${index}没有更多需要翻译的文档，退出循环`);
            hasMore = false;
            continue;
          }

          this.logger.debug(`索引${index}处理批次，获取到${newsDocuments.length}个文档`);
          statistics.total += newsDocuments.length;

          // 处理每个新闻文档
          for (const doc of newsDocuments) {
            try {
              // 检查是否达到最大处理数量
              if (maxDocs > 0 && processedCount >= maxDocs) {
                hasMore = false;
                break;
              }

              processedCount++;

              // 获取内容，优先使用已有内容
              let content = doc.content || '';
              let contentUpdated = false;

              // 内容爬取逻辑优化：添加爬取标记机制
              const shouldFetchContent = (!content || content.trim() === '') && doc.info_source && 
                (forceRefetchContent || !doc.content_fetched);

              if (shouldFetchContent) {
                try {
                  this.logger.log(`文档${doc._id}${forceRefetchContent ? '强制重新' : ''}尝试从${doc.info_source}获取内容`);
                  const fetchedContent = await this.fetchContentFromUrl(doc.info_source!);

                  // 更新爬取标记，无论是否成功获取到内容
                  const fetchUpdateData: any = {
                    content_fetched: true,
                    content_fetch_time: new Date().toISOString(),
                    content_fetch_success: false
                  };

                  // 如果成功获取到内容，先更新原始文档
                  if (fetchedContent && fetchedContent.trim() !== '') {
                    // 更新内容变量
                    content = fetchedContent;
                    contentUpdated = true;
                    fetchUpdateData.content = fetchedContent;
                    fetchUpdateData.content_fetch_success = true;

                    this.logger.log(`成功从${doc.info_source}获取内容并更新文档${doc._id}，内容长度: ${fetchedContent.length}`);
                  } else {
                    this.logger.warn(`从${doc.info_source}获取内容失败或内容为空，已标记为已爬取`);
                  }

                  // 更新数据库中的文档爬取标记
                  await this.elasticsearchService.update({
                    index: doc._index || index,
                    id: doc._id || '',
                    body: {
                      doc: fetchUpdateData
                    }
                  });

                } catch (fetchError) {
                  this.logger.error(`从${doc.info_source}获取内容失败: ${fetchError.message}`);
                  
                  // 即使爬取失败也要标记为已尝试爬取
                  try {
                    await this.elasticsearchService.update({
                      index: doc._index || index,
                      id: doc._id || '',
                      body: {
                        doc: {
                          content_fetched: true,
                          content_fetch_time: new Date().toISOString(),
                          content_fetch_success: false
                        }
                      }
                    });
                  } catch (updateError) {
                    this.logger.error(`更新爬取标记失败: ${updateError.message}`);
                  }
                }
              } else if (doc.content_fetched && !forceRefetchContent) {
                this.logger.debug(`文档${doc._id}已标记为爬取过，跳过内容爬取`);
              }

              // 在翻译前检查文档长度
              const contentLength = content?.length || 0;
              const ULTRA_LONG_THRESHOLD = 100000; // 100K字符阈值
              
              if (contentLength > ULTRA_LONG_THRESHOLD) {
                this.logger.warn(`文档${doc._id}的content过长 (${contentLength}字符，超过${ULTRA_LONG_THRESHOLD}字符阈值)，跳过翻译并清空已有翻译`);
                
                // 直接更新文档，将翻译字段设置为空
                const updateData: any = {
                  title_cn: '',
                  summary_cn: '',
                  content_cn: '',
                  themes_cn: '',
                  translated_at: new Date().toISOString(),
                  translation_skipped_reason: `content过长(${contentLength}字符)`
                };

                try {
                  await this.elasticsearchService.update({
                    index,
                    id: doc._id || '',
                    body: { doc: updateData }
                  });

                  statistics.success++; // 标记为成功处理（虽然跳过了翻译）
                  this.logger.log(`文档${doc._id}超长内容跳过翻译成功`);

                  // 如果启用了主题提取，也标记相关统计
                  if (autoExtractThemes && statistics.themeExtraction) {
                    statistics.themeExtraction.total++;
                    statistics.themeExtraction.success++; // 标记为成功（实际返回空）
                  }

                } catch (updateError: any) {
                  statistics.failed++;
                  this.logger.error(`更新超长文档失败: ${index}/${doc._id}: ${updateError.message}`);
                }
                
                continue; // 跳过后续的翻译逻辑
              }

              // 根据是否启用自动主题提取选择不同的处理方式
              if (autoExtractThemes && statistics.themeExtraction) {
                // 使用组合方法一次性完成翻译和主题提取
                try {
                  if (contentUpdated) {
                    this.logger.debug(`使用从 URL 获取的内容进行组合翻译，内容长度: ${content?.length || 0}`);
                  }

                  const combinedResult = await this.translationService.translateAndExtractInOneCall(
                    doc.title || '',
                    doc.summary || '',
                    content || '',
                    0, // retryCount
                    forceRetranslate // 传递强制重新翻译参数
                  );

                  // 更新文档（包含翻译和主题词）
                  const updateData: any = {
                    title_cn: combinedResult.title_cn,
                    summary_cn: combinedResult.summary_cn,
                    content_cn: combinedResult.content_cn
                  };

                  // 如果有主题词，也一起更新
                  if (combinedResult.themes_cn && combinedResult.themes_cn.trim() !== '') {
                    updateData.themes_cn = combinedResult.themes_cn;
                  }

                  // 修复：使用try-catch来正确判断更新是否成功
                  try {
                    await this.elasticsearchService.update({
                      index,
                      id: doc._id || '',
                      body: {
                        doc: updateData
                      }
                    });

                    // 更新成功
                    statistics.success++;
                    this.logger.debug(`成功完成组合翻译: ${index}/${doc._id}`);

                    // 统计主题提取结果
                    statistics.themeExtraction.total++;
                    if (combinedResult.themes_cn && combinedResult.themes_cn.trim() !== '') {
                      statistics.themeExtraction.success++;
                      this.logger.debug(`成功提取文档主题词: ${index}/${doc._id}, 主题词: ${combinedResult.themes_cn}`);
                    } else {
                      statistics.themeExtraction.skipped++;
                      this.logger.warn(`组合翻译中主题提取返回空结果: ${index}/${doc._id}`);
                    }
                  } catch (updateError: any) {
                    // 更新失败
                    statistics.failed++;
                    this.logger.error(`更新组合翻译结果失败: ${index}/${doc._id}: ${updateError.message}`, updateError.stack);
                    
                    // 记录翻译失败信息
                    await this.recordTranslationFailure(index, doc._id || '', updateError.message);
                  }
                } catch (combinedError: any) {
                  this.logger.error(`组合翻译失败: ${index}/${doc._id}: ${combinedError.message}`);

                  // 如果组合翻译失败，尝试回退到分别翻译
                  if (combinedError.message.includes('SKIP_DOCUMENT')) {
                    statistics.failed++;
                    this.logger.warn(`跳过文档（内容不适当）: ${index}/${doc._id}`);
                    continue;
                  }

                  try {
                    this.logger.warn(`组合翻译失败，回退到分别翻译: ${index}/${doc._id}`);
                    
                    // 在回退翻译前也检查content长度
                    if (contentLength > ULTRA_LONG_THRESHOLD) {
                      this.logger.warn(`回退翻译时检测到超长content (${contentLength}字符)，跳过翻译并清空字段: ${index}/${doc._id}`);
                      
                      // 直接设置为空并更新文档
                      const success = await this.updateNewsWithTranslation(
                        index,
                        doc._id || '',
                        { title_cn: '', summary_cn: '', content_cn: '' }
                      );

                      if (success) {
                        statistics.success++;
                        this.logger.log(`回退翻译时超长内容跳过成功: ${index}/${doc._id}`);
                      } else {
                        statistics.failed++;
                        this.logger.error(`回退翻译时更新超长文档失败: ${index}/${doc._id}`);
                      }
                      continue; // 跳过后续翻译逻辑
                    }
                    
                    const titleCn = await this.translationService.translateText(doc.title || '', 0, false, forceRetranslate);
                    const summaryCn = await this.translationService.translateText(doc.summary || '', 0, false, forceRetranslate);
                    const contentCn = await this.translationService.translateText(content || '', 0, false, forceRetranslate);

                    const success = await this.updateNewsWithTranslation(
                      index,
                      doc._id || '',
                      { title_cn: titleCn, summary_cn: summaryCn, content_cn: contentCn }
                    );

                    if (success) {
                      statistics.success++;
                      this.logger.debug(`回退方式翻译成功: ${index}/${doc._id}`);

                      // 尝试提取主题词
                      try {
                        statistics.themeExtraction.total++;
                        const themes = await this.translationService.extractThemes(titleCn, contentCn);
                        
                        if (themes && themes.trim() !== '') {
                          const themeSuccess = await this.updateNewsWithThemes(index, doc._id || '', themes);
                          
                          if (themeSuccess) {
                            statistics.themeExtraction.success++;
                            this.logger.debug(`回退方式主题提取成功: ${index}/${doc._id}, 主题词: ${themes}`);
                          } else {
                            statistics.themeExtraction.failed++;
                            this.logger.warn(`回退方式更新主题词失败: ${index}/${doc._id}`);
                          }
                        } else {
                          statistics.themeExtraction.skipped++;
                          this.logger.warn(`回退方式主题提取返回空结果: ${index}/${doc._id}`);
                        }
                      } catch (themeError: any) {
                        statistics.themeExtraction.failed++;
                        this.logger.error(`回退方式主题提取失败: ${index}/${doc._id}: ${themeError.message}`);
                      }
                    } else {
                      statistics.failed++;
                      this.logger.warn(`回退方式翻译更新失败: ${index}/${doc._id}`);
                    }
                  } catch (fallbackError: any) {
                    // 如果回退方式也失败，抛出原始错误
                    throw combinedError;
                  }
                }
              } else {
                // 只进行翻译，不提取主题词
                // 在分别翻译前也检查content长度
                if (contentLength > ULTRA_LONG_THRESHOLD) {
                  this.logger.warn(`分别翻译时检测到超长content (${contentLength}字符)，跳过翻译并清空字段: ${index}/${doc._id}`);
                  
                  // 直接设置为空并更新文档
                  const success = await this.updateNewsWithTranslation(
                    index,
                    doc._id || '',
                    { title_cn: '', summary_cn: '', content_cn: '' }
                  );

                  if (success) {
                    statistics.success++;
                    this.logger.log(`分别翻译时超长内容跳过成功: ${index}/${doc._id}`);
                  } else {
                    statistics.failed++;
                    this.logger.error(`分别翻译时更新超长文档失败: ${index}/${doc._id}`);
                  }
                  continue; // 跳过后续翻译逻辑
                }
                
                const titleCn = await this.translationService.translateText(doc.title || '', 0, false, forceRetranslate);
                const summaryCn = await this.translationService.translateText(doc.summary || '', 0, false, forceRetranslate);
                
                if (contentUpdated) {
                  this.logger.debug(`使用从 URL 获取的内容进行翻译，内容长度: ${content?.length || 0}`);
                }
                const contentCn = await this.translationService.translateText(content || '', 0, false, forceRetranslate);

                // 更新文档
                const success = await this.updateNewsWithTranslation(
                  index,
                  doc._id || '',
                  { title_cn: titleCn, summary_cn: summaryCn, content_cn: contentCn }
                );

                if (success) {
                  statistics.success++;
                  this.logger.debug(`成功翻译文档: ${index}/${doc._id}`);
                } else {
                  statistics.failed++;
                  this.logger.warn(`更新文档失败: ${index}/${doc._id}`);
                }
              }

              // 添加延迟避免过于频繁的请求
              await new Promise(resolve => setTimeout(resolve, 100));
            } catch (error: any) {
              statistics.failed++;
              this.logger.error(`处理文档失败: ${index}/${doc._id}: ${error.message}`, error.stack);
              
              // 记录翻译失败信息，避免重复处理
              try {
                await this.recordTranslationFailure(index, doc._id || '', error.message);
              } catch (recordError) {
                this.logger.error(`记录翻译失败信息失败: ${recordError.message}`);
              }
            }
          }

          // 如果返回的文档数量小于批次大小，说明没有更多文档了
          if (newsDocuments.length < batchSize) {
            hasMore = false;
          }
        }
      }

      // 计算总耗时
      statistics.endTime = new Date();
      statistics.elapsedTime = statistics.endTime.getTime() - statistics.startTime.getTime();

      this.logger.log(`翻译任务完成，总计处理: ${statistics.total}, 成功: ${statistics.success}, 失败: ${statistics.failed}, 跳过: ${statistics.skipped}, 耗时: ${statistics.elapsedTime}ms`);
      
      if (statistics.themeExtraction) {
        this.logger.log(`主题提取统计 - 总计: ${statistics.themeExtraction.total}, 成功: ${statistics.themeExtraction.success}, 失败: ${statistics.themeExtraction.failed}, 跳过: ${statistics.themeExtraction.skipped}`);
      }

      return statistics;
    } catch (error: any) {
      this.logger.error(`翻译任务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取翻译状态统计
   * @returns 各索引的翻译状态统计
   */
  async getTranslationStatus(): Promise<any> {
    const result = [];
    const newsIndices = await this.getNewsIndices();

    for (const index of newsIndices) {
      try {
        // 获取索引中的总文档数
        const totalResponse = await this.elasticsearchService.count({
          index
        });

        // 获取已翻译的文档数
        const translatedResponse = await this.elasticsearchService.count({
          index,
          body: {
            query: {
              bool: {
                must: [
                  { exists: { field: "title_cn" } },
                  { exists: { field: "summary_cn" } },
                  { exists: { field: "content_cn" } }
                ]
              }
            }
          }
        });

        // 计算未翻译的文档数
        const totalDocs = totalResponse.count;
        const translatedDocs = translatedResponse.count;
        const untranslatedDocs = totalDocs - translatedDocs;

        // 获取最后处理时间
        const lastProcessedTime = this.lastProcessedTimes.get(index);

        result.push({
          index,
          totalDocs,
          translatedDocs,
          untranslatedDocs,
          translationProgress: totalDocs > 0 ? (translatedDocs / totalDocs) * 100 : 0,
          lastProcessedTime: lastProcessedTime ? lastProcessedTime.toISOString() : null
        });
      } catch (error: any) {
        this.logger.error(`获取索引${index}的翻译状态失败: ${error.message}`, error.stack);
        result.push({
          index,
          error: error.message
        });
      }
    }

    return result;
  }

  /**
   * 获取API调用统计
   */
  getAPIStats() {
    return this.translationService.getAPIStats();
  }

  /**
   * 获取详细的失败统计信息
   */
  getFailureStats() {
    const stats = this.translationService.getFailureStats();
    const failedDocs = this.getFailedTranslations();
    return {
      ...stats,
      failedDocuments: failedDocs
    };
  }

  /**
   * 重置失败统计
   */
  resetFailureStats(): void {
    this.translationService.resetFailureStats();
    // 同时清空失败文档列表
    this.failedDocuments = [];
    this.logger.log('失败统计和失败文档列表已重置');
  }

  /**
   * 获取热门主题词
   * @param params 热门主题参数
   * @returns 热门主题列表及统计信息
   */
  async getHotThemes(params: HotThemesParams): Promise<{
    themes: HotTheme[];
    total: number;
    indexCount: number;
    indexes: string[];
    processedDocs: number;
  }> {
    const {
      topN = 10,
      minCount = 1,
      specificIndexes
    } = params;

    try {
      // 获取新闻索引
      let newsIndices = await this.getNewsIndices();

      // 如果指定了特定索引，则只处理这些索引
      if (specificIndexes && specificIndexes.length > 0) {
        newsIndices = newsIndices.filter(index =>
          specificIndexes.includes(index) ||
          specificIndexes.some(pattern => {
            // 支持使用通配符匹配索引
            if (pattern.includes('*')) {
              const regexPattern = pattern.replace(/\*/g, '.*');
              return new RegExp(`^${regexPattern}$`).test(index);
            }
            return index === pattern;
          })
        );
      }

      this.logger.log(`开始统计${newsIndices.length}个新闻索引的热门主题词`);

      // 主题词计数映射
      const themeCountMap = new Map<string, number>();
      let processedDocs = 0;

      // 处理每个索引
      for (const index of newsIndices) {
        let from = 0;
        const size = 1000; // 每次获取的文档数量
        let hasMore = true;

        while (hasMore) {
          // 查询有主题词的文档
          const response = await this.elasticsearchService.search<NewsDocument>({
            index,
            body: {
              query: {
                exists: { field: "themes_cn" }
              },
              _source: ["themes_cn"],
              from,
              size
            }
          });

          const hits = response.hits?.hits || [];
          if (hits.length === 0) {
            hasMore = false;
            continue;
          }

          // 处理每个文档的主题词
          for (const hit of hits) {
            const source = hit._source;
            if (source && source.themes_cn) {
              // 将主题词字符串分割为数组
              const themes = source.themes_cn.split(/[,，]/).map((theme: string) => theme.trim()).filter(Boolean);

              // 统计每个主题词的出现次数
              for (const theme of themes) {
                const count = themeCountMap.get(theme) || 0;
                themeCountMap.set(theme, count + 1);
              }
            }
          }

          processedDocs += hits.length;
          from += size;

          // 如果返回的文档数量小于批次大小，说明没有更多文档
          if (hits.length < size) {
            hasMore = false;
          }
        }

        this.logger.debug(`索引${index}处理完成}`);
      }

      // 过滤出现次数低于最小值的主题词
      const filteredThemes = Array.from(themeCountMap.entries())
        .filter(([_, count]) => count >= minCount)
        .map(([theme, count]) => ({ theme, count }));

      // 按出现次数降序排序
      const sortedThemes = filteredThemes.sort((a, b) => b.count - a.count);

      // 取前N个
      const topThemes = sortedThemes.slice(0, topN);

      this.logger.log(`热门主题词统计完成，共处理${processedDocs}条文档，找到${filteredThemes.length}个主题词，返回前${topThemes.length}个`);

      return {
        themes: topThemes,
        total: filteredThemes.length,
        indexCount: newsIndices.length,
        indexes: newsIndices,
        processedDocs
      };
    } catch (error: any) {
      this.logger.error(`获取热门主题词失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 提取新闻主题词
   * @param params 主题提取参数
   * @returns 主题提取统计信息
   */
  async extractNewsThemes(params: ExtractThemesParams): Promise<TranslationStatistics> {
    const {
      batchSize = 10,
      maxDocs = 0,
      forceReextract = false,
      specificIndexes,
      llmMode,
      customModel
    } = params;

    // 根据参数动态调整翻译服务配置
    if (llmMode || customModel) {
      const configUpdates: any = {};
      
      if (customModel) {
        configUpdates.model = customModel;
        this.logger.log(`主题提取使用自定义模型: ${customModel}`);
      } else if (llmMode) {
        // 根据模式设置模型和相关参数
        switch (llmMode) {
          case 'high_quality':
            configUpdates.model = 'qwen-max-latest';
            configUpdates.temperature = 0.05;
            configUpdates.maxConcurrentRequests = 2;
            break;
          case 'fast':
            configUpdates.model = 'qwen-turbo';
            configUpdates.temperature = 0.2;
            configUpdates.maxConcurrentRequests = 5;
            break;
          default: // 'default'
            configUpdates.model = 'qwen-turbo';
            configUpdates.temperature = 0.1;
            configUpdates.maxConcurrentRequests = 3;
            break;
        }
        this.logger.log(`主题提取使用模式: ${llmMode}, 模型: ${configUpdates.model}`);
      }
      
      this.translationService.updateThemeExtractionConfig(configUpdates);
    }

    let statistics: TranslationStatistics = {
      total: 0,
      success: 0,
      failed: 0,
      skipped: 0,
      indexCount: 0,
      indexes: [],
      startTime: new Date()
    };

    try {
      // 获取新闻索引
      let newsIndices = await this.getNewsIndices();

      // 如果指定了特定索引，则只处理这些索引
      if (specificIndexes && specificIndexes.length > 0) {
        newsIndices = newsIndices.filter(index =>
          specificIndexes.includes(index) ||
          specificIndexes.some(pattern => {
            // 支持使用通配符匹配索引
            if (pattern.includes('*')) {
              const regexPattern = pattern.replace(/\*/g, '.*');
              return new RegExp(`^${regexPattern}$`).test(index);
            }
            return index === pattern;
          })
        );
      }

      statistics.indexCount = newsIndices.length;
      statistics.indexes = newsIndices;

      this.logger.log(`开始提取${newsIndices.length}个新闻索引的主题词，批次大小: ${batchSize}, 强制重新提取: ${forceReextract}`);

      // 处理每个索引
      for (const index of newsIndices) {
        let processedCount = 0;
        let hasMore = true;

        while (hasMore) {
          // 获取需要提取主题词的文档
          const newsDocuments = await this.getNewsForThemeExtraction(index, batchSize, forceReextract);

          if (newsDocuments.length === 0) {
            hasMore = false;
            continue;
          }

          statistics.total += newsDocuments.length;

          // 处理每个新闻文档
          for (const doc of newsDocuments) {
            try {
              // 检查是否达到最大处理数量
              if (maxDocs > 0 && processedCount >= maxDocs) {
                hasMore = false;
                break;
              }

              processedCount++;

              // 提取主题词 - 支持中文和英文文档
              let titleForExtraction = '';
              let contentForExtraction = '';
              
              // 优先使用中文翻译，如果没有则使用英文原文
              if (doc.title_cn && doc.title_cn.trim() !== '') {
                titleForExtraction = doc.title_cn;
              } else if (doc.title && doc.title.trim() !== '') {
                titleForExtraction = doc.title;
              }
              
              if (doc.content_cn && doc.content_cn.trim() !== '') {
                contentForExtraction = doc.content_cn;
              } else if (doc.content && doc.content.trim() !== '') {
                contentForExtraction = doc.content;
              }

              // 如果既没有中文也没有英文内容，跳过该文档
              if (!titleForExtraction && !contentForExtraction) {
                statistics.skipped++;
                this.logger.warn(`跳过文档（无有效内容）: ${index}/${doc._id}`);
                continue;
              }

              const themes = await this.translationService.extractThemes(titleForExtraction, contentForExtraction);

              // 更新文档
              const success = await this.updateNewsWithThemes(
                index,
                doc._id || '',
                themes
              );

              if (success) {
                statistics.success++;
                this.logger.debug(`成功提取文档主题词: ${index}/${doc._id}, 主题词: ${themes}`);
              } else {
                statistics.failed++;
                this.logger.warn(`更新文档主题词失败: ${index}/${doc._id}`);
              }

              // 添加短暂延迟，避免API过载
              await new Promise(resolve => setTimeout(resolve, 100));
            } catch (error: any) {
              // 检查是否是跳过文档的特殊错误
              if (error.message.startsWith('SKIP_DOCUMENT:')) {
                statistics.skipped++;
                this.logger.warn(`跳过文档: ${index}/${doc._id}: ${error.message}`);

                // 记录跳过的文档，但不计入失败文档
                const skippedDoc: FailedDocument = {
                  index,
                  id: doc._id || '',
                  reason: error.message,
                  timestamp: new Date(),
                  skipped: true  // 标记为跳过文档
                };

                // 如果统计信息中没有跳过文档列表，创建一个
                if (!statistics.skippedDocuments) {
                  statistics.skippedDocuments = [];
                }

                // 添加到统计信息中
                statistics.skippedDocuments.push(skippedDoc);

                // 添加短暂停，避免过快处理下一个文档
                await new Promise(resolve => setTimeout(resolve, 500));
                continue;
              }

              statistics.failed++;

              // 记录失败文档
              const failedDoc: FailedDocument = {
                index,
                id: doc._id || '',
                reason: error.message,
                timestamp: new Date()
              };

              // 添加到失败文档列表
              this.addFailedDocument(failedDoc);

              // 如果统计信息中没有失败文档列表，创建一个
              if (!statistics.failedDocuments) {
                statistics.failedDocuments = [];
              }

              // 添加到统计信息中
              statistics.failedDocuments.push(failedDoc);

              this.logger.error(`处理文档时出错: ${index}/${doc._id}: ${error.message}`, error.stack);

              // 如果是熔断器触发错误，暂停处理
              if (error.message.includes('熔断器已触发')) {
                this.logger.warn(`检测到熔断器触发，暂停处理并等待重置`);
                // 暂停5分钟后继续
                await new Promise(resolve => setTimeout(resolve, 5 * 60 * 1000));
                continue;
              }

              // 如果是API限制或超时类错误，可能需要暂停一段时间
              if (error.message.includes('timed out') || error.message.includes('rate limit')) {
                this.logger.warn(`检测到API限制或超时，暂停30秒后继续`);
                await new Promise(resolve => setTimeout(resolve, 30000));
              }
            }
          }

          // 如果返回的文档数量小于批次大小，说明没有更多需要提取主题词的文档
          if (newsDocuments.length < batchSize) {
            hasMore = false;
          }
        }

        this.logger.log(`索引${index}处理完成，总计处理${processedCount}条新闻`);

        // 更新最后处理时间
        this.lastProcessedTimes.set(index, new Date());
      }

      // 计算耗时
      statistics.endTime = new Date();
      statistics.elapsedTime = statistics.endTime.getTime() - statistics.startTime.getTime();

      this.logger.log(`新闻主题提取任务完成，总耗时: ${statistics.elapsedTime}ms, 成功: ${statistics.success}, 失败: ${statistics.failed}`);

      return statistics;
    } catch (error: any) {
      this.logger.error(`提取新闻主题词任务失败: ${error.message}`, error.stack);

      // 计算耗时
      statistics.endTime = new Date();
      statistics.elapsedTime = statistics.endTime.getTime() - statistics.startTime.getTime();

      throw error;
    }
  }

  /**
   * 添加失败文档记录
   * @param doc 失败文档记录
   */
  private addFailedDocument(doc: FailedDocument): void {
    // 添加到失败文档列表
    this.failedDocuments.push(doc);

    // 如果失败文档超过最大存储数量，删除最早的记录
    if (this.failedDocuments.length > this.maxFailedDocuments) {
      this.failedDocuments.shift();
    }

    this.logger.debug(`当前失败文档记录数: ${this.failedDocuments.length}`);
  }

  /**
   * 获取失败的翻译文档
   * @returns 失败文档列表
   */
  getFailedTranslations(): FailedDocument[] {
    return [...this.failedDocuments];
  }

  /**
   * 重试失败的翻译
   * @param failedDocs 失败文档列表，如果不提供则使用内部存储的失败文档
   * @param maxRetries 最大重试次数
   * @returns 重试统计信息
   */
  async retryFailedTranslations(failedDocs?: FailedDocument[], maxRetries: number = 3): Promise<TranslationStatistics> {
    // 如果没有提供失败文档列表，使用内部存储的失败文档
    const docsToRetry = failedDocs || [...this.failedDocuments];

    if (docsToRetry.length === 0) {
      this.logger.log('没有需要重试的失败文档');
      return {
        total: 0,
        success: 0,
        failed: 0,
        skipped: 0,
        indexCount: 0,
        indexes: [],
        startTime: new Date(),
        endTime: new Date(),
        elapsedTime: 0
      };
    }

    this.logger.log(`开始重试${docsToRetry.length}个失败文档`);

    const statistics: TranslationStatistics = {
      total: docsToRetry.length,
      success: 0,
      failed: 0,
      skipped: 0,
      indexCount: 0,
      indexes: [],
      startTime: new Date(),
      failedDocuments: []
    };

    // 按索引分组
    const docsByIndex = new Map<string, FailedDocument[]>();

    for (const doc of docsToRetry) {
      if (!docsByIndex.has(doc.index)) {
        docsByIndex.set(doc.index, []);
        statistics.indexes.push(doc.index);
      }
      docsByIndex.get(doc.index)?.push(doc);
    }

    statistics.indexCount = docsByIndex.size;

    // 处理每个索引的失败文档
    for (const [index, docs] of docsByIndex.entries()) {
      this.logger.log(`处理索引${index}的${docs.length}个失败文档`);

      for (const doc of docs) {
        try {
          // 获取原始文档
          const newsDoc = await this.getDocumentById(index, doc.id);

          if (!newsDoc) {
            this.logger.warn(`文档不存在或无法获取: ${index}/${doc.id}`);
            statistics.skipped++;
            continue;
          }

          // 设置重试次数
          const retryCount = (doc.retryCount || 0) + 1;
          if (retryCount > maxRetries) {
            this.logger.warn(`文档${index}/${doc.id}超过最大重试次数(${maxRetries})`);
            statistics.skipped++;
            continue;
          }

          // 在重试翻译前检查content长度
          const contentLength = newsDoc.content?.length || 0;
          const ULTRA_LONG_THRESHOLD = 100000; // 100K字符阈值
          
          if (contentLength > ULTRA_LONG_THRESHOLD) {
            this.logger.warn(`重试翻译时检测到超长content (${contentLength}字符)，跳过翻译并清空字段: ${index}/${doc.id}`);
            
            // 直接设置为空并更新文档
            const success = await this.updateNewsWithTranslation(
              index,
              doc.id,
              { title_cn: '', summary_cn: '', content_cn: '' }
            );

            if (success) {
              statistics.success++;
              this.logger.log(`重试翻译时超长内容跳过成功: ${index}/${doc.id}`);
              
              // 从失败文档列表中移除
              this.removeFailedDocument(doc);
            } else {
              statistics.failed++;
              this.logger.error(`重试翻译时更新超长文档失败: ${index}/${doc.id}`);
            }
            continue; // 跳过后续翻译逻辑
          }

          // 翻译标题（重试时强制重新翻译）
          const titleCn = await this.translationService.translateText(newsDoc.title || '', 0, false, true);

          // 翻译摘要（重试时强制重新翻译）
          const summaryCn = await this.translationService.translateText(newsDoc.summary || '', 0, false, true);

          // 翻译内容（重试时强制重新翻译）
          const contentCn = await this.translationService.translateText(newsDoc.content || '', 0, false, true);

          // 更新文档
          const success = await this.updateNewsWithTranslation(
            index,
            doc.id,
            { title_cn: titleCn, summary_cn: summaryCn, content_cn: contentCn }
          );

          if (success) {
            statistics.success++;
            this.logger.debug(`成功重试翻译文档: ${index}/${doc.id}`);

            // 从失败文档列表中移除
            this.removeFailedDocument(doc);
          } else {
            statistics.failed++;
            this.logger.warn(`重试更新文档失败: ${index}/${doc.id}`);

            // 更新重试次数
            doc.retryCount = retryCount;
            if (!statistics.failedDocuments) {
              statistics.failedDocuments = [];
            }
            statistics.failedDocuments.push(doc);
          }

          // 添加短暂延迟，避免API过载
          await new Promise(resolve => setTimeout(resolve, 200));

        } catch (error: any) {
          statistics.failed++;
          this.logger.error(`重试处理文档时出错: ${index}/${doc.id}: ${error.message}`, error.stack);

          // 更新重试次数和失败原因
          doc.retryCount = (doc.retryCount || 0) + 1;
          doc.reason = error.message;
          doc.timestamp = new Date();

          if (!statistics.failedDocuments) {
            statistics.failedDocuments = [];
          }
          statistics.failedDocuments.push(doc);

          // 如果是API限制或超时类错误，可能需要暂停一段时间
          if (error.message.includes('timed out') || error.message.includes('rate limit')) {
            this.logger.warn(`检测到API限制或超时，暂停30秒后继续`);
            await new Promise(resolve => setTimeout(resolve, 30000));
          }
        }
      }
    }

    // 计算耗时
    statistics.endTime = new Date();
    statistics.elapsedTime = statistics.endTime.getTime() - statistics.startTime.getTime();

    this.logger.log(`重试完成，总耗时: ${statistics.elapsedTime}ms, 成功: ${statistics.success}, 失败: ${statistics.failed}, 跳过: ${statistics.skipped}`);

    return statistics;
  }

  /**
   * 翻译特定文档ID的文档
   * @param documentId 文档ID
   * @param specificIndexes 指定的索引列表
   * @param forceRetranslate 是否强制重新翻译
   * @param forceRefetchContent 是否强制重新爬取内容
   * @param autoExtractThemes 是否自动提取主题词
   * @returns 翻译统计信息
   */
  private async translateSpecificDocument(
    documentId: string,
    specificIndexes: string[],
    forceRetranslate: boolean,
    forceRefetchContent: boolean,
    autoExtractThemes: boolean
  ): Promise<TranslationStatistics> {
    const statistics: TranslationStatistics = {
      total: 0,
      success: 0,
      failed: 0,
      skipped: 0,
      indexCount: 0,
      indexes: [],
      startTime: new Date(),
      themeExtraction: autoExtractThemes ? {
        total: 0,
        success: 0,
        failed: 0,
        skipped: 0
      } : undefined
    };

    try {
      let foundDocument: NewsDocument | null = null;
      let foundIndex: string | null = null;

      // 在指定的索引中查找文档
      for (const index of specificIndexes) {
        try {
          const doc = await this.getDocumentById(index, documentId);
          if (doc) {
            foundDocument = doc;
            foundIndex = index;
            this.logger.log(`在索引${index}中找到文档${documentId}`);
            break;
          }
        } catch (error: any) {
          this.logger.debug(`在索引${index}中未找到文档${documentId}: ${error.message}`);
        }
      }

      if (!foundDocument || !foundIndex) {
        throw new Error(`在指定的索引${specificIndexes.join(', ')}中未找到文档ID: ${documentId}`);
      }

      statistics.total = 1;
      statistics.indexCount = 1;
      statistics.indexes = [foundIndex];

      // 检查是否需要翻译
      const hasTranslation = foundDocument.title_cn && foundDocument.summary_cn && foundDocument.content_cn;
      if (hasTranslation && !forceRetranslate) {
        this.logger.log(`文档${documentId}已翻译，跳过翻译`);
        statistics.skipped = 1;
        statistics.endTime = new Date();
        statistics.elapsedTime = statistics.endTime.getTime() - statistics.startTime.getTime();
        return statistics;
      }

      // 获取内容，优先使用已有内容
      let content = foundDocument.content || '';
      let contentUpdated = false;

      // 内容爬取逻辑
      const shouldFetchContent = (!content || content.trim() === '') && foundDocument.info_source && 
        (forceRefetchContent || !foundDocument.content_fetched);

      if (shouldFetchContent) {
        try {
          this.logger.log(`文档${documentId}${forceRefetchContent ? '强制重新' : ''}尝试从${foundDocument.info_source}获取内容`);
          const fetchedContent = await this.fetchContentFromUrl(foundDocument.info_source!);

          // 更新爬取标记，无论是否成功获取到内容
          const fetchUpdateData: any = {
            content_fetched: true,
            content_fetch_time: new Date().toISOString(),
            content_fetch_success: false
          };

          // 如果成功获取到内容，先更新原始文档
          if (fetchedContent && fetchedContent.trim() !== '') {
            content = fetchedContent;
            contentUpdated = true;
            fetchUpdateData.content = fetchedContent;
            fetchUpdateData.content_fetch_success = true;

            this.logger.log(`成功从${foundDocument.info_source}获取内容并更新文档${documentId}，内容长度: ${fetchedContent.length}`);
          } else {
            this.logger.warn(`从${foundDocument.info_source}获取内容失败或内容为空，已标记为已爬取`);
          }

          // 更新数据库中的文档爬取标记
          await this.elasticsearchService.update({
            index: foundIndex,
            id: documentId,
            body: {
              doc: fetchUpdateData
            }
          });

        } catch (fetchError) {
          this.logger.error(`从${foundDocument.info_source}获取内容失败: ${fetchError.message}`);
          
          // 即使爬取失败也要标记为已尝试爬取
          try {
            await this.elasticsearchService.update({
              index: foundIndex,
              id: documentId,
              body: {
                doc: {
                  content_fetched: true,
                  content_fetch_time: new Date().toISOString(),
                  content_fetch_success: false
                }
              }
            });
          } catch (updateError) {
            this.logger.error(`更新爬取标记失败: ${updateError.message}`);
          }
        }
      }

      // 执行翻译前检查文档长度
      const contentLength = content?.length || 0;
      const ULTRA_LONG_THRESHOLD = 100000; // 100K字符阈值
      
      if (contentLength > ULTRA_LONG_THRESHOLD) {
        this.logger.warn(`文档${documentId}的content过长 (${contentLength}字符，超过${ULTRA_LONG_THRESHOLD}字符阈值)，跳过翻译并清空已有翻译`);
        
        // 直接更新文档，将翻译字段设置为空
        const updateData: any = {
          title_cn: '',
          summary_cn: '',
          content_cn: '',
          themes_cn: '',
          translated_at: new Date().toISOString(),
          translation_skipped_reason: `content过长(${contentLength}字符)`
        };

        const updateSuccess = await this.elasticsearchService.update({
          index: foundIndex,
          id: documentId,
          body: { doc: updateData }
        });

        if (updateSuccess) {
          statistics.success = 1; // 标记为成功处理（虽然跳过了翻译）
          if (statistics.themeExtraction) {
            statistics.themeExtraction.total = 1;
            statistics.themeExtraction.success = 1; // 主题提取也标记为成功（实际返回空）
          }
          this.logger.log(`文档${documentId}超长内容跳过翻译成功`);
        } else {
          throw new Error('更新文档失败');
        }

        statistics.endTime = new Date();
        statistics.elapsedTime = statistics.endTime.getTime() - statistics.startTime.getTime();
        return statistics;
      }

      // 执行正常翻译
      if (autoExtractThemes && statistics.themeExtraction) {
        // 使用组合方法一次性完成翻译和主题提取
        try {
          if (contentUpdated) {
            this.logger.debug(`使用从 URL 获取的内容进行组合翻译，内容长度: ${content?.length || 0}`);
          }

          const combinedResult = await this.translationService.translateAndExtractInOneCall(
            foundDocument.title || '',
            foundDocument.summary || '',
            content || '',
            0, // retryCount
            forceRetranslate
          );

          // 更新文档（包含翻译和主题词）
          const updateData: any = {
            title_cn: combinedResult.title_cn,
            summary_cn: combinedResult.summary_cn,
            content_cn: combinedResult.content_cn,
            themes_cn: combinedResult.themes_cn,
            translated_at: new Date().toISOString()
          };

          const updateSuccess = await this.elasticsearchService.update({
            index: foundIndex,
            id: documentId,
            body: { doc: updateData }
          });

          if (updateSuccess) {
            statistics.success = 1;
            statistics.themeExtraction!.total = 1;
            statistics.themeExtraction!.success = 1;
            this.logger.log(`文档${documentId}翻译和主题提取成功`);
          } else {
            throw new Error('更新文档失败');
          }

        } catch (error: any) {
          this.logger.error(`文档${documentId}组合翻译失败: ${error.message}`);
          statistics.failed = 1;
          if (statistics.themeExtraction) {
            statistics.themeExtraction.total = 1;
            statistics.themeExtraction.failed = 1;
          }
          await this.recordTranslationFailure(foundIndex, documentId, error.message);
        }
      } else {
        // 只进行翻译，不提取主题词
        try {
          // 如果已经在前面检查过超长内容，这里就不会执行到
          // 但为了保险起见，再次检查一下（理论上不会执行到这里的超长检查）
          let titleCn = '', summaryCn = '', contentCn = '';
          
          if (contentLength <= ULTRA_LONG_THRESHOLD) {
            titleCn = await this.translationService.translateText(foundDocument.title || '', 0, false, forceRetranslate);
            summaryCn = await this.translationService.translateText(foundDocument.summary || '', 0, false, forceRetranslate);
            contentCn = await this.translationService.translateText(content || '', 0, false, forceRetranslate);
          } else {
            // 这种情况理论上不会出现，因为前面已经处理了
            this.logger.warn(`文档${documentId}在单独翻译模式下检测到超长内容，设置翻译为空`);
          }

          const updateData = {
            title_cn: titleCn,
            summary_cn: summaryCn,
            content_cn: contentCn,
            translated_at: new Date().toISOString()
          };

          const updateSuccess = await this.elasticsearchService.update({
            index: foundIndex,
            id: documentId,
            body: { doc: updateData }
          });

          if (updateSuccess) {
            statistics.success = 1;
            this.logger.log(`文档${documentId}翻译成功`);
          } else {
            throw new Error('更新文档失败');
          }

        } catch (error: any) {
          this.logger.error(`文档${documentId}翻译失败: ${error.message}`);
          statistics.failed = 1;
          await this.recordTranslationFailure(foundIndex, documentId, error.message);
        }
      }

      statistics.endTime = new Date();
      statistics.elapsedTime = statistics.endTime.getTime() - statistics.startTime.getTime();

      return statistics;

    } catch (error: any) {
      this.logger.error(`翻译特定文档失败: ${error.message}`, error.stack);
      statistics.failed = 1;
      statistics.endTime = new Date();
      statistics.elapsedTime = statistics.endTime.getTime() - statistics.startTime.getTime();
      return statistics;
    }
  }

  /**
   * 根据ID获取文档
   * @param index 索引名称
   * @param id 文档ID
   * @returns 文档对象或null
   */
  private async getDocumentById(index: string, id: string): Promise<NewsDocument | null> {
    try {
      const response = await this.elasticsearchService.get<NewsDocument>({
        index,
        id
      });

      if (response && response._source) {
        return {
          _id: response._id,
          _index: response._index,
          ...response._source
        };
      }

      return null;
    } catch (error: any) {
      this.logger.error(`获取文档失败, index=${index}, id=${id}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * 从失败文档列表中移除指定文档
   * @param doc 要移除的失败文档
   */
  private removeFailedDocument(doc: FailedDocument): void {
    this.failedDocuments = this.failedDocuments.filter(item =>
      !(item.index === doc.index && item.id === doc.id)
    );
  }

  /**
   * 从URL获取内容
   * @param url 要获取内容的URL
   * @returns 获取到的内容文本
   */
  private async fetchContentFromUrl(url: string): Promise<string> {
    try {
      if (!url || !url.startsWith('http')) {
        throw new Error('无效的URL');
      }

      this.logger.debug(`开始从URL获取内容: ${url}`);

      // 使用fetch API获取内容
      // 创建一个带超时的AbortController
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5'
          },
          signal: controller.signal
        });

        // 清除超时定时器
        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`请求失败，状态码: ${response.status}`);
        }

        // 获取响应文本
        const html = await response.text();

        // 简单提取正文内容，可以根据实际情况使用更复杂的提取逻辑
        let content = this.extractContentFromHtml(html);

        // 如果提取的内容为空，则返回原始 HTML
        if (!content || content.trim() === '') {
          this.logger.warn(`无法从 HTML 提取有意义的内容，返回原始 HTML`);
          return html;
        }

        this.logger.debug(`成功从URL获取内容，长度: ${content.length}`);
        return content;
      } catch (error) {
        // 清除超时定时器
        clearTimeout(timeoutId);
        throw error;
      }
    } catch (error: any) {
      this.logger.error(`从URL获取内容失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 从HTML中提取正文内容
   * @param html HTML字符串
   * @returns 提取的正文内容
   */
  private extractContentFromHtml(html: string): string {
    try {
      // 限制处理的HTML大小，提高效率
      if (html.length > 800_000) {
        html = html.slice(0, 800_000);
      }

      // 使用 cheerio 解析
      const $ = cheerio.load(html, { decodeEntities: false });

      // 删除常见广告/导航/无用元素
      $('script, style, nav, header, footer, aside, iframe, noscript').remove();
      // 根据 class/id 特征删除广告节点
      const adSelectors = [
        '[class*="advert" i]', '[id*="advert" i]',
        '[class*="ad-" i]', '[id*="ad-" i]',
        '[class*="ads" i]', '[id*="ads" i]',
        '[class*="sponsor" i]', '[id*="sponsor" i]'
      ];
      $(adSelectors.join(',')).remove();

      // 选择可能的正文容器
      const candidates: Array<{ element: any; text: string }> = [];
      $('article, main, section, div').each((_: number, el: any) => {
        const text = $(el).text().replace(/\s+/g, ' ').trim();
        if (text.length > 200) {
          candidates.push({ element: el, text });
        }
      });

      // 选最长文本的元素
      let mainText = '';
      if (candidates.length > 0) {
        candidates.sort((a, b) => b.text.length - a.text.length);
        mainText = candidates[0].text;
      }

      // Fallback: 使用清理版全文
      if (!mainText || mainText.length < 100) {
        mainText = $.root().text().replace(/\s+/g, ' ').trim();
      }

      // 行级广告清理
      mainText = this.cleanAdLines(mainText);

      return mainText;
    } catch (error: any) {
      this.logger.error(`提取HTML内容失败: ${error.message}`, error.stack);
      return html; // 失败时返回原始 HTML
    }
  }

  /** 删除广告行 */
  private cleanAdLines(text: string): string {
    const adLinePatterns = [
      /^(广告|推广|免责声明|Sponsored|Advertisement)[:：]/i,
      /更多精彩内容|点击(这里|链接)|版权所有|All rights reserved/i
    ];
    return text.split(/\n+/)
      .filter(line => !adLinePatterns.some(p => p.test(line.trim())))
      .join('\n')
      .trim();
  }

  /**
   * 搜索新闻列表
   * @param query 新闻列表查询参数
   * @returns 新闻列表查询结果
   */
  async searchNewsList(query: NewsListQueryDto): Promise<NewsListResult> {
    try {
      const {
        page = 1,
        limit = 10,
        keywords = [],
        themes = [],
        publishDateStart,
        publishDateEnd,
        indexPatterns = [],
        includeUntranslated = true,
        sort = { 'publish_date.year': 'desc' },
        keywordMatchType = KeywordMatchType.SUBSTRING
      } = query;

      // 计算分页参数
      const from = (page - 1) * limit;

      // 获取符合条件的索引
      let newsIndices = await this.getNewsIndices();

      // 检查是否为军事新闻查询
      const isMilitaryNewsQuery = indexPatterns.some(pattern => 
        pattern.includes('defence') || pattern.includes('defense')
      );

      // 构建查询条件
      const must: any[] = [];
      const should: any[] = [];
      const mustNot: any[] = [];

      // 处理军事新闻的特殊逻辑
      if (isMilitaryNewsQuery) {
        this.logger.debug('检测到军事新闻查询，将同时搜索defence/defense索引和主题词包含"军事"的新闻');
        
        // 创建一个should查询，包含两个条件：
        // 1. 来自defence/defense索引的新闻
        // 2. 主题词包含"军事"的新闻（来自任何索引）
        const militaryQuery = {
          bool: {
            should: [
              // 条件1：来自defence/defense索引的新闻
              {
                bool: {
                  must: [
                    {
                      bool: {
                        should: indexPatterns.map(pattern => ({
                          wildcard: {
                            '_index': pattern
                          }
                        })),
                        minimum_should_match: 1
                      }
                    }
                  ]
                }
              },
              // 条件2：主题词包含"军事"的新闻（排除已经在defence/defense索引中的）
              {
                bool: {
                  must: [
                    {
                      match_phrase: {
                        themes_cn: '军事'
                      }
                    }
                  ],
                  must_not: [
                    {
                      bool: {
                        should: indexPatterns.map(pattern => ({
                          wildcard: {
                            '_index': pattern
                          }
                        })),
                        minimum_should_match: 1
                      }
                    }
                  ]
                }
              }
            ],
            minimum_should_match: 1
          }
        };

        must.push(militaryQuery);
        
        // 对于军事新闻查询，使用所有索引而不是过滤
        // 这样可以确保我们能搜索到所有包含"军事"主题词的新闻
        this.logger.debug(`军事新闻查询将搜索所有 ${newsIndices.length} 个索引`);
      } else {
        // 非军事新闻查询，使用原有的索引过滤逻辑
        if (indexPatterns && indexPatterns.length > 0) {
          newsIndices = newsIndices.filter(index =>
            indexPatterns.some(pattern => {
              // 支持使用通配符匹配索引
              if (pattern.includes('*')) {
                const regexPattern = pattern.replace(/\*/g, '.*');
                return new RegExp(`^${regexPattern}$`).test(index);
              }
              return index === pattern;
            })
          );
        }
      }

      if (newsIndices.length === 0) {
        this.logger.warn('没有找到符合条件的新闻索引');
        return {
          data: [],
          total: 0,
          page,
          size: limit,
          totalPages: 0
        };
      }

      this.logger.debug(`搜索新闻列表，索引: ${newsIndices.join(', ')}`);

      // 关键词搜索
      if (keywords && keywords.length > 0) {
        const keywordQueries = keywords.map(keyword => {
          this.logger.debug(`处理关键词: ${keyword}, 匹配方式: ${keywordMatchType}`);
          
          // 根据匹配方式选择不同的查询策略
          if (keywordMatchType === KeywordMatchType.SUBSTRING) {
            // 使用match_phrase查询来处理子串匹配，这是最可靠的方式
            return {
              bool: {
                should: [
                  { match_phrase: { title: keyword } },
                  { match_phrase: { title_cn: keyword } },
                  { match_phrase: { summary: keyword } },
                  { match_phrase: { summary_cn: keyword } },
                  { match_phrase: { content: keyword } },
                  { match_phrase: { content_cn: keyword } },
                  { match_phrase: { source: keyword } },
                  { match_phrase: { author: keyword } },
                  { match_phrase: { info_source: keyword } },
                  { match_phrase: { url: keyword } },
                  { match_phrase: { themes_cn: keyword } },
                  { match_phrase: { published_at: keyword } },
                  // 同时使用wildcard查询作为补充，针对可能存在的.keyword字段
                  { wildcard: { 'title.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                  { wildcard: { 'title_cn.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                  { wildcard: { 'summary.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                  { wildcard: { 'summary_cn.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                  { wildcard: { 'content.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                  { wildcard: { 'content_cn.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                  { wildcard: { 'source.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                  { wildcard: { 'author.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                  { wildcard: { 'info_source.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                  { wildcard: { 'url.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                  { wildcard: { 'themes_cn.keyword': { value: `*${keyword}*`, case_insensitive: true } } },
                  { wildcard: { 'published_at.keyword': { value: `*${keyword}*`, case_insensitive: true } } }
                ],
                minimum_should_match: 1
              }
            };
          } else {
            // 相似性匹配 - 使用multi_match查询
            return {
              multi_match: {
                query: keyword,
                fields: [
                  'title', 'title_cn',
                  'summary', 'summary_cn',
                  'content', 'content_cn',
                  'source', 'author',
                  'info_source', 'url',
                  'themes_cn', 'published_at'
                ],
                type: 'best_fields',
                fuzziness: 'AUTO'
              }
            };
          }
        });

        // 如果有多个关键词，每个关键词都必须匹配
        must.push(...keywordQueries);
        
        // 添加调试日志
        this.logger.debug(`构建的查询条件: ${JSON.stringify(must, null, 2)}`);
      }

      // 主题词搜索（非军事新闻查询或军事新闻查询的额外主题词）
      if (themes && themes.length > 0) {
        const themeQueries = themes.map(theme => ({
          match_phrase: {
            themes_cn: theme
          }
        }));

        // 如果有多个主题词，每个主题词都必须匹配
        must.push(...themeQueries);
      }

      // 时间范围搜索
      if (publishDateStart || publishDateEnd) {
        // 处理日期范围查询
        const dateQueries: any[] = [];

        if (publishDateStart) {
          const startDate = new Date(publishDateStart);
          const startYear = startDate.getFullYear();
          const startMonth = startDate.getMonth() + 1;
          const startDay = startDate.getDate();

          // 年份大于开始年份
          dateQueries.push({
            range: {
              "publish_date.year": { gte: startYear }
            }
          });

          // 如果是同年，则月份必须大于等于开始月份
          dateQueries.push({
            bool: {
              should: [
                { range: { "publish_date.year": { gt: startYear } } },
                {
                  bool: {
                    must: [
                      { match: { "publish_date.year": startYear } },
                      { range: { "publish_date.month": { gte: startMonth } } }
                    ]
                  }
                }
              ]
            }
          });

          // 如果是同年同月，则日期必须大于等于开始日期
          dateQueries.push({
            bool: {
              should: [
                { range: { "publish_date.year": { gt: startYear } } },
                {
                  bool: {
                    must: [
                      { match: { "publish_date.year": startYear } },
                      { range: { "publish_date.month": { gt: startMonth } } }
                    ]
                  }
                },
                {
                  bool: {
                    must: [
                      { match: { "publish_date.year": startYear } },
                      { match: { "publish_date.month": startMonth } },
                      { range: { "publish_date.day": { gte: startDay } } }
                    ]
                  }
                }
              ]
            }
          });
        }

        if (publishDateEnd) {
          const endDate = new Date(publishDateEnd);
          const endYear = endDate.getFullYear();
          const endMonth = endDate.getMonth() + 1;
          const endDay = endDate.getDate();

          // 年份小于结束年份
          dateQueries.push({
            range: {
              "publish_date.year": { lte: endYear }
            }
          });

          // 如果是同年，则月份必须小于等于结束月份
          dateQueries.push({
            bool: {
              should: [
                { range: { "publish_date.year": { lt: endYear } } },
                {
                  bool: {
                    must: [
                      { match: { "publish_date.year": endYear } },
                      { range: { "publish_date.month": { lte: endMonth } } }
                    ]
                  }
                }
              ]
            }
          });

          // 如果是同年同月，则日期必须小于等于结束日期
          dateQueries.push({
            bool: {
              should: [
                { range: { "publish_date.year": { lt: endYear } } },
                {
                  bool: {
                    must: [
                      { match: { "publish_date.year": endYear } },
                      { range: { "publish_date.month": { lt: endMonth } } }
                    ]
                  }
                },
                {
                  bool: {
                    must: [
                      { match: { "publish_date.year": endYear } },
                      { match: { "publish_date.month": endMonth } },
                      { range: { "publish_date.day": { lte: endDay } } }
                    ]
                  }
                }
              ]
            }
          });
        }

        // 将所有日期查询条件添加到must数组中
        must.push({
          bool: {
            must: dateQueries
          }
        });
      }

      // 如果不包含未翻译的新闻，添加必须存在翻译字段的条件
      if (!includeUntranslated) {
        must.push(
          { exists: { field: 'title_cn' } },
          { exists: { field: 'content_cn' } }
        );
      }

      // 构建最终查询
      let finalQuery: any;

      if (must.length > 0 || should.length > 0 || mustNot.length > 0) {
        finalQuery = {
          bool: {}
        };

        if (must.length > 0) {
          finalQuery.bool.must = must;
        }

        if (should.length > 0) {
          finalQuery.bool.should = should;
          if (!finalQuery.bool.must) {
            finalQuery.bool.minimum_should_match = 1;
          }
        }

        if (mustNot.length > 0) {
          finalQuery.bool.must_not = mustNot;
        }
      } else {
        // 如果没有任何条件，则查询所有文档
        finalQuery = { match_all: {} };
      }

      // 构建排序
      // 默认按发布日期降序排序（最新的先显示）
      const sortConfig: any[] = [
        { "publish_date.year": { order: "desc" } },
        { "publish_date.month": { order: "desc" } },
        { "publish_date.day": { order: "desc" } }
      ];

      // 如果有其他排序字段，添加到排序配置中
      if (sort && Object.keys(sort).some(key => !key.startsWith('publish_date'))) {
        Object.entries(sort).forEach(([field, order]) => {
          if (!field.startsWith('publish_date')) {
            sortConfig.unshift({ [field]: { order } });
          }
        });
      }

      // 执行查询
      const response = await this.elasticsearchService.search({
        index: newsIndices,
        body: {
          query: finalQuery,
          sort: sortConfig,
          from,
          size: limit
        }
      });

      // 处理结果
      const hits = response.hits?.hits || [];
      const total = typeof response.hits.total === 'number'
        ? response.hits.total
        : (response.hits.total as any).value || 0;

      const documents = hits.map(hit => {
        const source = hit._source || {};
        return {
          _id: hit._id,
          _index: hit._index,
          ...source
        } as NewsDocument;
      });

      this.logger.debug(`新闻列表查询结果: 总数 ${total}, 当前页 ${page}, 每页数量 ${limit}, 返回文档数 ${documents.length}`);

      const totalPages = Math.ceil(total / limit);
      
      return {
        data: documents,
        total,
        page,
        size: limit,
        totalPages
      };
    } catch (error: any) {
      this.logger.error(`搜索新闻列表失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}