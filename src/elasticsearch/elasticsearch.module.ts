import { Module } from '@nestjs/common';
import { ElasticsearchModule } from '@nestjs/elasticsearch';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { ElasticsearchBaseService } from './services/elasticsearch.base.service';
import { ElasticsearchDebrisService } from './services/elasticsearch.debris.service';
import { ElasticsearchSatelliteService } from './services/elasticsearch.satellite.service';
import { ElasticsearchFreqService } from './services/elasticsearch.freq.service';
import { ElasticsearchOrbitService } from './services/elasticsearch.orbit.service';
import { ElasticsearchConstellationService } from './services/elasticsearch.constellation.service';
import { ElasticsearchDebrisEventService } from './services/elasticsearch.debris-event.service';
import { ElasticsearchCorrelationService } from './services/elasticsearch.correlation.service';
import { ElasticsearchLoopholeService } from './services/elasticsearch.loophole.service';
import { ElasticsearchNewsService } from './services/elasticsearch.news.service';
import { ElasticsearchLaunchService } from './services/elasticsearch.launch.service';
import { ElasticsearchRocketService } from './services/elasticsearch.rocket.service';
import { TranslationService } from './services/translation.service';
import { EntityRecognitionService } from './services/entity-recognition.service';
import { EntityRecognitionTestController } from './controllers/entity-recognition-test.controller';
import { ElasticsearchDebrisEventController } from './controllers/elasticsearch.debris-event.controller';
import { ElasticsearchConstellationController } from './controllers/elasticsearch.constellation.controller';
import { ElasticsearchCorrelationController } from './controllers/elasticsearch.correlation.controller';
import { ElasticsearchDebrisController } from './controllers/elasticsearch.debris.controller';
import { ElasticsearchSatelliteController } from './controllers/elasticsearch.satellite.controller';
import { ElasticsearchFreqController } from './controllers/elasticsearch.freq.controller';
import { ElasticsearchOrbitController } from './controllers/elasticsearch.orbit.controller';
import { ElasticsearchLoopholeController } from './controllers/elasticsearch.loophole.controller';
import { ElasticsearchNewsController } from './controllers/elasticsearch.news.controller';
import { ElasticsearchLaunchController } from './controllers/elasticsearch.launch.controller';
import { ElasticsearchRocketController } from './controllers/elasticsearch.rocket.controller';

/**
 * Elasticsearch模块
 */
@Module({
  imports: [
    HttpModule,
    ElasticsearchModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        nodes: ['http://123.57.173.156:9200'],
        auth: {
          username: 'web_readonly',
          password: 'web@readonly4all'
        },
        maxRetries: 10,
        requestTimeout: 120000, // 增加到2分钟
        pingTimeout: 120000,    // 增加到2分钟
        sniffOnStart: false,
        ssl: {
          rejectUnauthorized: false
        },
        retryOnConflict: 3,
        compression: true,
        suggestCompression: true,
        keepAlive: true,
        keepAliveInterval: 1000,
        resurrectStrategy: 'ping',
        name: 'spacedata-es-client',
        healthCheck: false,
        healthCheckInterval: 10000,
        ignoreVersionMismatch: true,
        tls: {
          rejectUnauthorized: false
        }
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [
    ElasticsearchDebrisEventController,
    ElasticsearchConstellationController,
    ElasticsearchCorrelationController,
    ElasticsearchDebrisController,
    ElasticsearchSatelliteController,
    ElasticsearchFreqController,
    ElasticsearchOrbitController,
    ElasticsearchLoopholeController,
    ElasticsearchNewsController,
    ElasticsearchLaunchController,
    ElasticsearchRocketController,
    EntityRecognitionTestController
  ],
  providers: [
    // 基础服务
    ElasticsearchBaseService,
    // 专用服务
    ElasticsearchDebrisService,
    ElasticsearchSatelliteService,
    ElasticsearchFreqService,
    ElasticsearchOrbitService,
    ElasticsearchConstellationService,
    ElasticsearchDebrisEventService,
    ElasticsearchCorrelationService,
    ElasticsearchLoopholeService,
    ElasticsearchNewsService,
    ElasticsearchLaunchService,
    ElasticsearchRocketService,
    TranslationService,
    EntityRecognitionService
  ],
  exports: [
    // 专用服务
    ElasticsearchDebrisService,
    ElasticsearchSatelliteService,
    ElasticsearchFreqService,
    ElasticsearchOrbitService,
    ElasticsearchConstellationService,
    ElasticsearchDebrisEventService,
    ElasticsearchCorrelationService,
    ElasticsearchLoopholeService,
    ElasticsearchNewsService,
    ElasticsearchLaunchService,
    ElasticsearchRocketService,
    // 翻译服务
    TranslationService
  ],
})
export class ESModule {}