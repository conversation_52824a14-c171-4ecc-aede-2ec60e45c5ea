import { Controller, Post, Body, UseGuards, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { ElasticsearchCorrelationService } from '../services/elasticsearch.correlation.service';
import { DebrisToEventQueryDto, EventToDebrisQueryDto } from '../dto/debris-event-correlation.dto';
import { CorrelationResponseDto, EventCorrelationResultDto } from '../dto/correlation-response.dto';

/**
 * Elasticsearch相关性控制器
 * 处理碎片与事件的相关性分析请求
 */
@Controller('correlation')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('碎片信息')
export class ElasticsearchCorrelationController {
  private readonly logger = new Logger(ElasticsearchCorrelationController.name);

  constructor(private readonly correlationService: ElasticsearchCorrelationService) {}

  @Post('debris-to-event')
  @ApiTags('碎片信息')
  @ApiOperation({
    summary: '查询碎片关联的事件',
    description: `根据碎片信息查询可能关联的碎片事件，并计算置信度。

关联逻辑说明：
1. COSPAR ID匹配（权重70%）：
   - **COSPAR ID匹配是关联的必要条件，如果COSPAR ID不匹配，则不会出现在结果列表中**
   - 完全匹配得分1.0
   - 年份匹配得分0.5
   - 序号部分（三位数字）匹配得分0.4
   - 字母部分首字母匹配得分0.1
   - COSPAR ID是关联的主要依据，匹配度越高，关联置信度越高

2. 时间匹配（权重15%）：
   - 如果首次纪元时间在事件时间之后且时间差在5年内，得分1.0
   - 如果时间差在5-10年内，按比例递减得分
   - 如果未提供首次纪元时间，默认得分0.5

3. 名称/类型匹配（权重15%）：
   - 碎片类型为"Payload Fragmentation Debris"得分0.8
   - 碎片类型包含"Debris"得分0.5
   - 其他类型得分0.2
   - 如果未提供名称，使用默认得分

4. 综合置信度计算：
   - 各维度得分按权重加权求和
   - 只返回置信度大于等于min_confidence的结果
   - 结果按置信度降序排序
   
注意：即使综合置信度高于min_confidence，如果COSPAR ID不匹配（得分为0），也不会出现在结果列表中。`
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: CorrelationResponseDto
  })
  async findRelatedEvents(@Body() query: DebrisToEventQueryDto): Promise<CorrelationResponseDto> {
    this.logger.debug(`接收到碎片关联事件查询请求: ${JSON.stringify(query)}`);
    const result = await this.correlationService.findRelatedEvents(query);
    this.logger.debug(`查询结果: ${JSON.stringify(result)}`);
    return result;
  }

  @Post('event-to-debris')
  @ApiTags('碎片信息')
  @ApiOperation({
    summary: '查询事件关联的碎片',
    description: '根据事件信息查询可能关联的碎片，并计算置信度'
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: EventCorrelationResultDto
  })
  async findRelatedDebris(@Body() query: EventToDebrisQueryDto): Promise<EventCorrelationResultDto> {
    this.logger.debug(`接收到事件关联碎片查询请求: ${JSON.stringify(query)}`);
    const result = await this.correlationService.findRelatedDebris(query);
    this.logger.debug(`查询结果: ${JSON.stringify(result)}`);
    return result;
  }
} 