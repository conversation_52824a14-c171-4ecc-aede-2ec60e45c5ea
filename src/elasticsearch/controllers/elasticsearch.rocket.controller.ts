import { Controller, Post, Body, Logger, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags, ApiBody, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { ElasticsearchRocketService } from '../services/elasticsearch.rocket.service';
import { RocketQueryDto } from '../dto/rocket-query.dto';

/**
 * Elasticsearch火箭信息控制器
 * 处理火箭信息的查询请求
 */
@Controller('api/es/rocket')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('发射信息')
export class ElasticsearchRocketController {
  private readonly logger = new Logger(ElasticsearchRocketController.name);

  constructor(private readonly rocketService: ElasticsearchRocketService) {}

  @Post('search')
  @ApiOperation({
    summary: '查询火箭信息',
    description: `
    根据提供的查询条件搜索火箭信息。

    ## 数据来源
    从Elasticsearch数据库中的veh_discos和veh_jonathan索引中查询火箭信息。

    ## 查询条件
    - 火箭型号：匹配rocket_name或lv_variant字段，不区分大小写

    ## 返回数据
    - 返回所有非空字段及其值
    - 结果区分精确匹配(match_type: 'exact')和模糊匹配(match_type: 'fuzzy')
    - 精确匹配的结果排在前面，模糊匹配的结果按相似度从大到小排序
    - 每个结果包含similarity_score字段，表示与查询条件的相似度(0-1)
    `
  })
  @ApiBody({
    type: RocketQueryDto,
    examples: {
      '基本查询': {
        summary: '基本查询示例',
        description: '使用分页参数进行基本查询',
        value: {
          page: 1,
          limit: 10
        }
      },
      '火箭型号查询': {
        summary: '火箭型号查询示例',
        description: '使用火箭型号进行查询',
        value: {
          page: 1,
          limit: 10,
          rocketName: 'Falcon'
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '成功返回火箭信息',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            total: { type: 'number', example: 42 },
            page: { type: 'number', example: 1 },
            limit: { type: 'number', example: 10 },
            hits: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  _id: { type: 'string', example: 'falcon9' },
                  _index: { type: 'string', example: 'veh_discos' },
                  _score: { type: 'number', example: 2.5 },
                  match_type: { type: 'string', example: 'exact', enum: ['exact', 'fuzzy'] },
                  similarity_score: { type: 'number', example: 0.85 },
                  rocket_name: { type: 'string', example: 'Falcon 9' },
                  lv_variant: { type: 'string', example: 'FT4' },
                  lv_family: { type: 'string', example: 'Falcon9' },
                  lv_manufacturer: { type: 'string', example: 'SPX' },
                  class: { type: 'string', example: 'O' },
                  length: { type: 'number', example: 71 },
                  diameter: { type: 'number', example: 3.65 },
                  launch_mass: { type: 'number', example: 480 },
                  leo_capacity: { type: 'number', example: 22800 },
                  gto_capacity: { type: 'number', example: 8300 }
                }
              }
            }
          }
        },
        timestamp: { type: 'string', example: '2023-06-15T12:34:56.789Z' }
      }
    }
  })
  async searchRocketInfo(@Body() query: RocketQueryDto): Promise<any> {
    try {
      this.logger.debug(`接收到火箭信息查询请求: ${JSON.stringify(query)}`);
      const result = await this.rocketService.searchRocketInfo(query);
      this.logger.debug(`查询结果: 找到${result.total}条记录`);
      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      this.logger.error(`查询火箭信息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}