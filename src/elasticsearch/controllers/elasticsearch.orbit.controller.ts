import { Controller, Post, Body, UseGuards, Logger, Query, Get, Put } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { ElasticsearchOrbitService } from '../services/elasticsearch.orbit.service';
import { OrbitQueryDto } from '../dto/orbit-query.dto';
import { BulkNoradIdsQueryDto } from '../dto/bulk-norad-ids-query.dto';
import { TleQueryConfig } from '../../../config/tle-query.config';

/**
 * Elasticsearch轨道查询控制器
 * 处理轨道信息的查询请求
 */
@Controller('orbit')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('轨道数据')
export class ElasticsearchOrbitController {
  private readonly logger = new Logger(ElasticsearchOrbitController.name);

  constructor(private readonly orbitService: ElasticsearchOrbitService) {}

  @Post('search')
  @ApiOperation({
    summary: '查询轨道信息',
    description: '根据提供的查询条件搜索轨道信息。当使用norad_id、cospar_id或satellite_name参数时，将按照优先级顺序（norad_id > cospar_id > satellite_name）查询，且只返回epoch字段最新的一条记录。使用轨道参数查询时，将返回分页结果，按epoch时间倒序排列。'
  })
  @ApiBody({
    type: OrbitQueryDto,
    examples: {
      byNoradId: {
        summary: '使用NORAD ID查询（只返回epoch最新的一条记录）',
        value: {
          norad_id: 25544
        }
      },
      byName: {
        summary: '使用名称查询（只返回epoch最新的一条记录）',
        value: {
          satellite_name: "ISS",
          fuzzy_match: true
        }
      },
      byOrbitType: {
        summary: '使用轨道类型查询（返回分页结果，按epoch倒序）',
        value: {
          page: 1,
          limit: 10,
          orbit_type: "LEO"
        }
      },
      byMultipleFilters: {
        summary: '使用多个筛选条件查询（优先级: norad_id > cospar_id > satellite_name）',
        description: '按优先级匹配条件，并且只返回epoch最新的一条记录',
        value: {
          norad_id: 25544,
          cospar_id: "1998-067A",
          satellite_name: "ISS"
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '查询成功'
  })
  async searchOrbitInfo(@Body() query: OrbitQueryDto): Promise<any> {
    this.logger.debug(`接收到轨道信息查询请求: ${JSON.stringify(query)}`);
    const result = await this.orbitService.searchOrbitInfo(query);
    this.logger.debug(`查询结果: ${JSON.stringify(result)}`);
    return result;
  }

  @Post('bulk-tle')
  @ApiOperation({
    summary: '批量查询卫星TLE数据',
    description: '根据提供的卫星NORAD ID数组，批量查询每个卫星的最新TLE数据。最多支持100个卫星ID同时查询。当norad_ids数组为空时，返回所有卫星的TLE数据，支持分页查询（每页最多10000条）或一次性查询（秒级返回），包含tle_raw、satellite_name、cospar_id、norad_id、epoch、time、constellation_name（星座名称）、orbital_elements（轨道根数详细信息）字段。对于相同norad_id的记录，只返回epoch时间最新的一条。'
  })
  @ApiBody({
    type: BulkNoradIdsQueryDto,
    examples: {
      standard: {
        summary: '批量查询多个卫星的TLE数据',
        value: {
          norad_ids: [25544, 43552, 37849]  // ISS, Tiangong, Envisat 的NORAD ID
        }
      },
      all_satellites_pagination: {
        summary: '查询所有卫星的TLE数据 - 分页模式',
        value: {
          norad_ids: [],  // 空数组表示查询所有卫星
          page: 1,
          limit: 1000,
          useOneTimeQuery: false
        }
      },
      all_satellites_one_time: {
        summary: '查询所有卫星的TLE数据 - 一次性模式（推荐）',
        description: '秒级返回全部数据，避免分页延迟',
        value: {
          norad_ids: [],  // 空数组表示查询所有卫星
          useOneTimeQuery: true
        }
      },
      all_satellites_large_page: {
        summary: '查询所有卫星的TLE数据 - 大页面分页',
        value: {
          norad_ids: [],  // 空数组表示查询所有卫星
          page: 1,
          limit: 5000,
          useOneTimeQuery: false
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        total: { type: 'number', example: 8500, description: '总记录数' },
        page: { type: 'number', example: 1, description: '当前页码（仅分页查询时返回）' },
        limit: { type: 'number', example: 1000, description: '每页记录数（仅分页查询时返回）' },
        totalPages: { type: 'number', example: 9, description: '总页数（仅分页查询时返回）' },
        currentPageCount: { type: 'number', example: 1000, description: '当前页实际记录数（仅分页查询时返回）' },
        hasNextPage: { type: 'boolean', example: true, description: '是否有下一页（仅分页查询时返回）' },
        hasPrevPage: { type: 'boolean', example: false, description: '是否有上一页（仅分页查询时返回）' },
        executionTime: { type: 'number', example: 3500, description: '执行耗时（毫秒，仅一次性查询时返回）' },
        batchCount: { type: 'number', example: 1, description: '批次数量（仅一次性查询时返回）' },
        results: { 
          type: 'array', 
          items: {
            type: 'object',
            properties: {
              norad_id: { type: 'number', example: 25544 },
              satellite_name: { type: 'string', example: 'ISS (ZARYA)' },
              constellation_name: { 
                type: 'string', 
                example: 'Starlink', 
                nullable: true,
                description: '星座名称，如Starlink、OneWeb等，可能为null' 
              },
              cospar_id: { type: 'string', example: '1998-067A' },
              epoch: { type: 'string', format: 'date-time', example: '2025-01-05T16:13:27Z' },
              time: { type: 'string', format: 'date-time', example: '2025-06-04T06:05:28.673Z', description: '数据时间戳' },
              tle_raw: { type: 'string', example: '0 ISS (ZARYA)\n1 25544U 98067A   23152.50000000  .00010000  00000-0  12345-3 0  9995\n2 25544  51.6400 190.6541 0004000 110.2500 154.3000 15.50500000348845' },
              orbital_elements: {
                type: 'object',
                nullable: true,
                description: '轨道根数详细信息',
                properties: {
                  inc_deg: { type: 'number', example: 12.857, description: '轨道倾角（度）' },
                  raan_deg: { type: 'number', example: 42.7329, description: '升交点赤经（度）' },
                  ecc: { type: 'number', example: 0.0962575, description: '偏心率' },
                  arg_peri_deg: { type: 'number', example: 185.3361, description: '近地点幅角（度）' },
                  mean_anom_deg: { type: 'number', example: 173.5613, description: '平近点角（度）' },
                  day_laps: { type: 'number', example: 1.00378081, description: '每日圈数' },
                  orbit_num: { type: 'number', example: 1213, description: '轨道圈数' },
                  orbital_period_min: { type: 'number', example: 1434.576140183433, description: '轨道周期（分钟）' },
                  sema_km: { type: 'number', example: 42134.959633031736, description: '半长轴（公里）' },
                  arg_alt_km: { type: 'number', example: 39819.76550990829, description: '近地点高度（公里）' },
                  arg_apo_deg: { type: 'number', example: 5.336099999999988, description: '远地点高度（公里）' },
                  launch_time: { type: 'string', example: '2022年03月04日', description: '发射时间' }
                }
              }
            }
          }
        }
      }
    }
  })
  async getBulkSatelliteTle(@Body() query: BulkNoradIdsQueryDto): Promise<any> {
    this.logger.debug(`接收到批量查询卫星TLE数据请求: ${JSON.stringify(query)}`);
    
    const startTime = Date.now();
    const result = await this.orbitService.getBulkSatelliteTleByNoradIds(
      query.norad_ids || [], 
      query.page || 1, 
      query.limit || 1000,
      query.useOneTimeQuery || false
    );
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 添加详细的性能和响应日志
    if (query.norad_ids && query.norad_ids.length > 0) {
      this.logger.log(`✅ 特定卫星查询完成: 请求${query.norad_ids.length}个卫星，返回${result.total}条记录，耗时${duration}ms`);
    } else if (query.useOneTimeQuery) {
      this.logger.log(`🚀 一次性查询完成: 总共${result.total}条记录，耗时${duration}ms (${(duration/1000).toFixed(1)}秒)`);
      
      // 性能评估 - 一次性查询
      if (duration < 5000) {
        this.logger.log(`⚡ 性能优秀: 一次性查询耗时${duration}ms < 5秒`);
      } else if (duration < 10000) {
        this.logger.log(`✅ 性能良好: 一次性查询耗时${duration}ms < 10秒`);
      } else {
        this.logger.warn(`⚠️ 性能需要关注: 一次性查询耗时${duration}ms > 10秒`);
      }
    } else {
      this.logger.log(`🚀 高性能分页查询完成: 第${result.page}页，每页${result.limit}条，总共${result.total}条记录，当前页${result.currentPageCount}条，耗时${duration}ms`);
      
      // 性能评估 - 分页查询
      if (duration < 1000) {
        this.logger.log(`⚡ 性能优秀: 查询耗时${duration}ms < 1秒`);
      } else if (duration < 3000) {
        this.logger.log(`✅ 性能良好: 查询耗时${duration}ms < 3秒`);
      } else {
        this.logger.warn(`⚠️ 性能需要关注: 查询耗时${duration}ms > 3秒`);
      }
      
      // 提示更高效的方案
      if (result._performance) {
        this.logger.log(`💡 性能提示: ${result._performance.message}`);
      }
    }
    
    // 数据质量检查
    if (result.results && result.results.length > 0) {
      const noradIds = result.results.map((item: any) => item.norad_id);
      const uniqueNoradIds = new Set(noradIds);
      
      if (uniqueNoradIds.size !== result.results.length) {
        this.logger.error(`❌ 数据去重失败: 返回${result.results.length}条记录，但只有${uniqueNoradIds.size}个不重复的norad_id`);
      } else {
        this.logger.debug(`✅ 数据去重成功: 所有${result.results.length}条记录都是不重复的`);
      }
    }
    
    // 分页信息（仅分页查询）
    if (!query.useOneTimeQuery && result._pagination?.method === 'composite') {
      this.logger.debug(`📄 使用composite聚合分页，afterKey: ${result._pagination.afterKey ? 'exists' : 'null'}`);
    }
    
    return result;
  }

  @Post('bulk-tle/all')
  @ApiOperation({
    summary: '一次性获取所有卫星TLE数据（秒级返回，无数量限制）',
    description: '智能查询策略获取卫星TLE数据：1) 先基于time字段统计最新时间窗口内的数据量（时间窗口可配置，默认30分钟）；2) 如果数据量<20000条，则改用按norad_id去重方式查询所有卫星的最新数据；3) 如果数据量≥20000条，则使用时间窗口查询：≤10000条使用直接查询，>10000条自动使用滚动查询。突破Elasticsearch 10000条限制，确保获取到的是最新且完整的数据集。前端调用可获取全部结果，无数量限制。支持sampleMode参数用于Swagger演示。返回数据现在包含constellation_name（星座名称）和orbital_elements（轨道根数详细信息）字段。'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        total: { type: 'number', example: 100, description: '返回的记录数' },
        actualTotal: { type: 'number', example: 12500, description: '实际查询到的总记录数（仅在sampleMode=true时显示）' },
        executionTime: { type: 'number', example: 3500, description: '执行耗时（毫秒）' },
        timeRangeStart: { type: 'string', format: 'date-time', example: '2025-06-04T05:35:28.673Z', description: '查询时间范围开始（最新时间-N分钟）' },
        timeRangeEnd: { type: 'string', format: 'date-time', example: '2025-06-04T06:05:28.673Z', description: '查询时间范围结束（最新时间）' },
        timeRangeMinutes: { type: 'number', example: 30, description: '时间范围（分钟，可配置）' },
        method: { type: 'string', example: 'scroll_query', description: '查询方法: norad_dedup_query(按NORAD ID去重), direct_query(直接查询), scroll_query(滚动查询)' },
        queryStrategy: { type: 'string', example: '滚动查询', description: '查询策略说明' },
        sampleMode: { type: 'boolean', example: true, description: '是否为示例模式（限制返回数量以适应Swagger UI）' },
        results: { 
          type: 'array', 
          items: {
            type: 'object',
            properties: {
              norad_id: { type: 'number', example: 25544 },
              satellite_name: { type: 'string', example: 'ISS (ZARYA)' },
              constellation_name: { 
                type: 'string', 
                example: 'Starlink', 
                nullable: true,
                description: '星座名称，如Starlink、OneWeb等，可能为null' 
              },
              cospar_id: { type: 'string', example: '1998-067A' },
              epoch: { type: 'string', format: 'date-time', example: '2025-01-05T16:13:27Z' },
              time: { type: 'string', format: 'date-time', example: '2025-06-04T06:05:28.673Z' },
              tle_raw: { type: 'string', example: '0 ISS (ZARYA)\n1 25544U 98067A   23152.50000000  .00010000  00000-0  12345-3 0  9995\n2 25544  51.6400 190.6541 0004000 110.2500 154.3000 15.50500000348845' },
              orbital_elements: {
                type: 'object',
                nullable: true,
                description: '轨道根数详细信息',
                properties: {
                  inc_deg: { type: 'number', example: 12.857, description: '轨道倾角（度）' },
                  raan_deg: { type: 'number', example: 42.7329, description: '升交点赤经（度）' },
                  ecc: { type: 'number', example: 0.0962575, description: '偏心率' },
                  arg_peri_deg: { type: 'number', example: 185.3361, description: '近地点幅角（度）' },
                  mean_anom_deg: { type: 'number', example: 173.5613, description: '平近点角（度）' },
                  day_laps: { type: 'number', example: 1.00378081, description: '每日圈数' },
                  orbit_num: { type: 'number', example: 1213, description: '轨道圈数' },
                  orbital_period_min: { type: 'number', example: 1434.576140183433, description: '轨道周期（分钟）' },
                  sema_km: { type: 'number', example: 42134.959633031736, description: '半长轴（公里）' },
                  arg_alt_km: { type: 'number', example: 39819.76550990829, description: '近地点高度（公里）' },
                  arg_apo_deg: { type: 'number', example: 5.336099999999988, description: '远地点高度（公里）' },
                  launch_time: { type: 'string', example: '2022年03月04日', description: '发射时间' }
                }
              }
            }
          },
          description: '智能查询获取的卫星TLE数据（不受ES 10000条限制，可返回完整数据集）。当数据量<20000时返回所有卫星的最新数据，否则返回时间窗口内的数据。当sampleMode=true时，只返回前100条用于Swagger演示。时间窗口可通过配置调整。现在包含constellation_name（星座名称）和orbital_elements（轨道根数详细信息）字段。'
        }
      }
    }
  })
  async getAllSatelliteTle(@Query('sampleMode') sampleMode?: boolean): Promise<any> {
    this.logger.log(`🚀 接收到一次性获取所有卫星TLE数据的请求${sampleMode ? '（Swagger示例模式）' : ''}`);
    
    const startTime = Date.now();
    const result = await this.orbitService.getAllSatelliteTleData(sampleMode);
    const endTime = Date.now();
    const totalDuration = endTime - startTime;
    
    // 性能对比日志
    this.logger.log(`🎯 一次性获取完成: 总共${result.total}条记录，总耗时${totalDuration}ms (${(totalDuration/1000).toFixed(1)}秒)`);
    this.logger.log(`📊 查询策略: ${result.queryStrategy}，方法: ${result.method}`);
    
    // 数据完整性日志
    if (result.expectedTotal && result.total !== result.expectedTotal) {
      this.logger.warn(`⚠️ 数据完整性警告: 预期${result.expectedTotal}条，实际获取${result.total}条`);
    } else if (result.expectedTotal) {
      this.logger.log(`✅ 数据完整性验证: 成功获取全部${result.total}条记录`);
    }
    
    // 与分页方式的性能对比
    const estimatedPaginationTime = Math.ceil(result.total / 1000) * 2000; // 假设每页2秒
    const timeSaved = estimatedPaginationTime - totalDuration;
    const improvementPercentage = ((timeSaved / estimatedPaginationTime) * 100).toFixed(1);
    
    this.logger.log(`📊 性能对比: 相比分页方式节省${timeSaved}ms，性能提升${improvementPercentage}%`);
    
    // 数据质量检查
    if (result.results && result.results.length > 0) {
      const noradIds = result.results.map((item: any) => item.norad_id);
      const uniqueNoradIds = new Set(noradIds);
      
      if (uniqueNoradIds.size !== result.results.length) {
        this.logger.error(`❌ 数据质量检查失败: 返回${result.results.length}条记录，但只有${uniqueNoradIds.size}个不重复的norad_id`);
      } else {
        this.logger.log(`✅ 数据质量检查通过: 所有${result.results.length}条记录都是不重复的最新数据`);
      }
    }
    
    return result;
  }

  @Get('tle-config')
  @ApiOperation({
    summary: '获取TLE查询配置',
    description: '获取当前的TLE查询配置，包括时间窗口、最大记录数等参数'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        timeWindowMinutes: { type: 'number', example: 30, description: '时间窗口（分钟）' },
        maxRecords: { type: 'number', example: 50000, description: '最大记录数' },
        timeout: { type: 'number', example: 60000, description: '查询超时时间（毫秒）' },
        scrollSize: { type: 'number', example: 10000, description: '滚动查询每批次大小' },
        scrollTimeout: { type: 'string', example: '2m', description: '滚动查询超时时间' }
      }
    }
  })
  async getTleQueryConfig(): Promise<TleQueryConfig> {
    this.logger.log('获取TLE查询配置');
    return this.orbitService.getTleQueryConfig();
  }

  @Put('tle-config')
  @ApiOperation({
    summary: '更新TLE查询配置',
    description: '动态更新TLE查询配置，支持修改时间窗口、最大记录数等参数。配置立即生效，无需重启服务。'
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        timeWindowMinutes: { 
          type: 'number', 
          example: 30, 
          description: '时间窗口（分钟），范围：1-1440',
          minimum: 1,
          maximum: 1440
        },
        maxRecords: { 
          type: 'number', 
          example: 50000, 
          description: '最大记录数，范围：1-100000',
          minimum: 1,
          maximum: 100000
        },
        timeout: { 
          type: 'number', 
          example: 60000, 
          description: '查询超时时间（毫秒），范围：1-300000',
          minimum: 1,
          maximum: 300000
        },
        scrollSize: { 
          type: 'number', 
          example: 10000, 
          description: '滚动查询每批次大小，范围：1-10000',
          minimum: 1,
          maximum: 10000
        },
        scrollTimeout: { 
          type: 'string', 
          example: '2m', 
          description: '滚动查询超时时间，如：1m, 2m, 30s'
        }
      }
    },
    examples: {
      updateTimeWindow: {
        summary: '修改时间窗口为30分钟',
        value: {
          timeWindowMinutes: 30
        }
      },
      updateMultiple: {
        summary: '同时修改多个配置项',
        value: {
          timeWindowMinutes: 45,
          maxRecords: 80000,
          timeout: 90000
        }
      },
      performanceOptimization: {
        summary: '性能优化配置',
        value: {
          timeWindowMinutes: 15,
          scrollSize: 5000,
          timeout: 30000
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'TLE查询配置更新成功' },
        config: {
          type: 'object',
          properties: {
            timeWindowMinutes: { type: 'number', example: 30 },
            maxRecords: { type: 'number', example: 50000 },
            timeout: { type: 'number', example: 60000 },
            scrollSize: { type: 'number', example: 10000 },
            scrollTimeout: { type: 'string', example: '2m' }
          }
        }
      }
    }
  })
  async updateTleQueryConfig(@Body() newConfig: Partial<TleQueryConfig>): Promise<any> {
    this.logger.log(`更新TLE查询配置: ${JSON.stringify(newConfig)}`);
    
    try {
      this.orbitService.updateTleQueryConfig(newConfig);
      const updatedConfig = this.orbitService.getTleQueryConfig();
      
      this.logger.log(`✅ TLE查询配置更新成功: 时间窗口=${updatedConfig.timeWindowMinutes}分钟, 最大记录数=${updatedConfig.maxRecords}`);
      
      return {
        success: true,
        message: 'TLE查询配置更新成功',
        config: updatedConfig
      };
    } catch (error) {
      this.logger.error(`❌ TLE查询配置更新失败: ${error.message}`);
      throw error;
    }
  }
}