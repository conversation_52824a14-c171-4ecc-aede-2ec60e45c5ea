import { Controller, Post, Body, UseGuards, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { ElasticsearchLoopholeService } from '../services/elasticsearch.loophole.service';
import { LoopholeQueryDto } from '../dto/loophole-query.dto';
import { LoopholeSearchResponse } from '../types/loophole.types';

/**
 * Elasticsearch漏洞查询控制器
 * 处理漏洞信息的查询请求
 */
@Controller('loophole')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('漏洞信息')
export class ElasticsearchLoopholeController {
  private readonly logger = new Logger(ElasticsearchLoopholeController.name);

  constructor(private readonly loopholeService: ElasticsearchLoopholeService) {}

  @Post('search')
  @ApiOperation({
    summary: '查询漏洞信息',
    description: '根据提供的查询条件搜索漏洞信息，支持CVE编号精确匹配和关键词模糊搜索'
  })
  @ApiBody({
    type: LoopholeQueryDto,
    examples: {
      byCveId: {
        summary: '使用CVE编号查询',
        value: {
          _id: "CVE-2024-1234"
        }
      },
      byKeywords: {
        summary: '使用关键词查询',
        value: {
          keywords: "Cobham Sailor"
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    schema: {
      properties: {
        total: { type: 'number', example: 100 },
        hits: { 
          type: 'array', 
          items: {
            type: 'object',
            properties: {
              _id: { type: 'string', example: 'CVE-2024-1234' },
              cve_id: { type: 'string', example: 'CVE-2024-1234' },
              descriptions: { type: 'string', example: 'A vulnerability in Cobham Sailor 900 VSAT systems allows an attacker to gain unauthorized access.' },
              affected: { type: 'string', example: 'Cobham Sailor 900 VSAT 1.0, 1.1' },
              severity: { type: 'string', example: '高' },
              published_date: { type: 'string', format: 'date-time', example: '2024-01-15T10:30:00Z' },
              patch_status: { type: 'string', example: '已修复' },
              patch_version: { type: 'string', example: '1.2.0' },
              references: { type: 'array', items: { type: 'string' }, example: ['https://nvd.nist.gov/vuln/detail/CVE-2024-1234'] },
              mitigation: { type: 'string', example: '升级到1.2.0版本以修复此漏洞' }
            }
          }
        }
      }
    }
  })
  async searchLoopholeInfo(@Body() query: LoopholeQueryDto): Promise<LoopholeSearchResponse> {
    this.logger.debug(`接收到漏洞信息查询请求: ${JSON.stringify(query)}`);
    const result = await this.loopholeService.searchLoopholeInfo(query);
    this.logger.debug(`查询结果: ${JSON.stringify(result)}`);
    return result;
  }
} 