import { Controller, Post, Body, Logger, Get, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags, ApiBody, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { ElasticsearchLaunchService } from '../services/elasticsearch.launch.service';
import { LaunchQueryDto, LaunchStatus, SortDirection } from '../dto/launch-query.dto';
import { LaunchCosparQueryDto } from '../dto/launch-cospar-query.dto';
import { LaunchSiteWikiQueryDto } from '../dto/launch-site-wiki-query.dto';
import { LaunchServiceProviderQueryDto } from '../dto/launch-service-provider-query.dto';

/**
 * Elasticsearch发射信息控制器
 * 处理发射信息的查询请求
 */
@Controller('api/es/launch')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('发射信息')
export class ElasticsearchLaunchController {
  private readonly logger = new Logger(ElasticsearchLaunchController.name);

  constructor(private readonly launchService: ElasticsearchLaunchService) {}

  @Post('search')
  @ApiOperation({
    summary: '查询发射信息',
    description: `
    根据提供的查询条件搜索发射信息，支持多种筛选条件。

    ## 数据来源
    从Elasticsearch数据库中的launch_jonathan和以launch_spacenow开头的所有索引中查询发射信息。

    ## 查询条件
    - 发射时间范围：匹配launch_date字段（注意：输入的时间为北京时间，API会自动转换为UTC时间进行查询）
    - 火箭型号：匹配rocket_name字段
    - 发射场：匹配site_name字段
    - 任务状态：匹配status字段
    - 服务商：匹配provider字段
    - 排序方向：控制结果按发射时间的排序方向（升序或降序）

    ## 时间转换说明
    - 用户输入的发射时间范围（launchDateStart和launchDateEnd）为北京时间（UTC+8）
    - 数据库中存储的launch_date字段为UTC时间
    - API会自动将用户输入的北京时间转换为UTC时间进行查询
    - 例如：用户输入的北京时间2023-01-01会被转换为2022-12-31T16:00:00.000Z（UTC时间）

    ## 状态值映射
    - 即将发射：对应数据库中的 To Be Confirmed, Go for Launch, To Be Determined, On Hold
    - 发射中：对应数据库中的 Launch in Flight
    - 发射成功：对应数据库中的 Launch Successful, Payload Deployed, 1
    - 发射失败：对应数据库中的 Launch Failure, Launch was a Partial Failure, 0, 2, 3, -1
    - 历史发射：返回所有发射成功和发射失败的发射信息（包含上述发射成功和发射失败的所有状态值）
    `
  })
  @ApiBody({
    type: LaunchQueryDto,
    examples: {
      '基本查询': {
        summary: '基本查询示例',
        description: '使用分页参数进行基本查询',
        value: {
          page: 1,
          limit: 10
        }
      },
      '时间范围查询': {
        summary: '时间范围查询示例',
        description: '使用发射时间范围进行查询（北京时间，会自动转换为UTC时间）',
        value: {
          page: 1,
          limit: 10,
          launchDateStart: '2023-01-01',
          launchDateEnd: '2023-12-31'
        }
      },
      '火箭型号查询': {
        summary: '火箭型号查询示例',
        description: '使用火箭型号进行查询',
        value: {
          page: 1,
          limit: 10,
          rocketName: 'Falcon 9'
        }
      },
      '状态查询': {
        summary: '状态查询示例',
        description: '使用任务状态进行查询',
        value: {
          page: 1,
          limit: 10,
          status: LaunchStatus.SUCCESS
        }
      },
      '历史发射查询': {
        summary: '历史发射查询示例',
        description: '查询所有历史发射（包括发射成功和发射失败）',
        value: {
          page: 1,
          limit: 10,
          status: LaunchStatus.HISTORY
        }
      },
      '多条件查询': {
        summary: '多条件查询示例',
        description: '使用多个条件进行复合查询',
        value: {
          page: 1,
          limit: 10,
          launchDateStart: '2023-01-01',
          launchDateEnd: '2023-12-31',
          rocketName: 'Falcon 9',
          siteName: 'Kennedy Space Center',
          status: LaunchStatus.SUCCESS,
          provider: 'SpaceX'
        }
      },
      '排序查询': {
        summary: '排序查询示例',
        description: '使用排序参数进行查询',
        value: {
          page: 1,
          limit: 10,
          sortDirection: SortDirection.ASC
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '成功返回发射信息列表',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            total: { type: 'number', example: 42 },
            page: { type: 'number', example: 1 },
            limit: { type: 'number', example: 10 },
            results: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  _id: { type: 'string', example: '1234' },
                  _index: { type: 'string', example: 'launch_jonathan' },
                  launch_date: { type: 'string', example: '2023-06-15' },
                  rocket_name: { type: 'string', example: 'Falcon 9' },
                  site_name: { type: 'string', example: 'Kennedy Space Center' },
                  status: { type: 'string', example: 'Launch Successful' },
                  provider: { type: 'string', example: 'SpaceX' }
                }
              }
            }
          }
        },
        timestamp: { type: 'string', example: '2023-06-15T12:34:56.789Z' }
      }
    }
  })
  async searchLaunchInfo(@Body() query: LaunchQueryDto): Promise<any> {
    try {
      this.logger.debug(`接收到发射信息查询请求: ${JSON.stringify(query)}`);
      const result = await this.launchService.searchLaunchInfo(query);
      this.logger.debug(`查询结果: 找到${result.total}条记录`);
      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      this.logger.error(`查询发射信息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  @Get('rocket-names')
  @ApiOperation({
    summary: '获取火箭名称集合',
    description: `
    从Elasticsearch数据库中的launch_jonathan和以launch_spacenow开头的所有索引中提取rocket_name字段的值，合并为一个集合并返回。

    ## 数据处理
    1. 从发射信息索引中提取火箭名称
    2. 过滤掉空值、"None"值、"null"值和"undefined"值
    3. 去重并按字母顺序排序

    ## 用途
    该API主要用于：
    1. 提供火箭型号的筛选条件
    2. 展示系统中所有已知的火箭型号
    3. 辅助用户进行发射信息查询
    `
  })
  @ApiResponse({
    status: 200,
    description: '成功返回火箭名称集合',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'array',
          items: { type: 'string' },
          example: ['Atlas V', 'Falcon 9', 'Long March 5', 'Soyuz-2.1a']
        },
        timestamp: { type: 'string', example: '2023-06-15T12:34:56.789Z' }
      }
    }
  })
  async getRocketNames(): Promise<any> {
    try {
      this.logger.debug('接收到获取火箭名称集合请求');
      const rocketNames = await this.launchService.getRocketNames();
      this.logger.debug(`获取到 ${rocketNames.length} 个火箭名称`);
      return {
        success: true,
        data: rocketNames,
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      this.logger.error(`获取火箭名称集合失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  @Get('site-names')
  @ApiOperation({
    summary: '获取发射场名称集合',
    description: `
    从Elasticsearch数据库中的launch_jonathan和以launch_spacenow开头的所有索引中提取site_name字段的值，合并为一个集合并返回。

    ## 数据处理
    1. 从发射信息索引中提取发射场名称
    2. 过滤掉空值、"None"值、"null"值和"undefined"值
    3. 去重并按字母顺序排序

    ## 用途
    该API主要用于：
    1. 提供发射场的筛选条件
    2. 展示系统中所有已知的发射场
    3. 辅助用户进行发射信息查询
    `
  })
  @ApiResponse({
    status: 200,
    description: '成功返回发射场名称集合',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'array',
          items: { type: 'string' },
          example: ['Cape Canaveral', 'Kennedy Space Center', 'Jiuquan Satellite Launch Center', 'Baikonur Cosmodrome']
        },
        timestamp: { type: 'string', example: '2023-06-15T12:34:56.789Z' }
      }
    }
  })
  async getSiteNames(): Promise<any> {
    try {
      this.logger.debug('接收到获取发射场名称集合请求');
      const siteNames = await this.launchService.getSiteNames();
      this.logger.debug(`获取到 ${siteNames.length} 个发射场名称`);
      return {
        success: true,
        data: siteNames,
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      this.logger.error(`获取发射场名称集合失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  @Get('providers')
  @ApiOperation({
    summary: '获取服务商名称集合',
    description: `
    从Elasticsearch数据库中的launch_jonathan和以launch_spacenow开头的所有索引中提取provider字段的值，合并为一个集合并返回。

    ## 数据处理
    1. 从发射信息索引中提取服务商名称
    2. 过滤掉空值、"None"值、"null"值和"undefined"值
    3. 去重并按字母顺序排序

    ## 用途
    该API主要用于：
    1. 提供服务商的筛选条件
    2. 展示系统中所有已知的发射服务提供商
    3. 辅助用户进行发射信息查询
    `
  })
  @ApiResponse({
    status: 200,
    description: '成功返回服务商名称集合',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'array',
          items: { type: 'string' },
          example: ['SpaceX', 'NASA', 'CASC', 'Roscosmos']
        },
        timestamp: { type: 'string', example: '2023-06-15T12:34:56.789Z' }
      }
    }
  })
  async getProviders(): Promise<any> {
    try {
      this.logger.debug('接收到获取服务商名称集合请求');
      const providers = await this.launchService.getProviders();
      this.logger.debug(`获取到 ${providers.length} 个服务商名称`);
      return {
        success: true,
        data: providers,
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      this.logger.error(`获取服务商名称集合失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 通过COSPAR ID查询发射信息
   */
  @Post('cospar')
  @ApiOperation({
    summary: '带载荷的发射信息查询',
    description: `
    通过COSPAR发射编号查询发射信息，包含载荷信息，精确匹配（不区分大小写）。

    ## 数据来源
    从Elasticsearch数据库中的launch_gunter索引中查询发射信息。该索引包含了发射任务的详细信息，包括载荷数据。

    ## 查询说明
    - COSPAR ID格式：支持多种格式，例如：
      * 现代格式：YYYY-NNN（如：2023-001）
      * 历史格式：YYYY α (NNN)（如：1957 α (001)）
    - 查询为精确匹配
    - 返回的数据包含：
      * 发射基本信息（发射日期、火箭、发射场等）
      * 载荷信息（任务载荷列表）
      * 发射状态
      * 发射描述（如果有）
    `
  })
  @ApiBody({
    type: LaunchCosparQueryDto,
    examples: {
      '现代COSPAR ID': {
        summary: '使用现代格式COSPAR ID查询',
        description: '使用YYYY-NNN格式的发射编号进行查询',
        value: {
          cospar_launch: '2023-001'
        }
      },
      '历史COSPAR ID': {
        summary: '使用历史格式COSPAR ID查询',
        description: '使用YYYY α (NNN)格式的发射编号进行查询',
        value: {
          cospar_launch: '1957 α (001)'
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    schema: {
      example: {
        success: true,
        data: {
          total: 1,
          results: [
            {
              _id: '1957 α (001)',
              _index: 'launch_gunter',
              cospar_launch: '1957 α (001)',
              launch_date: '04.10.1957',
              rocket_name: 'Sputnik (1)',
              site_name: 'Ba LC-1/5',
              launch_description: null,
              if_launched: true,
              payloads: [
                {
                  payload: 'Sputnik 1 (PS-1 #1)'
                }
              ],
              update_date: '2025-05-16T18:26:39',
              htm_update: '2025-05-16'
            }
          ]
        },
        timestamp: '2024-03-20T12:00:00.000Z'
      }
    }
  })
  async searchLaunchByCosparId(@Body() query: LaunchCosparQueryDto) {
    try {
      this.logger.debug(`接收到COSPAR ID查询请求: ${JSON.stringify(query)}`);
      const result = await this.launchService.searchLaunchByCosparId(query);
      this.logger.debug(`查询结果: 找到${result.total}条记录`);
      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`通过COSPAR ID查询发射信息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  @Get('wiki-site-names')
  @ApiOperation({
    summary: '获取wiki发射场名称集合',
    description: `
    从Elasticsearch数据库中的launchsites_wiki索引中提取site_name字段的值，合并为一个集合并返回。

    ## 数据处理
    1. 从wiki发射场信息索引中提取发射场名称
    2. 过滤掉空值、"None"值、"null"值和"undefined"值
    3. 去重并按字母顺序排序

    ## 用途
    该API主要用于：
    1. 提供发射场的筛选条件
    2. 展示系统中所有已知的发射场
    3. 辅助用户进行发射信息查询
    `
  })
  @ApiResponse({
    status: 200,
    description: '成功返回wiki发射场名称集合',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'array',
          items: { type: 'string' },
          example: ['Cape Canaveral', 'Kennedy Space Center', 'Jiuquan Satellite Launch Center', 'Baikonur Cosmodrome']
        },
        timestamp: { type: 'string', example: '2023-06-15T12:34:56.789Z' }
      }
    }
  })
  async getWikiSiteNames(): Promise<any> {
    try {
      this.logger.debug('接收到获取wiki发射场名称集合请求');
      const siteNames = await this.launchService.getWikiSiteNames();
      this.logger.debug(`获取到 ${siteNames.length} 个wiki发射场名称`);
      return {
        success: true,
        data: siteNames,
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      this.logger.error(`获取wiki发射场名称集合失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  @Get('all-launch-sites')
  @ApiOperation({
    summary: '获取全部发射场信息',
    description: `
    从Elasticsearch数据库中的launchsites_wiki索引中获取所有发射场的site_name和location字段信息。

    ## 数据处理
    1. 从wiki发射场信息索引中获取所有发射场的site_name和location字段
    2. 处理site_name字段的数组格式（一个发射场可能有多个名称）
    3. 将每个英文发射场名称转换为对应的中文名称（使用项目中的发射场名称映射文档）
    4. 过滤掉无效的记录（空值、无发射场名称等）
    5. 去重处理，避免重复的发射场记录
    6. 返回包含英文名称、中文名称和位置信息的完整列表

    ## 数据来源
    - 索引：launchsites_wiki
    - 字段：site_name（数组格式）、location
    - 名称映射：使用项目中data/launchSites.ts的映射关系

    ## 数据格式说明
    - ES原始数据：site_name字段为数组格式，如["Centre interarmées d'essais d'engins spéciaux (CIEES)", "Hammaguir"]
    - API返回：为每个英文名称创建记录，包含原始英文名称、对应的中文名称、位置信息和ES文档ID

    ## 用途
    该API主要用于：
    1. 获取完整的发射场信息列表
    2. 提供发射场的英文名称和中文名称对照
    3. 支持前端展示发射场详细信息
    4. 辅助发射信息查询和筛选功能
    5. 通过_id字段支持精确的发射场信息查询
    `
  })
  @ApiResponse({
    status: 200,
    description: '成功返回全部发射场信息',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              english_name: { type: 'string', example: 'Kennedy Space Center' },
              chinese_name: { type: 'string', example: '肯尼迪航天中心' },
              location: { type: 'string', example: '美国佛罗里达州默里特岛' },
              _id: { type: 'string', example: 'abc123def456' }
            }
          },
          example: [
            {
              english_name: 'Kennedy Space Center',
              chinese_name: '肯尼迪航天中心',
              location: '美国佛罗里达州默里特岛',
              _id: 'abc123def456'
            },
            {
              english_name: 'Cape Canaveral Space Force Station',
              chinese_name: '卡纳维拉尔角航天发射场',
              location: '美国佛罗里达州卡纳维拉尔角',
              _id: 'def456ghi789'
            },
            {
              english_name: 'Jiuquan Satellite Launch Center',
              chinese_name: '酒泉卫星发射中心',
              location: '中国甘肃省酒泉市',
              _id: 'ghi789jkl012'
            },
            {
              english_name: 'Baikonur Cosmodrome',
              chinese_name: '拜科努尔航天发射场',
              location: '哈萨克斯坦拜科努尔',
              _id: 'jkl012mno345'
            }
          ]
        },
        timestamp: { type: 'string', example: '2023-06-15T12:34:56.789Z' }
      }
    }
  })
  async getAllLaunchSites(): Promise<any> {
    try {
      this.logger.debug('接收到获取全部发射场信息请求');
      const launchSitesInfo = await this.launchService.getAllLaunchSitesInfo();
      this.logger.debug(`获取到 ${launchSitesInfo.length} 条发射场信息`);
      return {
        success: true,
        data: launchSitesInfo,
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      this.logger.error(`获取全部发射场信息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  @Get('wiki-site-info')
  @ApiOperation({
    summary: '查询发射场wiki信息',
    description: `
    根据发射场名称或ES文档ID查询launchsites_wiki索引中的发射场信息。

    ## 查询方式
    支持两种查询方式（至少需要提供其中一种参数）：

    ### 1. 通过ES文档ID查询（优先级更高）
    - 参数：_id
    - 说明：使用ES文档的唯一标识符进行精确查询
    - 优势：查询速度快，结果精确
    - 示例：_id=abc123def456

    ### 2. 通过发射场名称查询
    - 参数：siteName
    - 支持通过发射场的以下信息进行匹配：
      - 发射场代码（如：KSC）
      - 英文名称（如：Kennedy Space Center）
      - 中文名称（如：肯尼迪航天中心）
      - 别名（如：NASA Kennedy）
    - 只要输入的名称能在@launchSites.ts中找到对应的发射场，就会返回该发射场在wiki中的所有信息

    ## 查询优先级
    如果同时提供了_id和siteName参数，系统将优先使用_id进行查询。

    ## 返回数据
    - 返回匹配发射场的所有wiki信息（包含_id字段）
    - 如果未找到匹配的发射场，返回空数组
    `
  })
  @ApiResponse({
    status: 200,
    description: '成功返回发射场wiki信息',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              _id: { type: 'string', example: 'abc123def456' },
              site_name: { type: 'string', example: 'Kennedy Space Center' },
              description: { type: 'string', example: '美国国家航空航天局位于佛罗里达州的主要发射场...' },
              location: { type: 'string', example: '美国佛罗里达州默里特岛' },
              coordinates: { type: 'string', example: '28.524058°N 80.650825°W' },
              operator: { type: 'string', example: 'NASA' },
              launch_pads: { type: 'array', items: { type: 'string' }, example: ['LC-39A', 'LC-39B'] }
            }
          }
        },
        timestamp: { type: 'string', example: '2024-03-20T08:30:00.000Z' }
      }
    }
  })
  async searchLaunchSiteWiki(@Query() query: LaunchSiteWikiQueryDto): Promise<any> {
    try {
      this.logger.debug('接收到查询发射场wiki信息请求');
      const data = await this.launchService.searchLaunchSiteWiki(query);
      return {
        success: true,
        data,
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      this.logger.error(`查询发射场wiki信息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  @Post('service-providers/search')
  @ApiOperation({
    summary: '查询发射服务商信息',
    description: `
    通过关键词查询发射服务商信息，关键词将与服务商代码、简称、名称等字段进行精确匹配。
    
    ## 数据来源
    从Elasticsearch数据库中的orgs_jonathan索引查询发射服务商信息。
    
    ## 匹配字段
    - code：服务商代码
    - ucode：服务商唯一代码
    - shortname：服务商简称
    - name：服务商名称
    - short_ename：服务商英文简称
    - ename：服务商英文名称
    - uname：服务商唯一名称
    
    ## 匹配规则
    关键词与上述字段中的一个能精确匹配上即是匹配成功。
    `
  })
  @ApiBody({
    type: LaunchServiceProviderQueryDto,
    examples: {
      '基本查询': {
        summary: '基本查询示例',
        description: '使用关键词查询服务商信息',
        value: {
          keyword: 'SpaceX'
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '成功返回匹配的服务商信息',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            total: { type: 'number', example: 1 },
            results: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  _id: { type: 'string', example: '1234' },
                  code: { type: 'string', example: 'spacex' },
                  ucode: { type: 'string', example: 'spacex' },
                  shortname: { type: 'string', example: 'SpaceX' },
                  name: { type: 'string', example: 'Space Exploration Technologies Corp.' },
                  short_ename: { type: 'string', example: 'SpaceX' },
                  ename: { type: 'string', example: 'Space Exploration Technologies Corp.' },
                  uname: { type: 'string', example: 'spacexplorationtechnologiescorp' }
                }
              }
            }
          }
        },
        timestamp: { type: 'string', example: '2023-06-15T12:34:56.789Z' }
      }
    }
  })
  async searchServiceProviders(@Body() query: LaunchServiceProviderQueryDto): Promise<any> {
    try {
      this.logger.debug(`接收到发射服务商信息查询请求: ${JSON.stringify(query)}`);
      const result = await this.launchService.searchServiceProviderByKeyword(query.keyword);
      this.logger.debug(`查询结果: 找到${result.total}个服务商信息`);
      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      this.logger.error(`查询发射服务商信息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}
