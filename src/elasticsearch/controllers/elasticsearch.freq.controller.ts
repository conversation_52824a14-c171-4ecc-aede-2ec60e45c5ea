import { Controller, Post, Body, UseGuards, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { ElasticsearchFreqService } from '../services/elasticsearch.freq.service';
import { FreqQueryDto } from '../dto/freq-query.dto';
import { FreqSearchResponse } from '../types/freq.types';

/**
 * Elasticsearch频率查询控制器
 * 处理频率信息的查询请求
 */
@Controller('freq')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('频率数据')
export class ElasticsearchFreqController {
  private readonly logger = new Logger(ElasticsearchFreqController.name);

  constructor(private readonly freqService: ElasticsearchFreqService) {}

  @Post('search')
  @ApiOperation({
    summary: '查询频率信息',
    description: '根据提供的查询条件搜索频率信息，支持多种过滤条件和排序选项'
  })
  @ApiBody({
    type: FreqQueryDto,
    examples: {
      byFrequency: {
        summary: '使用频率范围查询',
        value: {
          page: 1,
          limit: 10,
          min_frequency: 100,
          max_frequency: 200
        }
      },
      byPurpose: {
        summary: '使用用途查询',
        value: {
          page: 1,
          limit: 10,
          purpose: "通信"
        }
      },
      byCountry: {
        summary: '使用国家查询',
        value: {
          page: 1,
          limit: 10,
          country: "中国"
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '查询成功'
  })
  async searchFrequencies(@Body() query: FreqQueryDto): Promise<FreqSearchResponse> {
    this.logger.debug(`接收到频率信息查询请求: ${JSON.stringify(query)}`);
    const result = await this.freqService.searchFrequencies(query);
    this.logger.debug(`查询结果: ${JSON.stringify(result)}`);
    return result;
  }
} 