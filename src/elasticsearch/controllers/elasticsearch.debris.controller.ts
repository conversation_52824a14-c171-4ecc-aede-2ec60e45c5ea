import { Controller, Post, Body, UseGuards, Logger, Get, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiBearerAuth, ApiBody, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { ElasticsearchDebrisService } from '../services/elasticsearch.debris.service';
import { DebrisQueryDto } from '../dto/debris-query.dto';
import { DebrisSearchResponse } from '../interfaces/debris-search-response.interface';

/**
 * Elasticsearch碎片查询控制器
 * 处理碎片信息的查询请求
 */
@Controller('debris')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('碎片信息')
export class ElasticsearchDebrisController {
  private readonly logger = new Logger(ElasticsearchDebrisController.name);

  constructor(private readonly debrisService: ElasticsearchDebrisService) {}

  /**
   * 查询碎片信息
   * @param query 查询参数
   * @returns 碎片信息查询结果
   */
  @Post('search')
  @ApiOperation({
    summary: '查询碎片信息',
    description: '根据提供的查询条件搜索碎片信息。查询逻辑如下：\n\n' +
      '1. 检查查询条件类型：\n' +
      '   - 如果只提供了轨道参数范围条件（period_minutes_range、incl_degrees_range、apogee_km_range、perigee_km_range），则直接从debris_spacetrack索引中搜索符合条件的碎片信息\n' +
      '   - 如果提供了其他查询条件（如关键词、COSPAR ID等），则按照以下逻辑进行查询\n\n' +
      '2. 首先查询debris_discos索引中是否有匹配的文档\n' +
      '3. 如果有匹配的文档，则与debris_spacetrack索引中满足聚合条件的文档进行聚合\n' +
      '4. 聚合时按cospar_id相同的条件进行，当有多个debris_spacetrack文档匹配时，选择_id最大的文档\n' +
      '5. 聚合后的文档将包含两个索引中的所有字段。对于两个索引中相同字段有不同值的情况：\n' +
      '   - 默认优先使用debris_discos索引中的值作为主值\n' +
      '   - 在source_fields中记录字段来源\n' +
      '   - 在field_values中以数组形式存储不同来源的值及其索引来源，例如：\n' +
      '     ```\n' +
      '     "field_values": {\n' +
      '       "name": [\n' +
      '         { "value": "Cosmos-2251 fragmentation debris", "source": "debris_discos" },\n' +
      '         { "value": "COSMOS 2251 DEB", "source": "debris_spacetrack" }\n' +
      '       ],\n' +
      '       "object_class": [\n' +
      '         { "value": "Payload Fragmentation Debris", "source": "debris_discos" },\n' +
      '         { "value": "DEBRIS", "source": "debris_spacetrack" }\n' +
      '       ]\n' +
      '     }\n' +
      '     ```\n' +
      '6. 如果查询条件中有范围类型的条件，会对聚合后的结果进行再次筛选\n' +
      '7. 如果debris_discos索引中没有匹配的文档，则查询debris_spacetrack索引\n' +
      '8. 返回结果按与查询条件的匹配程度进行排序\n\n' +
      '字段匹配逻辑：\n' +
      '- cospar_id和norad_id: 进行精确匹配，需要提供MatchType和value\n' +
      '- name和mission: 按相似度进行匹配，直接提供字符串值\n' +
      '- country和object_class: 只要表示的含义相同则算作匹配，需要提供MatchType和value\n' +
      '- 轨道参数范围（period_minutes_range、incl_degrees_range、apogee_km_range、perigee_km_range）: 直接匹配orbit_info对象中的相应字段，支持最小值和最大值范围查询\n\n' +
      '排序方式：\n' +
      '- 默认按查询条件的匹配程度（相关性分数）进行排序\n' +
      '- 可以通过sort参数指定其他排序字段和顺序\n\n' +
      '特殊查询场景：\n' +
      '- 仅轨道参数查询：当只提供轨道参数范围条件时，系统会直接从debris_spacetrack索引中搜索符合条件的碎片信息，这种查询方式特别适合寻找特定轨道特性的碎片\n' +
      '  * 当命中多个具有相同cospar_id的文档时，只保留_id字段最大的一个文档\n' +
      '  * 系统会根据查询结果中的cospar_id，从debris_discos索引中查找匹配的文档，并进行聚合\n' +
      '  * 聚合逻辑与普通查询相同，确保结果格式的一致性\n' +
      '- 轨道参数范围查询支持以下字段：\n' +
      '  * period_minutes_range：轨道周期范围（分钟）\n' +
      '  * incl_degrees_range：轨道倾角范围（度）\n' +
      '  * apogee_km_range：远地点高度范围（千米）\n' +
      '  * perigee_km_range：近地点高度范围（千米）\n' +
      '- 每个范围字段都支持设置最小值（min）和最大值（max），可以只设置其中一个或同时设置两个'
  })
  @ApiBody({
    type: DebrisQueryDto,
    examples: {
      byKeyword: {
        summary: '使用关键词查询',
        description: '通过关键词搜索多个字段',
        value: {
          page: 1,
          limit: 10,
          keyword: 'International Space Station'
        }
      },
      byName: {
        summary: '使用名称查询',
        description: '通过名称查询碎片',
        value: {
          page: 1,
          limit: 10,
          name: 'ISS'
        }
      },
      byOrbitRange: {
        summary: '仅使用轨道参数范围查询',
        description: '通过轨道参数范围直接查询debris_spacetrack索引中的碎片信息，无需其他查询条件',
        value: {
          page: 1,
          limit: 10,
          period_minutes_range: { min: 90, max: 100 },
          incl_degrees_range: { min: 45, max: 60 },
          apogee_km_range: { min: 500, max: 800 },
          perigee_km_range: { min: 400, max: 600 }
        }
      },
      byMultipleFilters: {
        summary: '使用多种条件组合查询',
        description: '通过组合多个条件进行复杂查询',
        value: {
          page: 1,
          limit: 10,
          country: {
            matchType: 'exact',
            value: 'USA'
          },
          name: 'International Space Station',
          mission: 'ISS',
          apogee_km_range: { min: 500, max: 800 },
          perigee_km_range: { min: 400, max: 600 },
          sort: { field: 'launch_date', order: 'desc' }
        }
      },
      byCoordinatedSearch: {
        summary: '精确与模糊搜索结合',
        description: '同时使用精确匹配和模糊匹配进行全面搜索',
        value: {
          page: 1,
          limit: 20,
          cospar_id: {
            matchType: 'exact',
            value: '1998-067A'
          },
          keyword: 'space station',
          object_class: {
            matchType: 'fuzzy', 
            value: 'PAYLOAD'
          },
          period_minutes_range: { min: 85, max: 95 }
        }
      },
      completeExample: {
        summary: '包含所有查询参数的示例',
        description: '展示所有可能的查询参数及其格式',
        value: {
          page: 1,
          limit: 20,
          keyword: 'International Space Station',
          cospar_id: {
            matchType: 'exact',
            value: '1998-067A'
          },
          norad_id: {
            matchType: 'exact',
            value: '25544'
          },
          name: 'ISS',
          country: {
            matchType: 'exact',
            value: 'USA'
          },
          launch_date: {
            matchType: 'exact',
            value: '1998-11-20'
          },
          launch_site: {
            matchType: 'fuzzy',
            value: 'Baikonur'
          },
          rcs_size: {
            matchType: 'exact',
            value: 'LARGE'
          },
          first_epoch: {
            matchType: 'exact',
            value: '1998-11-21'
          },
          object_class: {
            matchType: 'exact',
            value: 'PAYLOAD'
          },
          mission: 'International Space Station',
          period_minutes_range: { min: 90, max: 93 },
          incl_degrees_range: { min: 51, max: 52 },
          apogee_km_range: { min: 415, max: 425 },
          perigee_km_range: { min: 410, max: 420 },
          sort: { field: 'launch_date', order: 'desc' }
        }
      },
      byCustomSort: {
        summary: '自定义排序方式',
        description: '使用自定义字段进行排序',
        value: {
          page: 1,
          limit: 10,
          name: 'Debris',
          sort: { field: 'apogee_km', order: 'desc' }
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    schema: {
      properties: {
        total: { type: 'number', example: 100 },
        page: { type: 'number', example: 1 },
        size: { type: 'number', example: 10 },
        items: { 
          type: 'array', 
          items: {
            type: 'object',
            properties: {
              _id: { type: 'string', example: 'sat-12345' },
              norad_id: { type: 'number', example: 25544 },
              cospar_id: { type: 'string', example: '1998-067A' },
              name: { type: 'string', example: 'ISS (ZARYA)' },
              country: { type: 'string', example: 'USA' },
              period_minutes: { type: 'number', example: 92.7 },
              incl_degrees: { type: 'number', example: 51.6 },
              apogee_km: { type: 'number', example: 422 },
              perigee_km: { type: 'number', example: 417 },
              object_class: { type: 'string', example: 'PAYLOAD' },
              source_index: { type: 'string', example: 'debris_discos', description: '主要数据来源索引' },
              source_fields: { 
                type: 'object', 
                example: { 
                  name: 'debris_discos',
                  object_class: 'debris_discos',
                  period_minutes: 'debris_discos',
                  apogee_km: 'debris_spacetrack'
                },
                description: '当字段值在不同索引中不同时，记录使用的字段来自哪个索引'
              },
              field_values: {
                type: 'object',
                example: {
                  name: [
                    { value: 'ISS (ZARYA)', source: 'debris_discos' },
                    { value: 'ISS', source: 'debris_spacetrack' }
                  ],
                  object_class: [
                    { value: 'PAYLOAD', source: 'debris_discos' },
                    { value: 'DEBRIS', source: 'debris_spacetrack' }
                  ]
                },
                description: '存储不同索引中字段的不同值及其来源'
              },
              match_score: { type: 'number', example: 15.84 },
              matched_fields: {
                type: 'object',
                properties: {
                  keyword: { type: 'boolean', example: true },
                  norad_id: { type: 'boolean', example: false },
                  name: { type: 'boolean', example: true },
                  period_minutes_range: { type: 'boolean', example: true }
                }
              },
              matched_fields_description: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    field: { type: 'string', example: 'name' },
                    matchLevel: { type: 'string', example: '部分匹配' },
                    score: { type: 'number', example: 0.5 }
                  }
                }
              },
              matched_fields_count: { type: 'number', example: 3 }
            }
          }
        }
      }
    }
  })
  async searchDebris(@Body() query: DebrisQueryDto): Promise<DebrisSearchResponse> {
    this.logger.debug(`接收到碎片信息查询请求: ${JSON.stringify(query)}`);
    const result = await this.debrisService.searchDebris(query);
    this.logger.debug(`查询结果: ${JSON.stringify(result)}`);
    return result;
  }
  
  /**
   * 获取碎片名称集合
   * @returns 所有碎片的唯一名称数组
   */
  @Get('names')
  @ApiOperation({ summary: '获取碎片名称集合', description: '获取所有碎片的唯一名称集合' })
  @ApiResponse({ status: 200, description: '获取成功', type: [String] })
  async getDebrisNames(): Promise<string[]> {
    this.logger.debug('接收到获取碎片名称集合请求');
    const result = await this.debrisService.getDebrisNames();
    this.logger.debug(`获取到${result.length}个碎片名称`);
    return result;
  }

  /**
   * 获取碎片类别集合
   * @returns 所有碎片的唯一类别数组
   */
  @Get('object-classes')
  @ApiOperation({ summary: '获取碎片类别集合', description: '获取所有碎片的唯一类别集合' })
  @ApiResponse({ status: 200, description: '获取成功', type: [String] })
  async getDebrisObjectClasses(): Promise<string[]> {
    this.logger.debug('接收到获取碎片类别集合请求');
    const result = await this.debrisService.getDebrisObjectClasses();
    this.logger.debug(`获取到${result.length}个碎片类别`);
    return result;
  }

  /**
   * 获取碎片任务名称集合
   * @returns 所有碎片的唯一任务名称数组
   */
  @Get('missions')
  @ApiOperation({ summary: '获取碎片任务名称集合', description: '获取所有碎片的唯一任务名称集合' })
  @ApiResponse({ status: 200, description: '获取成功', type: [String] })
  async getDebrisMissions(): Promise<string[]> {
    this.logger.debug('接收到获取碎片任务名称集合请求');
    const result = await this.debrisService.getDebrisMissions();
    this.logger.debug(`获取到${result.length}个碎片任务名称`);
    return result;
  }

  /**
   * 获取所有碎片所属国家集合
   * @returns 唯一国家名称数组，格式为"中文名称(英文名称)"
   */
  @Get('countries')
  @ApiOperation({
    summary: '获取所有碎片所属国家集合',
    description: '从debris_spacetrack索引中获取所有唯一的国家名称，返回格式为"中文名称(英文名称)"'
  })
  @ApiResponse({
    status: 200,
    description: '成功获取碎片所属国家集合',
    type: [String]
  })
  async getDebrisCountries(): Promise<string[]> {
    this.logger.debug('获取所有碎片所属国家集合');
    return this.debrisService.getDebrisCountries();
  }

  /**
   * 调试查询参数DTO
   * @returns 示例查询参数
   */
  @Get('debug-query-dto')
  @ApiOperation({ summary: '查询参数示例', description: '返回查询参数示例' })
  debugQueryDto() {
    return {
      example: {
        "page": 1,
        "limit": 10,
        "cospar_id": {
          "matchType": "exact",
          "value": "1998-067A"
        },
        "keyword": "International Space Station",
        "country": {
          "matchType": "exact",
          "value": "USA"
        },
        "object_class": {
          "matchType": "fuzzy",
          "value": "PAYLOAD"
        },
        "apogee_km_range": {
          "min": 500,
          "max": 800
        },
        "perigee_km_range": {
          "min": 400,
          "max": 600
        },
        "sort": {
          "field": "launch_date",
          "order": "desc"
        }
      }
    };
  }
} 