import { Controller, Post, Body, UseGuards, Logger, Get, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiBearerAuth, ApiBody, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { ElasticsearchConstellationService } from '../services/elasticsearch.constellation.service';
import { 
  ConstellationQueryDto, 
  ConstellationSearchResponse, 
  ConstellationNamesResponseDto, 
  ConstellationOrganizationsResponseDto, 
  ConstellationPurposesResponseDto,
  ConstellationSearchResponseDto
} from '../types/constellation.types';

/**
 * 星座信息查询控制器
 * 处理星座信息的查询请求
 */
@Controller('constellation')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('星座信息')
export class ElasticsearchConstellationController {
  private readonly logger = new Logger(ElasticsearchConstellationController.name);

  constructor(private readonly constellationService: ElasticsearchConstellationService) {}

  @Post('search')
  @ApiOperation({
    summary: '查询星座信息',
    description: `根据提供的查询条件搜索星座信息，支持关键词、目标名称、目标ID、国家、组织和用途等条件。
    
CURL示例:
\`\`\`bash
# 完整查询示例
curl -X POST http://localhost:3001/constellation/search \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  -d '{
    "page": 1,
    "limit": 10,
    "keyword": "Starlink",
    "targetName": "Starlink",
    "targetId": "STARLINK-1234",
    "country": "USA",
    "organization": "SpaceX",
    "purpose": "互联网"
  }'

# 使用关键词查询
curl -X POST http://localhost:3001/constellation/search \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  -d '{
    "page": 1,
    "limit": 10,
    "keyword": "Starlink"
  }'
\`\`\`
    `
  })
  @ApiBody({
    type: ConstellationQueryDto,
    examples: {
      complete: {
        summary: '完整的查询示例',
        value: {
          page: 1,
          limit: 10,
          keyword: "Starlink",
          targetName: "Starlink",
          targetId: "STARLINK-1234",
          country: "USA",
          organization: "SpaceX",
          purpose: "互联网"
        }
      },
      byKeyword: {
        summary: '使用关键词查询',
        value: {
          page: 1,
          limit: 10,
          keyword: "Starlink"
        }
      },
      byTargetName: {
        summary: '使用目标名称查询',
        value: {
          page: 1,
          limit: 10,
          targetName: "OneWeb"
        }
      },
      byOrganization: {
        summary: '使用组织名称查询',
        value: {
          page: 1,
          limit: 10,
          organization: "SpaceX"
        }
      },
      byPurpose: {
        summary: '使用用途查询',
        value: {
          page: 1,
          limit: 10,
          purpose: "通信"
        }
      },
      byCountry: {
        summary: '使用国家查询',
        value: {
          page: 1,
          limit: 10,
          country: "中国"
        }
      },
      paginationExample: {
        summary: '分页查询示例',
        value: {
          page: 2,
          limit: 20
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: ConstellationSearchResponseDto
  })
  async searchConstellationInfo(@Body() query: ConstellationQueryDto): Promise<ConstellationSearchResponse> {
    this.logger.debug(`接收到星座信息查询请求: ${JSON.stringify(query)}`);
    const result = await this.constellationService.searchConstellationInfo(query);
    this.logger.debug(`查询结果: ${JSON.stringify(result)}`);
    return result;
  }

  @Get('names')
  @ApiTags('星座信息')
  @ApiOperation({
    summary: '获取星座名称列表',
    description: '获取系统中所有星座的名称列表，用于下拉选择等场景'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: ConstellationNamesResponseDto
  })
  async getConstellationNames(): Promise<ConstellationNamesResponseDto> {
    this.logger.debug('接收到获取星座名称列表请求');
    // 调用服务中已实现的方法获取星座名称列表
    const result = await this.constellationService.getConstellationNames();
    this.logger.debug(`获取到 ${result.constellationNames.length} 个星座名称`);
    return result;
  }

  @Get('organizations')
  @ApiTags('星座信息')
  @ApiOperation({
    summary: '获取星座组织列表',
    description: '获取系统中所有星座相关的组织列表，用于下拉选择等场景'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: ConstellationOrganizationsResponseDto
  })
  async getConstellationOrganizations(): Promise<ConstellationOrganizationsResponseDto> {
    this.logger.debug('接收到获取星座组织列表请求');
    // 调用服务中已实现的方法获取组织列表
    const result = await this.constellationService.getConstellationOrganizations();
    this.logger.debug(`获取到 ${result.organizations.length} 个组织`);
    return result;
  }

  @Get('purposes')
  @ApiTags('星座信息')
  @ApiOperation({
    summary: '获取星座用途列表',
    description: '获取系统中所有星座的用途列表，用于下拉选择等场景'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: ConstellationPurposesResponseDto
  })
  async getConstellationPurposes(): Promise<ConstellationPurposesResponseDto> {
    this.logger.debug('接收到获取星座用途列表请求');
    // 调用服务中已实现的方法获取用途列表
    const result = await this.constellationService.getConstellationPurposes();
    this.logger.debug(`获取到 ${result.purposes.length} 个用途`);
    return result;
  }
} 