import { Controller, Post, Body, Get, Logger } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiBody, ApiTags } from '@nestjs/swagger';
import { EntityRecognitionService } from '../services/entity-recognition.service';

/**
 * 实体识别测试控制器
 * 用于测试和验证实体识别功能
 */
@Controller('test/entity-recognition')
@ApiTags('测试接口')
export class EntityRecognitionTestController {
  private readonly logger = new Logger(EntityRecognitionTestController.name);

  constructor(private readonly entityRecognitionService: EntityRecognitionService) {}

  @Get('statistics')
  @ApiOperation({
    summary: '获取实体统计信息',
    description: '获取当前加载的实体名称集合的统计信息'
  })
  @ApiResponse({
    status: 200,
    description: '统计信息获取成功',
    schema: {
      type: 'object',
      properties: {
        satellites: { type: 'number', description: '卫星数量' },
        constellations: { type: 'number', description: '星座数量' },
        launchSites: { type: 'number', description: '发射场数量' },
        rockets: { type: 'number', description: '火箭数量' },
        providers: { type: 'number', description: '服务商数量' },
        total: { type: 'number', description: '总数量' }
      }
    }
  })
  async getEntityStatistics(): Promise<any> {
    this.logger.debug('接收到获取实体统计信息请求');
    await this.entityRecognitionService.initialize();
    const statistics = this.entityRecognitionService.getEntityStatistics();
    this.logger.debug(`实体统计信息: ${JSON.stringify(statistics)}`);
    return statistics;
  }

  @Post('recognize')
  @ApiOperation({
    summary: '测试实体识别',
    description: '对提供的文本内容进行实体识别测试'
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        content: {
          type: 'string',
          description: '要识别的文本内容',
          example: 'SpaceX successfully launched a Falcon 9 rocket carrying 23 Starlink satellites from Kennedy Space Center.'
        }
      },
      required: ['content']
    }
  })
  @ApiResponse({
    status: 200,
    description: '实体识别成功',
    schema: {
      type: 'object',
      properties: {
        content: { type: 'string', description: '原始内容' },
        entities: {
          type: 'object',
          properties: {
            satellites: { type: 'array', items: { type: 'string' } },
            constellations: { type: 'array', items: { type: 'string' } },
            launch_sites: { type: 'array', items: { type: 'string' } },
            rockets: { type: 'array', items: { type: 'string' } },
            providers: { type: 'array', items: { type: 'string' } }
          }
        },
        statistics: { type: 'object', description: '实体集合统计信息' }
      }
    }
  })
  async testEntityRecognition(@Body('content') content: string): Promise<any> {
    this.logger.debug(`接收到实体识别测试请求，内容长度: ${content?.length || 0}`);
    
    if (!content || content.trim() === '') {
      return {
        error: '内容不能为空',
        content: '',
        entities: {
          satellites: [],
          constellations: [],
          launch_sites: [],
          rockets: [],
          providers: []
        },
        statistics: null
      };
    }

    try {
      const result = await this.entityRecognitionService.testEntityRecognition(content);
      this.logger.debug(`实体识别完成，识别到实体数量: ${Object.values(result.entities).reduce((sum, arr) => sum + arr.length, 0)}`);
      return result;
    } catch (error) {
      this.logger.error(`实体识别测试失败: ${error.message}`, error.stack);
      return {
        error: error.message,
        content,
        entities: {
          satellites: [],
          constellations: [],
          launch_sites: [],
          rockets: [],
          providers: []
        },
        statistics: null
      };
    }
  }

  @Get('sample-test')
  @ApiOperation({
    summary: '运行示例测试',
    description: '使用预定义的示例内容运行实体识别测试'
  })
  @ApiResponse({
    status: 200,
    description: '示例测试完成'
  })
  async runSampleTest(): Promise<any> {
    this.logger.debug('接收到示例测试请求');
    
    const sampleContent = `
SpaceX successfully launched a Falcon 9 rocket carrying 23 Starlink satellites from Kennedy Space Center. 
The mission was operated by SpaceX and marked another milestone for the Starlink constellation. 
The rocket lifted off from Launch Complex 39A at Kennedy Space Center in Florida.
Blue Origin also announced plans for future launches from their facility.
The Hubble Space Telescope continues to provide valuable scientific data.
OneWeb constellation is expanding with new satellite deployments.
NASA's James Webb Space Telescope captured stunning images of distant galaxies.
The International Space Station (ISS) received new supplies via a Dragon capsule.
    `.trim();

    try {
      const result = await this.entityRecognitionService.testEntityRecognition(sampleContent);
      this.logger.debug(`示例测试完成，识别到实体数量: ${Object.values(result.entities).reduce((sum, arr) => sum + arr.length, 0)}`);
      return {
        ...result,
        message: '示例测试完成',
        expectedEntities: {
          satellites: ['Starlink', 'Hubble Space Telescope', 'James Webb Space Telescope', 'International Space Station'],
          constellations: ['Starlink', 'OneWeb'],
          launch_sites: ['Kennedy Space Center'],
          rockets: ['Falcon 9'],
          providers: ['SpaceX', 'Blue Origin', 'NASA']
        }
      };
    } catch (error) {
      this.logger.error(`示例测试失败: ${error.message}`, error.stack);
      return {
        error: error.message,
        message: '示例测试失败'
      };
    }
  }
}
