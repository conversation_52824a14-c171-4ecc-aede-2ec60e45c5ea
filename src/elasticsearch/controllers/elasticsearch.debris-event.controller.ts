import { Controller, Post, Body, Logger, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiBody } from '@nestjs/swagger';
import { ElasticsearchDebrisEventService } from '../services/elasticsearch.debris-event.service';
import { DebrisEventQueryDto } from '../dto/debris-event-query.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

/**
 * 碎片事件信息控制器
 * 提供碎片事件信息相关的API接口
 */
@ApiTags('碎片信息')
@Controller('api/debris-events')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class ElasticsearchDebrisEventController {
  private readonly logger = new Logger(ElasticsearchDebrisEventController.name);

  constructor(
    private readonly debrisEventService: ElasticsearchDebrisEventService
  ) {}

  @Post('search')
  @ApiOperation({
    summary: '查询碎片事件信息',
    description: '根据提供的查询条件搜索碎片事件信息，支持多种过滤条件和排序选项'
  })
  @ApiBody({
    type: DebrisEventQueryDto,
    examples: {
      byEventId: {
        summary: '使用事件ID查询',
        value: {
          page: 1,
          limit: 10,
          event_id: "EVENT-001"
        }
      },
      byDate: {
        summary: '使用日期范围查询',
        value: {
          page: 1,
          limit: 10,
          start_date: "2023-01-01",
          end_date: "2023-12-31"
        }
      },
      byType: {
        summary: '使用事件类型查询',
        value: {
          page: 1,
          limit: 10,
          event_type: "碰撞"
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    schema: {
      properties: {
        total: { type: 'number', example: 100 },
        page: { type: 'number', example: 1 },
        size: { type: 'number', example: 10 },
        items: { 
          type: 'array', 
          items: {
            type: 'object',
            properties: {
              event_id: { type: 'string', example: 'EVENT-001' },
              event_type: { type: 'string', example: '碰撞' },
              event_date: { type: 'string', format: 'date-time', example: '2023-05-15T10:30:00Z' },
              description: { type: 'string', example: '两颗碎片发生近距离接触' },
              objects_involved: { type: 'array', items: { type: 'string' }, example: ['DEBRIS-001', 'DEBRIS-002'] },
              severity: { type: 'string', example: '高' }
            }
          }
        }
      }
    }
  })
  async searchDebrisEvents(@Body() query: DebrisEventQueryDto): Promise<any> {
    this.logger.debug(`接收到碎片事件信息查询请求: ${JSON.stringify(query)}`);
    const result = await this.debrisEventService.searchDebrisEvents(query);
    this.logger.debug(`查询结果: ${JSON.stringify(result)}`);
    return result;
  }
} 