import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsEnum, IsBoolean, IsDateString, IsInt, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { BaseQueryDto } from './base-query.dto';

export enum OrbitType {
  LEO = 'LEO',    // 低地轨道：高度 < 2000km
  MEO = 'MEO',    // 中地轨道：2000km ≤ 高度 < 35786km
  GEO = 'GEO'     // 地球同步轨道：高度 ≥ 35786km
}

export class OrbitQueryDto extends BaseQueryDto {
  @ApiProperty({
    description: '卫星编号',
    required: false,
  })
  @IsOptional()
  @IsString()
  satelliteId?: string;

  @ApiProperty({
    description: '轨道高度范围（最小值，单位：km）',
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  minAltitude?: number;

  @ApiProperty({
    description: '轨道高度范围（最大值，单位：km）',
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  maxAltitude?: number;

  @ApiProperty({
    description: '时间范围（开始）',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  startTime?: string;

  @ApiProperty({
    description: '时间范围（结束）',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  endTime?: string;

  @ApiProperty({ 
    description: '卫星名称（支持精确匹配和模糊匹配，需配合 fuzzy_match 参数使用）',
    required: false,
    example: 'Canyon 2'
  })
  @IsOptional()
  @IsString()
  satellite_name?: string;

  @ApiProperty({ 
    description: '是否使用模糊匹配卫星名称（true: 模糊匹配，false: 精确匹配）',
    required: false,
    default: false,
    example: false
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  fuzzy_match?: boolean;

  @ApiProperty({ 
    description: '卫星编号（NORAD ID，用于唯一标识卫星）',
    required: false,
    example: 3889
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  norad_id?: number;

  @ApiProperty({ 
    description: '国际代号（COSPAR ID，格式：YYYY-NNNX，如 1969-036A）',
    required: false,
    example: '1969-036A'
  })
  @IsOptional()
  @IsString()
  cospar_id?: string;

  @ApiProperty({ 
    description: '轨道类型（LEO: 低地轨道，MEO: 中地轨道，GEO: 地球同步轨道）',
    required: false,
    enum: OrbitType,
    enumName: 'OrbitType',
    example: OrbitType.LEO
  })
  @IsOptional()
  @IsEnum(OrbitType)
  orbit_type?: OrbitType;

  @ApiProperty({ 
    description: '轨道高度最小值（单位：千米，用于范围查询）',
    required: false,
    minimum: 0,
    example: 500
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  min_altitude?: number;

  @ApiProperty({ 
    description: '轨道高度最大值（单位：千米，用于范围查询）',
    required: false,
    minimum: 0,
    example: 1000
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  max_altitude?: number;

  @ApiProperty({ 
    description: '轨道倾角最小值（单位：度，范围：0-180，用于范围查询）',
    required: false,
    minimum: 0,
    maximum: 180,
    example: 30
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(180)
  min_inclination?: number;

  @ApiProperty({ 
    description: '轨道倾角最大值（单位：度，范围：0-180，用于范围查询）',
    required: false,
    minimum: 0,
    maximum: 180,
    example: 60
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(180)
  max_inclination?: number;

  @ApiProperty({ 
    description: '升交点赤经最小值（单位：度，范围：0-360，用于范围查询）',
    required: false,
    minimum: 0,
    maximum: 360,
    example: 0
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(360)
  min_raan?: number;

  @ApiProperty({ 
    description: '升交点赤经最大值（单位：度，范围：0-360，用于范围查询）',
    required: false,
    minimum: 0,
    maximum: 360,
    example: 180
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(360)
  max_raan?: number;

  @ApiProperty({ 
    description: '偏心率最小值（范围：0-1）',
    required: false,
    minimum: 0,
    maximum: 1,
    example: 0.001
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(1)
  min_eccentricity?: number;

  @ApiProperty({ 
    description: '偏心率最大值（范围：0-1）',
    required: false,
    minimum: 0,
    maximum: 1,
    example: 0.01
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(1)
  max_eccentricity?: number;

  @ApiProperty({ 
    description: '近地点幅角最小值（单位：度，范围：0-360）',
    required: false,
    minimum: 0,
    maximum: 360,
    example: 0
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(360)
  min_arg_perigee?: number;

  @ApiProperty({ 
    description: '近地点幅角最大值（单位：度，范围：0-360）',
    required: false,
    minimum: 0,
    maximum: 360,
    example: 180
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(360)
  max_arg_perigee?: number;

  @ApiProperty({ 
    description: '平近点角最小值（单位：度，范围：0-360）',
    required: false,
    minimum: 0,
    maximum: 360,
    example: 0
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(360)
  min_mean_anomaly?: number;

  @ApiProperty({ 
    description: '平近点角最大值（单位：度，范围：0-360）',
    required: false,
    minimum: 0,
    maximum: 360,
    example: 180
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(360)
  max_mean_anomaly?: number;

  @ApiProperty({ 
    description: '每天圈数最小值',
    required: false,
    minimum: 0,
    example: 12
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  min_revolutions_per_day?: number;

  @ApiProperty({ 
    description: '每天圈数最大值',
    required: false,
    minimum: 0,
    example: 16
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  max_revolutions_per_day?: number;

  @ApiProperty({ 
    description: '轨道周期最小值（单位：分钟）',
    required: false,
    minimum: 0,
    example: 90
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  min_orbital_period?: number;

  @ApiProperty({ 
    description: '轨道周期最大值（单位：分钟）',
    required: false,
    minimum: 0,
    example: 120
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  max_orbital_period?: number;

  @ApiProperty({ 
    description: '半长轴最小值（单位：千米）',
    required: false,
    minimum: 0,
    example: 6500
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  min_semi_major_axis?: number;

  @ApiProperty({ 
    description: '半长轴最大值（单位：千米）',
    required: false,
    minimum: 0,
    example: 7500
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  max_semi_major_axis?: number;
} 