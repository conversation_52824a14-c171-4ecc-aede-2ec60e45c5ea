import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsDateString, IsInt, Min, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * 发射状态枚举
 */
export enum LaunchStatus {
  UPCOMING = '即将发射',
  IN_FLIGHT = '发射中',
  SUCCESS = '发射成功',
  FAILURE = '发射失败',
  HISTORY = '历史发射'
}

/**
 * 排序方向枚举
 */
export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc'
}

/**
 * 发射信息查询 DTO
 */
export class LaunchQueryDto {
  @ApiProperty({
    description: '页码',
    required: false,
    default: 1,
    minimum: 1
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({
    description: '每页数量',
    required: false,
    default: 10,
    minimum: 1
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  limit?: number = 10;

  @ApiProperty({
    description: '发射时间开始（北京时间，格式：YYYY-MM-DD）',
    required: false,
    type: String,
    example: '2023-01-01'
  })
  @IsOptional()
  @IsDateString()
  launchDateStart?: string;

  @ApiProperty({
    description: '发射时间结束（北京时间，格式：YYYY-MM-DD）',
    required: false,
    type: String,
    example: '2023-12-31'
  })
  @IsOptional()
  @IsDateString()
  launchDateEnd?: string;

  @ApiProperty({
    description: '火箭型号',
    required: false,
    type: String,
    example: 'Falcon 9'
  })
  @IsOptional()
  @IsString()
  rocketName?: string;

  @ApiProperty({
    description: '发射场',
    required: false,
    type: String,
    example: 'Kennedy Space Center'
  })
  @IsOptional()
  @IsString()
  siteName?: string;

  @ApiProperty({
    description: '任务状态',
    required: false,
    enum: LaunchStatus,
    example: LaunchStatus.SUCCESS
  })
  @IsOptional()
  @IsEnum(LaunchStatus)
  status?: LaunchStatus;

  @ApiProperty({
    description: '服务商',
    required: false,
    type: String,
    example: 'SpaceX'
  })
  @IsOptional()
  @IsString()
  provider?: string;

  @ApiProperty({
    description: '排序方向（按发射时间排序）',
    required: false,
    enum: SortDirection,
    default: SortDirection.DESC,
    example: SortDirection.DESC
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;
}
