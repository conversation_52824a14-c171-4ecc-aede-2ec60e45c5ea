import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsInt, IsBoolean, IsArray, Min, IsString, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * 大模型模式枚举
 */
export enum LLMMode {
  DEFAULT = 'default',
  HIGH_QUALITY = 'high_quality',
  FAST = 'fast'
}

/**
 * 翻译新闻请求DTO
 * 用于翻译API的请求参数
 */
export class TranslateNewsDto {
  @ApiProperty({
    description: '每批处理的文档数量，建议根据文档大小适当设置，过大可能导致请求超时，过小会增加调用次数',
    default: 10,
    required: false,
    minimum: 1,
    maximum: 50,
    example: 20,
    type: Number
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  batchSize?: number;

  @ApiProperty({
    description: '最大处理文档数，0表示不限制处理数量，可用于限制单次API调用处理的总文档数量，控制执行时间',
    default: 0,
    required: false,
    minimum: 0,
    example: 100,
    type: Number
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  maxDocs?: number;

  @ApiProperty({
    description: '是否强制重新翻译已翻译的内容，默认false只翻译未翻译内容，设为true会重新翻译所有内容（包括已有翻译的）',
    default: false,
    required: false,
    example: false,
    type: Boolean
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  forceRetranslate?: boolean;

  @ApiProperty({
    description: '是否强制重新爬取内容，默认false只爬取未爬取过的新闻，设为true会重新爬取所有有info_source的新闻内容（包括之前爬取失败的）',
    default: false,
    required: false,
    example: false,
    type: Boolean
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  forceRefetchContent?: boolean;

  @ApiProperty({
    description: '指定要处理的索引名称数组，支持精确指定索引名称或使用通配符匹配，不指定则处理所有以news_开头的索引',
    required: false,
    isArray: true,  // 明确指定这是一个数组
    type: [String],
    example: ['news_2023_04', 'news_2023_05']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  specificIndexes?: string[];

  @ApiProperty({
    description: '大模型模式选择：default（默认，qwen-turbo）、high_quality（高质量，qwen-max-latest）、fast（快速，qwen-turbo，更高并发）',
    required: false,
    enum: LLMMode,
    default: LLMMode.DEFAULT,
    example: LLMMode.DEFAULT
  })
  @IsOptional()
  @IsEnum(LLMMode)
  llmMode?: LLMMode;

  @ApiProperty({
    description: '自定义模型名称，如果指定则覆盖llmMode设置，支持qwen-turbo、qwen-max-latest等',
    required: false,
    type: String,
    example: 'qwen-turbo'
  })
  @IsOptional()
  @IsString()
  customModel?: string;

  @ApiProperty({
    description: '是否自动提取主题词和类别，默认true。设为true时翻译完成后会自动提取主题词并识别内容类型（科普/军事），设为false时只进行翻译',
    default: true,
    required: false,
    example: true,
    type: Boolean
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  autoExtractThemes?: boolean;

  @ApiProperty({
    description: '指定要翻译的特定文档ID。如果指定此参数，则只翻译该ID对应的文档，忽略其他批量处理参数。需要配合specificIndexes参数指定文档所在的索引',
    required: false,
    type: String,
    example: 'bbfa7e8c6c4ff26acf0e27f7fff9ba97'
  })
  @IsOptional()
  @IsString()
  documentId?: string;
}

/**
 * 主题提取请求DTO
 * 用于主题提取API的请求参数
 * 支持同时进行主题词提取和内容类型识别（科普/军事）
 * 支持处理中文和英文文档，对于英文文档会直接提取中文主题词
 * 注意：内容类型识别只会选择其中一个最符合的类别，不会同时标记为两种类型
 */
export class ExtractThemesDto {
  @ApiProperty({
    description: '每批处理的文档数量，建议根据文档大小适当设置，过大可能导致请求超时，过小会增加调用次数',
    default: 10,
    required: false,
    minimum: 1,
    maximum: 50,
    example: 20,
    type: Number
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  batchSize?: number;

  @ApiProperty({
    description: '最大处理文档数，0表示不限制处理数量，可用于限制单次API调用处理的总文档数量，控制执行时间',
    default: 0,
    required: false,
    minimum: 0,
    example: 100,
    type: Number
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  maxDocs?: number;

  @ApiProperty({
    description: '是否强制重新提取已有主题词的文档，默认false只提取没有主题词的文档（包括中文和英文文档），设为true会重新提取所有文档的主题词和内容类型',
    default: false,
    required: false,
    example: false,
    type: Boolean
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  forceReextract?: boolean;

  @ApiProperty({
    description: '指定要处理的索引名称数组，支持精确指定索引名称或使用通配符匹配，不指定则处理所有以news_开头的索引',
    required: false,
    isArray: true,  // 明确指定这是一个数组
    type: [String],
    example: ['news_2023_04', 'news_2023_05']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  specificIndexes?: string[];

  @ApiProperty({
    description: '大模型模式选择：default（默认，qwen-turbo）、high_quality（高质量，qwen-max-latest）、fast（快速，qwen-turbo，更高并发）',
    required: false,
    enum: LLMMode,
    default: LLMMode.DEFAULT,
    example: LLMMode.DEFAULT
  })
  @IsOptional()
  @IsEnum(LLMMode)
  llmMode?: LLMMode;

  @ApiProperty({
    description: '自定义模型名称，如果指定则覆盖llmMode设置，支持qwen-turbo、qwen-max-latest等',
    required: false,
    type: String,
    example: 'qwen-turbo'
  })
  @IsOptional()
  @IsString()
  customModel?: string;
}

/**
 * 热门主题请求DTO
 * 用于热门主题API的请求参数
 */
export class HotThemesDto {
  @ApiProperty({
    description: '返回前N个热门主题词',
    default: 10,
    required: false,
    minimum: 1,
    maximum: 100,
    example: 10,
    type: Number
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  topN?: number;

  @ApiProperty({
    description: '主题词最小出现次数，用于过滤出现频率过低的主题词',
    default: 1,
    required: false,
    minimum: 1,
    example: 2,
    type: Number
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  minCount?: number;

  @ApiProperty({
    description: '指定要处理的索引名称数组，支持精确指定索引名称或使用通配符匹配，不指定则处理所有以news_开头的索引',
    required: false,
    isArray: true,
    type: [String],
    example: ['news_2023_04', 'news_2023_05']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  specificIndexes?: string[];
}