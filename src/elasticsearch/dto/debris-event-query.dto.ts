import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsDateString, Matches } from 'class-validator';
import { Type } from 'class-transformer';

export class DebrisEventQueryDto {
  @ApiProperty({
    description: '事件ID',
    example: 'EVENT123',
    required: false
  })
  @IsString()
  @IsOptional()
  _id?: string;

  @ApiProperty({
    description: '碎片标识 (格式: YYYY-XXX[A-Z])',
    example: '1964-006A',
    required: false
  })
  @IsString()
  @IsOptional()
  @Matches(/^\d{4}-\d{3}[A-Z]*$/, {
    message: '碎片标识格式必须为YYYY-XXX[A-Z]'
  })
  piece?: string;

  @ApiProperty({
    description: '事件开始时间',
    example: '2024-01-01T00:00:00Z',
    required: false
  })
  @IsDateString()
  @IsOptional()
  start_date?: string;

  @ApiProperty({
    description: '事件结束时间',
    example: '2024-12-31T23:59:59Z',
    required: false
  })
  @IsDateString()
  @IsOptional()
  end_date?: string;

  @ApiProperty({
    description: '关键词搜索',
    example: 'collision',
    required: false
  })
  @IsString()
  @IsOptional()
  keyword?: string;
} 