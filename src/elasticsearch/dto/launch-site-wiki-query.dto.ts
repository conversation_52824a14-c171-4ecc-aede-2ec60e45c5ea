import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';

/**
 * 发射场wiki信息查询DTO
 */
export class LaunchSiteWikiQueryDto {
  @ApiProperty({
    description: '发射场名称（支持代码、英文名、中文名、别名）',
    required: false,
    type: String,
    example: 'Kennedy Space Center'
  })
  @IsOptional()
  @IsString()
  siteName?: string;

  @ApiProperty({
    description: 'ES文档ID（精确查询特定发射场记录）',
    required: false,
    type: String,
    example: 'abc123def456'
  })
  @IsOptional()
  @IsString()
  _id?: string;
} 