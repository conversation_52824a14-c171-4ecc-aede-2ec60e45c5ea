import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNumber, IsOptional, Min, Max, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * 批量查询卫星norad_id获取TLE数据的DTO
 */
export class BulkNoradIdsQueryDto {
  @ApiProperty({ 
    description: '卫星NORAD ID数组（最多支持100个ID）。如果为空数组，则返回所有卫星的TLE数据',
    required: false,
    type: [Number],
    example: [25544, 43552, 37849],
    default: []
  })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  norad_ids?: number[] = [];

  @ApiProperty({
    description: '页码，从1开始。仅在norad_ids为空时生效',
    required: false,
    minimum: 1,
    default: 1,
    example: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({
    description: '每页记录数，最大10000。仅在norad_ids为空时生效',
    required: false,
    minimum: 1,
    maximum: 10000,
    default: 1000,
    example: 1000
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10000)
  @Type(() => Number)
  limit?: number = 1000;

  @ApiProperty({
    description: '是否使用一次性查询模式（秒级返回全部数据）。仅在norad_ids为空时生效。true=一次性获取所有数据，false=分页查询',
    required: false,
    default: false,
    example: false
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  useOneTimeQuery?: boolean = false;
} 