import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsInt, Min } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * 火箭信息查询 DTO
 */
export class RocketQueryDto {
  @ApiProperty({
    description: '页码',
    required: false,
    default: 1,
    minimum: 1
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({
    description: '每页数量',
    required: false,
    default: 10,
    minimum: 1
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  limit?: number = 10;

  @ApiProperty({
    description: '火箭型号（不区分大小写，匹配rocket_name或lv_variant字段）',
    required: false,
    type: String,
    example: 'Falcon 9'
  })
  @IsOptional()
  @IsString()
  rocketName?: string;
}
