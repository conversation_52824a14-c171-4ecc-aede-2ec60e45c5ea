import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

/**
 * 漏洞查询 DTO
 */
export class LoopholeQueryDto {
  @ApiProperty({
    description: '漏洞ID（CVE编号），精确匹配',
    required: false,
    example: 'CVE-2024-1234',
  })
  @IsOptional()
  @IsString()
  _id?: string;

  @ApiProperty({
    description: '关键词搜索（可匹配漏洞描述、受影响组件、CVE编号等所有字段）',
    required: false,
    example: 'Cobham Sailor',
  })
  @IsOptional()
  @IsString()
  keywords?: string;
} 