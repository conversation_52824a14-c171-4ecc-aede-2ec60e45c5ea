import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * 碎片信息关联事件查询DTO
 */
export class DebrisToEventQueryDto {
  @ApiProperty({
    description: 'COSPAR ID (格式: YYYY-XXX[A-Z])',
    example: '1999-025EZ'
  })
  @IsString()
  @Matches(/^\d{4}-\d{3}[A-Z]*$/, {
    message: 'COSPAR ID格式必须为YYYY-XXX[A-Z]'
  })
  cospar_id: string;

  @ApiProperty({
    description: '发射日期',
    example: '1999-05-20',
    required: false
  })
  @IsString()
  @IsOptional()
  launch_date?: string;

  @ApiProperty({
    description: '首次纪元时间',
    example: '1999-05-20T00:00:00Z',
    required: false
  })
  @IsString()
  @IsOptional()
  first_epoch?: string;

  @ApiProperty({
    description: '碎片名称',
    example: 'FENGYUN 1C DEB',
    required: false
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: '最小关联置信度 (0-1)',
    example: 0.5,
    minimum: 0,
    maximum: 1
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  min_confidence: number;
}

/**
 * 事件关联碎片查询DTO
 */
export class EventToDebrisQueryDto {
  @ApiProperty({
    description: '碎片标识 (格式: YYYY-XXX[A-Z])',
    example: '1964-006A'
  })
  @IsString()
  @Matches(/^\d{4}-\d{3}[A-Z]*$/, {
    message: '碎片标识格式必须为YYYY-XXX[A-Z]'
  })
  piece: string;

  @ApiProperty({
    description: '事件日期',
    example: '1964-01-01T00:00:00Z'
  })
  @IsString()
  sdate: string;

  @ApiProperty({
    description: '事件类型',
    example: 'Collision'
  })
  @IsString()
  event_type: string;

  @ApiProperty({
    description: '编目碎片数量',
    example: 10
  })
  @IsNumber()
  @Min(0)
  cataloguedFragments: number;

  @ApiProperty({
    description: '最小关联置信度 (0-1)',
    example: 0.5,
    minimum: 0,
    maximum: 1
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  min_confidence: number;
} 