import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsInt, IsString, IsObject, IsNumber, Min } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * 匹配类型枚举
 */
export enum MatchType {
  EXACT = 'exact',
  FUZZY = 'fuzzy'
}

/**
 * 基础字段匹配配置
 */
export class BaseFieldMatchConfig {
  @ApiProperty({
    description: '匹配类型',
    enum: MatchType,
    example: 'exact'
  })
  @IsEnum(MatchType)
  @IsNotEmpty()
  matchType!: MatchType;

  @ApiProperty({
    description: '字段值',
    example: 'HELIOS 2A',
    oneOf: [
      { type: 'string' },
      { type: 'number' }
    ]
  })
  @IsNotEmpty()
  value!: string | number;
}

/**
 * 带相似度阈值的字段匹配配置（用于卫星查询）
 */
export class SatelliteFieldMatchConfig extends BaseFieldMatchConfig {
  @ApiProperty({
    description: '相似度阈值（0-1之间），只返回相似度高于该阈值的结果',
    required: false,
    default: 0.6,
    minimum: 0,
    maximum: 1,
    type: Number
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  similarity_threshold?: number = 0.6;
}

/**
 * 简单字段匹配配置（用于碎片查询）
 */
export class DebrisFieldMatchConfig extends BaseFieldMatchConfig {
  // 继承基础配置，不添加额外字段
}

/**
 * 排序配置
 */
export class SortConfig {
  @ApiProperty({
    description: '排序字段',
    example: 'update_time'
  })
  @IsNotEmpty()
  field!: string;

  @ApiProperty({
    description: '排序方向',
    enum: ['asc', 'desc'],
    example: 'desc'
  })
  @IsEnum(['asc', 'desc'])
  order!: 'asc' | 'desc';
}

export class BaseQueryDto {
  @ApiProperty({
    description: '页码',
    default: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: '每页数量',
    default: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number = 10;

  @ApiProperty({
    description: '排序字段',
    required: false,
    example: { "news_id": 'desc' },
    default: { "publish_date.year": 'desc', "publish_date.month": 'desc', "publish_date.day": 'desc' }
  })
  @IsOptional()
  @IsObject()
  sort?: Record<string, 'asc' | 'desc'>;

  @ApiProperty({
    description: '搜索关键词',
    required: false,
  })
  @IsOptional()
  @IsString()
  keyword?: string;
}