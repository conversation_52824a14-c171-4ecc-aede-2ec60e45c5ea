import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsEnum, ValidateNested, IsInt, Min, IsNotEmpty, IsDateString } from 'class-validator';
import { Type } from 'class-transformer';
import { MatchType, SatelliteFieldMatchConfig, SortConfig } from './base-query.dto';

// 重新导出共享类型供其他模块使用
export { MatchType, SatelliteFieldMatchConfig, SortConfig };

export enum OrbitType {
  LEO = 'LEO',
  MEO = 'MEO',
  GEO = 'GEO'
}

export class SatelliteQueryDto {
  @ApiProperty({
    description: '页码',
    required: false,
    default: 1,
    minimum: 1
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({
    description: '每页数量',
    required: false,
    default: 10,
    minimum: 1
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  limit?: number = 10;

  @ApiProperty({
    description: '关键词搜索',
    required: false,
    example: 'ISS',
    type: String
  })
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiProperty({
    description: '相似度阈值（0-1之间），只返回相似度高于该阈值的结果',
    required: false,
    default: 0.6,
    minimum: 0,
    maximum: 1,
    type: Number
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  similarity_threshold?: number = 0.6;

  @ApiProperty({
    description: '排序配置',
    required: false,
    type: SortConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SortConfig)
  sort?: SortConfig;

  @ApiProperty({
    description: '卫星名称查询配置',
    required: false,
    type: SatelliteFieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SatelliteFieldMatchConfig)
  satellite_name?: SatelliteFieldMatchConfig;

  @ApiProperty({
    description: '卫星别名查询配置',
    required: false,
    type: SatelliteFieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SatelliteFieldMatchConfig)
  alternative_name?: SatelliteFieldMatchConfig;

  @ApiProperty({
    description: 'COSPAR ID查询配置',
    required: false,
    type: SatelliteFieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SatelliteFieldMatchConfig)
  cospar_id?: SatelliteFieldMatchConfig;

  @ApiProperty({
    description: '注册国家查询配置',
    required: false,
    type: SatelliteFieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SatelliteFieldMatchConfig)
  country_of_registry?: SatelliteFieldMatchConfig;

  @ApiProperty({
    description: '所有者查询配置',
    required: false,
    type: SatelliteFieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SatelliteFieldMatchConfig)
  owner?: SatelliteFieldMatchConfig;

  @ApiProperty({
    description: '状态查询配置',
    required: false,
    type: SatelliteFieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SatelliteFieldMatchConfig)
  status?: SatelliteFieldMatchConfig;

  @ApiProperty({
    description: 'NORAD ID查询配置',
    required: false,
    type: SatelliteFieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SatelliteFieldMatchConfig)
  norad_id?: SatelliteFieldMatchConfig;

  @ApiProperty({
    description: '发射日期查询配置',
    required: false,
    type: SatelliteFieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SatelliteFieldMatchConfig)
  launch_date?: SatelliteFieldMatchConfig;

  @ApiProperty({
    description: '部署日期查询配置',
    required: false,
    type: SatelliteFieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SatelliteFieldMatchConfig)
  deployed_date?: SatelliteFieldMatchConfig;

  @ApiProperty({
    description: '轨道类型',
    required: false,
    enum: OrbitType,
    example: 'LEO'
  })
  @IsOptional()
  @IsEnum(OrbitType)
  orbit_type?: OrbitType;

  @ApiProperty({
    description: '最小轨道高度（km）',
    required: false,
    type: Number,
    example: 400
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  min_altitude?: number;

  @ApiProperty({
    description: '最大轨道高度（km）',
    required: false,
    type: Number,
    example: 1000
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  max_altitude?: number;

  @ApiProperty({
    description: '最小倾角（度）',
    required: false,
    type: Number,
    example: 45
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  min_inclination?: number;

  @ApiProperty({
    description: '最大倾角（度）',
    required: false,
    type: Number,
    example: 90
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  max_inclination?: number;

  @ApiProperty({
    description: '最小轨道周期（分钟）',
    required: false,
    type: Number,
    example: 90
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  min_period?: number;

  @ApiProperty({
    description: '最大轨道周期（分钟）',
    required: false,
    type: Number,
    example: 120
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  max_period?: number;

  @ApiProperty({
    description: '最小远地点高度（km）',
    required: false,
    type: Number,
    example: 400
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  min_apogee?: number;

  @ApiProperty({
    description: '最大远地点高度（km）',
    required: false,
    type: Number,
    example: 1000
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  max_apogee?: number;

  @ApiProperty({
    description: '最小近地点高度（km）',
    required: false,
    type: Number,
    example: 400
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  min_perigee?: number;

  @ApiProperty({
    description: '最大近地点高度（km）',
    required: false,
    type: Number,
    example: 1000
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  max_perigee?: number;

  @ApiProperty({
    description: '发射日期开始',
    required: false,
    type: String,
    example: '2020-01-01'
  })
  @IsOptional()
  @IsDateString()
  launch_date_start?: string;

  @ApiProperty({
    description: '发射日期结束',
    required: false,
    type: String,
    example: '2023-12-31'
  })
  @IsOptional()
  @IsDateString()
  launch_date_end?: string;

  @ApiProperty({
    description: '发射场',
    required: false,
    type: SatelliteFieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SatelliteFieldMatchConfig)
  launch_site?: SatelliteFieldMatchConfig;

  @ApiProperty({
    description: '发射火箭',
    required: false,
    type: SatelliteFieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SatelliteFieldMatchConfig)
  launch_vehicle?: SatelliteFieldMatchConfig;
} 