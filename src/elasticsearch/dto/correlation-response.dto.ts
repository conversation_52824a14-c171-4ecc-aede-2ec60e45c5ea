import { ApiProperty } from '@nestjs/swagger';
import { EventDocument, DebrisDocument } from '../types/elasticsearch.types';

export class MatchScores {
  @ApiProperty({
    description: 'COSPAR ID匹配分数',
    example: 0.8
  })
  cosparMatch: number;

  @ApiProperty({
    description: '时间匹配分数',
    example: 0.6
  })
  timeScore: number;

  @ApiProperty({
    description: '名称相似度分数',
    example: 0.7
  })
  nameScore: number;
}

export class EventDetails {
  @ApiProperty({
    description: '碎片标识',
    example: '1999-025A'
  })
  piece: string;

  @ApiProperty({
    description: '事件时间',
    example: '2007-01-11T22:26:00Z'
  })
  sdate: string;

  @ApiProperty({
    description: '事件名称',
    example: 'Fengyun 1C',
    required: false
  })
  name?: string;

  @ApiProperty({
    description: '事件类型',
    example: 'Collision'
  })
  event_type: string;

  @ApiProperty({
    description: '编目碎片数量',
    example: 3428
  })
  cataloguedFragments: number;

  @ApiProperty({
    description: '对象类型',
    example: 'Payload',
    required: false
  })
  object_class?: string;

  @ApiProperty({
    description: '数据来源',
    example: 'discos',
    enum: ['discos', 'jonathan']
  })
  source: 'discos' | 'jonathan';
}

export class CorrelationResultDto {
  @ApiProperty({
    description: '事件ID',
    example: '383'
  })
  event_id: string;

  @ApiProperty({
    description: '关联置信度',
    example: 0.85
  })
  confidence: number;

  @ApiProperty({
    description: '各维度匹配分数',
    type: MatchScores
  })
  scores: MatchScores;

  @ApiProperty({
    description: '事件详情',
    type: EventDetails
  })
  event_details: EventDetails;
}

export class CorrelationResponseDto {
  @ApiProperty({
    description: '总命中数',
    example: 2
  })
  total: number;

  @ApiProperty({
    description: '事件列表',
    type: [CorrelationResultDto]
  })
  hits: CorrelationResultDto[];
}

export class DebrisCorrelationResult {
  @ApiProperty({
    description: '碎片ID',
    example: '1999-025EZ'
  })
  debris_id: string;

  @ApiProperty({
    description: '关联置信度',
    example: 0.85
  })
  confidence: number;

  @ApiProperty({
    description: '各维度匹配分数',
    type: MatchScores
  })
  scores: MatchScores;

  @ApiProperty({
    description: '碎片详情',
    example: {
      cospar_id: '1999-025EZ',
      launch_date: '1999-05-20',
      first_epoch: '1999-05-20T00:00:00Z',
      name: 'FENGYUN 1C DEB',
      object_class: 'DEBRIS'
    }
  })
  debris_details: DebrisDocument;
}

export class EventCorrelationResultDto {
  @ApiProperty({
    description: '关联碎片列表',
    type: [DebrisCorrelationResult]
  })
  debris_list: DebrisCorrelationResult[];
} 