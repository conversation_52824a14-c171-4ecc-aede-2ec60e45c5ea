import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsArray, IsString, IsDateString, IsInt, Min, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { BaseQueryDto } from './base-query.dto';

/**
 * 关键词匹配类型枚举
 */
export enum KeywordMatchType {
  SUBSTRING = 'substring',   // 子串匹配
  SIMILARITY = 'similarity'  // 相似性匹配
}

/**
 * 新闻列表查询DTO
 * 用于新闻列表API的请求参数
 */
export class NewsListQueryDto extends BaseQueryDto {
  @ApiProperty({
    description: '关键词数组，用于在新闻的所有字段中进行子串匹配搜索，包括：标题、摘要、内容、来源、作者、链接、主题词、发布时间等所有文本字段',
    required: false,
    isArray: true,
    type: [String],
    example: ['satellite', 'launch']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  keywords?: string[];

  @ApiProperty({
    description: '关键词匹配方式，可选值：substring(子串匹配)、similarity(相似性匹配)，默认为substring',
    required: false,
    enum: KeywordMatchType,
    default: KeywordMatchType.SUBSTRING
  })
  @IsOptional()
  @IsEnum(KeywordMatchType)
  keywordMatchType?: KeywordMatchType = KeywordMatchType.SUBSTRING;

  @ApiProperty({
    description: '主题词数组，用于筛选包含特定主题的新闻',
    required: false,
    isArray: true,
    type: [String],
    example: ['航天', '卫星']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  themes?: string[];

  @ApiProperty({
    description: '发布日期开始时间',
    required: false,
    type: String,
    example: '2023-01-01'
  })
  @IsOptional()
  @IsDateString()
  publishDateStart?: string;

  @ApiProperty({
    description: '发布日期结束时间',
    required: false,
    type: String,
    example: '2023-12-31'
  })
  @IsOptional()
  @IsDateString()
  publishDateEnd?: string;

  @ApiProperty({
    description: '索引名称模式数组，支持使用通配符匹配，例如：["*defence*", "*defense*"]可匹配包含defence或defense的索引',
    required: false,
    isArray: true,
    type: [String],
    example: ['news_2023_*', '*defence*']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  indexPatterns?: string[];

  @ApiProperty({
    description: '是否包含未翻译的新闻，默认为true',
    required: false,
    type: Boolean,
    default: true
  })
  @IsOptional()
  @Type(() => Boolean)
  includeUntranslated?: boolean = true;
}
