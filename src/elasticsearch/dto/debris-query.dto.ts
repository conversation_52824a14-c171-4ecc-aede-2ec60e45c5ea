import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsEnum, ValidateNested, IsInt, Min, IsObject, IsNotEmpty } from 'class-validator';
import { Type } from 'class-transformer';
import { BaseQueryDto } from './base-query.dto';

import { MatchType, DebrisFieldMatchConfig } from "./base-query.dto";

// 重新导出共享类型供其他模块使用
export { MatchType, DebrisFieldMatchConfig };

export class DebrisQueryDto {
  @ApiProperty({
    description: '页码',
    required: false,
    default: 1,
    minimum: 1
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({
    description: '每页数量',
    required: false,
    default: 10,
    minimum: 1
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  limit?: number = 10;

  @ApiProperty({
    description: '排序配置',
    required: false,
    example: { field: 'norad_id', order: 'desc' }
  })
  @IsOptional()
  @IsObject()
  sort?: { field: string; order: 'asc' | 'desc' };

  @ApiProperty({
    description: '关键词搜索',
    required: false,
    example: 'International Space Station'
  })
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiProperty({
    description: 'COSPAR ID查询配置',
    required: false,
    type: DebrisFieldMatchConfig,
    example: {
      matchType: 'exact',
      value: '1998-067A'
    }
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DebrisFieldMatchConfig)
  cospar_id?: DebrisFieldMatchConfig;

  @ApiProperty({
    description: 'NORAD ID查询配置',
    required: false,
    type: DebrisFieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DebrisFieldMatchConfig)
  norad_id?: DebrisFieldMatchConfig;

  @ApiProperty({
    description: '碎片名称',
    required: false,
    example: 'ISS'
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: '所属国家查询配置',
    required: false,
    type: DebrisFieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DebrisFieldMatchConfig)
  country?: DebrisFieldMatchConfig;

  @ApiProperty({
    description: '发射日期查询配置',
    required: false,
    type: DebrisFieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DebrisFieldMatchConfig)
  launch_date?: DebrisFieldMatchConfig;

  @ApiProperty({
    description: '发射场查询配置',
    required: false,
    type: DebrisFieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DebrisFieldMatchConfig)
  launch_site?: DebrisFieldMatchConfig;

  @ApiProperty({
    description: '雷达散射截面大小查询配置',
    required: false,
    type: DebrisFieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DebrisFieldMatchConfig)
  rcs_size?: DebrisFieldMatchConfig;

  @ApiProperty({
    description: '坠毁日期查询配置',
    required: false,
    type: DebrisFieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DebrisFieldMatchConfig)
  decay?: DebrisFieldMatchConfig;

  @ApiProperty({
    description: '首次观测时间查询配置',
    required: false,
    type: DebrisFieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DebrisFieldMatchConfig)
  first_epoch?: DebrisFieldMatchConfig;

  @ApiProperty({
    description: '对象类型查询配置',
    required: false,
    type: DebrisFieldMatchConfig
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DebrisFieldMatchConfig)
  object_class?: DebrisFieldMatchConfig;

  @ApiProperty({
    description: '任务名称',
    required: false,
    example: 'International Space Station'
  })
  @IsOptional()
  @IsString()
  mission?: string;

  @ApiProperty({
    description: '轨道周期范围（分钟）',
    required: false,
    example: { min: 90, max: 120 }
  })
  @IsOptional()
  @IsObject()
  period_minutes_range?: { min?: number; max?: number };

  @ApiProperty({
    description: '轨道倾角范围（度）',
    required: false,
    example: { min: 45, max: 60 }
  })
  @IsOptional()
  @IsObject()
  incl_degrees_range?: { min?: number; max?: number };

  @ApiProperty({
    description: '远地点高度范围（km）',
    required: false,
    example: { min: 500, max: 800 }
  })
  @IsOptional()
  @IsObject()
  apogee_km_range?: { min?: number; max?: number };

  @ApiProperty({
    description: '近地点高度范围（km）',
    required: false,
    example: { min: 400, max: 600 }
  })
  @IsOptional()
  @IsObject()
  perigee_km_range?: { min?: number; max?: number };
} 