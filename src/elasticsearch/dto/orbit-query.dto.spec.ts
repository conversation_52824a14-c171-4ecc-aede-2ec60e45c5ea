import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { OrbitQueryDto } from './orbit-query.dto';

describe('OrbitQueryDto', () => {
  it('should pass validation with valid data', async () => {
    const dto = plainToInstance(OrbitQueryDto, {
      page: 1,
      limit: 10,
      satelliteId: 'SAT123',
      minAltitude: 500,
      maxAltitude: 1000,
      startTime: '2024-01-01T00:00:00Z',
      endTime: '2024-12-31T23:59:59Z',
      sort: { altitude: 'desc' },
      keyword: 'test'
    });

    const errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('should pass validation with empty data', async () => {
    const dto = plainToInstance(OrbitQueryDto, {});
    const errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('should fail validation with invalid page number', async () => {
    const dto = plainToInstance(OrbitQueryDto, {
      page: -1
    });

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('min');
  });

  it('should fail validation with invalid date format', async () => {
    const dto = plainToInstance(OrbitQueryDto, {
      startTime: 'invalid-date'
    });

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isDateString');
  });

  it('should fail validation with invalid altitude values', async () => {
    const dto = plainToInstance(OrbitQueryDto, {
      minAltitude: 'invalid',
      maxAltitude: 'invalid'
    });

    const errors = await validate(dto);
    expect(errors.length).toBe(2);
    expect(errors[0].constraints).toHaveProperty('isNumber');
    expect(errors[1].constraints).toHaveProperty('isNumber');
  });
}); 