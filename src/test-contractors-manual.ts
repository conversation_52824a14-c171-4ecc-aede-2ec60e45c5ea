import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DatabaseService } from './services/database.service';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 手动测试发射承包商翻译处理逻辑
 */
async function bootstrap() {
  try {
    // 创建NestJS应用实例
    const app = await NestFactory.create(AppModule);
    
    // 获取DatabaseService实例
    const databaseService = app.get(DatabaseService);
    
    console.log('正在测试发射承包商翻译处理逻辑...');
    
    // 创建一个测试函数，直接访问内部实现
    const testFunction = async () => {
      // 测试数据
      const testContractors = [
        'Zhejiang University',
        'Zhongke Xingrui',
        'Zhuhai Orbita Aerospace Science and Technology Co.',
        'Zhuhai Orbita Control Engineering Co. Ltd.',
        'Beijing Institute of Satellite Information Engineering',
        'Beijing Aerospace Propulsion Institute',
        'Beijing SPACE VIEW Technology Co., Ltd',
        'Beijing Smart Satellite Technology Co. Ltd.',
        'Shanghai Academy of Spaceflight Technology',
        'Harbin Institute of Technology',
        'Wuhan University',
        'Tsinghua University',
        'China Academy of Sciences'
      ];
      
      // 访问DatabaseService的内部方法，简化版实现
      const processContractor = (contractor: string) => {
        // 已知的中英文对照表
        const knownTranslations: { [key: string]: { cn: string; en: string } } = {
          'Zhejiang University': { cn: '浙江大学', en: 'Zhejiang University' },
          'Zhongke Xingrui': { cn: '中科星睿', en: 'Zhongke Xingrui' },
          'Zhuhai Orbita Aerospace Science and Technology Co.': { cn: '珠海欧比特宇航科技股份有限公司', en: 'Zhuhai Orbita Aerospace Science and Technology Co.' },
          'Zhuhai Orbita Control Engineering Co. Ltd.': { cn: '珠海欧比特控制工程有限公司', en: 'Zhuhai Orbita Control Engineering Co. Ltd.' },
          'Beijing Institute of Satellite Information Engineering': { cn: '北京卫星信息工程研究所', en: 'Beijing Institute of Satellite Information Engineering' },
          'Beijing Aerospace Propulsion Institute': { cn: '北京航天动力研究所', en: 'Beijing Aerospace Propulsion Institute' },
          'Beijing SPACE VIEW Technology Co., Ltd': { cn: '北京天目创新科技有限公司', en: 'Beijing SPACE VIEW Technology Co., Ltd' },
          'Beijing Smart Satellite Technology Co. Ltd.': { cn: '北京智星卫星科技有限公司', en: 'Beijing Smart Satellite Technology Co. Ltd.' },
          'Shanghai Academy of Spaceflight Technology': { cn: '上海航天技术研究院', en: 'Shanghai Academy of Spaceflight Technology' },
          'Harbin Institute of Technology': { cn: '哈尔滨工业大学', en: 'Harbin Institute of Technology' },
          'Wuhan University': { cn: '武汉大学', en: 'Wuhan University' },
          'Tsinghua University': { cn: '清华大学', en: 'Tsinghua University' },
          'China Academy of Sciences': { cn: '中国科学院', en: 'China Academy of Sciences' },
          'Academy of Military Science': { cn: '中国人民解放军军事科学院', en: 'Academy of Military Science' }
        };
        
        // 存储部分匹配的键和模式
        const partialMatches: {pattern: RegExp; translation: { cn: string; en: string }}[] = [
          { pattern: /Zhejiang University/i, translation: { cn: '浙江大学', en: 'Zhejiang University' } },
          { pattern: /Zhongke/i, translation: { cn: '中科', en: 'Zhongke' } },
          { pattern: /Zhuhai Orbita/i, translation: { cn: '珠海欧比特', en: 'Zhuhai Orbita' } },
          { pattern: /Wuhan University/i, translation: { cn: '武汉大学', en: 'Wuhan University' } },
          { pattern: /Shanghai Academy/i, translation: { cn: '上海航天技术研究院', en: 'Shanghai Academy' } },
          { pattern: /Harbin Institute/i, translation: { cn: '哈尔滨工业大学', en: 'Harbin Institute' } },
          { pattern: /Tsinghua University/i, translation: { cn: '清华大学', en: 'Tsinghua University' } },
          { pattern: /(Chinese|China) Academy of Sciences?/i, translation: { cn: '中国科学院', en: 'Chinese Academy of Sciences' } },
          { pattern: /Academy of Military Science/i, translation: { cn: '中国人民解放军军事科学院', en: 'Academy of Military Science' } }
        ];
        
        // 判断是否已有已知翻译
        if (knownTranslations[contractor]) {
          return knownTranslations[contractor];
        }
        
        // 部分匹配：处理中国承包商的各种名称变体
        for (const { pattern, translation } of partialMatches) {
          if (pattern.test(contractor)) {
            // 保留原始英文名称，但使用标准化的中文名称
            return { 
              cn: translation.cn, 
              en: contractor 
            };
          }
        }
        
        // 判断是否为英文
        const isEnglish = /[a-zA-Z]/.test(contractor);
        
        // 判断是否为中文
        const isChinese = /[\u4e00-\u9fa5]/.test(contractor);
        
        if (isEnglish && !isChinese) {
          // 纯英文，首先检查是否包含China或Chinese关键词
          if (/China|Chinese/i.test(contractor)) {
            return { 
              cn: `中国${contractor.replace(/China|Chinese/i, '').trim()}`, 
              en: contractor 
            };
          } 
          // 检查中国城市开头的承包商
          else if (/^Shanghai/i.test(contractor)) {
            return { 
              cn: `上海${contractor.replace(/Shanghai/i, '').trim()}`, 
              en: contractor 
            };
          }
          else if (/^Shenzhen/i.test(contractor)) {
            return { 
              cn: `深圳${contractor.replace(/Shenzhen/i, '').trim()}`, 
              en: contractor 
            };
          }
          else if (/^Zhuhai/i.test(contractor)) {
            return { 
              cn: `珠海${contractor.replace(/Zhuhai/i, '').trim()}`, 
              en: contractor 
            };
          }
          else if (/^Nanjing/i.test(contractor)) {
            return { 
              cn: `南京${contractor.replace(/Nanjing/i, '').trim()}`, 
              en: contractor 
            };
          }
          else if (/^Xi'an/i.test(contractor)) {
            return { 
              cn: `西安${contractor.replace(/Xi'an/i, '').trim()}`, 
              en: contractor 
            };
          }
          else if (/^Wuhan/i.test(contractor)) {
            return { 
              cn: `武汉${contractor.replace(/Wuhan/i, '').trim()}`, 
              en: contractor 
            };
          }
          else if (/^Tianjin/i.test(contractor)) {
            return { 
              cn: `天津${contractor.replace(/Tianjin/i, '').trim()}`, 
              en: contractor 
            };
          }
          else if (/^Harbin/i.test(contractor)) {
            return { 
              cn: `哈尔滨${contractor.replace(/Harbin/i, '').trim()}`, 
              en: contractor 
            };
          }
          else if (/^Hangzhou|Zhejiang/i.test(contractor)) {
            return { 
              cn: `浙江${contractor.replace(/Hangzhou|Zhejiang/i, '').trim()}`, 
              en: contractor 
            };
          }
          else if (/^Zhongke/i.test(contractor)) {
            return { 
              cn: `中科${contractor.replace(/Zhongke/i, '').trim()}`, 
              en: contractor 
            };
          }
          else {
            // 其他英文名称，创建默认中文表示
            return { cn: contractor, en: contractor };
          }
        } else if (isChinese && !isEnglish) {
          // 纯中文
          return { cn: contractor, en: contractor };
        } else if (isChinese && isEnglish) {
          // 中英混合
          return { cn: contractor, en: contractor };
        } else {
          // 其他情况
          return { cn: contractor, en: contractor };
        }
      };
      
      // 处理每个测试承包商
      const results = testContractors.map(contractor => {
        const translation = processContractor(contractor);
        return {
          original: contractor,
          translation
        };
      });
      
      // 输出结果
      console.log('翻译处理结果:');
      console.table(results.map(r => ({
        original: r.original,
        cn: r.translation.cn,
        en: r.translation.en
      })));
      
      // 将结果写入文件
      const outputPath = path.join(process.cwd(), 'manual-contractors-test.json');
      fs.writeFileSync(outputPath, JSON.stringify({
        success: true,
        results
      }, null, 2));
      
      console.log(`结果已写入文件: ${outputPath}`);
    };
    
    // 执行测试
    await testFunction();
    
    // 关闭应用
    await app.close();
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

// 执行测试
bootstrap(); 