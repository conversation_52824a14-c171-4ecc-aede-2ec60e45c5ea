import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import * as fs from 'fs';
import { DatabaseService } from './services/database.service';

interface SatellitePurpose {
  cn: string;
  en: string;
}

interface ApiResponse {
  success: boolean;
  purposes: SatellitePurpose[];
  timestamp?: string;
}

/**
 * 测试卫星用途接口功能
 * 这个脚本将启动一个临时的NestJS应用程序实例，并直接测试DatabaseService的getSatellitePurposes方法
 */
async function bootstrap() {
  // 创建一个NestJS应用实例
  const app = await NestFactory.create(AppModule, {
    logger: ['log', 'error', 'warn', 'debug'],
  });

  try {
    console.log('调用服务方法测试...');
    
    // 获取DatabaseService实例
    const databaseService = app.get(DatabaseService);
    
    // 直接调用服务方法
    const result = await databaseService.getSatellitePurposes();
    
    console.log(`获取到 ${result.purposes.length} 个卫星用途`);
    
    // 保存结果到文件中
    fs.writeFileSync('api-satellite-purposes-result.json', JSON.stringify(result, null, 2));
    console.log('结果已写入 api-satellite-purposes-result.json 文件');
    
    // 检查是否有未翻译的项目
    const untranslatedItems = result.purposes.filter((p: SatellitePurpose) => !/[\u4e00-\u9fa5]/.test(p.cn));
    if (untranslatedItems.length > 0) {
      console.log(`警告：发现 ${untranslatedItems.length} 个未翻译的卫星用途项:`);
      untranslatedItems.forEach((item: SatellitePurpose, index: number) => {
        if (index < 10) { // 只显示前10个
          console.log(`- ${item.en}: ${item.cn}`);
        }
      });
      if (untranslatedItems.length > 10) {
        console.log(`... 以及其他 ${untranslatedItems.length - 10} 个项目`);
      }
    } else {
      console.log('所有卫星用途项均已翻译成中文，无未翻译项');
    }
    
  } catch (error) {
    console.error('测试过程中发生错误:', error.message);
    console.error('错误堆栈:', error.stack);
  } finally {
    await app.close();
  }
}

// 运行测试
bootstrap(); 