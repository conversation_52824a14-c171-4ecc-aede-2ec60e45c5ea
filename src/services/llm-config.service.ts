import { Injectable, Logger, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { 
  UpdateLLMConfigDto, 
  LLMConfigResponseDto, 
  ConfigType, 
  TestConnectionDto, 
  TestConnectionResponseDto,
  ResetConfigDto,
  ConfigStatsDto
} from '../dto/llm-config.dto';
import { 
  TranslationService 
} from '../elasticsearch/services/translation.service';
import { 
  TranslationConfig, 
  ThemeExtractionConfig,
  getTranslationConfig,
  getThemeExtractionConfig,
  mergeConfig,
  validateLLMConfig
} from '../../config/llm.config';

/**
 * 大模型配置管理服务
 * 提供配置的增删改查、测试连接、统计信息等功能
 */
@Injectable()
export class LLMConfigService {
  private readonly logger = new Logger(LLMConfigService.name);
  
  // 配置缓存，避免频繁读取配置文件
  private configCache = new Map<string, any>();
  
  // 统计信息
  private stats = {
    translation: {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalResponseTime: 0,
      lastUsed: new Date().toISOString()
    },
    themeExtraction: {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalResponseTime: 0,
      lastUsed: new Date().toISOString()
    }
  };

  constructor(
    private readonly translationService: TranslationService
  ) {
    this.logger.log('LLM配置管理服务初始化完成');
    this.initializeStats();
  }

  /**
   * 初始化统计信息
   */
  private initializeStats(): void {
    // 从翻译服务获取现有统计信息
    try {
      const translationStats = this.translationService.getAPIStats();
      this.stats.translation = {
        totalRequests: translationStats.callCount,
        successfulRequests: translationStats.callCount,
        failedRequests: 0,
        totalResponseTime: translationStats.totalTime,
        lastUsed: new Date().toISOString()
      };
    } catch (error) {
      this.logger.warn('无法获取翻译服务统计信息，使用默认值');
    }
  }

  /**
   * 获取配置信息
   * @param configType 配置类型
   * @returns 配置信息
   */
  async getConfig(configType: ConfigType): Promise<LLMConfigResponseDto> {
    try {
      let config: TranslationConfig | ThemeExtractionConfig;
      
      if (configType === ConfigType.TRANSLATION) {
        config = this.translationService.getTranslationConfig();
      } else {
        config = this.translationService.getThemeExtractionConfig();
      }

      // 脱敏API密钥
      const maskedApiKey = this.maskApiKey(config.apiKey);

      const response: LLMConfigResponseDto = {
        configType,
        provider: config.provider,
        model: config.model,
        baseURL: config.baseURL || '',
        apiKey: maskedApiKey,
        systemPrompt: (config as any).systemPrompt || '',
        maxTokens: config.maxTokens || 4000,
        temperature: config.temperature || 0.1,
        timeout: config.timeout || 30000,
        maxRetries: config.maxRetries || 2,
        retryDelay: config.retryDelay || 1000,
        maxConcurrentRequests: config.maxConcurrentRequests || 3,
        lastUpdated: this.configCache.get(`${configType}_lastUpdated`) || new Date().toISOString()
      };

      return response;
    } catch (error) {
      this.logger.error(`获取${configType}配置失败:`, error);
      throw new InternalServerErrorException(`获取配置失败: ${error.message}`);
    }
  }

  /**
   * 获取所有配置
   * @returns 所有配置信息
   */
  async getAllConfigs(): Promise<LLMConfigResponseDto[]> {
    const configs = [];
    
    try {
      const translationConfig = await this.getConfig(ConfigType.TRANSLATION);
      const themeExtractionConfig = await this.getConfig(ConfigType.THEME_EXTRACTION);
      
      configs.push(translationConfig, themeExtractionConfig);
      
      return configs;
    } catch (error) {
      this.logger.error('获取所有配置失败:', error);
      throw new InternalServerErrorException(`获取所有配置失败: ${error.message}`);
    }
  }

  /**
   * 更新配置
   * @param updateDto 更新数据
   * @returns 更新后的配置
   */
  async updateConfig(updateDto: UpdateLLMConfigDto): Promise<LLMConfigResponseDto> {
    try {
      const { configType, ...updateData } = updateDto;
      
      // 移除undefined字段
      const cleanUpdateData = Object.fromEntries(
        Object.entries(updateData).filter(([_, value]) => value !== undefined)
      );

      this.logger.log(`开始更新${configType}配置:`, cleanUpdateData);

      // 根据配置类型更新相应的配置
      if (configType === ConfigType.TRANSLATION) {
        this.translationService.updateTranslationConfig(cleanUpdateData);
      } else {
        this.translationService.updateThemeExtractionConfig(cleanUpdateData);
      }

      // 更新缓存中的最后更新时间
      this.configCache.set(`${configType}_lastUpdated`, new Date().toISOString());

      this.logger.log(`${configType}配置更新成功`);

      // 返回更新后的配置
      return await this.getConfig(configType);
    } catch (error) {
      this.logger.error(`更新${updateDto.configType}配置失败:`, error);
      throw new BadRequestException(`更新配置失败: ${error.message}`);
    }
  }

  /**
   * 测试连接
   * @param testDto 测试数据
   * @returns 测试结果
   */
  async testConnection(testDto: TestConnectionDto): Promise<TestConnectionResponseDto> {
    const { configType, testText = 'Hello, world!' } = testDto;
    const startTime = Date.now();
    
    try {
      this.logger.log(`开始测试${configType}配置连接`);

      let result: string;
      
      if (configType === ConfigType.TRANSLATION) {
        // 测试翻译功能
        result = await this.translationService.translateText(testText);
        this.updateStats(configType, true, Date.now() - startTime);
      } else {
        // 测试主题提取功能
        result = await this.translationService.extractThemes('测试标题', testText);
        this.updateStats(configType, true, Date.now() - startTime);
      }

      const responseTime = Date.now() - startTime;

      return {
        success: true,
        responseTime,
        result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.logger.error(`测试${configType}配置连接失败:`, error);
      this.updateStats(configType, false, responseTime);

      return {
        success: false,
        responseTime,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 重置配置到默认值
   * @param resetDto 重置数据
   * @returns 重置后的配置
   */
  async resetConfig(resetDto: ResetConfigDto): Promise<LLMConfigResponseDto> {
    const { configType, confirm = false } = resetDto;
    
    if (!confirm) {
      throw new BadRequestException('需要确认才能重置配置');
    }

    try {
      this.logger.log(`开始重置${configType}配置到默认值`);

      let defaultConfig: TranslationConfig | ThemeExtractionConfig;
      
      if (configType === ConfigType.TRANSLATION) {
        defaultConfig = getTranslationConfig('default');
        this.translationService.updateTranslationConfig(defaultConfig);
      } else {
        defaultConfig = getThemeExtractionConfig('default');
        this.translationService.updateThemeExtractionConfig(defaultConfig);
      }

      // 更新缓存中的最后更新时间
      this.configCache.set(`${configType}_lastUpdated`, new Date().toISOString());

      this.logger.log(`${configType}配置重置成功`);

      return await this.getConfig(configType);
    } catch (error) {
      this.logger.error(`重置${configType}配置失败:`, error);
      throw new BadRequestException(`重置配置失败: ${error.message}`);
    }
  }

  /**
   * 获取配置统计信息
   * @returns 统计信息
   */
  async getStats(): Promise<ConfigStatsDto> {
    try {
      // 获取翻译服务的实时统计信息
      const translationApiStats = this.translationService.getAPIStats();
      const translationFailureStats = this.translationService.getFailureStats();

      return {
        translation: {
          totalRequests: translationApiStats.callCount,
          successfulRequests: translationApiStats.callCount - translationFailureStats.failures.other.count,
          failedRequests: translationFailureStats.failures.other.count,
          averageResponseTime: translationApiStats.callCount > 0 
            ? translationApiStats.totalTime / translationApiStats.callCount 
            : 0,
          lastUsed: this.stats.translation.lastUsed
        },
        themeExtraction: {
          totalRequests: this.stats.themeExtraction.totalRequests,
          successfulRequests: this.stats.themeExtraction.successfulRequests,
          failedRequests: this.stats.themeExtraction.failedRequests,
          averageResponseTime: this.stats.themeExtraction.totalRequests > 0
            ? this.stats.themeExtraction.totalResponseTime / this.stats.themeExtraction.totalRequests
            : 0,
          lastUsed: this.stats.themeExtraction.lastUsed
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('获取统计信息失败:', error);
      throw new InternalServerErrorException(`获取统计信息失败: ${error.message}`);
    }
  }

  /**
   * 脱敏API密钥
   * @param apiKey 原始API密钥
   * @returns 脱敏后的API密钥
   */
  private maskApiKey(apiKey: string): string {
    if (!apiKey || apiKey.length < 8) {
      return '****';
    }
    
    const prefix = apiKey.substring(0, 4);
    const suffix = apiKey.substring(apiKey.length - 4);
    const middle = '*'.repeat(Math.max(4, apiKey.length - 8));
    
    return `${prefix}${middle}${suffix}`;
  }

  /**
   * 更新统计信息
   * @param configType 配置类型
   * @param success 是否成功
   * @param responseTime 响应时间
   */
  private updateStats(configType: ConfigType, success: boolean, responseTime: number): void {
    const statsKey = configType === ConfigType.TRANSLATION ? 'translation' : 'themeExtraction';
    
    this.stats[statsKey].totalRequests++;
    this.stats[statsKey].totalResponseTime += responseTime;
    this.stats[statsKey].lastUsed = new Date().toISOString();
    
    if (success) {
      this.stats[statsKey].successfulRequests++;
    } else {
      this.stats[statsKey].failedRequests++;
    }
  }

  /**
   * 清除统计信息
   */
  async clearStats(): Promise<void> {
    this.stats = {
      translation: {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        totalResponseTime: 0,
        lastUsed: new Date().toISOString()
      },
      themeExtraction: {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        totalResponseTime: 0,
        lastUsed: new Date().toISOString()
      }
    };
    
    // 同时清除翻译服务的统计信息
    this.translationService.resetFailureStats();
    
    this.logger.log('统计信息已清除');
  }
} 