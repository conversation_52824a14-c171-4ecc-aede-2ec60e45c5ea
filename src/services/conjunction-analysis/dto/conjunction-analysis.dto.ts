import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsString, IsN<PERSON>ber, IsDateString, Is<PERSON><PERSON>y, ValidateNested, IsOptional, Min, Max } from 'class-validator';

export class SatelliteBasicInfoDto {
  @ApiProperty({
    description: '卫星编号',
    example: '25544',
    required: true
  })
  @IsString()
  satId: string;

  @ApiProperty({
    description: '卫星名称',
    example: 'ISS (ZARYA)',
    required: true
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'TLE数据第一行',
    example: '1 25544U 98067A   24054.88088078  .00014558  00000+0  26729-3 0  9990',
    required: true
  })
  @IsString()
  line1: string;

  @ApiProperty({
    description: 'TLE数据第二行',
    example: '2 25544  51.6416 150.2506 0004789  89.9089  32.8809 15.49512781434577',
    required: true
  })
  @IsString()
  line2: string;
}

export class ConjunctionAnalysisRequestDto {
  @ApiProperty({
    description: '背景目标卫星集合',
    type: [SatelliteBasicInfoDto],
    required: true,
    example: [
      {
        satId: '48274',
        name: 'STARLINK-3432',
        line1: '1 48274U 21041AF  24054.86885282  .00002571  00000+0  16843-3 0  9994',
        line2: '2 48274  53.0559  30.0121 0001038  89.7222 270.3898 15.06396635146374'
      },
      {
        satId: '48275',
        name: 'STARLINK-3433',
        line1: '1 48275U 21041AG  24054.85716922  .00002438  00000+0  16016-3 0  9990',
        line2: '2 48275  53.0558  30.0167 0001067  91.5344 268.5778 15.06396475146373'
      }
    ]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SatelliteBasicInfoDto)
  backgroundSatellites: SatelliteBasicInfoDto[];

  @ApiProperty({
    description: '关注目标卫星集合',
    type: [SatelliteBasicInfoDto],
    required: true,
    example: [
      {
        satId: '48276',
        name: 'STARLINK-3434',
        line1: '1 48276U 21041AH  24054.85132335  .00002443  00000+0  16052-3 0  9991',
        line2: '2 48276  53.0559  30.0189 0001073  91.8765 268.2357 15.06396512146372'
      },
      {
        satId: '48277',
        name: 'STARLINK-3435',
        line1: '1 48277U 21041AJ  24054.84547754  .00002453  00000+0  16115-3 0  9996',
        line2: '2 48277  53.0558  30.0198 0001078  92.1234 267.9888 15.06396498146371'
      }
    ]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SatelliteBasicInfoDto)
  targetSatellites: SatelliteBasicInfoDto[];

  @ApiProperty({
    description: '开始时间(UTC)',
    example: '2024-02-23T20:00:00Z',
    required: true
  })
  @IsDateString()
  startTime: string;

  @ApiProperty({
    description: '结束时间(UTC)',
    example: '2024-02-24T04:00:00Z',
    required: true
  })
  @IsDateString()
  endTime: string;

  @ApiProperty({
    description: '距离门限(千米)',
    example: 100,
    required: true,
    minimum: 0
  })
  @IsNumber()
  @Min(0)
  threshold: number;

  @ApiProperty({
    description: '时间步长(秒)',
    example: 30,
    required: false,
    minimum: 1,
    maximum: 3600,
    default: 60
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(3600)
  timeStep?: number;

  @ApiProperty({
    description: '每批处理的卫星数量',
    example: 100,
    required: false,
    minimum: 1,
    maximum: 1000,
    default: 100
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(1000)
  batchSize?: number;
}

export class RTNVectorDto {
  @ApiProperty({
    description: '径向分量(度)',
    example: 45.67
  })
  @IsNumber()
  r: number;

  @ApiProperty({
    description: '切向分量(度)',
    example: -23.45
  })
  @IsNumber()
  t: number;

  @ApiProperty({
    description: '法向分量(度)',
    example: 12.34
  })
  @IsNumber()
  n: number;
}

export class ClosestApproachDto {
  @ApiProperty({
    description: '最近距离时间(UTC)',
    example: '2024-02-24T02:30:00Z'
  })
  time: Date;

  @ApiProperty({
    description: '最近距离(千米)',
    example: 85.6
  })
  @IsNumber()
  distance: number;

  @ApiProperty({
    description: 'RTN方向',
    type: RTNVectorDto
  })
  @ValidateNested()
  @Type(() => RTNVectorDto)
  rtn: RTNVectorDto;
}

export class SatelliteInfoDto {
  @ApiProperty({
    description: '卫星编号',
    example: '25544'
  })
  @IsString()
  satId: string;

  @ApiProperty({
    description: '卫星名称',
    example: 'ISS (ZARYA)'
  })
  @IsString()
  name: string;
}

export class ConjunctionEventDto {
  @ApiProperty({
    description: '交汇编号',
    example: '20240223001',
    required: true
  })
  @IsString()
  conjunctionId: string;

  @ApiProperty({
    description: '卫星1信息',
    type: SatelliteInfoDto
  })
  @ValidateNested()
  @Type(() => SatelliteInfoDto)
  satellite1: SatelliteInfoDto;

  @ApiProperty({
    description: '卫星2信息',
    type: SatelliteInfoDto
  })
  @ValidateNested()
  @Type(() => SatelliteInfoDto)
  satellite2: SatelliteInfoDto;

  @ApiProperty({
    description: '交汇门限(千米)',
    example: 100
  })
  @IsNumber()
  threshold: number;

  @ApiProperty({
    description: '开始时间(UTC)',
    example: '2024-02-24T02:30:00Z'
  })
  startTime: Date;

  @ApiProperty({
    description: '结束时间(UTC)',
    example: '2024-02-24T02:40:00Z'
  })
  endTime: Date;

  @ApiProperty({
    description: '交汇时长(秒)',
    example: 600
  })
  @IsNumber()
  duration: number;

  @ApiProperty({
    description: '最近距离信息',
    type: ClosestApproachDto
  })
  @ValidateNested()
  @Type(() => ClosestApproachDto)
  closestApproach: ClosestApproachDto;
}

export class ConjunctionAnalysisResponseDto {
  @ApiProperty({
    description: '交汇事件列表',
    type: [ConjunctionEventDto],
    example: [
      {
        conjunctionId: '20240223001',
        satellite1: {
          satId: '48274',
          name: 'STARLINK-3432'
        },
        satellite2: {
          satId: '48276',
          name: 'STARLINK-3434'
        },
        threshold: 100,
        startTime: '2024-02-23T21:15:00Z',
        endTime: '2024-02-23T21:18:30Z',
        duration: 210,
        closestApproach: {
          time: '2024-02-23T21:16:45Z',
          distance: 82.3,
          rtn: {
            r: 35.67,
            t: -28.45,
            n: 15.34
          }
        }
      },
      {
        conjunctionId: '20240223002',
        satellite1: {
          satId: '48275',
          name: 'STARLINK-3433'
        },
        satellite2: {
          satId: '48277',
          name: 'STARLINK-3435'
        },
        threshold: 100,
        startTime: '2024-02-23T22:45:00Z',
        endTime: '2024-02-23T22:48:00Z',
        duration: 180,
        closestApproach: {
          time: '2024-02-23T22:46:30Z',
          distance: 88.9,
          rtn: {
            r: -42.56,
            t: 32.18,
            n: 18.73
          }
        }
      }
    ]
  })
  conjunctions: ConjunctionEventDto[];
} 