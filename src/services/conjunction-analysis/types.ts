/**
 * 卫星基本信息接口
 */
export interface ISatelliteBasicInfo {
  satId: string;    // 卫星编号
  name: string;     // 卫星名称
  line1: string;    // TLE数据第一行
  line2: string;    // TLE数据第二行
}

/**
 * RTN方向向量接口
 */
export interface IRTNVector {
  r: number;  // 径向分量(度)
  t: number;  // 切向分量(度)
  n: number;  // 法向分量(度)
}

/**
 * 交汇事件接口
 */
export interface IConjunctionEvent {
  conjunctionId: string;      // 交汇编号
  satellite1: {              // 卫星1信息
    satId: string;          // 卫星编号
    name: string;           // 卫星名称
  };
  satellite2: {              // 卫星2信息
    satId: string;          // 卫星编号
    name: string;           // 卫星名称
  };
  threshold: number;         // 交汇门限(km)
  startTime: Date;          // 开始时间
  endTime: Date;            // 结束时间
  duration: number;         // 交汇时长(秒)
  closestApproach: {        // 最近距离信息
    time: Date;            // 时间点
    distance: number;      // 距离(km)
    rtn: IRTNVector;      // RTN方向
  };
}

/**
 * 交汇分析请求参数接口
 */
export interface IConjunctionAnalysisParams {
  backgroundSatellites: ISatelliteBasicInfo[];  // 背景目标卫星集合
  targetSatellites: ISatelliteBasicInfo[];      // 关注目标卫星集合
  startTime: Date;                              // 开始时间
  endTime: Date;                                // 结束时间
  threshold: number;                            // 距离门限(km)
  timeStep?: number;                           // 时间步长(秒)，默认为60
  batchSize?: number;                          // 每批处理的卫星数量，默认为100
} 