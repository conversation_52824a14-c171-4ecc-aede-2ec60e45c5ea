import { Injectable } from '@nestjs/common';
import { OrbitCalculator } from '../orbit-calculator/OrbitCalculator';
import { ISatelliteBasicInfo, IConjunctionEvent, IConjunctionAnalysisParams, IRTNVector } from './types';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class ConjunctionAnalysisService {
  private readonly defaultTimeStep = 60; // 默认时间步长(秒)
  private readonly defaultBatchSize = 100; // 默认批处理大小

  constructor(private readonly orbitCalculator: OrbitCalculator) {}

  /**
   * 计算两个向量的叉积
   */
  private crossProduct(
    v1: { x: number; y: number; z: number },
    v2: { x: number; y: number; z: number }
  ): { x: number; y: number; z: number } {
    return {
      x: v1.y * v2.z - v1.z * v2.y,
      y: v1.z * v2.x - v1.x * v2.z,
      z: v1.x * v2.y - v1.y * v2.x
    };
  }

  /**
   * 计算向量的模
   */
  private vectorMagnitude(v: { x: number; y: number; z: number }): number {
    return Math.sqrt(v.x * v.x + v.y * v.y + v.z * v.z);
  }

  /**
   * 计算两个向量的点积
   */
  private dotProduct(
    v1: { x: number; y: number; z: number },
    v2: { x: number; y: number; z: number }
  ): number {
    return v1.x * v2.x + v1.y * v2.y + v1.z * v2.z;
  }

  /**
   * 计算RTN方向
   * @param pos1 卫星1位置
   * @param vel1 卫星1速度
   * @param pos2 卫星2位置
   * @returns RTN方向角度
   */
  private calculateRTN(
    pos1: { x: number; y: number; z: number },
    vel1: { x: number; y: number; z: number },
    pos2: { x: number; y: number; z: number }
  ): IRTNVector {
    // 计算相对位置向量
    const relativePos = {
      x: pos2.x - pos1.x,
      y: pos2.y - pos1.y,
      z: pos2.z - pos1.z
    };

    // 计算R方向(径向)单位向量
    const rMag = this.vectorMagnitude(pos1);
    const rUnit = {
      x: pos1.x / rMag,
      y: pos1.y / rMag,
      z: pos1.z / rMag
    };

    // 计算N方向(轨道面法向)单位向量
    const h = this.crossProduct(pos1, vel1);
    const hMag = this.vectorMagnitude(h);
    const nUnit = {
      x: h.x / hMag,
      y: h.y / hMag,
      z: h.z / hMag
    };

    // 计算T方向(切向)单位向量
    const t = this.crossProduct(nUnit, rUnit);

    // 计算相对位置在RTN坐标系中的投影
    const r = this.dotProduct(relativePos, rUnit);
    const t_proj = this.dotProduct(relativePos, t);
    const n = this.dotProduct(relativePos, nUnit);

    // 转换为角度
    return {
      r: Math.atan2(r, Math.sqrt(t_proj * t_proj + n * n)) * 180 / Math.PI,
      t: Math.atan2(t_proj, Math.sqrt(r * r + n * n)) * 180 / Math.PI,
      n: Math.atan2(n, Math.sqrt(r * r + t_proj * t_proj)) * 180 / Math.PI
    };
  }

  /**
   * 生成交汇事件ID
   * @param time 交汇发生时间
   * @param index 序号
   * @returns 交汇事件ID
   */
  private generateConjunctionId(time: Date, index: number): string {
    const timestamp = time.toISOString().slice(0, 10).replace(/-/g, '');
    return `${timestamp}${index.toString().padStart(3, '0')}`;
  }

  /**
   * 分析两颗卫星在指定时间段内的交汇情况
   */
  private async analyzePairConjunction(
    sat1: ISatelliteBasicInfo,
    sat2: ISatelliteBasicInfo,
    startTime: Date,
    endTime: Date,
    threshold: number,
    timeStep: number,
    index: number
  ): Promise<IConjunctionEvent | null> {
    let isInConjunction = false;
    let conjunctionStart: Date | null = null;
    let minDistance = Infinity;
    let minDistanceTime: Date | null = null;
    let minDistanceRTN: IRTNVector | null = null;
    let minDistancePos1: any = null;
    let minDistancePos2: any = null;

    // 计算时间序列
    const totalSeconds = (endTime.getTime() - startTime.getTime()) / 1000;
    const steps = Math.ceil(totalSeconds / timeStep);

    for (let i = 0; i <= steps; i++) {
      const currentTime = new Date(startTime.getTime() + i * timeStep * 1000);

      // 并行计算两颗卫星的位置
      const [pos1, pos2] = await Promise.all([
        this.orbitCalculator.calculatePosition(sat1, currentTime),
        this.orbitCalculator.calculatePosition(sat2, currentTime)
      ]);

      if (!pos1 || !pos2) continue;

      // 计算距离
      const dx = pos2.position.x - pos1.position.x;
      const dy = pos2.position.y - pos1.position.y;
      const dz = pos2.position.z - pos1.position.z;
      const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);

      // 更新最小距离
      if (distance < minDistance) {
        minDistance = distance;
        minDistanceTime = currentTime;
        minDistancePos1 = pos1;
        minDistancePos2 = pos2;
        minDistanceRTN = this.calculateRTN(
          pos1.position,
          pos1.velocity,
          pos2.position
        );
      }

      // 判断是否进入交汇状态
      if (distance <= threshold) {
        if (!isInConjunction) {
          isInConjunction = true;
          conjunctionStart = currentTime;
        }
      } else if (isInConjunction) {
        // 结束交汇，生成交汇事件
        return {
          conjunctionId: this.generateConjunctionId(conjunctionStart!, index),
          satellite1: {
            satId: sat1.satId,
            name: sat1.name
          },
          satellite2: {
            satId: sat2.satId,
            name: sat2.name
          },
          threshold,
          startTime: conjunctionStart!,
          endTime: currentTime,
          duration: (currentTime.getTime() - conjunctionStart!.getTime()) / 1000,
          closestApproach: {
            time: minDistanceTime!,
            distance: minDistance,
            rtn: minDistanceRTN!
          }
        };
      }
    }

    // 如果结束时仍在交汇状态
    if (isInConjunction) {
      return {
        conjunctionId: this.generateConjunctionId(conjunctionStart!, index),
        satellite1: {
          satId: sat1.satId,
          name: sat1.name
        },
        satellite2: {
          satId: sat2.satId,
          name: sat2.name
        },
        threshold,
        startTime: conjunctionStart!,
        endTime,
        duration: (endTime.getTime() - conjunctionStart!.getTime()) / 1000,
        closestApproach: {
          time: minDistanceTime!,
          distance: minDistance,
          rtn: minDistanceRTN!
        }
      };
    }

    return null;
  }

  /**
   * 分析一组卫星对的交汇情况
   */
  private async analyzeSatelliteBatch(
    pairs: Array<[ISatelliteBasicInfo, ISatelliteBasicInfo]>,
    startTime: Date,
    endTime: Date,
    threshold: number,
    timeStep: number,
    indexRef: { current: number }  // 使用引用对象来共享序号
  ): Promise<IConjunctionEvent[]> {
    const conjunctions: IConjunctionEvent[] = [];

    // 并行分析每对卫星
    const conjunctionPromises = pairs.map(([sat1, sat2]) =>
      this.analyzePairConjunction(sat1, sat2, startTime, endTime, threshold, timeStep, indexRef.current)
    );

    const results = await Promise.all(conjunctionPromises);

    // 收集有效的交汇事件
    results.forEach(result => {
      if (result) {
        // 只在有交汇事件时递增序号
        result.conjunctionId = this.generateConjunctionId(result.startTime, indexRef.current++);
        conjunctions.push(result);
      }
    });

    return conjunctions;
  }

  /**
   * 分析卫星交汇情况
   */
  public async analyzeConjunctions(params: IConjunctionAnalysisParams): Promise<IConjunctionEvent[]> {
    const {
      backgroundSatellites,
      targetSatellites,
      startTime,
      endTime,
      threshold,
      timeStep = this.defaultTimeStep,
      batchSize = this.defaultBatchSize
    } = params;

    const allConjunctions: IConjunctionEvent[] = [];
    const indexRef = { current: 1 };  // 使用引用对象来跨批次共享序号

    // 生成所有需要分析的卫星对
    const satellitePairs: Array<[ISatelliteBasicInfo, ISatelliteBasicInfo]> = [];
    for (const bgSat of backgroundSatellites) {
      for (const tgtSat of targetSatellites) {
        satellitePairs.push([bgSat, tgtSat]);
      }
    }

    // 将卫星对分批处理
    for (let i = 0; i < satellitePairs.length; i += batchSize) {
      const batch = satellitePairs.slice(i, i + batchSize);
      const batchResults = await this.analyzeSatelliteBatch(
        batch,
        startTime,
        endTime,
        threshold,
        timeStep,
        indexRef  // 传递序号引用
      );
      
      allConjunctions.push(...batchResults);
    }

    // 按开始时间排序
    return allConjunctions.sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
  }
} 