import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as fs from 'fs';
import * as path from 'path';
import { ElasticsearchOrbitService } from '../../elasticsearch/services/elasticsearch.orbit.service';
import { OrbitCalculator } from '../orbit-calculator/OrbitCalculator';
import { FilePushService } from './file-push.service';
import { 
  ISatellitePointCloud, 
  ISatellitePointCloudItem, 
  IPointCloudMetadata,
  ICartesianPosition,
  IGeographicPosition,
  IRGBColor,
  ISatelliteProperties,
  IOrbitClassColorMap,
  IPointCloudGenerationConfig,
  ISatelliteTLEData,
  IPointCloudGenerationTask
} from './types';

/**
 * 卫星3D Tiles点云生成服务
 * 负责定时获取卫星TLE数据并生成3D点云文件供前端使用
 */
@Injectable()
export class SatelliteTilesService {
  private readonly logger = new Logger(SatelliteTilesService.name);
  private readonly outputDir = path.join(process.cwd(), 'public', 'tiles');
  private readonly satellitesFilePath = path.join(this.outputDir, 'satellites.json');
  private readonly metadataFilePath = path.join(this.outputDir, 'metadata.json');
  
  // 当前生成任务状态
  private currentTask: IPointCloudGenerationTask | null = null;
  
  // 轨道类型颜色映射
  private readonly orbitClassColors: IOrbitClassColorMap = {
    'LEO': { r: 0, g: 255, b: 0 },      // 绿色 - 低地球轨道
    'MEO': { r: 255, g: 165, b: 0 },    // 橙色 - 中地球轨道
    'GEO': { r: 255, g: 0, b: 0 },      // 红色 - 地球同步轨道
    'HEO': { r: 255, g: 0, b: 255 },    // 紫色 - 高椭圆轨道
    'SSO': { r: 0, g: 255, b: 255 },    // 青色 - 太阳同步轨道
    'Polar': { r: 0, g: 0, b: 255 },    // 蓝色 - 极地轨道
    'Elliptical': { r: 255, g: 255, b: 0 }, // 黄色 - 椭圆轨道
    'Unknown': { r: 128, g: 128, b: 128 }   // 灰色 - 未知轨道
  };

  constructor(
    private readonly orbitService: ElasticsearchOrbitService,
    private readonly orbitCalculator: OrbitCalculator,
    private readonly filePushService: FilePushService
  ) {
    this.ensureOutputDirectory();
  }

  /**
   * 定时任务：每4小时生成一次卫星点云
   */
  @Cron('0 */4 * * *', {
    name: 'generateSatellitePointCloud',
    timeZone: 'UTC'
  })
  async scheduledGeneratePointCloud(): Promise<void> {
    this.logger.log('🕐 定时任务触发：开始生成卫星点云数据');
    
    try {
      await this.generateSatellitePointCloud({
        outputPath: this.outputDir,
        colorByOrbitClass: true,
        includeStatistics: true,
        coordinateSystem: 'cartesian',
        compressionLevel: 6
      });
      
      this.logger.log('✅ 定时任务完成：卫星点云数据生成成功');
    } catch (error) {
      this.logger.error('❌ 定时任务失败：卫星点云数据生成失败', error.stack);
    }
  }

  /**
   * 手动生成卫星点云数据（带文件推送支持）
   * @param dto 生成请求DTO
   * @returns 生成结果
   */
  async generateSatellitePointCloudWithPush(dto: any): Promise<{
    success: boolean;
    taskId: string;
    totalSatellites: number;
    generationDuration: number;
    outputPath: string;
    message: string;
    pushResults?: any[];
    generatedFiles?: string[];
    fileSizes?: Record<string, number>;
  }> {
    const config: IPointCloudGenerationConfig = {
      outputPath: this.outputDir,
      colorByOrbitClass: dto.colorByOrbitClass ?? true,
      includeStatistics: dto.includeStatistics ?? true,
      coordinateSystem: dto.coordinateSystem ?? 'cartesian',
      compressionLevel: dto.compressionLevel ?? 6
    };

    // 生成基础点云数据
    const baseResult = await this.generateSatellitePointCloud(config);
    
    // 准备推送文件列表
    const filesToPush = [this.satellitesFilePath, this.metadataFilePath];
    const generatedFiles = filesToPush.map(fp => path.basename(fp));
    const fileSizes = this.filePushService.getFileSizes(filesToPush);

    let pushResults: any[] = [];

    // 如果配置了推送目标，执行文件推送
    if (dto.pushTargets && dto.pushTargets.length > 0) {
      this.logger.log(`📤 开始推送文件到 ${dto.pushTargets.length} 个目标...`);
      
      try {
        pushResults = await this.filePushService.pushFiles(
          filesToPush,
          dto.pushTargets,
          {
            enableGzip: dto.enableGzip,
            chunking: dto.chunking,
            callbackUrl: dto.callbackUrl
          }
        );

        // 清理临时文件（压缩文件和分片文件）
        const tempFiles = pushResults.flatMap(result => 
          result.files.filter((file: string) => 
            file.endsWith('.gz') || file.includes('_chunk_')
          ).map((file: string) => path.join(this.outputDir, file))
        );
        
        if (tempFiles.length > 0) {
          await this.filePushService.cleanupTempFiles(tempFiles);
        }

        this.logger.log(`✅ 文件推送完成，成功: ${pushResults.filter(r => r.success).length}/${pushResults.length}`);
      } catch (error) {
        this.logger.error('❌ 文件推送过程中发生错误', error.stack);
      }
    }

    return {
      ...baseResult,
      pushResults,
      generatedFiles,
      fileSizes
    };
  }

  /**
   * 手动生成卫星点云数据
   * @param config 生成配置
   * @returns 生成结果
   */
  async generateSatellitePointCloud(config: IPointCloudGenerationConfig): Promise<{
    success: boolean;
    taskId: string;
    totalSatellites: number;
    generationDuration: number;
    outputPath: string;
    message: string;
  }> {
    const startTime = Date.now();
    const taskId = `task_${new Date().toISOString().replace(/[:.]/g, '_')}`;
    
    // 检查是否有正在运行的任务
    if (this.currentTask && this.currentTask.status === 'running') {
      throw new Error('已有点云生成任务正在运行中，请稍后再试');
    }

    // 创建新任务
    this.currentTask = {
      id: taskId,
      status: 'running',
      startTime: new Date(),
      progress: 0,
      totalSatellites: 0,
      processedSatellites: 0
    };

    try {
      this.logger.log(`🚀 开始生成卫星点云数据，任务ID: ${taskId}`);
      
      // 步骤1: 获取所有卫星TLE数据
      this.logger.log('📡 正在获取所有卫星TLE数据...');
      this.currentTask.progress = 10;
      
      const tleResponse = await this.orbitService.getAllSatelliteTleData();
      if (!tleResponse.success || !tleResponse.results) {
        throw new Error('获取卫星TLE数据失败');
      }

      const tleData: ISatelliteTLEData[] = tleResponse.results;
      this.currentTask.totalSatellites = tleData.length;
      this.logger.log(`📊 获取到 ${tleData.length} 颗卫星的TLE数据`);

      // 步骤2: 转换TLE数据为轨道计算器格式
      this.logger.log('🔄 正在转换TLE数据格式...');
      this.currentTask.progress = 20;
      
      const satellites = this.convertTLEDataToSatellites(tleData);
      
      // 步骤3: 批量计算卫星位置
      this.logger.log('🧮 正在计算卫星位置...');
      this.currentTask.progress = 30;
      
      const calculationTime = new Date();
      const positionResults = await this.orbitCalculator.calculatePositions(
        satellites,
        calculationTime,
        {
          continueOnError: true,
          batchSize: 100
        }
      );

      this.logger.log(`✅ 成功计算 ${positionResults.positions.length} 颗卫星位置`);
      if (positionResults.errors.length > 0) {
        this.logger.warn(`⚠️  ${positionResults.errors.length} 颗卫星计算失败`);
      }

      // 步骤4: 生成点云数据
      this.logger.log('🎨 正在生成点云数据...');
      this.currentTask.progress = 60;
      
      const pointCloudItems = this.generatePointCloudItems(
        positionResults.positions,
        tleData,
        config
      );

      // 步骤5: 生成统计信息
      this.logger.log('📈 正在生成统计信息...');
      this.currentTask.progress = 80;
      
      const statistics = this.generateStatistics(pointCloudItems);

      // 步骤6: 构建最终数据结构
      const pointCloudData: ISatellitePointCloud = {
        generatedAt: new Date().toISOString(),
        totalSatellites: pointCloudItems.length,
        calculationTime: calculationTime.toISOString(),
        satellites: pointCloudItems,
        statistics
      };

      // 步骤7: 保存文件
      this.logger.log('💾 正在保存点云文件...');
      this.currentTask.progress = 90;
      
      await this.savePointCloudData(pointCloudData);
      await this.updateMetadata(pointCloudData, startTime);

      // 完成任务
      const generationDuration = Date.now() - startTime;
      this.currentTask.status = 'completed';
      this.currentTask.endTime = new Date();
      this.currentTask.progress = 100;
      this.currentTask.processedSatellites = pointCloudItems.length;

      this.logger.log(`🎉 卫星点云生成完成！耗时: ${generationDuration}ms, 卫星数量: ${pointCloudItems.length}`);

      return {
        success: true,
        taskId,
        totalSatellites: pointCloudItems.length,
        generationDuration,
        outputPath: '/tiles/satellites.json',
        message: `成功生成 ${pointCloudItems.length} 颗卫星的点云数据`
      };

    } catch (error) {
      // 任务失败
      if (this.currentTask) {
        this.currentTask.status = 'failed';
        this.currentTask.endTime = new Date();
        this.currentTask.errorMessage = error.message;
      }

      this.logger.error(`❌ 卫星点云生成失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取当前生成任务状态
   */
  getCurrentTaskStatus(): IPointCloudGenerationTask | null {
    return this.currentTask;
  }

  /**
   * 获取点云元数据
   */
  async getMetadata(): Promise<IPointCloudMetadata | null> {
    try {
      if (!fs.existsSync(this.metadataFilePath)) {
        return null;
      }

      const metadataContent = await fs.promises.readFile(this.metadataFilePath, 'utf-8');
      return JSON.parse(metadataContent);
    } catch (error) {
      this.logger.error('读取元数据文件失败', error);
      return null;
    }
  }

  /**
   * 获取卫星点云数据
   */
  async getSatellitePointCloudData(): Promise<ISatellitePointCloud | null> {
    try {
      if (!fs.existsSync(this.satellitesFilePath)) {
        return null;
      }

      const satelliteContent = await fs.promises.readFile(this.satellitesFilePath, 'utf-8');
      return JSON.parse(satelliteContent);
    } catch (error) {
      this.logger.error('读取卫星点云文件失败', error);
      return null;
    }
  }

  /**
   * 确保输出目录存在
   */
  private ensureOutputDirectory(): void {
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
      this.logger.log(`📁 创建输出目录: ${this.outputDir}`);
    }
  }

  /**
   * 转换TLE数据为轨道计算器格式
   */
  private convertTLEDataToSatellites(tleData: ISatelliteTLEData[]) {
    return tleData.map(tle => {
      const tleLines = tle.tle_raw.split('\n');
      return {
        satId: tle.norad_id.toString(),
        name: tle.satellite_name,
        line1: tleLines[1] || '',
        line2: tleLines[2] || ''
      };
    }).filter(sat => sat.line1 && sat.line2); // 过滤掉无效的TLE数据
  }

  /**
   * 生成点云数据项
   */
  private generatePointCloudItems(
    positions: any[],
    tleData: ISatelliteTLEData[],
    config: IPointCloudGenerationConfig
  ): ISatellitePointCloudItem[] {
    const tleMap = new Map<string, ISatelliteTLEData>();
    tleData.forEach(tle => {
      tleMap.set(tle.norad_id.toString(), tle);
    });

    return positions.map(pos => {
      const tle = tleMap.get(pos.satId);
      if (!tle) return null;

      // 提取轨道信息
      const orbitClass = this.extractOrbitClass(pos);
      const properties: ISatelliteProperties = {
        noradId: parseInt(pos.satId),
        cosparId: tle.cospar_id,
        orbitClass,
        inclination: pos.orbital?.inclination,
        period: pos.orbital?.period,
        apogee: pos.orbital?.apogee,
        perigee: pos.orbital?.perigee,
        eccentricity: pos.orbital?.eccentricity
      };

      // 生成颜色
      const color = config.colorByOrbitClass 
        ? this.orbitClassColors[orbitClass] || this.orbitClassColors['Unknown']
        : { r: 255, g: 255, b: 255 }; // 默认白色

      // 转换坐标
      const cartesianPos: ICartesianPosition = {
        x: pos.position.x,
        y: pos.position.y,
        z: pos.position.z
      };

      const geographicPos: IGeographicPosition = {
        longitude: pos.longitude,
        latitude: pos.latitude,
        altitude: pos.altitude
      };

      return {
        id: pos.satId,
        name: tle.satellite_name,
        position: cartesianPos,
        geographic: geographicPos,
        properties,
        color
      };
    }).filter(item => item !== null) as ISatellitePointCloudItem[];
  }

  /**
   * 提取轨道类型
   */
  private extractOrbitClass(position: any): string {
    // 根据高度判断轨道类型 (altitude单位是km)
    const altitude = position.altitude || 0;
    
    if (altitude < 2000) { // 2000km
      return 'LEO';
    } else if (altitude < 35786) { // 35786km
      return 'MEO';
    } else if (altitude >= 35786 && altitude <= 35790) {
      return 'GEO';
    } else {
      return 'HEO';
    }
  }

  /**
   * 生成统计信息
   */
  private generateStatistics(items: ISatellitePointCloudItem[]) {
    const orbitClassDistribution: Record<string, number> = {};
    const altitudes: number[] = [];

    items.forEach(item => {
      // 统计轨道类型分布
      const orbitClass = item.properties.orbitClass;
      orbitClassDistribution[orbitClass] = (orbitClassDistribution[orbitClass] || 0) + 1;
      
      // 收集高度数据 (转换为米)
      altitudes.push(item.geographic.altitude * 1000);
    });

    // 计算高度范围
    const altitudeRange = {
      min: Math.min(...altitudes),
      max: Math.max(...altitudes),
      average: altitudes.reduce((sum, alt) => sum + alt, 0) / altitudes.length
    };

    return {
      orbitClassDistribution,
      altitudeRange
    };
  }

  /**
   * 保存点云数据到文件
   */
  private async savePointCloudData(data: ISatellitePointCloud): Promise<void> {
    const jsonContent = JSON.stringify(data, null, 2);
    await fs.promises.writeFile(this.satellitesFilePath, jsonContent, 'utf-8');
    this.logger.log(`💾 点云数据已保存到: ${this.satellitesFilePath}`);
  }

  /**
   * 更新元数据
   */
  private async updateMetadata(data: ISatellitePointCloud, startTime: number): Promise<void> {
    const now = new Date();
    const nextScheduled = new Date(now.getTime() + 4 * 60 * 60 * 1000); // 4小时后

    const metadata: IPointCloudMetadata = {
      lastGenerated: data.generatedAt,
      nextScheduled: nextScheduled.toISOString(),
      totalSatellites: data.totalSatellites,
      generationDuration: Date.now() - startTime,
      status: 'idle',
      version: '1.0.0'
    };

    const metadataContent = JSON.stringify(metadata, null, 2);
    await fs.promises.writeFile(this.metadataFilePath, metadataContent, 'utf-8');
    this.logger.log(`📋 元数据已更新: ${this.metadataFilePath}`);
  }
} 