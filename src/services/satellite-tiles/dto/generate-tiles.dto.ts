import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsBoolean, IsString, IsNumber, Min, Max, IsEnum, IsUrl, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * 文件推送目标配置
 */
export class FilePushTargetDto {
  @ApiProperty({
    description: '推送类型',
    enum: ['local', 'ftp', 'sftp', 'http', 's3', 'oss'],
    example: 'http'
  })
  @IsEnum(['local', 'ftp', 'sftp', 'http', 's3', 'oss'])
  type: 'local' | 'ftp' | 'sftp' | 'http' | 's3' | 'oss';

  @ApiProperty({
    description: '目标地址或路径',
    example: 'https://frontend.example.com/api/tiles/upload'
  })
  @IsString()
  target: string;

  @ApiProperty({
    description: '认证信息 (可选)',
    example: { token: 'bearer_token', key: 'access_key' },
    required: false
  })
  @IsOptional()
  credentials?: Record<string, string>;

  @ApiProperty({
    description: '推送选项 (可选)',
    example: { timeout: 30000, retries: 3 },
    required: false
  })
  @IsOptional()
  options?: Record<string, any>;
}

/**
 * 文件分片配置
 */
export class FileChunkingDto {
  @ApiProperty({
    description: '是否启用分片',
    example: true,
    default: false
  })
  @IsOptional()
  @IsBoolean()
  enabled?: boolean;

  @ApiProperty({
    description: '分片大小 (MB)',
    example: 10,
    minimum: 1,
    maximum: 100,
    default: 10
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  chunkSize?: number;

  @ApiProperty({
    description: '分片文件命名模式',
    example: 'satellites_chunk_{index}.json',
    default: 'satellites_chunk_{index}.json'
  })
  @IsOptional()
  @IsString()
  namePattern?: string;
}

/**
 * 生成卫星点云瓦片请求DTO
 */
export class GenerateSatelliteTilesDto {
  @ApiProperty({
    description: '是否强制重新生成（忽略缓存）',
    example: false,
    required: false,
    default: false
  })
  @IsOptional()
  @IsBoolean()
  forceRegenerate?: boolean;

  @ApiProperty({
    description: '是否按轨道类型着色',
    example: true,
    required: false,
    default: true
  })
  @IsOptional()
  @IsBoolean()
  colorByOrbitClass?: boolean;

  @ApiProperty({
    description: '是否包含统计信息',
    example: true,
    required: false,
    default: true
  })
  @IsOptional()
  @IsBoolean()
  includeStatistics?: boolean;

  @ApiProperty({
    description: '坐标系统',
    example: 'cartesian',
    enum: ['cartesian', 'geographic'],
    required: false,
    default: 'cartesian'
  })
  @IsOptional()
  @IsEnum(['cartesian', 'geographic'])
  coordinateSystem?: 'cartesian' | 'geographic';

  @ApiProperty({
    description: '压缩级别 (0-9)',
    example: 6,
    minimum: 0,
    maximum: 9,
    required: false,
    default: 6
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(9)
  compressionLevel?: number;

  @ApiProperty({
    description: '文件推送目标列表',
    type: [FilePushTargetDto],
    required: false,
    example: [{
      type: 'http',
      target: 'https://frontend.example.com/api/tiles/upload',
      credentials: { token: 'bearer_token' },
      options: { timeout: 30000 }
    }]
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FilePushTargetDto)
  pushTargets?: FilePushTargetDto[];

  @ApiProperty({
    description: '文件分片配置',
    type: FileChunkingDto,
    required: false,
    example: {
      enabled: true,
      chunkSize: 10,
      namePattern: 'satellites_chunk_{index}.json'
    }
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => FileChunkingDto)
  chunking?: FileChunkingDto;

  @ApiProperty({
    description: '是否启用gzip压缩',
    example: true,
    required: false,
    default: true
  })
  @IsOptional()
  @IsBoolean()
  enableGzip?: boolean;

  @ApiProperty({
    description: '是否启用增量更新',
    example: false,
    required: false,
    default: false
  })
  @IsOptional()
  @IsBoolean()
  enableIncremental?: boolean;

  @ApiProperty({
    description: '生成完成后的回调URL',
    example: 'https://frontend.example.com/api/tiles/callback',
    required: false
  })
  @IsOptional()
  @IsUrl()
  callbackUrl?: string;
}

/**
 * 文件推送结果
 */
export class FilePushResultDto {
  @ApiProperty({
    description: '推送目标',
    example: 'https://frontend.example.com/api/tiles/upload'
  })
  target: string;

  @ApiProperty({
    description: '推送是否成功',
    example: true
  })
  success: boolean;

  @ApiProperty({
    description: '推送的文件列表',
    example: ['satellites.json', 'metadata.json']
  })
  files: string[];

  @ApiProperty({
    description: '推送耗时 (毫秒)',
    example: 5000
  })
  duration: number;

  @ApiProperty({
    description: '错误信息 (如果失败)',
    example: null,
    required: false
  })
  error?: string;

  @ApiProperty({
    description: '推送的数据大小 (字节)',
    example: 1048576
  })
  dataSize: number;
}

/**
 * 生成卫星点云瓦片响应DTO
 */
export class GenerateTilesResponseDto {
  @ApiProperty({
    description: '生成是否成功',
    example: true
  })
  success: boolean;

  @ApiProperty({
    description: '响应消息',
    example: '成功生成 5000 颗卫星的点云数据'
  })
  message: string;

  @ApiProperty({
    description: '任务ID',
    example: 'task_2025-01-06T14_00_00_000Z'
  })
  taskId: string;

  @ApiProperty({
    description: '卫星总数',
    example: 5000
  })
  totalSatellites: number;

  @ApiProperty({
    description: '生成耗时 (毫秒)',
    example: 15000
  })
  generationDuration: number;

  @ApiProperty({
    description: '输出路径',
    example: '/tiles/satellites.json'
  })
  outputPath: string;

  @ApiProperty({
    description: '文件推送结果列表',
    type: [FilePushResultDto],
    required: false
  })
  pushResults?: FilePushResultDto[];

  @ApiProperty({
    description: '生成的文件列表',
    example: ['satellites.json', 'metadata.json'],
    required: false
  })
  generatedFiles?: string[];

  @ApiProperty({
    description: '文件大小信息 (字节)',
    example: { 'satellites.json': 1048576, 'metadata.json': 2048 },
    required: false
  })
  fileSizes?: Record<string, number>;
}

/**
 * 瓦片生成状态响应DTO
 */
export class TilesStatusResponseDto {
  @ApiProperty({
    description: '生成状态',
    enum: ['idle', 'generating', 'error'],
    example: 'idle'
  })
  status: 'idle' | 'generating' | 'error';

  @ApiProperty({
    description: '最后生成时间',
    example: '2025-01-06T14:00:00Z'
  })
  lastGenerated: string;

  @ApiProperty({
    description: '下次计划生成时间',
    example: '2025-01-06T18:00:00Z'
  })
  nextScheduled: string;

  @ApiProperty({
    description: '卫星总数',
    example: 5000
  })
  totalSatellites: number;

  @ApiProperty({
    description: '生成耗时 (毫秒)',
    example: 15000
  })
  generationDuration: number;

  @ApiProperty({
    description: '错误信息',
    example: null,
    required: false
  })
  errorMessage?: string;

  @ApiProperty({
    description: '数据版本',
    example: '1.0.0'
  })
  version: string;

  @ApiProperty({
    description: '当前进度 (0-100)',
    example: 100,
    required: false
  })
  progress?: number;

  @ApiProperty({
    description: '最后推送结果',
    type: [FilePushResultDto],
    required: false
  })
  lastPushResults?: FilePushResultDto[];
} 