import { Injectable, Logger } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import * as zlib from 'zlib';
import axios from 'axios';
import { FilePushTargetDto, FilePushResultDto, FileChunkingDto } from './dto/generate-tiles.dto';

/**
 * 文件推送服务
 * 支持将生成的点云文件推送到指定位置
 */
@Injectable()
export class FilePushService {
  private readonly logger = new Logger(FilePushService.name);

  /**
   * 推送文件到多个目标
   * @param filePaths 要推送的文件路径列表
   * @param targets 推送目标配置列表
   * @param options 推送选项
   * @returns 推送结果列表
   */
  async pushFiles(
    filePaths: string[],
    targets: FilePushTargetDto[],
    options: {
      enableGzip?: boolean;
      chunking?: FileChunkingDto;
      callbackUrl?: string;
    } = {}
  ): Promise<FilePushResultDto[]> {
    const results: FilePushResultDto[] = [];

    for (const target of targets) {
      try {
        this.logger.log(`开始推送文件到目标: ${target.target}`);
        const startTime = Date.now();

        let filesToPush = filePaths;
        let totalSize = 0;

        // 处理文件分片
        if (options.chunking?.enabled) {
          filesToPush = await this.chunkFiles(filePaths, options.chunking);
        }

        // 处理gzip压缩
        if (options.enableGzip) {
          filesToPush = await this.compressFiles(filesToPush);
        }

        // 计算总文件大小
        for (const filePath of filesToPush) {
          if (fs.existsSync(filePath)) {
            totalSize += fs.statSync(filePath).size;
          }
        }

        // 根据目标类型执行推送
        await this.pushToTarget(filesToPush, target);

        const duration = Date.now() - startTime;
        
        results.push({
          target: target.target,
          success: true,
          files: filesToPush.map(fp => path.basename(fp)),
          duration,
          dataSize: totalSize
        });

        this.logger.log(`✅ 文件推送成功: ${target.target}, 耗时: ${duration}ms, 大小: ${(totalSize / 1024 / 1024).toFixed(2)}MB`);

      } catch (error) {
        this.logger.error(`❌ 文件推送失败: ${target.target}`, error.stack);
        
        results.push({
          target: target.target,
          success: false,
          files: [],
          duration: 0,
          dataSize: 0,
          error: error.message
        });
      }
    }

    // 发送回调通知
    if (options.callbackUrl) {
      await this.sendCallback(options.callbackUrl, results);
    }

    return results;
  }

  /**
   * 根据目标类型推送文件
   */
  private async pushToTarget(filePaths: string[], target: FilePushTargetDto): Promise<void> {
    switch (target.type) {
      case 'http':
        await this.pushToHttp(filePaths, target);
        break;
      case 'local':
        await this.pushToLocal(filePaths, target);
        break;
      case 'ftp':
        await this.pushToFtp(filePaths, target);
        break;
      case 'sftp':
        await this.pushToSftp(filePaths, target);
        break;
      case 's3':
        await this.pushToS3(filePaths, target);
        break;
      case 'oss':
        await this.pushToOss(filePaths, target);
        break;
      default:
        throw new Error(`不支持的推送类型: ${target.type}`);
    }
  }

  /**
   * 推送到HTTP接口
   */
  private async pushToHttp(filePaths: string[], target: FilePushTargetDto): Promise<void> {
    const timeout = target.options?.timeout || 60000;
    const retries = target.options?.retries || 3;

    for (const filePath of filePaths) {
      if (!fs.existsSync(filePath)) {
        throw new Error(`文件不存在: ${filePath}`);
      }

      const fileName = path.basename(filePath);
      const fileContent = fs.readFileSync(filePath);
      
      let lastError: Error | null = null;
      
      for (let attempt = 1; attempt <= retries; attempt++) {
        try {
          const headers: Record<string, string> = {
            'Content-Type': 'application/octet-stream',
            'Content-Length': fileContent.length.toString(),
            'X-File-Name': fileName,
            'X-File-Size': fileContent.length.toString()
          };

          // 添加认证头
          if (target.credentials?.token) {
            headers['Authorization'] = `Bearer ${target.credentials.token}`;
          }
          if (target.credentials?.apiKey) {
            headers['X-API-Key'] = target.credentials.apiKey;
          }

          const response = await axios.post(target.target, fileContent, {
            headers,
            timeout
          });

          if (response.status >= 200 && response.status < 300) {
            this.logger.log(`✅ HTTP推送成功: ${fileName} -> ${target.target}`);
            break;
          } else {
            throw new Error(`HTTP响应错误: ${response.status} ${response.statusText}`);
          }

        } catch (error) {
          lastError = error;
          this.logger.warn(`⚠️  HTTP推送失败 (尝试 ${attempt}/${retries}): ${error.message}`);
          
          if (attempt < retries) {
            // 指数退避重试
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
          }
        }
      }

      if (lastError) {
        throw lastError;
      }
    }
  }

  /**
   * 推送到本地目录
   */
  private async pushToLocal(filePaths: string[], target: FilePushTargetDto): Promise<void> {
    const targetDir = target.target;
    
    // 确保目标目录存在
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
    }

    for (const filePath of filePaths) {
      if (!fs.existsSync(filePath)) {
        throw new Error(`文件不存在: ${filePath}`);
      }

      const fileName = path.basename(filePath);
      const targetPath = path.join(targetDir, fileName);
      
      // 复制文件
      fs.copyFileSync(filePath, targetPath);
      this.logger.log(`✅ 本地推送成功: ${fileName} -> ${targetPath}`);
    }
  }

  /**
   * 推送到FTP服务器 (占位实现)
   */
  private async pushToFtp(filePaths: string[], target: FilePushTargetDto): Promise<void> {
    // TODO: 实现FTP推送
    // 可以使用 'ftp' 或 'basic-ftp' 库
    throw new Error('FTP推送功能待实现');
  }

  /**
   * 推送到SFTP服务器 (占位实现)
   */
  private async pushToSftp(filePaths: string[], target: FilePushTargetDto): Promise<void> {
    // TODO: 实现SFTP推送
    // 可以使用 'ssh2-sftp-client' 库
    throw new Error('SFTP推送功能待实现');
  }

  /**
   * 推送到AWS S3 (占位实现)
   */
  private async pushToS3(filePaths: string[], target: FilePushTargetDto): Promise<void> {
    // TODO: 实现S3推送
    // 可以使用 '@aws-sdk/client-s3' 库
    throw new Error('S3推送功能待实现');
  }

  /**
   * 推送到阿里云OSS (占位实现)
   */
  private async pushToOss(filePaths: string[], target: FilePushTargetDto): Promise<void> {
    // TODO: 实现OSS推送
    // 可以使用 'ali-oss' 库
    throw new Error('OSS推送功能待实现');
  }

  /**
   * 文件分片处理
   */
  private async chunkFiles(filePaths: string[], chunking: FileChunkingDto): Promise<string[]> {
    const chunkedFiles: string[] = [];
    const chunkSizeBytes = (chunking.chunkSize || 10) * 1024 * 1024; // 转换为字节

    for (const filePath of filePaths) {
      if (!fs.existsSync(filePath)) {
        continue;
      }

      const fileSize = fs.statSync(filePath).size;
      
      if (fileSize <= chunkSizeBytes) {
        // 文件小于分片大小，直接使用原文件
        chunkedFiles.push(filePath);
        continue;
      }

      // 需要分片
      const fileName = path.basename(filePath, path.extname(filePath));
      const fileExt = path.extname(filePath);
      const fileDir = path.dirname(filePath);
      
      const fileContent = fs.readFileSync(filePath);
      const totalChunks = Math.ceil(fileSize / chunkSizeBytes);

      this.logger.log(`📦 开始分片文件: ${filePath}, 大小: ${(fileSize / 1024 / 1024).toFixed(2)}MB, 分片数: ${totalChunks}`);

      for (let i = 0; i < totalChunks; i++) {
        const start = i * chunkSizeBytes;
        const end = Math.min(start + chunkSizeBytes, fileSize);
        const chunkContent = fileContent.slice(start, end);
        
        const chunkFileName = (chunking.namePattern || '{name}_chunk_{index}{ext}')
          .replace('{name}', fileName)
          .replace('{index}', (i + 1).toString().padStart(3, '0'))
          .replace('{ext}', fileExt);
        
        const chunkFilePath = path.join(fileDir, chunkFileName);
        fs.writeFileSync(chunkFilePath, chunkContent);
        chunkedFiles.push(chunkFilePath);
        
        this.logger.log(`📦 生成分片: ${chunkFileName}, 大小: ${(chunkContent.length / 1024 / 1024).toFixed(2)}MB`);
      }
    }

    return chunkedFiles;
  }

  /**
   * 文件压缩处理
   */
  private async compressFiles(filePaths: string[]): Promise<string[]> {
    const compressedFiles: string[] = [];

    for (const filePath of filePaths) {
      if (!fs.existsSync(filePath)) {
        continue;
      }

      const compressedPath = filePath + '.gz';
      const fileContent = fs.readFileSync(filePath);
      
      // 使用gzip压缩
      const compressed = zlib.gzipSync(fileContent, { level: 9 });
      fs.writeFileSync(compressedPath, compressed);
      
      const originalSize = fileContent.length;
      const compressedSize = compressed.length;
      const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);
      
      this.logger.log(`🗜️  文件压缩完成: ${path.basename(filePath)}, 压缩率: ${compressionRatio}%, ${(originalSize / 1024 / 1024).toFixed(2)}MB -> ${(compressedSize / 1024 / 1024).toFixed(2)}MB`);
      
      compressedFiles.push(compressedPath);
    }

    return compressedFiles;
  }

  /**
   * 发送回调通知
   */
  private async sendCallback(callbackUrl: string, results: FilePushResultDto[]): Promise<void> {
    try {
      const payload = {
        timestamp: new Date().toISOString(),
        results,
        summary: {
          total: results.length,
          success: results.filter(r => r.success).length,
          failed: results.filter(r => !r.success).length,
          totalDataSize: results.reduce((sum, r) => sum + r.dataSize, 0)
        }
      };

      await axios.post(callbackUrl, payload, {
        headers: { 'Content-Type': 'application/json' },
        timeout: 10000
      });

      this.logger.log(`✅ 回调通知发送成功: ${callbackUrl}`);
    } catch (error) {
      this.logger.error(`❌ 回调通知发送失败: ${callbackUrl}`, error.message);
    }
  }

  /**
   * 清理临时文件
   */
  async cleanupTempFiles(filePaths: string[]): Promise<void> {
    for (const filePath of filePaths) {
      try {
        if (fs.existsSync(filePath) && (filePath.endsWith('.gz') || filePath.includes('_chunk_'))) {
          fs.unlinkSync(filePath);
          this.logger.log(`🗑️  清理临时文件: ${path.basename(filePath)}`);
        }
      } catch (error) {
        this.logger.warn(`⚠️  清理临时文件失败: ${filePath}`, error.message);
      }
    }
  }

  /**
   * 获取文件大小信息
   */
  getFileSizes(filePaths: string[]): Record<string, number> {
    const sizes: Record<string, number> = {};
    
    for (const filePath of filePaths) {
      if (fs.existsSync(filePath)) {
        const fileName = path.basename(filePath);
        sizes[fileName] = fs.statSync(filePath).size;
      }
    }
    
    return sizes;
  }
} 