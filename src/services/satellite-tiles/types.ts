/**
 * 卫星3D Tiles点云相关类型定义
 */

/**
 * 3D笛卡尔坐标
 */
export interface ICartesianPosition {
  x: number;
  y: number;
  z: number;
}

/**
 * 地理坐标
 */
export interface IGeographicPosition {
  longitude: number;  // 经度(度)
  latitude: number;   // 纬度(度)
  altitude: number;   // 高度(米)
}

/**
 * RGB颜色
 */
export interface IRGBColor {
  r: number;  // 红色分量 (0-255)
  g: number;  // 绿色分量 (0-255)
  b: number;  // 蓝色分量 (0-255)
}

/**
 * 卫星属性信息
 */
export interface ISatelliteProperties {
  noradId: number;
  cosparId?: string;
  orbitClass: string;      // LEO, MEO, GEO, HEO等
  inclination?: number;    // 轨道倾角
  period?: number;         // 轨道周期(分钟)
  apogee?: number;         // 远地点高度(km)
  perigee?: number;        // 近地点高度(km)
  eccentricity?: number;   // 偏心率
}

/**
 * 卫星点云数据项
 */
export interface ISatellitePointCloudItem {
  id: string;                           // 卫星ID (通常是NORAD ID)
  name: string;                         // 卫星名称
  position: ICartesianPosition;         // 笛卡尔坐标位置
  geographic: IGeographicPosition;      // 地理坐标位置
  properties: ISatelliteProperties;     // 卫星属性
  color: IRGBColor;                     // 显示颜色
}

/**
 * 卫星点云数据集
 */
export interface ISatellitePointCloud {
  generatedAt: string;                  // 生成时间 (ISO 8601格式)
  totalSatellites: number;              // 卫星总数
  calculationTime: string;              // 位置计算时间 (ISO 8601格式)
  satellites: ISatellitePointCloudItem[]; // 卫星数据列表
  statistics: {
    orbitClassDistribution: Record<string, number>; // 轨道类型分布
    altitudeRange: {
      min: number;
      max: number;
      average: number;
    };
  };
}

/**
 * 点云生成元数据
 */
export interface IPointCloudMetadata {
  lastGenerated: string;                // 最后生成时间
  nextScheduled: string;                // 下次计划生成时间
  totalSatellites: number;              // 卫星总数
  generationDuration: number;           // 生成耗时(毫秒)
  status: 'idle' | 'generating' | 'error'; // 生成状态
  errorMessage?: string;                // 错误信息
  version: string;                      // 数据版本
}

/**
 * 轨道类型颜色映射
 */
export interface IOrbitClassColorMap {
  [orbitClass: string]: IRGBColor;
}

/**
 * 点云生成配置
 */
export interface IPointCloudGenerationConfig {
  outputPath: string;                   // 输出路径
  colorByOrbitClass: boolean;           // 是否按轨道类型着色
  includeStatistics: boolean;           // 是否包含统计信息
  coordinateSystem: 'cartesian' | 'geographic'; // 坐标系统
  compressionLevel: number;             // 压缩级别 (0-9)
}

/**
 * 卫星TLE数据简化接口
 */
export interface ISatelliteTLEData {
  norad_id: number;
  satellite_name: string;
  cospar_id?: string;
  tle_raw: string;
  epoch: string;
  time: string;
}

/**
 * 点云生成任务状态
 */
export interface IPointCloudGenerationTask {
  id: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime: Date;
  endTime?: Date;
  progress: number;                     // 进度百分比 (0-100)
  totalSatellites: number;
  processedSatellites: number;
  errorMessage?: string;
} 