import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Satellite } from '../entities/satellite.entity';
import { ElasticsearchSatelliteService } from '../elasticsearch/services/elasticsearch.satellite.service';
import { SatelliteQueryDto } from '../elasticsearch/dto/satellite-query.dto';
import { AggregationTaskService } from './aggregation-task.service';

// 扩展SatelliteQueryDto类型，添加排序字段
interface ExtendedSatelliteQueryDto extends SatelliteQueryDto {
  sort_by?: string;
  sort_dir?: 'asc' | 'desc';
}

/**
 * 卫星信息服务
 * 管理本地数据库中的卫星数据
 */
@Injectable()
export class SatelliteService {
  private readonly logger = new Logger(SatelliteService.name);

  constructor(
    @InjectRepository(Satellite)
    private satelliteRepository: Repository<Satellite>,
    private elasticsearchSatelliteService: ElasticsearchSatelliteService,
    private aggregationTaskService: AggregationTaskService,
  ) {}

  /**
   * 根据条件在本地数据库中搜索卫星信息
   * @param query 查询条件
   * @returns 搜索结果
   */
  async searchSatelliteInfoLocal(query: ExtendedSatelliteQueryDto) {
    try {
      this.logger.debug(`开始在本地数据库中查询卫星信息: ${JSON.stringify(query)}`);
      
      // 构建查询
      const qb = this.satelliteRepository.createQueryBuilder('satellite');
      
      // 处理关键词搜索
      if (query.keyword) {
        qb.where(`
          EXISTS (
            SELECT FROM jsonb_array_elements(satellite.satellite_name) AS name
            WHERE (name->>'value')::text ILIKE :keyword
          ) OR EXISTS (
            SELECT FROM jsonb_array_elements(satellite.alternative_name) AS name
            WHERE (name->>'value')::text ILIKE :keyword
          )
        `, { keyword: `%${query.keyword}%` });
      }
      
      // 处理特定字段的精确匹配
      if (query.norad_id) {
        qb.andWhere(`
          EXISTS (
            SELECT FROM jsonb_array_elements(satellite.norad_id) AS item
            WHERE (item->>'value')::text = :norad_id
          )
        `, { norad_id: query.norad_id });
      }
      
      if (query.cospar_id) {
        qb.andWhere(`
          EXISTS (
            SELECT FROM jsonb_array_elements(satellite.cospar_id) AS item
            WHERE (item->>'value')::text = :cospar_id
          )
        `, { cospar_id: query.cospar_id });
      }
      
      if (query.status) {
        qb.andWhere(`
          EXISTS (
            SELECT FROM jsonb_array_elements(satellite.status) AS item
            WHERE (item->>'value')::text ILIKE :status
          )
        `, { status: `%${query.status}%` });
      }
      
      // 计算总记录数
      const totalCount = await qb.getCount();
      
      // 分页处理
      const page = query.page || 1;
      const limit = query.limit || 10;
      const offset = (page - 1) * limit;
      
      // 排序
      if (query.sort_by && query.sort_dir) {
        // 注意：由于JSONB类型的特殊性，这里的排序是基于第一个元素的value
        // 可能需要根据实际需求进行调整
        qb.orderBy(`(
          SELECT (jsonb_array_elements(satellite.${query.sort_by})->>'value')::text 
          LIMIT 1
        )`, query.sort_dir === 'asc' ? 'ASC' : 'DESC');
      } else {
        // 默认排序
        qb.orderBy('satellite.id', 'DESC');
      }
      
      // 执行分页查询
      const satellites = await qb
        .skip(offset)
        .take(limit)
        .getMany();
      
      // 返回符合 SatelliteSearchResponse 格式的结果
      return {
        total: totalCount,
        page,
        limit,
        hits: satellites,
      };
    } catch (error) {
      this.logger.error(`本地数据库查询卫星信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 从ES同步所有卫星数据到本地数据库
   * 注意: 这个操作可能比较耗时，建议在后台执行
   */
  async syncAllSatellitesFromES(): Promise<void> {
    try {
      this.logger.log('开始从ES同步所有卫星数据到本地数据库');
      
      // 获取所有卫星名称
      const satelliteNames = await this.elasticsearchSatelliteService.getSatelliteNames();
      this.logger.log(`共获取到 ${satelliteNames.length} 个卫星名称，开始同步`);
      
      // 每批处理的卫星数量
      const batchSize = 50;
      let processedCount = 0;
      
      // 批量处理卫星数据
      for (let i = 0; i < satelliteNames.length; i += batchSize) {
        const batchNames = satelliteNames.slice(i, i + batchSize);
        await this.syncSatelliteBatch(batchNames);
        
        processedCount += batchNames.length;
        this.logger.log(`已同步 ${processedCount} / ${satelliteNames.length} 个卫星数据`);
      }
      
      this.logger.log('所有卫星数据同步完成');
    } catch (error) {
      this.logger.error(`同步卫星数据失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 批量同步卫星数据
   * @param satelliteNames 卫星名称列表
   */
  private async syncSatelliteBatch(satelliteNames: string[]): Promise<void> {
    for (const name of satelliteNames) {
      try {
        await this.syncSatelliteByName(name);
      } catch (error) {
        this.logger.error(`同步卫星 "${name}" 失败: ${error.message}`);
        // 继续处理下一个卫星
      }
    }
  }

  /**
   * 同步单个卫星数据
   * @param satelliteName 卫星名称
   */
  async syncSatelliteByName(satelliteName: string): Promise<void> {
    try {
      this.logger.debug(`开始同步卫星 "${satelliteName}" 的数据`);
      
      // 查询ES获取卫星信息
      const result = await this.elasticsearchSatelliteService.searchSatelliteInfo({
        keyword: satelliteName,
        similarity_threshold: 0.8, // 使用较高的相似度阈值确保精确匹配
      });
      
      if (!result || !result.hits || result.hits.length === 0) {
        this.logger.warn(`未找到卫星 "${satelliteName}" 的数据`);
        return;
      }
      
      // 处理每个查询结果
      for (const hit of result.hits) {
        // 生成文档键
        const docKey = this.generateDocumentKey(hit);
        
        // 检查数据库中是否已存在该卫星
        const existingSatellite = await this.findSatelliteByKey(docKey);
        
        if (existingSatellite) {
          // 更新现有记录
          this.logger.debug(`更新卫星 "${satelliteName}" 的数据`);
          
          // 保留原ID
          hit.id = existingSatellite.id;
          
          await this.satelliteRepository.save(hit);
        } else {
          // 创建新记录
          this.logger.debug(`创建卫星 "${satelliteName}" 的新记录`);
          await this.satelliteRepository.save(hit);
        }
      }
      
      this.logger.debug(`卫星 "${satelliteName}" 的数据同步完成`);
    } catch (error) {
      this.logger.error(`同步卫星 "${satelliteName}" 失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 根据文档键查找卫星
   * @param docKey 文档键
   * @returns 卫星实体或undefined
   */
  private async findSatelliteByKey(docKey: string): Promise<Satellite | undefined> {
    // 解析文档键
    const [noradId, cosparId, name] = docKey.split('|');
    
    // 构建查询
    const qb = this.satelliteRepository.createQueryBuilder('satellite');
    
    // NORAD ID 匹配条件
    if (noradId && noradId !== 'null') {
      qb.andWhere(`
        EXISTS (
          SELECT FROM jsonb_array_elements(satellite.norad_id) AS item
          WHERE (item->>'value')::text = :noradId
        )
      `, { noradId });
    }
    
    // COSPAR ID 匹配条件
    if (cosparId && cosparId !== 'null') {
      qb.andWhere(`
        EXISTS (
          SELECT FROM jsonb_array_elements(satellite.cospar_id) AS item
          WHERE (item->>'value')::text = :cosparId
        )
      `, { cosparId });
    }
    
    // 卫星名称匹配条件
    if (name && name !== 'null') {
      qb.andWhere(`
        EXISTS (
          SELECT FROM jsonb_array_elements(satellite.satellite_name) AS item
          WHERE (item->>'value')::text = :name
        )
      `, { name });
    }
    
    // 执行查询
    const result = await qb.getOne();
    return result || undefined;
  }

  /**
   * 从文档生成唯一键
   * @param doc 卫星文档
   * @returns 文档键
   */
  private generateDocumentKey(doc: any): string {
    // 提取主要标识符
    let noradId = 'null';
    let cosparId = 'null';
    let name = 'null';
    
    // 提取 NORAD ID
    if (doc.norad_id && doc.norad_id.length > 0 && doc.norad_id[0].value) {
      noradId = doc.norad_id[0].value;
    }
    
    // 提取 COSPAR ID
    if (doc.cospar_id && doc.cospar_id.length > 0 && doc.cospar_id[0].value) {
      cosparId = doc.cospar_id[0].value;
    }
    
    // 提取卫星名称
    if (doc.satellite_name && doc.satellite_name.length > 0 && doc.satellite_name[0].value) {
      name = doc.satellite_name[0].value;
    }
    
    // 生成文档键
    return `${noradId}|${cosparId}|${name}`;
  }
  
  /**
   * 获取所有卫星名称
   * 从本地数据库中读取
   */
  async getSatelliteNamesLocal(): Promise<string[]> {
    try {
      this.logger.debug('从本地数据库获取卫星名称集合');
      
      // 查询并提取所有不同的卫星名称
      const result = await this.satelliteRepository.query(`
        SELECT DISTINCT jsonb_array_elements(satellite_name)->>'value' as name
        FROM satellites
        WHERE jsonb_array_length(satellite_name) > 0
        UNION
        SELECT DISTINCT jsonb_array_elements(alternative_name)->>'value' as name
        FROM satellites
        WHERE jsonb_array_length(alternative_name) > 0
      `);
      
      // 过滤并排序
      const names = result
        .map((item: { name: string }) => item.name)
        .filter((name: string) => name && name !== 'None' && name !== 'null' && name !== 'undefined')
        .sort();
      
      this.logger.debug(`从本地数据库获取到 ${names.length} 个卫星名称`);
      return names;
    } catch (error) {
      this.logger.error(`从本地数据库获取卫星名称失败: ${error.message}`, error.stack);
      throw error;
    }
  }
  
  /**
   * 获取所有卫星状态
   * 从本地数据库中读取
   */
  async getSatelliteStatusesLocal(): Promise<Array<{en: string, zh: string}>> {
    try {
      this.logger.debug('从本地数据库获取卫星状态集合');
      
      // 查询并提取所有不同的卫星状态
      const result = await this.satelliteRepository.query(`
        SELECT DISTINCT jsonb_array_elements(status)->>'value' as status
        FROM satellites
        WHERE jsonb_array_length(status) > 0
      `);
      
      // 状态中英文映射
      const statusMap: Record<string, string> = {
        'ACTIVE': '活跃',
        'ALIVE': '活跃',
        'OPERATIONAL': '运行中',
        'DEAD': '失效',
        'DECAYED': '已衰减',
        'DEPLOYMENT': '部署',
        'DEPLOYMENT FAILURE': '部署失败',
        'DEPLOYMENT PROHIBITED': '禁止部署',
        'FUTURE': '计划中',
        'IN ORBIT': '在轨道上',
        'LAUNCH FAILURE': '发射失败',
        'NO SIGNAL': '无信号',
        'NOT LAUNCHED': '未发射',
        'ON SPACECRAFT': '在航天器上',
        'REENTRY': '再入大气层',
        'CANCELLED': '已取消'
      };
      
      // 处理状态并添加中文翻译
      const statuses = result
        .map((item: { status: string }) => {
          const status = item.status;
          if (!status || status === 'None' || status === 'null' || status === 'undefined') {
            return null;
          }
          
          const normalizedStatus = status.toUpperCase();
          let zh = statusMap[normalizedStatus] || '未知';
          
          return { en: status, zh };
        })
        .filter((item: any) => item !== null)
        .sort((a: {en: string}, b: {en: string}) => a.en.localeCompare(b.en));
      
      this.logger.debug(`从本地数据库获取到 ${statuses.length} 个卫星状态`);
      return statuses;
    } catch (error) {
      this.logger.error(`从本地数据库获取卫星状态失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 卫星信息聚合
   * 将ES中来自不同索引的卫星信息聚合并存储到本地数据库
   * @param options 聚合选项
   * @returns 聚合结果，包含成功聚合的卫星数量
   */
  async aggregateSatelliteData(options?: { 
    keyword?: string; 
    limit?: number;
    saveToDatabase?: boolean;
  }): Promise<{ success: boolean; totalAggregated: number; message: string }> {
    let task: any = null;
    
    try {
      this.logger.log('开始聚合卫星数据');
      
      const saveToDatabase = options?.saveToDatabase !== false; // 默认保存到数据库
      const limit = options?.limit || 0; // 默认值为0，表示不限制数量，处理所有数据
      const keyword = options?.keyword || ''; // 可选的关键词搜索
      
      // 创建聚合任务记录
      let taskType = 'full';
      if (limit > 0) {
        taskType = 'partial';
      }
      if (keyword) {
        taskType = 'keyword';
      }
      
      task = await this.aggregationTaskService.createTask({
        task_type: taskType,
        parameters: options || {},
      });
      
      // 更新任务状态为运行中
      await this.aggregationTaskService.startTask(task.id);
      this.logger.log(`创建聚合任务记录，ID: ${task.id}, 类型: ${taskType}`);
      
      // 定义要查询的索引
      const indices = [
        'satsinfo_gunter',
        'satsinfo_n2yo',
        'satsinfo_nanosats',
        'satsinfo_satnogs',
        'satsinfo_ucs'
      ];
      
      // 构建查询
      const query: any = {
        bool: {
          must: [] as any[]
        }
      };
      
      // 如果提供了关键词，添加到查询条件
      if (keyword) {
        query.bool.must.push({
          multi_match: {
            query: keyword,
            fields: [
              'satellite_name^3',
              'alternative_name^2',
              'cospar_id^2',
              'norad_id^2',
              'owner',
              'purpose',
              'detailed_purpose',
              'country_of_registry'
            ],
            type: 'best_fields',
            fuzziness: 'AUTO'
          }
        });
      }
      
      // 所有搜索结果
      let allSearchResults: any[] = [];
      // 批次大小
      const batchSize = 1000;
      
      // 使用scrollSearch方法获取所有数据，不受10,000条记录限制
      this.logger.log('使用Scroll API获取所有卫星数据');
      
      for (const index of indices) {
        this.logger.log(`开始处理索引: ${index}`);
        
        try {
          // 使用scrollSearch方法获取所有数据
          const indexResults = await this.elasticsearchSatelliteService.scrollSearch({
            indices: [index],
            query,
            size: batchSize
          });
          
          this.logger.log(`从索引 ${index} 获取了 ${indexResults.length} 条记录`);
          
          // 将结果添加到总结果中
          allSearchResults = allSearchResults.concat(indexResults);
          
          // 如果设置了限制，并且已经达到限制，则停止获取更多数据
          if (limit > 0 && allSearchResults.length >= limit) {
            this.logger.log(`已达到用户设置的限制 ${limit}，停止获取更多数据`);
            // 截取到限制数量
            allSearchResults = allSearchResults.slice(0, limit);
            break;
          }
          
          // 更新任务进度
          const progress = limit > 0 ? Math.min(50, (allSearchResults.length / limit) * 50) : 25;
          await this.aggregationTaskService.updateTaskProgress(task.id, progress, allSearchResults.length);
        } catch (error) {
          this.logger.error(`处理索引 ${index} 时出错: ${error.message}`, error.stack);
          // 继续处理其他索引
        }
      }
      
      this.logger.log(`所有索引处理完成，总共获取 ${allSearchResults.length} 条记录`);
      
      // 如果没有获取到记录，直接返回
      if (allSearchResults.length === 0) {
        this.logger.log('未找到符合条件的卫星数据');
        await this.aggregationTaskService.completeTask(task.id, 0);
        return { success: true, totalAggregated: 0, message: '未找到符合条件的卫星数据' };
      }
      
      // 对结果进行分组和合并
      this.logger.log('开始对卫星数据进行分组和合并');
      
      // 卫星组数组，每个组包含来自不同数据源的同一颗卫星的记录
      const satelliteGroups: any[][] = [];
      
      // 处理每条卫星记录
      for (const record of allSearchResults) {
        const source = record._source;
        const index = record._index;
        
        // 添加数据源信息
        source._index = index;
        
        // 提取标识信息
        const noradId = String(source.norad_id || '').trim();
        const cosparId = String(source.cospar_id || '').trim();
        const satelliteName = String(source.satellite_name || '').trim();
        
        // 标记是否已找到匹配的组
        let foundMatch = false;
        
        // 遍历现有组，尝试找到匹配
        for (let i = 0; i < satelliteGroups.length; i++) {
          const group = satelliteGroups[i];
          
          // 检查是否可以与当前组匹配
          if (this.canMergeWithGroup(source, group)) {
            // 找到匹配，添加到该组
            group.push(source);
            foundMatch = true;
            break;
          }
        }
        
        // 如果没有找到匹配的组，创建一个新组
        if (!foundMatch) {
          satelliteGroups.push([source]);
        }
        
        // 每处理1000条记录输出一次日志
        if (satelliteGroups.length % 1000 === 0) {
          this.logger.log(`已处理 ${satelliteGroups.length} 组卫星数据`);
        }
      }
      
      this.logger.log(`根据标识字段分组后，共有 ${satelliteGroups.length} 组卫星数据`);
      
      // 聚合结果数组
      const aggregatedSatellites: any[] = [];
      
      // 聚合每个组的数据
      for (let groupIndex = 0; groupIndex < satelliteGroups.length; groupIndex++) {
        const groupRecords = satelliteGroups[groupIndex];
        
        // 每处理1000组输出一次日志
        if ((groupIndex + 1) % 1000 === 0) {
          this.logger.log(`已聚合 ${groupIndex + 1}/${satelliteGroups.length} 组卫星数据`);
        }
        
        // 创建聚合记录模板
        const aggregatedSatellite: any = {
          satellite_name: [],
          alternative_name: [],
          cospar_id: [],
          country_of_registry: [],
          owner: [],
          users: [],
          status: [],
          norad_id: [],
          launch_info: [],
          orbit_info: [],
          mass_kg: [],
          power_watts: [],
          lifetime_years: [],
          contractor: [],
          purpose: [],
          detailed_purpose: [],
          payload: [],
          payload_description: [],
          update_time: [],
          _sources: []
        };
        
        // 字段值记录，用于去重
        const fieldValues = new Map<string, Set<string>>();
        
        // 处理每条记录
        for (const record of groupRecords) {
          // 提取数据源（索引名称）
          const source = record._index.replace('satsinfo_', '');
          
          // 添加到数据源列表
          if (!aggregatedSatellite._sources.includes(source)) {
            aggregatedSatellite._sources.push(source);
          }
          
          // 处理每个字段
          for (const field of Object.keys(aggregatedSatellite)) {
            // 跳过元数据字段
            if (field === '_sources' || field.startsWith('_')) continue;
            
            // 获取字段值
            const value = record[field];
            
            // 如果字段值不为空
            if (value !== undefined && value !== null && value !== '') {
              // 确保字段值集合存在
              if (!fieldValues.has(field)) {
                fieldValues.set(field, new Set<string>());
              }
              
              // 将字段值转换为JSON字符串，用于去重
              const valueStr = JSON.stringify(value);
              
              // 如果该值尚未添加
              if (!fieldValues.get(field)!.has(valueStr)) {
                // 添加到字段值集合
                fieldValues.get(field)!.add(valueStr);
                
                // 添加到聚合记录
                aggregatedSatellite[field].push({
                  value,
                  sources: [source]
                });
              } else {
                // 如果该值已存在，添加数据源
                const existingItem = aggregatedSatellite[field].find((item: any) => 
                  JSON.stringify(item.value) === valueStr
                );
                
                if (existingItem && !existingItem.sources.includes(source)) {
                  existingItem.sources.push(source);
                }
              }
            }
          }
        }
        
        // 添加到聚合结果
        aggregatedSatellites.push(aggregatedSatellite);
      }
      
      this.logger.log(`聚合完成，共 ${aggregatedSatellites.length} 条卫星数据`);
      
      // 在保存结果之前，更新任务进度
      await this.aggregationTaskService.updateTaskProgress(task.id, 75, allSearchResults.length);
      
      // 如果不需要保存到数据库，直接返回结果
      if (!saveToDatabase) {
        return {
          success: true,
          totalAggregated: aggregatedSatellites.length,
          message: `成功聚合 ${aggregatedSatellites.length} 条卫星数据（未保存到数据库）`
        };
      }
      
      // 保存到数据库
      this.logger.log(`开始将聚合结果保存到数据库`);
      
      // 批量保存的大小
      const saveBatchSize = 500;
      let savedCount = 0;
      
      // 分批保存数据
      for (let i = 0; i < aggregatedSatellites.length; i += saveBatchSize) {
        const batch = aggregatedSatellites.slice(i, i + saveBatchSize);
        
        // 创建实体对象
        const entities = batch.map(data => {
          const entity = new Satellite();
          
          // 复制字段
          for (const field of Object.keys(data)) {
            (entity as any)[field] = data[field];
          }
          
          return entity;
        });
        
        // 保存实体
        await this.satelliteRepository.save(entities);
        
        savedCount += batch.length;
        this.logger.log(`已保存 ${savedCount}/${aggregatedSatellites.length} 条卫星数据到数据库`);
      }
      
      this.logger.log(`成功将 ${savedCount} 条聚合后的卫星数据保存到数据库`);
      
      // 在分组的最后，更新任务状态为已完成
      this.logger.log(`卫星数据聚合完成，共处理 ${allSearchResults.length} 条记录，聚合为 ${aggregatedSatellites.length} 条记录`);
      await this.aggregationTaskService.completeTask(task.id, aggregatedSatellites.length);
      
      return {
        success: true,
        totalAggregated: aggregatedSatellites.length,
        message: `成功聚合 ${aggregatedSatellites.length} 条卫星数据`
      };
    } catch (error) {
      this.logger.error(`聚合卫星数据失败: ${error.message}`, error.stack);
      
      // 如果任务ID存在，更新任务状态为失败
      if (task && task.id) {
        await this.aggregationTaskService.failTask(task.id, error.message);
      }
      
      throw new Error(`聚合卫星数据失败: ${error.message}`);
    }
  }

  /**
   * 判断一条卫星记录是否可以与一个组合并
   * 按照优先级 norad_id > cospar_id > satellite_name 进行匹配
   * @param record 卫星记录
   * @param group 卫星组
   * @returns 是否可以合并
   */
  private canMergeWithGroup(record: any, group: any[]): boolean {
    if (!record || !group || group.length === 0) return false;
    
    // 提取当前记录的标识信息
    const recordNoradId = String(record.norad_id || '').trim();
    const recordCosparId = String(record.cospar_id || '').trim();
    const recordName = String(record.satellite_name || '').trim();
    
    // 检查组中是否有任何记录的norad_id与当前记录的norad_id不同但都不为空
    // 如果有，则不能合并
    for (const groupRecord of group) {
      const groupNoradId = String(groupRecord.norad_id || '').trim();
      
      // 如果两条记录的norad_id都不为空但不相同，则不能合并
      if (recordNoradId && groupNoradId && recordNoradId !== groupNoradId) {
        return false;
      }
    }
    
    // 检查组中是否有任何记录的cospar_id与当前记录的cospar_id不同但都不为空
    // 如果有，则不能合并
    for (const groupRecord of group) {
      const groupNoradId = String(groupRecord.norad_id || '').trim();
      const groupCosparId = String(groupRecord.cospar_id || '').trim();
      
      // 如果两条记录的norad_id都为空，但cospar_id都不为空且不相同，则不能合并
      if ((!recordNoradId || !groupNoradId) && 
          recordCosparId && groupCosparId && 
          recordCosparId !== groupCosparId) {
        return false;
      }
    }
    
    // 记录组中是否有任何记录的norad_id为空
    let anyGroupNoradIdEmpty = false;
    // 记录组中是否有任何记录的cospar_id为空
    let anyGroupCosparIdEmpty = false;
    
    // 遍历组中的每条记录进行匹配
    for (const groupRecord of group) {
      const groupNoradId = String(groupRecord.norad_id || '').trim();
      const groupCosparId = String(groupRecord.cospar_id || '').trim();
      const groupName = String(groupRecord.satellite_name || '').trim();
      
      // 更新空值标记
      if (!groupNoradId) anyGroupNoradIdEmpty = true;
      if (!groupCosparId) anyGroupCosparIdEmpty = true;
      
      // 规则1: 如果两条记录的norad_id都不为空
      if (recordNoradId && groupNoradId) {
        // 如果norad_id相同，则可以合并
        if (recordNoradId === groupNoradId) {
          return true;
        }
      }
    }
    
    // 如果当前记录的norad_id为空或者组中有任何记录的norad_id为空，则检查cospar_id
    if (!recordNoradId || anyGroupNoradIdEmpty) {
      for (const groupRecord of group) {
        const groupNoradId = String(groupRecord.norad_id || '').trim();
        const groupCosparId = String(groupRecord.cospar_id || '').trim();
        
        // 如果当前组记录的norad_id不为空且当前记录的norad_id也不为空，但值不同，则跳过
        if (groupNoradId && recordNoradId && groupNoradId !== recordNoradId) {
          continue;
        }
        
        // 规则2: 如果两条记录的cospar_id都不为空
        if (recordCosparId && groupCosparId) {
          // 如果cospar_id相同，则可以合并
          if (recordCosparId === groupCosparId) {
            return true;
          }
        }
      }
    }
    
    // 如果当前记录的cospar_id为空或者组中有任何记录的cospar_id为空，则检查satellite_name
    if ((!recordCosparId || anyGroupCosparIdEmpty) && (!recordNoradId || anyGroupNoradIdEmpty)) {
      for (const groupRecord of group) {
        const groupNoradId = String(groupRecord.norad_id || '').trim();
        const groupCosparId = String(groupRecord.cospar_id || '').trim();
        const groupName = String(groupRecord.satellite_name || '').trim();
        
        // 如果当前组记录的norad_id不为空且当前记录的norad_id也不为空，但值不同，则跳过
        if (groupNoradId && recordNoradId && groupNoradId !== recordNoradId) {
          continue;
        }
        
        // 如果当前组记录的cospar_id不为空且当前记录的cospar_id也不为空，但值不同，则跳过
        if (groupCosparId && recordCosparId && groupCosparId !== recordCosparId) {
          continue;
        }
        
        // 规则3: 如果两条记录的satellite_name都不为空
        if (recordName && groupName) {
          // 如果satellite_name相同，则可以合并
          if (recordName === groupName) {
            return true;
          }
        }
      }
    }
    
    // 如果没有找到匹配，则不能合并
    return false;
  }

  /**
   * 根据NORAD ID查找卫星
   * @param noradId NORAD ID
   * @returns 卫星实体或undefined
   */
  private async findSatelliteByNoradId(noradId: string): Promise<Satellite | undefined> {
    if (!noradId) return undefined;
    
    const qb = this.satelliteRepository.createQueryBuilder('satellite');
    qb.where(`
      EXISTS (
        SELECT FROM jsonb_array_elements(satellite.norad_id) AS item
        WHERE (item->>'value')::text = :noradId
      )
    `, { noradId });
    
    const result = await qb.getOne();
    return result || undefined;
  }

  /**
   * 根据COSPAR ID查找卫星
   * @param cosparId COSPAR ID
   * @returns 卫星实体或undefined
   */
  private async findSatelliteByCosparId(cosparId: string): Promise<Satellite | undefined> {
    if (!cosparId) return undefined;
    
    const qb = this.satelliteRepository.createQueryBuilder('satellite');
    qb.where(`
      EXISTS (
        SELECT FROM jsonb_array_elements(satellite.cospar_id) AS item
        WHERE (item->>'value')::text = :cosparId
      )
    `, { cosparId });
    
    const result = await qb.getOne();
    return result || undefined;
  }

  /**
   * 根据卫星名称查找卫星
   * @param name 卫星名称
   * @returns 卫星实体或undefined
   */
  private async findSatelliteByName(name: string): Promise<Satellite | undefined> {
    if (!name) return undefined;
    
    const qb = this.satelliteRepository.createQueryBuilder('satellite');
    qb.where(`
      EXISTS (
        SELECT FROM jsonb_array_elements(satellite.satellite_name) AS item
        WHERE (item->>'value')::text = :name
      )
    `, { name });
    
    const result = await qb.getOne();
    return result || undefined;
  }

  /**
   * 从本地数据库中获取单个卫星的详细信息
   * @param id 卫星ID
   * @returns 卫星详细信息
   */
  async getSatelliteById(id: number): Promise<Satellite> {
    try {
      // 确保 id 是有效的数字
      if (id === null || id === undefined || isNaN(id)) {
        this.logger.warn(`无效的卫星ID: ${id}`);
        throw new Error(`无效的卫星ID: ${id}`);
      }

      this.logger.debug(`根据ID获取卫星详细信息: ${id}`);
      
      const satellite = await this.satelliteRepository.findOne({ where: { id } });
      
      if (!satellite) {
        this.logger.warn(`未找到ID为 ${id} 的卫星信息`);
        throw new Error(`未找到ID为 ${id} 的卫星信息`);
      }
      
      this.logger.debug(`成功获取ID为 ${id} 的卫星信息`);
      return satellite;
    } catch (error) {
      this.logger.error(`获取卫星详细信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 清空卫星数据表
   * 在重新聚合前清空数据库中的卫星数据
   * @returns 清理结果
   */
  async clearSatelliteData(): Promise<{ success: boolean; message: string }> {
    try {
      this.logger.log('开始清空卫星数据表');
      
      // 执行清空操作
      const result = await this.satelliteRepository.clear();
      
      this.logger.log('卫星数据表清空完成');
      return { 
        success: true, 
        message: '卫星数据表清空成功'
      };
    } catch (error) {
      this.logger.error(`清空卫星数据表失败: ${error.message}`, error.stack);
      return { 
        success: false, 
        message: `清空失败: ${error.message}`
      };
    }
  }

  /**
   * 测试卫星数据聚合逻辑
   * 创建测试数据并执行聚合操作，验证聚合结果
   */
  async testAggregationLogic(): Promise<{ success: boolean; message: string }> {
    try {
      this.logger.log('开始测试卫星数据聚合逻辑');
      
      // 清空数据库
      await this.clearSatelliteData();
      this.logger.log('已清空数据库，准备创建测试数据');
      
      // 创建测试数据
      const testData = [
        // 测试用例1: 相同的norad_id，不同的名称
        {
          _index: 'satsinfo_n2yo',
          _source: {
            norad_id: '25544',
            cospar_id: '1998-067A',
            satellite_name: 'ISS (ZARYA)',
            status: 'active'
          }
        },
        {
          _index: 'satsinfo_satnogs',
          _source: {
            norad_id: '25544',
            satellite_name: 'International Space Station',
            status: 'operational'
          }
        },
        
        // 测试用例2: 相同的cospar_id，不同的名称，无norad_id
        {
          _index: 'satsinfo_gunter',
          _source: {
            cospar_id: '2020-001A',
            satellite_name: 'Starlink-1',
            status: 'active'
          }
        },
        {
          _index: 'satsinfo_ucs',
          _source: {
            cospar_id: '2020-001A',
            satellite_name: 'Starlink Group 1',
            purpose: 'Communications'
          }
        },
        
        // 测试用例3: 相同的名称，不同的norad_id和cospar_id
        {
          _index: 'satsinfo_n2yo',
          _source: {
            norad_id: '12345',
            cospar_id: '1990-001A',
            satellite_name: 'GPS IIR-1',
            status: 'active'
          }
        },
        {
          _index: 'satsinfo_ucs',
          _source: {
            norad_id: '54321',  // 不同的norad_id，应该不会聚合
            cospar_id: '1990-002A',
            satellite_name: 'GPS IIR-1',
            purpose: 'Navigation'
          }
        },
        
        // 测试用例4: 相同的名称，一个有norad_id，一个没有
        {
          _index: 'satsinfo_nanosats',
          _source: {
            satellite_name: 'CubeSat-1',
            status: 'planned'
          }
        },
        {
          _index: 'satsinfo_gunter',
          _source: {
            norad_id: '98765',
            satellite_name: 'CubeSat-1',
            purpose: 'Technology demonstration'
          }
        },
        
        // 测试用例5: 完全不同的卫星
        {
          _index: 'satsinfo_n2yo',
          _source: {
            norad_id: '11111',
            cospar_id: '2000-001A',
            satellite_name: 'Unique Satellite',
            status: 'active'
          }
        }
      ];
      
      // 模拟ES搜索结果
      const mockSearchResults = testData.map(item => ({
        _index: item._index,
        _source: item._source
      }));
      
      // 保存原始的searchRawData方法
      const originalSearchRawData = this.elasticsearchSatelliteService.searchRawData;
      
      // 保存原始的canMergeWithGroup方法
      const originalCanMergeWithGroup = this.canMergeWithGroup;
      
      // 替换为带有详细日志的canMergeWithGroup方法
      this.canMergeWithGroup = (record: any, group: any[]): boolean => {
        const recordNoradId = String(record.norad_id || '').trim();
        const recordCosparId = String(record.cospar_id || '').trim();
        const recordName = String(record.satellite_name || '').trim();
        
        this.logger.debug(`尝试匹配记录: norad_id=${recordNoradId}, cospar_id=${recordCosparId}, name=${recordName}`);
        
        // 记录组中是否有任何记录的norad_id为空
        let anyGroupNoradIdEmpty = false;
        // 记录组中是否有任何记录的cospar_id为空
        let anyGroupCosparIdEmpty = false;
        
        // 遍历组中的每条记录进行匹配
        for (const groupRecord of group) {
          const groupNoradId = String(groupRecord.norad_id || '').trim();
          const groupCosparId = String(groupRecord.cospar_id || '').trim();
          const groupName = String(groupRecord.satellite_name || '').trim();
          
          this.logger.debug(`与组记录比较: norad_id=${groupNoradId}, cospar_id=${groupCosparId}, name=${groupName}`);
          
          // 更新空值标记
          if (!groupNoradId) anyGroupNoradIdEmpty = true;
          if (!groupCosparId) anyGroupCosparIdEmpty = true;
          
          // 规则1: 如果两条记录的norad_id都不为空
          if (recordNoradId && groupNoradId) {
            // 如果norad_id相同，则可以合并
            if (recordNoradId === groupNoradId) {
              this.logger.debug(`匹配成功: norad_id相同 (${recordNoradId})`);
              return true;
            }
            this.logger.debug(`norad_id不同，继续检查下一条记录`);
            // 如果norad_id不同，则不能合并，即使其他字段匹配
            // 继续检查下一条记录
            continue;
          }
        }
        
        // 如果当前记录的norad_id为空或者组中有任何记录的norad_id为空，则检查cospar_id
        if (!recordNoradId || anyGroupNoradIdEmpty) {
          this.logger.debug(`检查cospar_id匹配: recordNoradId=${recordNoradId}, anyGroupNoradIdEmpty=${anyGroupNoradIdEmpty}`);
          
          for (const groupRecord of group) {
            const groupNoradId = String(groupRecord.norad_id || '').trim();
            const groupCosparId = String(groupRecord.cospar_id || '').trim();
            
            // 如果当前组记录的norad_id不为空且当前记录的norad_id也不为空，但值不同，则跳过
            if (groupNoradId && recordNoradId && groupNoradId !== recordNoradId) {
              this.logger.debug(`norad_id不同，跳过此记录`);
              continue;
            }
            
            // 规则2: 如果两条记录的cospar_id都不为空
            if (recordCosparId && groupCosparId) {
              // 如果cospar_id相同，则可以合并
              if (recordCosparId === groupCosparId) {
                this.logger.debug(`匹配成功: cospar_id相同 (${recordCosparId})`);
                return true;
              }
              this.logger.debug(`cospar_id不同，继续检查下一条记录`);
              // 如果cospar_id不同，则不能合并，即使satellite_name匹配
              // 继续检查下一条记录
              continue;
            }
          }
        }
        
        // 如果当前记录的cospar_id为空或者组中有任何记录的cospar_id为空，则检查satellite_name
        if ((!recordCosparId || anyGroupCosparIdEmpty) && (!recordNoradId || anyGroupNoradIdEmpty)) {
          this.logger.debug(`检查satellite_name匹配: recordCosparId=${recordCosparId}, anyGroupCosparIdEmpty=${anyGroupCosparIdEmpty}`);
          
          for (const groupRecord of group) {
            const groupNoradId = String(groupRecord.norad_id || '').trim();
            const groupCosparId = String(groupRecord.cospar_id || '').trim();
            const groupName = String(groupRecord.satellite_name || '').trim();
            
            // 如果当前组记录的norad_id不为空且当前记录的norad_id也不为空，但值不同，则跳过
            if (groupNoradId && recordNoradId && groupNoradId !== recordNoradId) {
              this.logger.debug(`norad_id不同，跳过此记录`);
              continue;
            }
            
            // 如果当前组记录的cospar_id不为空且当前记录的cospar_id也不为空，但值不同，则跳过
            if (groupCosparId && recordCosparId && groupCosparId !== recordCosparId) {
              this.logger.debug(`cospar_id不同，跳过此记录`);
              continue;
            }
            
            // 规则3: 如果两条记录的satellite_name都不为空
            if (recordName && groupName) {
              // 如果satellite_name相同，则可以合并
              if (recordName === groupName) {
                this.logger.debug(`匹配成功: satellite_name相同 (${recordName})`);
                return true;
              }
              this.logger.debug(`satellite_name不同，不能合并`);
            }
          }
        }
        
        this.logger.debug(`未找到匹配，不能合并`);
        // 如果没有找到匹配，则不能合并
        return false;
      };
      
      // 替换为模拟方法
      this.elasticsearchSatelliteService.searchRawData = async () => {
        return mockSearchResults;
      };
      
      this.logger.log('开始执行聚合操作');
      
      // 执行聚合操作
      const result = await this.aggregateSatelliteData({
        saveToDatabase: true
      });
      
      // 恢复原始方法
      this.elasticsearchSatelliteService.searchRawData = originalSearchRawData;
      this.canMergeWithGroup = originalCanMergeWithGroup;
      
      this.logger.log(`聚合操作完成，结果: ${JSON.stringify(result)}`);
      
      // 查询数据库验证结果
      const satellites = await this.satelliteRepository.find();
      
      this.logger.log(`数据库中的卫星记录数: ${satellites.length}`);
      
      // 详细记录每个聚合结果
      for (let i = 0; i < satellites.length; i++) {
        const satellite = satellites[i];
        this.logger.log(`\n卫星记录 #${i + 1}:`);
        this.logger.log(`数据源: ${JSON.stringify(satellite._sources)}`);
        
        if (satellite.norad_id && satellite.norad_id.length > 0) {
          this.logger.log(`NORAD ID: ${JSON.stringify(satellite.norad_id)}`);
        }
        
        if (satellite.cospar_id && satellite.cospar_id.length > 0) {
          this.logger.log(`COSPAR ID: ${JSON.stringify(satellite.cospar_id)}`);
        }
        
        if (satellite.satellite_name && satellite.satellite_name.length > 0) {
          this.logger.log(`卫星名称: ${JSON.stringify(satellite.satellite_name)}`);
        }
      }
      
      // 验证测试用例1: 相同的norad_id应该被聚合
      const iss = satellites.find(s => 
        s.norad_id && 
        s.norad_id.some(n => n.value === '25544')
      );
      
      if (iss) {
        this.logger.log('\n验证测试用例1 (相同的norad_id):');
        this.logger.log(`找到ISS卫星记录，数据源: ${JSON.stringify(iss._sources)}`);
        
        // 验证是否包含两个数据源
        const hasN2yo = iss._sources.includes('n2yo');
        const hasSatnogs = iss._sources.includes('satnogs');
        
        this.logger.log(`包含n2yo数据源: ${hasN2yo}`);
        this.logger.log(`包含satnogs数据源: ${hasSatnogs}`);
        
        if (!hasN2yo || !hasSatnogs) {
          throw new Error('测试用例1失败: ISS卫星记录未包含所有预期的数据源');
        }
      } else {
        throw new Error('测试用例1失败: 未找到ISS卫星记录');
      }
      
      // 验证测试用例2: 相同的cospar_id应该被聚合
      const starlink = satellites.find(s => 
        s.cospar_id && 
        s.cospar_id.some(c => c.value === '2020-001A')
      );
      
      if (starlink) {
        this.logger.log('\n验证测试用例2 (相同的cospar_id):');
        this.logger.log(`找到Starlink卫星记录，数据源: ${JSON.stringify(starlink._sources)}`);
        
        // 验证是否包含两个数据源
        const hasGunter = starlink._sources.includes('gunter');
        const hasUcs = starlink._sources.includes('ucs');
        
        this.logger.log(`包含gunter数据源: ${hasGunter}`);
        this.logger.log(`包含ucs数据源: ${hasUcs}`);
        
        if (!hasGunter || !hasUcs) {
          throw new Error('测试用例2失败: Starlink卫星记录未包含所有预期的数据源');
        }
      } else {
        throw new Error('测试用例2失败: 未找到Starlink卫星记录');
      }
      
      // 验证测试用例3: 不同的norad_id不应该被聚合，即使名称相同
      const gpsRecords = satellites.filter(s => 
        s.satellite_name && 
        s.satellite_name.some(n => n.value === 'GPS IIR-1')
      );
      
      this.logger.log('\n验证测试用例3 (不同的norad_id):');
      this.logger.log(`找到 ${gpsRecords.length} 条GPS卫星记录`);
      
      if (gpsRecords.length !== 2) {
        throw new Error(`测试用例3失败: 应该有2条独立的GPS卫星记录，实际有 ${gpsRecords.length} 条`);
      }
      
      // 验证测试用例4: 相同的名称，一个有norad_id，一个没有
      const cubesatRecords = satellites.filter(s => 
        s.satellite_name && 
        s.satellite_name.some(n => n.value === 'CubeSat-1')
      );
      
      this.logger.log('\n验证测试用例4 (相同的名称，一个有norad_id):');
      this.logger.log(`找到 ${cubesatRecords.length} 条CubeSat卫星记录`);
      
      if (cubesatRecords.length !== 1) {
        throw new Error(`测试用例4失败: 应该有1条聚合的CubeSat卫星记录，实际有 ${cubesatRecords.length} 条`);
      }
      
      // 验证测试用例5: 完全不同的卫星
      const uniqueSatellite = satellites.find(s => 
        s.satellite_name && 
        s.satellite_name.some(n => n.value === 'Unique Satellite')
      );
      
      if (uniqueSatellite) {
        this.logger.log('\n验证测试用例5 (完全不同的卫星):');
        this.logger.log(`找到Unique Satellite卫星记录，数据源: ${JSON.stringify(uniqueSatellite._sources)}`);
        
        if (uniqueSatellite._sources.length !== 1 || !uniqueSatellite._sources.includes('n2yo')) {
          throw new Error('测试用例5失败: Unique Satellite卫星记录应该只包含n2yo数据源');
        }
      } else {
        throw new Error('测试用例5失败: 未找到Unique Satellite卫星记录');
      }
      
      this.logger.log('所有测试用例验证通过');
      
      return {
        success: true,
        message: '卫星数据聚合逻辑测试成功，所有测试用例通过'
      };
    } catch (error) {
      this.logger.error(`卫星数据聚合逻辑测试失败: ${error.message}`, error.stack);
      return {
        success: false,
        message: `卫星数据聚合逻辑测试失败: ${error.message}`
      };
    }
  }

  /**
   * 测试自定义卫星数据聚合逻辑
   * @param testDataFile 包含测试数据的JSON文件路径
   * @returns 测试结果
   */
  async testCustomAggregation(testDataFile: string): Promise<any> {
    try {
      this.logger.log(`开始测试自定义卫星数据聚合逻辑，使用文件: ${testDataFile}`);
      
      // 清空数据库
      await this.clearSatelliteData();
      this.logger.log('已清空数据库，准备加载测试数据');
      
      // 读取测试数据文件
      const fs = require('fs');
      const testData = JSON.parse(fs.readFileSync(testDataFile, 'utf8'));
      
      this.logger.log(`成功加载测试数据，共 ${testData.length} 条记录`);
      
      // 保存原始的searchRawData方法
      const originalSearchRawData = this.elasticsearchSatelliteService.searchRawData;
      
      // 替换为模拟方法
      this.elasticsearchSatelliteService.searchRawData = async () => {
        return testData;
      };
      
      this.logger.log('开始执行聚合操作');
      
      // 执行聚合操作
      const result = await this.aggregateSatelliteData({
        saveToDatabase: true
      });
      
      // 恢复原始方法
      this.elasticsearchSatelliteService.searchRawData = originalSearchRawData;
      
      this.logger.log(`聚合操作完成，结果: ${JSON.stringify(result)}`);
      
      // 查询数据库验证结果
      const satellites = await this.satelliteRepository.find();
      
      this.logger.log(`数据库中的卫星记录数: ${satellites.length}`);
      
      // 详细记录每个聚合结果
      for (let i = 0; i < satellites.length; i++) {
        const satellite = satellites[i];
        this.logger.log(`\n卫星记录 #${i + 1}:`);
        this.logger.log(`数据源: ${JSON.stringify(satellite._sources)}`);
        
        if (satellite.norad_id && satellite.norad_id.length > 0) {
          this.logger.log(`NORAD ID: ${JSON.stringify(satellite.norad_id)}`);
        }
        
        if (satellite.cospar_id && satellite.cospar_id.length > 0) {
          this.logger.log(`COSPAR ID: ${JSON.stringify(satellite.cospar_id)}`);
        }
        
        if (satellite.satellite_name && satellite.satellite_name.length > 0) {
          this.logger.log(`卫星名称: ${JSON.stringify(satellite.satellite_name)}`);
        }
      }
      
      // 验证OneWeb-0639卫星是否被正确聚合
      // 使用NORAD ID 55803查找卫星记录
      const onewebSatellite = satellites.find(s => 
        s.norad_id && 
        s.norad_id.some(n => String(n.value) === '55803')
      );
      
      if (onewebSatellite) {
        this.logger.log('\n验证OneWeb-0639卫星聚合:');
        this.logger.log(`找到OneWeb-0639卫星记录，数据源: ${JSON.stringify(onewebSatellite._sources)}`);
        
        // 验证是否包含所有数据源
        const hasSatnogs = onewebSatellite._sources.includes('satnogs');
        const hasN2yo = onewebSatellite._sources.includes('n2yo');
        const hasUcs = onewebSatellite._sources.includes('ucs');
        
        this.logger.log(`包含satnogs数据源: ${hasSatnogs}`);
        this.logger.log(`包含n2yo数据源: ${hasN2yo}`);
        this.logger.log(`包含ucs数据源: ${hasUcs}`);
        
        if (!hasSatnogs || !hasN2yo || !hasUcs) {
          throw new Error('OneWeb-0639卫星记录未包含所有预期的数据源');
        }
        
        // 验证是否包含正确的NORAD ID
        const hasCorrectNoradId = onewebSatellite.norad_id.some(n => String(n.value) === '55803');
        if (!hasCorrectNoradId) {
          throw new Error('OneWeb-0639卫星记录未包含正确的NORAD ID');
        }
        
        // 验证是否包含正确的COSPAR ID
        const hasCorrectCosparId = onewebSatellite.cospar_id.some(c => String(c.value) === '2023-029H');
        if (!hasCorrectCosparId) {
          throw new Error('OneWeb-0639卫星记录未包含正确的COSPAR ID');
        }
        
        this.logger.log('OneWeb-0639卫星聚合验证通过');
        
        return {
          success: true,
          message: '自定义卫星数据聚合测试成功',
          satellite: onewebSatellite
        };
      } else {
        // 如果找不到卫星记录，记录所有卫星的NORAD ID以便调试
        this.logger.error('未找到OneWeb-0639卫星记录，当前所有卫星的NORAD ID:');
        for (const satellite of satellites) {
          if (satellite.norad_id) {
            this.logger.error(`卫星ID ${satellite.id} 的NORAD ID: ${JSON.stringify(satellite.norad_id)}`);
          }
        }
        throw new Error('未找到OneWeb-0639卫星记录');
      }
    } catch (error) {
      this.logger.error(`自定义卫星数据聚合测试失败: ${error.message}`, error.stack);
      return {
        success: false,
        message: `自定义卫星数据聚合测试失败: ${error.message}`
      };
    }
  }

  /**
   * 增量聚合卫星数据
   * 只聚合ES中有但本地数据库中没有的卫星信息
   * @param options 聚合选项
   * @returns 聚合结果，包含新增聚合的卫星数量
   */
  async incrementalAggregateSatelliteData(options?: { 
    saveToDatabase?: boolean;
  }): Promise<{ success: boolean; totalNewAggregated: number; message: string }> {
    let task: any = null;
    
    try {
      this.logger.log('开始增量聚合卫星数据');
      
      const saveToDatabase = options?.saveToDatabase !== false; // 默认保存到数据库
      
      // 创建聚合任务记录
      task = await this.aggregationTaskService.createTask({
        task_type: 'incremental',
        parameters: options || {},
      });
      
      // 更新任务状态为运行中
      await this.aggregationTaskService.startTask(task.id);
      this.logger.log(`创建增量聚合任务记录，ID: ${task.id}`);
      
      // 定义要查询的索引
      const indices = [
        'satsinfo_gunter',
        'satsinfo_n2yo',
        'satsinfo_nanosats',
        'satsinfo_satnogs',
        'satsinfo_ucs'
      ];
      
      // 构建查询
      const query: any = {
        bool: {
          must: [] as any[]
        }
      };
      
      // 从本地数据库获取所有现有卫星的标识信息
      this.logger.log('从本地数据库获取现有卫星的标识信息');
      
      // 查询所有卫星的norad_id, cospar_id, satellite_name, alternative_name
      const existingSatellites = await this.satelliteRepository
        .createQueryBuilder('satellite')
        .select([
          'satellite.id',
          'satellite.norad_id',
          'satellite.cospar_id',
          'satellite.satellite_name',
          'satellite.alternative_name'
        ])
        .getMany();
      
      this.logger.log(`从本地数据库获取到 ${existingSatellites.length} 条卫星记录`);
      
      // 创建查找表，用于快速检查卫星是否已存在
      const existingNoradIds = new Set<string>();
      const existingCosparIds = new Set<string>();
      const existingSatelliteNames = new Set<string>();
      const existingAlternativeNames = new Set<string>();
      
      // 填充查找表
      for (const satellite of existingSatellites) {
        // 处理norad_id
        if (satellite.norad_id && Array.isArray(satellite.norad_id)) {
          for (const item of satellite.norad_id) {
            if (item.value) {
              existingNoradIds.add(String(item.value).trim().toLowerCase());
            }
          }
        }
        
        // 处理cospar_id
        if (satellite.cospar_id && Array.isArray(satellite.cospar_id)) {
          for (const item of satellite.cospar_id) {
            if (item.value) {
              existingCosparIds.add(String(item.value).trim().toLowerCase());
            }
          }
        }
        
        // 处理satellite_name
        if (satellite.satellite_name && Array.isArray(satellite.satellite_name)) {
          for (const item of satellite.satellite_name) {
            if (item.value) {
              existingSatelliteNames.add(String(item.value).trim().toLowerCase());
            }
          }
        }
        
        // 处理alternative_name
        if (satellite.alternative_name && Array.isArray(satellite.alternative_name)) {
          for (const item of satellite.alternative_name) {
            if (item.value) {
              existingAlternativeNames.add(String(item.value).trim().toLowerCase());
            }
          }
        }
      }
      
      this.logger.log(`创建查找表完成: ${existingNoradIds.size} 个NORAD ID, ${existingCosparIds.size} 个COSPAR ID, ${existingSatelliteNames.size} 个卫星名称, ${existingAlternativeNames.size} 个卫星别名`);
      
      // 所有搜索结果
      let allSearchResults: any[] = [];
      // 批次大小
      const batchSize = 1000;
      
      // 使用scrollSearch方法获取所有数据
      this.logger.log('使用Scroll API获取所有卫星数据');
      
      for (const index of indices) {
        this.logger.log(`开始处理索引: ${index}`);
        
        try {
          // 使用scrollSearch方法获取所有数据
          const indexResults = await this.elasticsearchSatelliteService.scrollSearch({
            indices: [index],
            query,
            size: batchSize
          });
          
          this.logger.log(`从索引 ${index} 获取了 ${indexResults.length} 条记录`);
          
          // 将结果添加到总结果中
          allSearchResults = allSearchResults.concat(indexResults);
          
          // 更新任务进度
          const progress = 25 * (indices.indexOf(index) + 1) / indices.length;
          await this.aggregationTaskService.updateTaskProgress(task.id, progress, allSearchResults.length);
        } catch (error) {
          this.logger.error(`处理索引 ${index} 时出错: ${error.message}`, error.stack);
          // 继续处理其他索引
        }
      }
      
      this.logger.log(`所有索引处理完成，总共获取 ${allSearchResults.length} 条记录`);
      
      // 如果没有获取到记录，直接返回
      if (allSearchResults.length === 0) {
        this.logger.log('未找到任何卫星数据');
        await this.aggregationTaskService.completeTask(task.id, 0);
        return { success: true, totalNewAggregated: 0, message: '未找到任何卫星数据' };
      }
      
      // 筛选出新的卫星数据
      this.logger.log('开始筛选新的卫星数据');
      
      const newSatelliteRecords: any[] = [];
      
      for (const record of allSearchResults) {
        const source = record._source;
        const index = record._index;
        
        // 添加数据源信息
        source._index = index;
        
        // 提取标识信息
        const noradId = source.norad_id ? String(source.norad_id).trim().toLowerCase() : '';
        const cosparId = source.cospar_id ? String(source.cospar_id).trim().toLowerCase() : '';
        const satelliteName = source.satellite_name ? String(source.satellite_name).trim().toLowerCase() : '';
        const alternativeName = source.alternative_name ? String(source.alternative_name).trim().toLowerCase() : '';
        
        // 检查是否是新的卫星
        const isNewSatellite = (
          // 如果所有标识字段都为空，则跳过
          (noradId || cosparId || satelliteName || alternativeName) && 
          // 检查所有标识字段是否都不在现有数据中
          (!noradId || !existingNoradIds.has(noradId)) &&
          (!cosparId || !existingCosparIds.has(cosparId)) &&
          (!satelliteName || !existingSatelliteNames.has(satelliteName)) &&
          (!alternativeName || !existingAlternativeNames.has(alternativeName))
        );
        
        if (isNewSatellite) {
          newSatelliteRecords.push(source);
        }
      }
      
      this.logger.log(`筛选完成，找到 ${newSatelliteRecords.length} 条新的卫星数据`);
      
      // 更新任务进度
      await this.aggregationTaskService.updateTaskProgress(task.id, 50, newSatelliteRecords.length);
      
      // 如果没有新的卫星数据，直接返回
      if (newSatelliteRecords.length === 0) {
        this.logger.log('未找到新的卫星数据');
        await this.aggregationTaskService.completeTask(task.id, 0);
        return { success: true, totalNewAggregated: 0, message: '未找到新的卫星数据' };
      }
      
      // 对新的卫星数据进行分组和合并
      this.logger.log('开始对新的卫星数据进行分组和合并');
      
      // 卫星组数组，每个组包含来自不同数据源的同一颗卫星的记录
      const satelliteGroups: any[][] = [];
      
      // 处理每条卫星记录
      for (const record of newSatelliteRecords) {
        // 标记是否已找到匹配的组
        let foundMatch = false;
        
        // 遍历现有组，尝试找到匹配
        for (let i = 0; i < satelliteGroups.length; i++) {
          const group = satelliteGroups[i];
          
          // 检查是否可以与当前组匹配
          if (this.canMergeWithGroup(record, group)) {
            // 找到匹配，添加到该组
            group.push(record);
            foundMatch = true;
            break;
          }
        }
        
        // 如果没有找到匹配的组，创建一个新组
        if (!foundMatch) {
          satelliteGroups.push([record]);
        }
      }
      
      this.logger.log(`根据标识字段分组后，共有 ${satelliteGroups.length} 组新的卫星数据`);
      
      // 聚合结果数组
      const aggregatedSatellites: any[] = [];
      
      // 聚合每个组的数据
      for (let groupIndex = 0; groupIndex < satelliteGroups.length; groupIndex++) {
        const groupRecords = satelliteGroups[groupIndex];
        
        // 创建聚合记录模板
        const aggregatedSatellite: any = {
          satellite_name: [],
          alternative_name: [],
          cospar_id: [],
          country_of_registry: [],
          owner: [],
          users: [],
          status: [],
          norad_id: [],
          launch_info: [],
          orbit_info: [],
          mass_kg: [],
          power_watts: [],
          lifetime_years: [],
          contractor: [],
          purpose: [],
          detailed_purpose: [],
          payload: [],
          payload_description: [],
          update_time: [],
          _sources: []
        };
        
        // 字段值记录，用于去重
        const fieldValues = new Map<string, Set<string>>();
        
        // 处理每条记录
        for (const record of groupRecords) {
          // 提取数据源（索引名称）
          const source = record._index.replace('satsinfo_', '');
          
          // 添加到数据源列表
          if (!aggregatedSatellite._sources.includes(source)) {
            aggregatedSatellite._sources.push(source);
          }
          
          // 处理每个字段
          for (const field of Object.keys(aggregatedSatellite)) {
            // 跳过元数据字段
            if (field === '_sources' || field.startsWith('_')) continue;
            
            // 获取字段值
            const value = record[field];
            
            // 如果字段值不为空
            if (value !== undefined && value !== null && value !== '') {
              // 确保字段值集合存在
              if (!fieldValues.has(field)) {
                fieldValues.set(field, new Set<string>());
              }
              
              // 将字段值转换为JSON字符串，用于去重
              const valueStr = JSON.stringify(value);
              
              // 如果该值尚未添加
              if (!fieldValues.get(field)!.has(valueStr)) {
                // 添加到字段值集合
                fieldValues.get(field)!.add(valueStr);
                
                // 添加到聚合记录
                aggregatedSatellite[field].push({
                  value,
                  sources: [source]
                });
              } else {
                // 如果该值已存在，添加数据源
                const existingItem = aggregatedSatellite[field].find((item: any) => 
                  JSON.stringify(item.value) === valueStr
                );
                
                if (existingItem && !existingItem.sources.includes(source)) {
                  existingItem.sources.push(source);
                }
              }
            }
          }
        }
        
        // 添加到聚合结果
        aggregatedSatellites.push(aggregatedSatellite);
      }
      
      this.logger.log(`聚合完成，共 ${aggregatedSatellites.length} 条新的卫星数据`);
      
      // 在保存结果之前，更新任务进度
      await this.aggregationTaskService.updateTaskProgress(task.id, 75, newSatelliteRecords.length);
      
      // 如果不需要保存到数据库，直接返回结果
      if (!saveToDatabase) {
        return {
          success: true,
          totalNewAggregated: aggregatedSatellites.length,
          message: `成功聚合 ${aggregatedSatellites.length} 条新的卫星数据（未保存到数据库）`
        };
      }
      
      // 保存到数据库
      this.logger.log(`开始将聚合结果保存到数据库`);
      
      // 批量保存的大小
      const saveBatchSize = 500;
      let savedCount = 0;
      
      // 分批保存数据
      for (let i = 0; i < aggregatedSatellites.length; i += saveBatchSize) {
        const batch = aggregatedSatellites.slice(i, i + saveBatchSize);
        
        // 创建实体对象
        const entities = batch.map(data => {
          const entity = new Satellite();
          
          // 复制字段
          for (const field of Object.keys(data)) {
            (entity as any)[field] = data[field];
          }
          
          return entity;
        });
        
        // 保存实体
        await this.satelliteRepository.save(entities);
        
        savedCount += batch.length;
        this.logger.log(`已保存 ${savedCount}/${aggregatedSatellites.length} 条新的卫星数据到数据库`);
      }
      
      this.logger.log(`成功将 ${savedCount} 条聚合后的新卫星数据保存到数据库`);
      
      // 更新任务状态为已完成
      this.logger.log(`增量卫星数据聚合完成，共处理 ${newSatelliteRecords.length} 条记录，聚合为 ${aggregatedSatellites.length} 条记录`);
      await this.aggregationTaskService.completeTask(task.id, aggregatedSatellites.length);
      
      return {
        success: true,
        totalNewAggregated: aggregatedSatellites.length,
        message: `成功聚合 ${aggregatedSatellites.length} 条新的卫星数据`
      };
    } catch (error) {
      this.logger.error(`增量聚合卫星数据失败: ${error.message}`, error.stack);
      
      // 如果任务ID存在，更新任务状态为失败
      if (task && task.id) {
        await this.aggregationTaskService.failTask(task.id, error.message);
      }
      
      throw new Error(`增量聚合卫星数据失败: ${error.message}`);
    }
  }

  /**
   * 获取卫星轨道高度（本地数据库）
   * @returns 轨道高度列表
   */
  async getSatelliteOrbitClassesLocal(): Promise<any[]> {
    this.logger.debug('开始获取卫星轨道高度（本地数据库）');
    
    try {
      // 定义标准化的轨道高度
      const standardOrbitClasses = [
        'LEO', 'GEO', 'MEO', 'HEO', 'SSO', 'Elliptical', 'Heliocentric',
        'Polar', 'Molniya', 'Tundra', 'Cislunar', 'Deep Space', 'Lagrangian', 'GTO', 'Inclined'
      ];
      
      // 使用CTE查询提取轨道高度
      const query = `
        WITH orbit_classes AS (
          SELECT DISTINCT jsonb_extract_path_text(jsonb_array_elements(orbit_info), 'value', 'orbit_class') as orbit_class 
          FROM satellites 
          WHERE orbit_info IS NOT NULL
        ) 
        SELECT orbit_class 
        FROM orbit_classes 
        WHERE orbit_class IN ('${standardOrbitClasses.join("','")}')
        ORDER BY orbit_class;
      `;
      
      const result = await this.satelliteRepository.query(query);
      
      // 轨道高度中英文映射
      const orbitClassMap: Record<string, string> = {
        'LEO': '低地球轨道',
        'GEO': '地球同步轨道',
        'MEO': '中地球轨道',
        'HEO': '高椭圆轨道',
        'SSO': '太阳同步轨道',
        'Elliptical': '椭圆轨道',
        'Heliocentric': '日心轨道',
        'Polar': '极地轨道',
        'Molniya': '莫尔尼亚轨道',
        'Tundra': '苔原轨道',
        'Cislunar': '地月轨道',
        'Deep Space': '深空轨道',
        'Lagrangian': '拉格朗日点轨道',
        'GTO': '地球同步转移轨道',
        'Inclined': '倾斜轨道'
      };
      
      // 构建结果数组
      const orbitClasses = result.map((item: { orbit_class: string }) => ({
        en: item.orbit_class,
        zh: orbitClassMap[item.orbit_class] || item.orbit_class // 如果没有对应的中文翻译，则使用英文
      }));
      
      this.logger.debug(`成功获取卫星轨道高度（本地数据库），共 ${orbitClasses.length} 种轨道高度`);
      return orbitClasses;
    } catch (error) {
      this.logger.error(`获取卫星轨道高度（本地数据库）失败: ${error.message}`);
      return [];
    }
  }

  /**
   * 获取卫星星座集合（本地数据库）
   * @returns 星座集合，包含英文和中文名称
   */
  async getSatelliteConstellationsLocal(): Promise<Array<{en: string, zh: string}>> {
    this.logger.debug('开始获取卫星星座集合（本地数据库）');
    
    try {
      // 使用SQL查询从卫星名称中提取星座信息
      const query = `
        WITH constellation_values AS (
          SELECT jsonb_array_elements(constellation) ->> 'value' as constellation_name
          FROM satellites 
          WHERE constellation IS NOT NULL AND jsonb_array_length(constellation) > 0
        ) 
        SELECT DISTINCT constellation_name
        FROM constellation_values
        WHERE constellation_name IS NOT NULL
        ORDER BY constellation_name;
      `;
      
      const result = await this.satelliteRepository.query(query);
      
      // 星座中英文映射
      const constellationMap: Record<string, string> = {
        'Starlink': '星链',
        'OneWeb': '万维网',
        'Iridium': '铱星',
        'Globalstar': '全球星',
        'O3b': 'O3b',
        'Inmarsat': '国际海事卫星',
        'Orbcomm': '轨道通信',
        'Planet': '行星实验室',
        'Planet Dove': '行星实验室 Dove',
        'Planet Flock': '行星实验室 Flock',
        'Planet SkySat': '行星实验室 SkySat',
        'Galileo': '伽利略',
        'GPS': '全球定位系统',
        'GLONASS': '格洛纳斯',
        'BeiDou': '北斗',
        'BeiDou Compass': '北斗导航',
        'QZSS': '准天顶卫星系统',
        'NavIC': '印度区域导航卫星系统',
        'NavIC IRNSS': '印度区域导航卫星系统',
        'Telesat': 'Telesat',
        'Kuiper': 'Kuiper',
        'SES': 'SES',
        'Eutelsat': '欧洲通信卫星',
        'Intelsat': '国际通信卫星',
        'GOES': '静止气象卫星',
        'Meteosat': '气象卫星',
        'Himawari': '向日葵',
        'FengYun': '风云',
        'GaoFen': '高分',
        'Landsat': '陆地卫星',
        'Sentinel': '哨兵'
      };
      
      // 构建结果数组
      const constellations = result.map((item: { constellation_name: string }) => ({
        en: item.constellation_name,
        zh: constellationMap[item.constellation_name] || item.constellation_name // 如果没有对应的中文翻译，则使用英文
      }));
      
      this.logger.debug(`成功获取卫星星座集合，共 ${constellations.length} 个星座`);
      return constellations;
    } catch (error) {
      this.logger.error(`获取卫星星座集合失败: ${error.message}`);
      return [];
    }
  }

  /**
   * 获取卫星轨道类型（本地数据库）
   * @returns 轨道类型列表
   */
  async getSatelliteOrbitTypesLocal(): Promise<Array<{en: string, zh: string}>> {
    this.logger.debug('开始获取卫星轨道类型（本地数据库）');
    
    try {
      // 使用CTE查询提取轨道类型
      const query = `
        WITH orbit_types AS (
          SELECT DISTINCT jsonb_extract_path_text(jsonb_array_elements(orbit_info), 'value', 'orbit_type') as orbit_type 
          FROM satellites 
          WHERE orbit_info IS NOT NULL
        ) 
        SELECT orbit_type 
        FROM orbit_types 
        WHERE orbit_type IS NOT NULL AND orbit_type != ''
        ORDER BY orbit_type;
      `;
      
      const result = await this.satelliteRepository.query(query);
      
      // 轨道类型中英文映射
      const orbitTypeMap: Record<string, string> = {
        'Polar': '极地轨道',
        'Elliptical': '椭圆轨道',
        'Deep Highly Eccentric': '深度高偏心轨道',
        'Cislunar': '地月轨道',
        'Sun-Synchronous': '太阳同步轨道',
        'Non-Polar Inclined': '非极地倾斜轨道',
        'Retrograde': '逆行轨道',
        'Sun-Synchronous near polar': '近极太阳同步轨道',
        'Molniya': '莫尔尼亚轨道',
        'Equatorial': '赤道轨道',
        'Circular': '圆形轨道',
        'Inclined': '倾斜轨道',
        'Highly Elliptical': '高椭圆轨道'
      };
      
      // 构建结果数组
      const orbitTypes = result.map((item: { orbit_type: string }) => ({
        en: item.orbit_type,
        zh: orbitTypeMap[item.orbit_type] || item.orbit_type // 如果没有对应的中文翻译，则使用英文
      }));
      
      this.logger.debug(`成功获取卫星轨道类型（本地数据库），共 ${orbitTypes.length} 种轨道类型`);
      return orbitTypes;
    } catch (error) {
      this.logger.error(`获取卫星轨道类型（本地数据库）失败: ${error.message}`);
      return [];
    }
  }

  /**
   * 从ES数据库获取星座信息并更新本地数据库中的卫星星座字段
   * 使用两种方式匹配星座信息：
   * 1. 从卫星名称中提取星座信息
   * 2. 从ES数据库中的constell_n2yo索引获取星座成员信息，通过norad_id、cospar_id、name进行匹配
   * @returns 更新结果
   */
  async updateSatelliteConstellationInfo(): Promise<{ success: boolean; message: string; updated: number }> {
    this.logger.debug('开始从ES数据库获取星座信息并更新本地数据库');
    
    try {
      // 第一步：从卫星名称中提取星座信息
      const nameBasedResult = await this.updateConstellationFromSatelliteName();
      
      // 第二步：从ES数据库中的constell_n2yo索引获取星座成员信息
      const esBasedResult = await this.updateConstellationFromESData();
      
      const totalUpdated = nameBasedResult.updated + esBasedResult.updated;
      
      return {
        success: true,
        message: `成功更新${totalUpdated}个卫星的星座信息`,
        updated: totalUpdated
      };
    } catch (error) {
      this.logger.error(`更新卫星星座信息失败: ${error.message}`, error.stack);
      return {
        success: false,
        message: `更新卫星星座信息失败: ${error.message}`,
        updated: 0
      };
    }
  }
  
  /**
   * 从卫星名称中提取星座信息
   * @returns 更新结果
   * @private
   */
  private async updateConstellationFromSatelliteName(): Promise<{ updated: number }> {
    this.logger.debug('开始从卫星名称中提取星座信息');
    
    try {
      const identifySatellites = `
        -- 首先识别所有卫星名称包含Starlink等特定星座名称的卫星ID
        SELECT DISTINCT s.id,
          CASE
            WHEN EXISTS (
              SELECT 1
              FROM jsonb_array_elements(
                CASE WHEN s.satellite_name IS NULL OR s.satellite_name = '[]'::jsonb THEN '[]'::jsonb
                ELSE s.satellite_name END
              ) AS name
              WHERE name->>'value' LIKE 'Starlink%'
            ) OR EXISTS (
              SELECT 1
              FROM jsonb_array_elements(
                CASE WHEN s.alternative_name IS NULL OR s.alternative_name = '[]'::jsonb THEN '[]'::jsonb
                ELSE s.alternative_name END
              ) AS alt_name
              WHERE alt_name->>'value' LIKE 'Starlink%'
            ) THEN 'Starlink'
            
            WHEN EXISTS (
              SELECT 1
              FROM jsonb_array_elements(
                CASE WHEN s.satellite_name IS NULL OR s.satellite_name = '[]'::jsonb THEN '[]'::jsonb
                ELSE s.satellite_name END
              ) AS name
              WHERE name->>'value' LIKE 'OneWeb%'
            ) OR EXISTS (
              SELECT 1
              FROM jsonb_array_elements(
                CASE WHEN s.alternative_name IS NULL OR s.alternative_name = '[]'::jsonb THEN '[]'::jsonb
                ELSE s.alternative_name END
              ) AS alt_name
              WHERE alt_name->>'value' LIKE 'OneWeb%'
            ) THEN 'OneWeb'
            
            -- 其他星座模式，复制前面的逻辑
            WHEN EXISTS (
              SELECT 1
              FROM jsonb_array_elements(
                CASE WHEN s.satellite_name IS NULL OR s.satellite_name = '[]'::jsonb THEN '[]'::jsonb
                ELSE s.satellite_name END
              ) AS name
              WHERE name->>'value' LIKE 'Iridium%'
            ) OR EXISTS (
              SELECT 1
              FROM jsonb_array_elements(
                CASE WHEN s.alternative_name IS NULL OR s.alternative_name = '[]'::jsonb THEN '[]'::jsonb
                ELSE s.alternative_name END
              ) AS alt_name
              WHERE alt_name->>'value' LIKE 'Iridium%'
            ) THEN 'Iridium'
            
            -- 在此添加其他星座模式...
            ELSE NULL
          END AS constellation_name
        FROM satellites s
        WHERE 
          -- 至少satellite_name或alternative_name有一个不为空
          (s.satellite_name IS NOT NULL AND s.satellite_name != '[]'::jsonb) OR 
          (s.alternative_name IS NOT NULL AND s.alternative_name != '[]'::jsonb)
      `;
      
      // 首先获取需要更新的卫星ID及其对应的星座名称
      const satellites = await this.satelliteRepository.query(identifySatellites);
      
      this.logger.debug(`识别出${satellites.length}个需要检查星座信息的卫星`);
      
      // 逐一检查和更新
      let updatedCount = 0;
      
      for (const sat of satellites) {
        if (!sat.constellation_name) continue;
        
        // 查询卫星的当前星座信息
        const currSat = await this.satelliteRepository.findOne({
          where: { id: sat.id },
          select: ['id', 'constellation']
        });
        
        // 检查当前星座信息是否与提取的星座名称相关
        let needsUpdate = false;
        
        if (!currSat) {
          // 如果找不到卫星信息，则跳过
          continue;
        } else if (!currSat.constellation || currSat.constellation.length === 0) {
          // 如果当前没有星座信息，则需要更新
          needsUpdate = true;
        } else {
          // 检查现有星座信息是否与新提取的有关联
          const isRelated = currSat.constellation.some(item => 
            item.value.toLowerCase().includes(sat.constellation_name.toLowerCase()) || 
            sat.constellation_name.toLowerCase().includes(item.value.toLowerCase())
          );
          
          // 如果没有关联，则需要更新
          needsUpdate = !isRelated;
        }
        
        // 如果需要更新，则执行更新
        if (needsUpdate) {
          await this.satelliteRepository.update(
            { id: sat.id },
            { 
              constellation: [
                { value: sat.constellation_name, sources: ['name_extraction'] }
              ] 
            }
          );
          updatedCount++;
        }
      }
      
      this.logger.debug(`从卫星名称中提取星座信息完成，更新了${updatedCount}个卫星的星座信息`);
      
      return { updated: updatedCount };
    } catch (error) {
      this.logger.error(`从卫星名称中提取星座信息失败: ${error.message}`, error.stack);
      return { updated: 0 };
    }
  }
  
  /**
   * 从ES数据库中的constell_n2yo索引获取星座成员信息
   * @returns 更新结果
   * @private
   */
  private async updateConstellationFromESData(): Promise<{ updated: number }> {
    this.logger.debug('开始从ES数据库中获取星座成员信息');
    
    try {
      // 创建ES客户端
      const { Client } = require('@elastic/elasticsearch');
      const client = new Client({
        node: 'http://123.57.173.156:9200',
        auth: {
          username: 'web_readonly',
          password: 'web@readonly4all'
        }
      });
      
      // 查询constell_n2yo索引获取所有星座信息
      const { body } = await client.search({
        index: 'constell_n2yo',
        size: 1000,
        body: {
          query: {
            match_all: {}
          },
          _source: ['constellation_name', 'sats_included']
        }
      });
      
      // 处理星座信息
      let totalUpdated = 0;
      
      for (const hit of body.hits.hits) {
        const constellationName = hit._source.constellation_name;
        const satellites = hit._source.sats_included || [];
        
        if (!constellationName || satellites.length === 0) {
          continue;
        }
        
        this.logger.debug(`处理星座 ${constellationName}，包含 ${satellites.length} 个卫星`);
        
        // 遍历星座中的卫星，更新本地数据库
        for (const sat of satellites) {
          const { norad_id, cospar_id, name } = sat;
          
          // 构建查询条件
          const conditions = [];
          const params = [];
          
          if (norad_id) {
            conditions.push(`EXISTS (
              SELECT FROM jsonb_array_elements(s.norad_id) AS n 
              WHERE (n->>'value')::text = $${params.length + 1}
            )`);
            params.push(norad_id.toString());
          }
          
          if (cospar_id) {
            conditions.push(`EXISTS (
              SELECT FROM jsonb_array_elements(s.cospar_id) AS c 
              WHERE (c->>'value')::text = $${params.length + 1}
            )`);
            params.push(cospar_id);
          }
          
          if (name) {
            conditions.push(`EXISTS (
              SELECT FROM jsonb_array_elements(s.satellite_name) AS sn 
              WHERE (sn->>'value')::text ILIKE $${params.length + 1}
            )`);
            params.push(`%${name}%`);
          }
          
          if (conditions.length === 0) {
            continue;
          }
          
          // 构建SQL查询
          const query = `
            WITH matched_satellites AS (
              SELECT id
              FROM satellites s
              WHERE ${conditions.join(' OR ')}
              AND (s.constellation IS NULL OR s.constellation = '[]'::jsonb)
            ),
            updates AS (
              UPDATE satellites s
              SET constellation = jsonb_build_array(
                jsonb_build_object(
                  'value', $${params.length + 1},
                  'sources', jsonb_build_array('constell_n2yo')
                )
              )
              FROM matched_satellites m
              WHERE s.id = m.id
              RETURNING s.id
            )
            SELECT COUNT(*) as updated FROM updates;
          `;
          
          params.push(constellationName);
          
          // 执行更新
          const result = await this.satelliteRepository.query(query, params);
          const updated = parseInt(result[0].updated, 10);
          
          if (updated > 0) {
            this.logger.debug(`更新了 ${updated} 个卫星的星座信息为 ${constellationName}`);
            totalUpdated += updated;
          }
        }
      }
      
      this.logger.debug(`从ES数据库中获取星座成员信息完成，更新了${totalUpdated}个卫星的星座信息`);
      
      return { updated: totalUpdated };
    } catch (error) {
      this.logger.error(`从ES数据库中获取星座成员信息失败: ${error.message}`, error.stack);
      return { updated: 0 };
    }
  }

  /**
   * 从ES数据库的orbital_tle索引更新卫星轨道信息
   * 通过norad_id、cospar_id、satellite_name匹配卫星，并更新轨道信息中缺失的字段
   * @returns 更新结果
   */
  async updateSatelliteOrbitInfoFromTLE(): Promise<{ success: boolean; message: string; updated: number }> {
    this.logger.debug('开始从ES数据库的orbital_tle索引更新卫星轨道信息');
    const startTime = Date.now();
    
    try {
      // 创建ES客户端（只创建一次）
      const { Client } = require('@elastic/elasticsearch');
      const client = new Client({
        node: 'http://123.57.173.156:9200',
        auth: {
          username: 'web_readonly',
          password: 'web@readonly4all'
        }
      });
      
      // 批处理配置
      const batchSize = 50; // 每批处理的卫星数量
      let totalProcessed = 0;
      let updatedCount = 0;
      
      // 1. 获取所有卫星数据（后续分批处理）
      this.logger.debug('正在获取所有卫星数据...');
      const allSatellites = await this.satelliteRepository.find();
      this.logger.debug(`共找到 ${allSatellites.length} 个卫星`);
      
      // 按批次处理卫星数据
      for (let i = 0; i < allSatellites.length; i += batchSize) {
        const batchStartTime = Date.now();
        const satelliteBatch = allSatellites.slice(i, i + batchSize);
        
        this.logger.debug(`开始处理第 ${Math.floor(i / batchSize) + 1} 批卫星数据，共 ${satelliteBatch.length} 条`);
        
        // 2. 准备ES批量查询
        const satellitesWithIdentifiers = satelliteBatch.filter(satellite => {
          const noradIds = this.extractValues(satellite.norad_id);
          const cosparIds = this.extractValues(satellite.cospar_id);
          const names = this.extractValues(satellite.satellite_name);
          
          return noradIds.length > 0 || cosparIds.length > 0 || names.length > 0;
        });
        
        if (satellitesWithIdentifiers.length === 0) {
          this.logger.debug('该批次中没有卫星具有可用的标识符，跳过');
          totalProcessed += satelliteBatch.length;
          continue;
        }
        
        // 3. 执行批量查询
        const msearchBody: any[] = [];
        const satelliteMap = new Map<number, Satellite>(); // 用于跟踪查询结果对应的卫星
        
        satellitesWithIdentifiers.forEach((satellite, index) => {
          const noradIds = this.extractValues(satellite.norad_id);
          const cosparIds = this.extractValues(satellite.cospar_id);
          const names = this.extractValues(satellite.satellite_name);
          
          // 添加查询头
          msearchBody.push({});
          
          // 构建查询体
          const should: any[] = [];
          
          // 按norad_id查询（优先级最高）
          noradIds.forEach(noradId => {
            should.push({
              term: {
                norad_id: noradId
              }
            });
          });
          
          // 按cospar_id查询
          cosparIds.forEach(cosparId => {
            should.push({
              term: {
                cospar_id: cosparId
              }
            });
          });
          
          // 按卫星名称查询（优先级最低）- 使用精确匹配
          names.forEach(name => {
            should.push({
              match_phrase: {
                satellite_name: name
              }
            });
            
            // 也尝试匹配name字段 - 使用精确匹配
            should.push({
              match_phrase: {
                name: name
              }
            });
          });
          
          msearchBody.push({
            query: {
              bool: {
                should,
                minimum_should_match: 1
              }
            },
            sort: [
              { time: { order: 'desc' } }
            ],
            size: 1
          });
          
          // 记录查询索引和对应的卫星
          satelliteMap.set(index, satellite);
        });
        
        // 执行msearch查询
        this.logger.debug(`正在执行批量查询，共 ${satellitesWithIdentifiers.length} 个查询`);
        const response = await client.msearch({
          index: 'orbital_tle',
          body: msearchBody
        });
        
        // 处理响应
        const result = response.body || response;
        
        if (!result || !result.responses) {
          this.logger.warn('ES查询返回了无效的响应格式');
          totalProcessed += satelliteBatch.length;
          continue;
        }
        
        // 处理查询结果并更新卫星
        const updatedSatellites: Satellite[] = [];
        
        result.responses.forEach((resp: any, index: number) => {
          const satellite = satelliteMap.get(index);
          
          if (!satellite) return; // 跳过没有匹配的卫星
          
          // 检查是否有匹配的TLE数据
          if (!resp.hits || resp.hits.total.value === 0 || !resp.hits.hits || resp.hits.hits.length === 0) {
            this.logger.debug(`卫星ID ${satellite.id} 在ES中未找到匹配的轨道数据`);
            return;
          }
          
          // 获取最新的TLE数据
          const tleData = resp.hits.hits[0]._source;
          
          // 验证TLE数据是否包含必要的字段
          if (!this.validateTLEData(tleData)) {
            this.logger.debug(`卫星ID ${satellite.id} 的TLE数据缺少必要字段，跳过`);
            return;
          }
          
          // 映射TLE数据到轨道信息字段
          const newOrbitInfo = this.mapTLEToOrbitInfo(tleData);
          
          // 检查是否需要更新
          if (this.needsOrbitInfoUpdate(satellite, newOrbitInfo)) {
            // 合并现有轨道信息和新轨道信息
            const mergedOrbitInfo = this.mergeOrbitInfo(satellite.orbit_info, newOrbitInfo);
            
            // 更新卫星轨道信息
            satellite.orbit_info = mergedOrbitInfo;
            updatedSatellites.push(satellite);
            
            this.logger.debug(`卫星ID ${satellite.id} 需要更新轨道信息`);
          } else {
            this.logger.debug(`卫星ID ${satellite.id} 已有完整的轨道信息，无需更新`);
          }
        });
        
        // 批量保存到数据库
        if (updatedSatellites.length > 0) {
          this.logger.debug(`正在保存 ${updatedSatellites.length} 个更新的卫星数据`);
          await this.satelliteRepository.save(updatedSatellites);
          updatedCount += updatedSatellites.length;
        }
        
        totalProcessed += satelliteBatch.length;
        const batchEndTime = Date.now();
        const batchDuration = (batchEndTime - batchStartTime) / 1000;
        
        this.logger.debug(`第 ${Math.floor(i / batchSize) + 1} 批处理完成，共更新 ${updatedSatellites.length} 个卫星，耗时 ${batchDuration.toFixed(2)} 秒`);
        this.logger.debug(`总进度: ${Math.round((totalProcessed / allSatellites.length) * 100)}% (${totalProcessed}/${allSatellites.length})`);
      }
      
      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;
      
      this.logger.debug(`轨道信息更新完成，共更新 ${updatedCount} 个卫星，总耗时 ${duration.toFixed(2)} 秒`);
      
      return {
        success: true,
        message: `成功更新 ${updatedCount} 个卫星的轨道信息，耗时 ${duration.toFixed(2)} 秒`,
        updated: updatedCount
      };
    } catch (error) {
      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;
      
      this.logger.error(`更新卫星轨道信息失败: ${error.message}`, error.stack);
      return {
        success: false,
        message: `更新卫星轨道信息失败: ${error.message}，耗时 ${duration.toFixed(2)} 秒`,
        updated: 0
      };
    }
  }
  
  /**
   * 验证TLE数据是否包含必要的字段
   * @param tleData TLE数据
   * @returns 是否包含必要字段
   * @private
   */
  private validateTLEData(tleData: any): boolean {
    // 检查基本字段
    if (!tleData) return false;
    
    // 检查必要的字段
    const requiredFields = ['norad_id', 'orbital_elements'];
    
    // 如果缺少任何必要字段，返回false
    for (const field of requiredFields) {
      if (!tleData[field]) return false;
    }
    
    // 检查轨道元素字段
    if (tleData.orbital_elements) {
      const requiredOrbitalFields = ['inc_deg', 'ecc', 'sema_km'];
      
      for (const field of requiredOrbitalFields) {
        if (tleData.orbital_elements[field] === undefined) return false;
      }
    }
    
    return true;
  }
  
  /**
   * 检查卫星是否需要更新轨道信息
   * @param satellite 卫星数据
   * @param newOrbitInfo 新的轨道信息
   * @returns 是否需要更新
   * @private
   */
  private needsOrbitInfoUpdate(satellite: Satellite, newOrbitInfo: any[]): boolean {
    // 如果没有轨道信息，则需要更新
    if (!satellite.orbit_info || satellite.orbit_info.length === 0) {
      return true;
    }
    
    // 检查是否存在缺失的重要字段
    const existingFields = new Set();
    
    // 收集现有轨道信息中的所有字段
    if (Array.isArray(satellite.orbit_info)) {
      for (const item of satellite.orbit_info) {
        if (item.value) {
          Object.keys(item.value).forEach(key => existingFields.add(key));
        }
      }
    }
    
    // 重要字段列表
    const importantFields = [
      'orbit_class', 'orbit_type', 'perigee_km', 'apogee_km', 'eccentricity', 
      'incl_degrees', 'period_minutes', 'orbit_overview'
    ];
    
    // 检查是否缺少任何重要字段
    for (const field of importantFields) {
      if (!existingFields.has(field)) {
        return true;
      }
    }
    
    // 检查现有字段的值是否都有效
    for (const item of satellite.orbit_info) {
      if (item.value) {
        for (const field of importantFields) {
          if (item.value[field] === undefined || item.value[field] === null || item.value[field] === '') {
            return true;
          }
        }
      }
    }
    
    // 已有完整的轨道信息，无需更新
    return false;
  }
  
  /**
   * 合并现有轨道信息和新轨道信息
   * @param existingInfo 现有轨道信息
   * @param newInfo 新轨道信息
   * @returns 合并后的轨道信息
   * @private
   */
  private mergeOrbitInfo(existingInfo: any[], newInfo: any[]): any[] {
    // 如果没有现有信息，直接返回新信息
    if (!existingInfo || !Array.isArray(existingInfo) || existingInfo.length === 0) {
      return newInfo;
    }
    
    // 如果新信息无效，保留现有信息
    if (!newInfo || !Array.isArray(newInfo) || newInfo.length === 0) {
      return existingInfo;
    }
    
    // 创建合并后的信息
    const mergedInfo = JSON.parse(JSON.stringify(existingInfo));
    
    // 获取现有信息中的所有字段
    const existingFields = new Set();
    for (const item of mergedInfo) {
      if (item.value) {
        Object.keys(item.value).forEach(key => existingFields.add(key));
      }
    }
    
    // 从新信息中添加缺失的字段
    for (const newItem of newInfo) {
      if (!newItem.value) continue;
      
      const newItemFields = Object.keys(newItem.value);
      const sourcesToAdd = new Set(newItem.sources || []);
      
      for (const field of newItemFields) {
        // 如果字段不存在或字段值无效
        const fieldMissing = !existingFields.has(field);
        const fieldValueInvalid = mergedInfo.some((item: any) => 
          item.value && item.value[field] !== undefined && 
          (item.value[field] === null || item.value[field] === '')
        );
        
        if (fieldMissing || fieldValueInvalid) {
          // 尝试找到第一个可用的元素来添加这个字段
          let fieldAdded = false;
          
          for (const item of mergedInfo) {
            if (item.value) {
              // 如果字段缺失，添加它
              if (fieldMissing) {
                item.value[field] = newItem.value[field];
                existingFields.add(field);
                fieldAdded = true;
              }
              // 如果字段值无效，更新它
              else if (fieldValueInvalid && (item.value[field] === null || item.value[field] === '')) {
                item.value[field] = newItem.value[field];
                fieldAdded = true;
              }
              
              // 添加新的数据源
              if (fieldAdded && Array.isArray(item.sources)) {
                for (const source of sourcesToAdd) {
                  if (!item.sources.includes(source)) {
                    item.sources.push(source);
                  }
                }
                break;
              }
            }
          }
          
          // 如果没有找到可以添加字段的元素，创建一个新元素
          if (!fieldAdded && mergedInfo.length === 0) {
            const newElement = {
              value: { [field]: newItem.value[field] },
              sources: Array.from(sourcesToAdd)
            };
            mergedInfo.push(newElement);
            existingFields.add(field);
          }
        }
      }
    }
    
    return mergedInfo;
  }
  
  /**
   * 从数组字段中提取值
   * @param field 数组字段
   * @returns 提取的值数组
   * @private
   */
  private extractValues(field: any[]): string[] {
    if (!field || !Array.isArray(field) || field.length === 0) {
      return [];
    }
    
    return field
      .filter(item => item && item.value)
      .map(item => item.value.toString());
  }
  
  /**
   * 将TLE数据映射到轨道信息字段
   * @param tleData TLE数据
   * @returns 轨道信息数组
   * @private
   */
  private mapTLEToOrbitInfo(tleData: any): any[] {
    // 构建轨道信息
    const orbitInfo: any = {
      value: {
        // 轨道概览
        orbit_overview: this.generateOrbitOverview(tleData),
        
        // 轨道高度/类型
        orbit_class: this.determineOrbitClass(tleData),
        orbit_type: this.determineOrbitType(tleData),
        
        // 地球同步轨道经度
        longitude_of_geo_degrees: tleData.mean_motion && Math.abs(tleData.mean_motion - 1.0) < 0.01 
          ? tleData.mean_anomaly || 0 
          : 0,
        
        // 近地点高度、远地点高度
        perigee_km: tleData.perigee ? tleData.perigee : 
                   (tleData.semi_major_axis ? (tleData.semi_major_axis * (1 - tleData.eccentricity) - 6371) : null),
        
        apogee_km: tleData.apogee ? tleData.apogee : 
                  (tleData.semi_major_axis ? (tleData.semi_major_axis * (1 + tleData.eccentricity) - 6371) : null),
        
        // 轨道偏心率
        eccentricity: tleData.eccentricity || 0,
        
        // 轨道倾角
        incl_degrees: tleData.inclination || 0,
        
        // 轨道周期
        period_minutes: tleData.period ? tleData.period : 
                       (tleData.mean_motion ? (1440 / tleData.mean_motion) : null),
        
        // 衰变日期
        decay_date: tleData.decay_date || null,
        
        // 部署/入轨日期
        deployed_date: tleData.launch_date || null
      },
      sources: ['orbital_tle']
    };
    
    // 过滤掉null或undefined的字段
    const keys = Object.keys(orbitInfo.value);
    for (const key of keys) {
      if (orbitInfo.value[key] === null || orbitInfo.value[key] === undefined) {
        delete orbitInfo.value[key];
      }
    }
    
    return [orbitInfo];
  }
  
  /**
   * 生成轨道概览信息
   * @param tleData TLE数据
   * @returns 轨道概览
   * @private
   */
  private generateOrbitOverview(tleData: any): string {
    if (!tleData) return '';
    
    let overview = '';
    
    // 如果有近地点和远地点高度
    if (tleData.perigee && tleData.apogee) {
      overview += `${Math.round(tleData.perigee)} km × ${Math.round(tleData.apogee)} km`;
      
      // 如果有轨道倾角
      if (tleData.inclination) {
        overview += `, ${tleData.inclination.toFixed(1)}°`;
      }
    } 
    // 如果有半长轴和偏心率
    else if (tleData.semi_major_axis && tleData.eccentricity !== undefined) {
      const perigee = Math.round(tleData.semi_major_axis * (1 - tleData.eccentricity) - 6371);
      const apogee = Math.round(tleData.semi_major_axis * (1 + tleData.eccentricity) - 6371);
      
      overview += `${perigee} km × ${apogee} km`;
      
      // 如果有轨道倾角
      if (tleData.inclination) {
        overview += `, ${tleData.inclination.toFixed(1)}°`;
      }
    } 
    // 如果只有轨道高度和轨道倾角
    else if (tleData.mean_altitude && tleData.inclination) {
      overview += `${Math.round(tleData.mean_altitude)} km, ${tleData.inclination.toFixed(1)}°`;
    }
    
    // 如果是地球同步轨道
    if (tleData.mean_motion && Math.abs(tleData.mean_motion - 1.0) < 0.01) {
      if (overview) {
        overview += ' (GEO)';
      } else {
        overview = 'GEO';
      }
    }
    
    return overview || '';
  }
  
  /**
   * 根据TLE数据确定轨道类型
   * @param tleData TLE数据
   * @returns 轨道类型
   * @private
   */
  private determineOrbitType(tleData: any): string {
    if (!tleData) return '';
    
    // 根据轨道倾角确定轨道类型
    if (tleData.inclination !== undefined) {
      // 极地轨道
      if (tleData.inclination > 80 && tleData.inclination < 100) {
        return 'Polar';
      }
      
      // 赤道轨道
      if (tleData.inclination < 20) {
        return 'Equatorial';
      }
      
      // 倾斜轨道
      return 'Inclined';
    }
    
    // 根据偏心率确定轨道类型
    if (tleData.eccentricity !== undefined) {
      // 圆轨道
      if (tleData.eccentricity < 0.01) {
        return 'Circular';
      }
      
      // 椭圆轨道
      if (tleData.eccentricity < 0.3) {
        return 'Elliptical';
      }
      
      // 高椭圆轨道
      return 'Highly Elliptical';
    }
    
    return '';
  }
  
  /**
   * 根据TLE数据确定轨道高度类别
   * @param tleData TLE数据
   * @returns 轨道高度类别
   * @private
   */
  private determineOrbitClass(tleData: any): string {
    if (!tleData) return '';
    
    // 计算平均轨道高度
    let meanAltitude = 0;
    
    if (tleData.mean_altitude) {
      meanAltitude = tleData.mean_altitude;
    } else if (tleData.perigee && tleData.apogee) {
      meanAltitude = (tleData.perigee + tleData.apogee) / 2;
    } else if (tleData.semi_major_axis) {
      meanAltitude = tleData.semi_major_axis - 6371;
    } else if (tleData.mean_motion) {
      // 使用开普勒第三定律计算半长轴
      const semiMajorAxis = Math.pow(6371 + 1.0 / Math.pow(tleData.mean_motion / (24 * 3600), 2/3), 1/3);
      meanAltitude = semiMajorAxis - 6371;
    }
    
    // 根据平均轨道高度确定轨道高度类别
    if (meanAltitude < 2000) {
      return 'LEO';  // 低地球轨道
    } else if (meanAltitude < 35786) {
      return 'MEO';  // 中地球轨道
    } else if (meanAltitude >= 35786 && meanAltitude <= 35790) {
      return 'GEO';  // 地球同步轨道
    } else {
      return 'HEO';  // 高地球轨道
    }
  }

  /**
   * 从ES中更新卫星users字段到本地数据库
   * 提取ES中的users字段并更新本地数据库中对应的记录
   * @returns 更新结果
   */
  async updateSatelliteUsersFromES(): Promise<{ success: boolean; message: string; updated: number }> {
    try {
      this.logger.log('开始从ES中更新卫星users字段到本地数据库');
      
      // 定义要查询的索引
      const indices = [
        'satsinfo_gunter',
        'satsinfo_n2yo',
        'satsinfo_nanosats',
        'satsinfo_satnogs',
        'satsinfo_ucs'
      ];
      
      // 构建查询 - 只查询有users字段的记录
      const query: any = {
        bool: {
          must: [
            {
              exists: {
                field: 'users'
              }
            }
          ]
        }
      };
      
      // 所有搜索结果
      let allSearchResults: any[] = [];
      // 批次大小
      const batchSize = 1000;
      
      // 使用scrollSearch方法获取所有数据
      for (const index of indices) {
        this.logger.log(`开始处理索引: ${index}`);
        
        try {
          // 使用scrollSearch方法获取所有数据
          const indexResults = await this.elasticsearchSatelliteService.scrollSearch({
            indices: [index],
            query,
            size: batchSize
          });
          
          this.logger.log(`从索引 ${index} 获取了 ${indexResults.length} 条有users字段的记录`);
          
          // 将结果添加到总结果中
          allSearchResults = allSearchResults.concat(indexResults);
        } catch (error) {
          this.logger.error(`处理索引 ${index} 时出错: ${error.message}`, error.stack);
          // 继续处理其他索引
        }
      }
      
      this.logger.log(`所有索引处理完成，总共获取 ${allSearchResults.length} 条有users字段的记录`);
      
      // 如果没有获取到记录，直接返回
      if (allSearchResults.length === 0) {
        this.logger.log('未找到任何有users字段的卫星数据');
        return { success: true, updated: 0, message: '未找到任何有users字段的卫星数据' };
      }
      
      // 更新计数器
      let updatedCount = 0;
      
      // 批量处理大小
      const processBatchSize = 50;
      
      // 分批处理数据
      for (let i = 0; i < allSearchResults.length; i += processBatchSize) {
        const batch = allSearchResults.slice(i, i + processBatchSize);
        this.logger.debug(`处理第 ${Math.floor(i / processBatchSize) + 1}/${Math.ceil(allSearchResults.length / processBatchSize)} 批数据`);
        
        // 并行处理这一批数据
        const updatePromises = batch.map(async (record) => {
          const source = record._source;
          const index = record._index.replace('satsinfo_', '');
          
          // 跳过没有users字段的记录
          if (!source.users) return false;
          
          // 提取标识信息
          const noradId = source.norad_id ? String(source.norad_id).trim() : '';
          const cosparId = source.cospar_id ? String(source.cospar_id).trim() : '';
          const satelliteName = source.satellite_name ? String(source.satellite_name).trim() : '';
          
          // 尝试查找对应的卫星记录
          let localSatellite: Satellite | undefined;
          
          // 按照NORAD ID查找
          if (noradId) {
            localSatellite = await this.findSatelliteByNoradId(noradId);
          }
          
          // 如果没找到，按照COSPAR ID查找
          if (!localSatellite && cosparId) {
            localSatellite = await this.findSatelliteByCosparId(cosparId);
          }
          
          // 如果没找到，按照卫星名称查找
          if (!localSatellite && satelliteName) {
            localSatellite = await this.findSatelliteByName(satelliteName);
          }
          
          // 如果找到本地卫星记录
          if (localSatellite) {
            // 确保users字段是数组
            if (!localSatellite.users) {
              localSatellite.users = [];
            }
            
            // 提取users字段值
            const usersValues = Array.isArray(source.users) ? source.users : [source.users];
            
            // 遍历用户值
            for (const userValue of usersValues) {
              // 检查是否已存在相同值
              const existingUserItem = localSatellite.users.find((item: any) => 
                JSON.stringify(item.value) === JSON.stringify(userValue)
              );
              
              if (existingUserItem) {
                // 如果值已存在，检查来源是否需要更新
                if (!existingUserItem.sources.includes(index)) {
                  existingUserItem.sources.push(index);
                }
              } else {
                // 如果值不存在，添加新记录
                localSatellite.users.push({
                  value: userValue,
                  sources: [index]
                });
              }
            }
            
            // 保存更新后的卫星记录
            await this.satelliteRepository.save(localSatellite);
            return true;
          }
          
          return false;
        });
        
        // 等待所有更新完成
        const updateResults = await Promise.all(updatePromises);
        
        // 统计更新数量
        updatedCount += updateResults.filter(Boolean).length;
        
        this.logger.debug(`已更新 ${updatedCount} 条记录`);
      }
      
      this.logger.log(`卫星users字段更新完成，共更新 ${updatedCount} 条记录`);
      
      return {
        success: true,
        updated: updatedCount,
        message: `成功从ES更新 ${updatedCount} 条卫星的users字段`
      };
    } catch (error) {
      this.logger.error(`更新卫星users字段失败: ${error.message}`, error.stack);
      return {
        success: false,
        updated: 0,
        message: `更新卫星users字段失败: ${error.message}`
      };
    }
  }
} 