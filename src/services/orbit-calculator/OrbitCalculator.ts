import { 
  ISatellitePosition, 
  ITLESatellite, 
  IOrbitCalculatorConfig 
} from './types';
import * as satellite from 'satellite.js';

/**
 * 轨道计算器服务类
 * 用于计算卫星位置，支持LEO和深空轨道卫星
 */
export class OrbitCalculator {
  private readonly config: IOrbitCalculatorConfig;
  private readonly logger: any; // 日志服务
  private readonly batchSize: number = 100; // 每批处理的卫星数量

  constructor(config: IOrbitCalculatorConfig) {
    this.config = config;
    // 确保logger始终可用，即使没有正确初始化
    this.logger = {
      log: console.log,
      debug: console.debug,
      info: console.info,
      warn: console.warn,
      error: console.error
    };
  }

  /**
   * 判断是否为深空轨道卫星
   * 根据TLE第二行的轨道周期来判断
   * 如果轨道周期大于225分钟（约对应海拔5877km），则认为是深空轨道
   * @param line2 TLE第二行
   * @returns 是否为深空轨道
   */
  private isDeepSpace(line2: string): boolean {
    // TLE第二行的平均运动（轨道周期的倒数）在第53-63位
    const meanMotion = parseFloat(line2.substring(52, 63));
    // 轨道周期(分钟) = 1440 / 平均运动
    const period = 1440 / meanMotion;
    return period > 225;
  }

  /**
   * 从TLE数据解析历元时间
   * @param line1 TLE第一行
   * @returns 历元时间
   */
  private getEpochFromTLE(line1: string): Date {
    // TLE第一行的历元时间在第19-32位
    const epochYear = parseInt(line1.substring(18, 20));
    const epochDays = parseFloat(line1.substring(20, 32));
    
    // 将两位年份转换为四位年份
    const year = epochYear < 57 ? 2000 + epochYear : 1900 + epochYear;
    
    // 计算具体日期
    const epoch = new Date(Date.UTC(year, 0, 1));
    epoch.setUTCMilliseconds(epochDays * 86400000);
    
    return epoch;
  }

  /**
   * 计算卫星位置
   * @param satInfo TLE卫星信息
   * @param time 计算时间点，默认为当前时间
   * @returns 卫星位置信息
   */
  public async calculatePosition(
    satInfo: ITLESatellite,
    time: Date = new Date()
  ): Promise<ISatellitePosition> {
    try {
      // 验证传入的卫星信息是否有效
      if (!satInfo) {
        throw new Error('卫星信息不能为空');
      }
      
      const { satId = 'UNKNOWN', name = 'Unknown Satellite', line1, line2 } = satInfo;
      
      // 验证必要的TLE行数据
      if (!line1 || !line2) {
        throw new Error(`卫星${satId}的TLE数据不完整`);
      }

      // 验证TLE格式但不中断处理流程
      if (line1.length < 69 || line2.length < 69) {
        this.logger.warn(`卫星${satId}的TLE数据长度不标准: line1=${line1.length}, line2=${line2.length}`);
        // 我们会继续尝试解析，而不是立即抛出错误
      }
      
      const epoch = this.getEpochFromTLE(line1);
      const isDeep = this.isDeepSpace(line2);

      // 使用satellite.js库计算卫星位置，添加更多错误检查
      let satrec;
      try {
        satrec = satellite.twoline2satrec(line1, line2);
        if (!satrec) {
          throw new Error('返回的satrec对象为空');
        }
      } catch (e) {
        throw new Error(`解析卫星${satId}的TLE数据失败: ${e.message || String(e)}`);
      }

      // 计算卫星位置和速度
      let positionAndVelocity;
      try {
        positionAndVelocity = satellite.propagate(satrec, time);
        if (!positionAndVelocity) {
          throw new Error('返回的位置和速度对象为空');
        }
        if (!positionAndVelocity.position) {
          throw new Error('没有返回有效的位置数据');
        }
        if (!positionAndVelocity.velocity) {
          throw new Error('没有返回有效的速度数据');
        }
      } catch (e) {
        throw new Error(`计算卫星${satId}的位置失败: ${e.message || String(e)}`);
      }

      // 确保position和velocity是EciVec3类型
      const position = positionAndVelocity.position as satellite.EciVec3<number>;
      const velocity = positionAndVelocity.velocity as satellite.EciVec3<number>;

      // 计算地理坐标
      const gmst = satellite.gstime(time);
      const geodeticCoords = satellite.eciToGeodetic(position, gmst);

      return {
        satId,
        name,
        epoch,
        position: {
          x: position.x,
          y: position.y,
          z: position.z
        },
        velocity: {
          x: velocity.x,
          y: velocity.y,
          z: velocity.z
        },
        latitude: satellite.degreesLat(geodeticCoords.latitude),
        longitude: satellite.degreesLong(geodeticCoords.longitude),
        altitude: geodeticCoords.height,
        algorithm: isDeep ? 'SDP4' : 'SGP4'
      };
    } catch (error) {
      this.logger.error('计算卫星位置失败:', error);
      throw error;
    }
  }

  /**
   * 批量计算多颗卫星的位置（并行计算）
   * @param satellites TLE卫星信息列表
   * @param time 计算时间点，默认为当前时间
   * @param options 计算选项
   * @returns 卫星位置信息列表和错误信息
   */
  public async calculatePositions(
    satellites: ITLESatellite[],
    time: Date = new Date(),
    options: {
      continueOnError?: boolean;  // 是否在发生错误时继续计算其他卫星
      batchSize?: number;         // 每批处理的卫星数量
    } = {}
  ): Promise<{
    positions: ISatellitePosition[];
    errors: Array<{
      satId: string;
      error: Error;
    }>;
  }> {
    const {
      continueOnError = true,
      batchSize = this.batchSize
    } = options;

    const positions: ISatellitePosition[] = [];
    const errors: Array<{ satId: string; error: Error }> = [];

    // 验证输入参数
    if (!satellites || !Array.isArray(satellites)) {
      throw new Error('卫星列表不能为空且必须是数组');
    }

    if (satellites.length === 0) {
      this.logger.warn('卫星列表为空，无法进行计算');
      return { positions, errors };
    }

    // 验证时间参数
    if (!(time instanceof Date) || isNaN(time.getTime())) {
      throw new Error('无效的时间参数');
    }

    // 将卫星列表分成多个批次
    const batches: ITLESatellite[][] = [];
    for (let i = 0; i < satellites.length; i += batchSize) {
      batches.push(satellites.slice(i, i + batchSize));
    }

    try {
      // 逐批并行处理
      for (const batch of batches) {
        // 预检查批次中的卫星数据
        const validatedBatch = batch.filter(sat => {
          if (!sat) {
            this.logger.warn('发现空的卫星数据，已跳过');
            errors.push({
              satId: '未知卫星',
              error: new Error('卫星数据为空')
            });
            return false;
          }
          
          // 检查必要的TLE数据
          if (!sat.line1 || !sat.line2) {
            const satId = sat.satId || '未知卫星';
            this.logger.warn(`卫星${satId}的TLE数据不完整，已跳过`);
            errors.push({
              satId: satId,
              error: new Error('TLE数据不完整')
            });
            return false;
          }
          
          return true;
        });
        
        if (validatedBatch.length === 0) {
          this.logger.warn('当前批次中没有有效的卫星数据，跳过处理');
          continue;
        }
        
        const batchPromises = validatedBatch.map(async (sat) => {
          try {
            const position = await this.calculatePosition(sat, time);
            return {
              success: true as const,
              position
            };
          } catch (error) {
            if (!continueOnError) {
              throw error;
            }
            return {
              success: false as const,
              error: {
                satId: sat.satId || '未知卫星',
                error: error instanceof Error ? error : new Error(String(error))
              }
            };
          }
        });

        // 并行执行当前批次的所有计算
        const results = await Promise.all(batchPromises);

        // 处理结果
        for (const result of results) {
          try {
            if (result?.success) {
              if (result.position) {
                positions.push(result.position);
              } else {
                this.logger.warn('计算结果标记为成功但没有position数据');
              }
            } else if (result?.error) {
              // 安全地访问error对象
              const satId = result.error?.satId || '未知卫星';
              const errorObj = result.error?.error;
              const errorMessage = errorObj instanceof Error ? errorObj : 
                               (typeof errorObj === 'string' ? errorObj : '未知错误');
              
              this.logger.error(`计算卫星 ${satId} 位置失败:`, errorMessage);
              
              errors.push({
                satId: satId,
                error: errorObj instanceof Error ? errorObj : new Error(String(errorMessage))
              });
            } else {
              // 处理既不是成功也没有错误信息的情况
              this.logger.error('计算卫星位置返回了无效的结果格式:', 
                            JSON.stringify(result, (key, value) => 
                              value instanceof Error ? value.toString() : value));
              errors.push({
                satId: '未知卫星',
                error: new Error('计算结果格式无效')
              });
            }
          } catch (e) {
            // 捕获处理结果时可能发生的任何错误
            this.logger.error('处理卫星计算结果时发生错误:', e);
            errors.push({
              satId: '未知卫星',
              error: e instanceof Error ? e : new Error(String(e))
            });
          }
        }
      }

      return { positions, errors };
    } catch (error) {
      this.logger.error('批量计算卫星位置时发生错误:', error);
      throw error;
    }
  }

  /**
   * 异步迭代器方式批量计算卫星位置
   * 适用于大量卫星的情况，可以流式处理结果
   * @param satellites TLE卫星信息列表
   * @param time 计算时间点
   * @param batchSize 每批处理的卫星数量
   */
  public async *calculatePositionsStream(
    satellites: ITLESatellite[],
    time: Date = new Date(),
    batchSize: number = this.batchSize
  ): AsyncGenerator<{
    position?: ISatellitePosition;
    error?: {
      satId: string;
      error: Error;
    };
  }> {
    // 将卫星列表分成多个批次
    const batches: ITLESatellite[][] = [];
    for (let i = 0; i < satellites.length; i += batchSize) {
      batches.push(satellites.slice(i, i + batchSize));
    }

    // 逐批处理
    for (const batch of batches) {
      const batchPromises = batch.map(async (sat) => {
        try {
          const position = await this.calculatePosition(sat, time);
          return { position };
        } catch (error) {
          return {
            error: {
              satId: sat?.satId || '未知卫星',
              error: error instanceof Error ? error : new Error(String(error))
            }
          };
        }
      });

      // 并行执行当前批次的计算
      const results = await Promise.all(batchPromises);

      // 逐个产出结果
      for (const result of results) {
        yield result;
      }
    }
  }
} 