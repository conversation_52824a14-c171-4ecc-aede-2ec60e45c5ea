/**
 * 卫星位置信息接口
 */
export interface ISatellitePosition {
  satId: string;
  name: string;
  epoch: Date;
  position: {
    x: number;  // km
    y: number;  // km
    z: number;  // km
  };
  velocity: {
    x: number;  // km/s
    y: number;  // km/s
    z: number;  // km/s
  };
  latitude: number;   // degrees
  longitude: number;  // degrees
  altitude: number;   // km
  algorithm: 'SGP4' | 'SDP4';  // 使用的算法
}

/**
 * TLE卫星信息接口
 */
export interface ITLESatellite {
  satId: string;
  name: string;
  line1: string;
  line2: string;
}

/**
 * 轨道计算器配置接口
 */
export interface IOrbitCalculatorConfig {
  wgs84: {
    earthRadius: number;  // km
    mu: number;          // 地球引力常数 (km^3/s^2)
    j2: number;          // J2摄动系数
    j3: number;          // J3摄动系数
    j4: number;          // J4摄动系数
  };
} 