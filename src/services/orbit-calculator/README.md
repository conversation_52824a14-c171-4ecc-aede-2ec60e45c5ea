# 轨道计算器服务

## 功能说明
该服务用于计算卫星的轨道位置，支持LEO和深空轨道卫星。主要功能包括：

1. 根据TLE数据计算卫星位置
2. 自动选择SGP4/SDP4算法
3. 提供地心惯性坐标系(ECI)和地理坐标系结果

## 使用方法

```typescript
import { OrbitCalculator } from './OrbitCalculator';

// 创建轨道计算器实例
const calculator = new OrbitCalculator({
  wgs84: {
    earthRadius: 6378.137,  // 地球半径(km)
    mu: 398600.4418,       // 地球引力常数(km^3/s^2)
    j2: 0.00108262998905,  // J2摄动系数
    j3: -0.00000253215306, // J3摄动系数
    j4: -0.00000161098761  // J4摄动系数
  }
});

// 准备TLE数据
const satInfo = {
  satId: 'NORAD_25544',
  name: 'ISS',
  line1: '1 25544U 98067A   21156.30527927  .00003432  00000-0  70541-4 0  9993',
  line2: '2 25544  51.6455 339.3385 0003456  83.5898  66.2665 15.48940926286793'
};

// 计算单颗卫星位置
const position = await calculator.calculatePosition(satInfo);

// 批量计算多颗卫星位置
const satellites = [satInfo, ...];
const positions = await calculator.calculatePositions(satellites);
```

## 输入参数说明

### TLE卫星信息 (ITLESatellite)
```typescript
{
  satId: string;    // 卫星ID (如NORAD编号)
  name: string;     // 卫星名称
  line1: string;    // TLE第一行
  line2: string;    // TLE第二行
}
```

## 输出数据说明

### 卫星位置信息 (ISatellitePosition)
```typescript
{
  satId: string;      // 卫星ID
  name: string;       // 卫星名称
  epoch: Date;        // TLE历元时间
  position: {         // ECI坐标系位置(km)
    x: number;
    y: number;
    z: number;
  };
  velocity: {         // ECI坐标系速度(km/s)
    x: number;
    y: number;
    z: number;
  };
  latitude: number;   // 地理纬度(度)
  longitude: number;  // 地理经度(度)
  altitude: number;   // 地理高度(km)
  algorithm: 'SGP4' | 'SDP4';  // 使用的算法
}
```

## 算法说明

1. SGP4 (Simplified General Perturbations 4)
   - 用于近地轨道卫星(LEO)
   - 轨道周期小于225分钟
   - 适用于大多数近地轨道卫星

2. SDP4 (Simplified Deep Space Perturbations 4)
   - 用于深空轨道卫星
   - 轨道周期大于225分钟
   - 考虑了更多的摄动力

## 注意事项

1. 所有距离单位为千米(km)，速度单位为千米/秒(km/s)
2. 位置计算使用ECI(地心惯性)坐标系
3. 地理坐标使用WGS84坐标系
4. TLE数据的有效期通常为几天到几周，请及时更新
5. 计算结果的精度取决于TLE数据的质量和时效性 