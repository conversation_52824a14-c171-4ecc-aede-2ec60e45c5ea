declare module 'satellite.js' {
  export interface EciVec3 {
    x: number;
    y: number;
    z: number;
  }

  export interface Satrec {
    error: number;
    satnum: string;
    epochyr: number;
    epochdays: number;
    jdsatepoch: number;
    ndot: number;
    nddot: number;
    bstar: number;
    inclo: number;
    nodeo: number;
    ecco: number;
    argpo: number;
    mo: number;
    no_kozai: number;
    classification: string;
    intldesg: string;
    ephtype: number;
    elnum: number;
    revnum: number;
  }

  export interface GeodeticPosition {
    longitude: number;
    latitude: number;
    height: number;
  }

  export interface PositionAndVelocity {
    position: EciVec3;
    velocity: EciVec3;
  }

  export function twoline2satrec(line1: string, line2: string): Satrec | null;
  export function propagate(satrec: Satrec, date: Date): PositionAndVelocity;
  export function gstime(date: Date): number;
  export function eciToGeodetic(position: EciVec3, gmst: number): GeodeticPosition;
  export function degreesLat(radians: number): number;
  export function degreesLong(radians: number): number;
} 