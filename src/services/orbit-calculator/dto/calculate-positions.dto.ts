import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON>rray, IsDateString, IsOptional, ValidateNested, IsBoolean, IsNumber, Min, Max, IsString, IsNotEmpty } from 'class-validator';
import { Type } from 'class-transformer';

export class TLESatelliteDto {
  @ApiProperty({
    description: '卫星ID (如NORAD编号)',
    example: '25544',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  satId: string;

  @ApiProperty({
    description: '卫星名称',
    example: 'ISS (ZARYA)',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'TLE数据第一行',
    example: '1 25544U 98067A   24054.88088078  .00014558  00000+0  26729-3 0  9990',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  line1: string;

  @ApiProperty({
    description: 'TLE数据第二行',
    example: '2 25544  51.6416 150.2506 0004789  89.9089  32.8809 15.49512781434577',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  line2: string;
}

export class CalculatePositionsRequestDto {
  @ApiProperty({
    description: '卫星TLE数据列表',
    type: [TLESatelliteDto],
    example: [
      {
        satId: '25544',
        name: 'ISS (ZARYA)',
        line1: '1 25544U 98067A   24054.88088078  .00014558  00000+0  26729-3 0  9990',
        line2: '2 25544  51.6416 150.2506 0004789  89.9089  32.8809 15.49512781434577'
      },
      {
        satId: '48274',
        name: 'STARLINK-3432',
        line1: '1 48274U 21041AF  24054.86885282  .00002571  00000+0  16843-3 0  9994',
        line2: '2 48274  53.0559  30.0121 0001038  89.7222 270.3898 15.06396635146374'
      }
    ],
    isArray: true,
    required: true
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TLESatelliteDto)
  satellites: TLESatelliteDto[];

  @ApiProperty({
    description: '计算时间点(UTC)',
    example: '2024-02-24T08:30:00Z',
    required: false
  })
  @IsDateString()
  @IsOptional()
  time?: string;

  @ApiProperty({
    description: '发生错误时是否继续计算其他卫星',
    example: true,
    required: false,
    default: true
  })
  @IsBoolean()
  @IsOptional()
  continueOnError?: boolean;

  @ApiProperty({
    description: '每批处理的卫星数量',
    example: 100,
    required: false,
    minimum: 1,
    maximum: 1000,
    default: 100
  })
  @IsNumber()
  @IsOptional()
  @Min(1)
  @Max(1000)
  batchSize?: number;
}

export class SatellitePositionDto {
  @ApiProperty({
    description: '卫星ID',
    example: '25544'
  })
  satId: string;

  @ApiProperty({
    description: '卫星名称',
    example: 'ISS (ZARYA)'
  })
  name: string;

  @ApiProperty({
    description: 'TLE历元时间',
    example: '2024-02-23T21:08:28.000Z'
  })
  epoch: Date;

  @ApiProperty({
    description: 'ECI坐标系位置(km)',
    example: {
      x: -6145.934,
      y: 2031.647,
      z: 2466.712
    }
  })
  position: {
    x: number;
    y: number;
    z: number;
  };

  @ApiProperty({
    description: 'ECI坐标系速度(km/s)',
    example: {
      x: -1.523,
      y: -5.537,
      z: 4.685
    }
  })
  velocity: {
    x: number;
    y: number;
    z: number;
  };

  @ApiProperty({
    description: '地理纬度(度)',
    example: 23.4558
  })
  latitude: number;

  @ApiProperty({
    description: '地理经度(度)',
    example: 161.7878
  })
  longitude: number;

  @ApiProperty({
    description: '地理高度(km)',
    example: 408.123
  })
  altitude: number;

  @ApiProperty({
    description: '使用的轨道计算算法',
    enum: ['SGP4', 'SDP4'],
    example: 'SGP4'
  })
  algorithm: 'SGP4' | 'SDP4';
}

export class CalculationErrorDto {
  @ApiProperty({
    description: '卫星ID',
    example: '25544'
  })
  satId: string;

  @ApiProperty({
    description: '错误信息',
    example: 'Failed to parse TLE data'
  })
  error: string;
}

export class CalculatePositionsResponseDto {
  @ApiProperty({
    description: '成功计算的卫星位置列表',
    type: [SatellitePositionDto],
    example: [{
      satId: '25544',
      name: 'ISS (ZARYA)',
      epoch: '2024-02-23T21:08:28.000Z',
      position: {
        x: -6145.934,
        y: 2031.647,
        z: 2466.712
      },
      velocity: {
        x: -1.523,
        y: -5.537,
        z: 4.685
      },
      latitude: 23.4558,
      longitude: 161.7878,
      altitude: 408.123,
      algorithm: 'SGP4'
    }]
  })
  positions: SatellitePositionDto[];

  @ApiProperty({
    description: '计算失败的卫星列表',
    type: [CalculationErrorDto],
    example: []
  })
  errors: CalculationErrorDto[];
} 