import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AggregationTask } from '../entities/aggregation-task.entity';

/**
 * 聚合任务服务
 * 用于管理卫星数据聚合任务
 */
@Injectable()
export class AggregationTaskService {
  private readonly logger = new Logger(AggregationTaskService.name);

  constructor(
    @InjectRepository(AggregationTask)
    private taskRepository: Repository<AggregationTask>,
  ) {}

  /**
   * 创建新的聚合任务
   * @param options 任务参数
   * @returns 创建的任务
   */
  async createTask(options: {
    task_type: string;
    parameters: any;
  }): Promise<AggregationTask> {
    this.logger.debug(`创建聚合任务: ${JSON.stringify(options)}`);
    
    const task = new AggregationTask();
    task.task_type = options.task_type;
    task.status = 'pending';
    task.start_time = new Date();
    task.parameters = options.parameters;
    task.progress = 0;
    
    return this.taskRepository.save(task);
  }

  /**
   * 开始执行任务
   * @param taskId 任务ID
   * @returns 更新后的任务
   */
  async startTask(taskId: number): Promise<AggregationTask> {
    const task = await this.taskRepository.findOne({ where: { id: taskId } });
    if (!task) {
      throw new Error(`未找到ID为${taskId}的任务`);
    }
    
    task.status = 'running';
    task.start_time = new Date(); // 更新实际开始时间
    
    return this.taskRepository.save(task);
  }

  /**
   * 更新任务进度
   * @param taskId 任务ID
   * @param progress 进度百分比 (0-100)
   * @param processedRecords 已处理的记录数
   * @returns 更新后的任务
   */
  async updateTaskProgress(
    taskId: number, 
    progress: number,
    processedRecords: number
  ): Promise<AggregationTask> {
    const task = await this.taskRepository.findOne({ where: { id: taskId } });
    if (!task) {
      throw new Error(`未找到ID为${taskId}的任务`);
    }
    
    task.progress = progress;
    task.processed_records = processedRecords;
    
    return this.taskRepository.save(task);
  }

  /**
   * 完成任务
   * @param taskId 任务ID
   * @param aggregatedRecords 聚合的记录数
   * @returns 更新后的任务
   */
  async completeTask(
    taskId: number,
    aggregatedRecords: number
  ): Promise<AggregationTask> {
    const task = await this.taskRepository.findOne({ where: { id: taskId } });
    if (!task) {
      throw new Error(`未找到ID为${taskId}的任务`);
    }
    
    task.status = 'completed';
    task.end_time = new Date();
    task.aggregated_records = aggregatedRecords;
    task.progress = 100;
    
    return this.taskRepository.save(task);
  }

  /**
   * 标记任务失败
   * @param taskId 任务ID
   * @param errorMessage 错误信息
   * @returns 更新后的任务
   */
  async failTask(
    taskId: number,
    errorMessage: string
  ): Promise<AggregationTask> {
    const task = await this.taskRepository.findOne({ where: { id: taskId } });
    if (!task) {
      throw new Error(`未找到ID为${taskId}的任务`);
    }
    
    task.status = 'failed';
    task.end_time = new Date();
    task.error_message = errorMessage;
    
    return this.taskRepository.save(task);
  }

  /**
   * 获取所有聚合任务
   * @param page 页码
   * @param limit 每页数量
   * @returns 任务列表和总数
   */
  async getTasks(page = 1, limit = 10): Promise<{ tasks: AggregationTask[]; total: number }> {
    const [tasks, total] = await this.taskRepository.findAndCount({
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });
    
    return { tasks, total };
  }

  /**
   * 获取指定任务详情
   * @param taskId 任务ID
   * @returns 任务详情
   */
  async getTaskById(taskId: number): Promise<AggregationTask> {
    const task = await this.taskRepository.findOne({ where: { id: taskId } });
    if (!task) {
      throw new Error(`未找到ID为${taskId}的任务`);
    }
    
    return task;
  }

  /**
   * 获取正在运行的任务
   * @returns 正在运行的任务列表
   */
  async getRunningTasks(): Promise<AggregationTask[]> {
    try {
      this.logger.debug('获取正在运行的聚合任务');
      const tasks = await this.taskRepository.find({
        where: { status: 'running' },
        order: { start_time: 'DESC' },
      });
      
      this.logger.debug(`成功获取正在运行的任务，共 ${tasks.length} 个`);
      return tasks;
    } catch (error) {
      this.logger.error(`获取正在运行的任务失败: ${error.message}`, error.stack);
      return []; // 出错时返回空数组，避免抛出异常
    }
  }

  /**
   * 获取最新的任务
   * @returns 最新的任务
   */
  async getLatestTask(): Promise<AggregationTask | null> {
    try {
      this.logger.debug('获取最新聚合任务');
      
      // 使用更明确的查询方式
      const tasks = await this.taskRepository.find({
        order: { 
          createdAt: 'DESC' 
        },
        take: 1
      });
      
      const task = tasks.length > 0 ? tasks[0] : null;
      
      if (task) {
        this.logger.debug(`成功获取最新任务, ID: ${task.id}, 类型: ${task.task_type}, 状态: ${task.status}`);
      } else {
        this.logger.debug('未找到任何聚合任务');
      }
      
      return task;
    } catch (error) {
      this.logger.error(`获取最新任务失败: ${error.message}`, error.stack);
      return null; // 出错时返回null，避免抛出异常
    }
  }
} 