import { Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { ElasticsearchNewsService } from '../../elasticsearch/services/elasticsearch.news.service';
import { 
  NewsSchedulerConfig, 
  TaskConfig, 
  TaskStatus, 
  TaskExecutionResult,
  getNewsSchedulerConfig,
  validateNewsSchedulerConfig
} from '../../../config/news-scheduler.config';

/**
 * 新闻处理定时任务服务
 * 负责定时执行新闻翻译和主题提取的定时任务逻辑
 */
@Injectable()
export class NewsSchedulerService {
  private readonly logger = new Logger(NewsSchedulerService.name);
  private readonly config: NewsSchedulerConfig;
  
  // 任务状态管理
  private currentStatus: TaskStatus = TaskStatus.IDLE;
  private lastExecutionResults: TaskExecutionResult[] = [];
  private isRunning = false;

  constructor(
    private readonly newsService: ElasticsearchNewsService
  ) {
    // 加载配置
    this.config = getNewsSchedulerConfig();
    
    // 验证配置
    try {
      validateNewsSchedulerConfig(this.config);
      this.logger.log(`新闻定时任务服务初始化完成，配置: ${JSON.stringify({
        enabled: this.config.enabled,
        cronExpression: this.config.cronExpression,
        timezone: this.config.timezone
      })}`);
    } catch (error) {
      this.logger.error(`新闻定时任务配置验证失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 定时任务：执行新闻翻译和主题提取
   * 使用动态cron表达式，从配置文件读取
   */
  @Cron('0 5,12,18 * * *', {
    name: 'newsProcessingTask',
    timeZone: 'Asia/Shanghai'
  })
  async scheduledNewsProcessing(): Promise<void> {
    // 检查是否启用定时任务
    if (!this.config.enabled) {
      this.logger.debug('新闻定时任务已禁用，跳过执行');
      return;
    }

    // 检查是否有任务正在运行
    if (this.isRunning) {
      this.logger.warn('新闻处理任务正在运行中，跳过本次执行');
      return;
    }

    this.logger.log('🕐 新闻处理定时任务触发：开始执行新闻翻译和主题提取');
    
    try {
      await this.executeNewsProcessingPipeline();
    } catch (error) {
      this.logger.error('❌ 新闻处理定时任务执行失败', error.stack);
    }
  }

  /**
   * 执行新闻处理流水线
   * 先执行翻译任务，再执行主题提取任务
   */
  async executeNewsProcessingPipeline(): Promise<void> {
    this.isRunning = true;
    this.currentStatus = TaskStatus.RUNNING;
    this.lastExecutionResults = [];

    const pipelineStartTime = new Date();
    this.logger.log('🚀 开始执行新闻处理流水线');

    try {
      // 执行优化的翻译任务（包含主题提取）
      this.logger.log('📝 开始执行新闻翻译和主题提取任务（优化版一次性调用）');
      const translationResult = await this.executeTranslationTask();
      this.lastExecutionResults.push(translationResult);

      if (translationResult.status === TaskStatus.FAILED) {
        throw new Error(`翻译和主题提取任务失败: ${translationResult.error}`);
      }

      // 显示翻译和主题提取的统计信息
      const stats = translationResult.statistics;
      const translationStats = `翻译: 成功${stats?.success || 0}，失败${stats?.failed || 0}`;
      const themeStats = stats?.themeExtraction 
        ? `，主题提取: 成功${stats?.themeExtraction?.success}，失败${stats?.themeExtraction?.failed}，跳过${stats?.themeExtraction?.skipped}`
        : '';
      
      this.logger.log(`✅ 翻译和主题提取任务完成，耗时: ${translationResult.duration}ms，${translationStats}${themeStats}`);

      // 流水线执行完成
      const pipelineEndTime = new Date();
      const pipelineDuration = pipelineEndTime.getTime() - pipelineStartTime.getTime();
      
      this.currentStatus = TaskStatus.COMPLETED;
      this.logger.log(`🎉 新闻处理流水线执行完成！总耗时: ${pipelineDuration}ms（使用优化的一次性调用，大幅减少API调用次数）`);

    } catch (error) {
      this.currentStatus = TaskStatus.FAILED;
      this.logger.error(`❌ 新闻处理流水线执行失败: ${error.message}`, error.stack);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * 执行翻译任务
   * @returns 任务执行结果
   */
  private async executeTranslationTask(): Promise<TaskExecutionResult> {
    const startTime = new Date();
    const result: TaskExecutionResult = {
      taskType: 'translation',
      status: TaskStatus.RUNNING,
      startTime
    };

    try {
      // 构建翻译任务参数（启用自动主题提取以使用优化的一次性调用）
      const translationParams = {
        batchSize: this.config.translationConfig.batchSize,
        maxDocs: this.config.translationConfig.maxDocs,
        forceRetranslate: this.config.translationConfig.forceReprocess,
        specificIndexes: this.config.translationConfig.specificIndexes,
        llmMode: this.config.translationConfig.llmMode,
        customModel: this.config.translationConfig.customModel,
        autoExtractThemes: true  // 启用自动主题提取，使用优化的一次性调用
      };

      this.logger.debug(`翻译任务参数: ${JSON.stringify(translationParams)}`);

      // 执行翻译任务
      const statistics = await this.newsService.translateNews(translationParams);

      // 任务成功完成
      const endTime = new Date();
      result.status = TaskStatus.COMPLETED;
      result.endTime = endTime;
      result.duration = endTime.getTime() - startTime.getTime();
      result.statistics = statistics;

      return result;

    } catch (error) {
      // 任务执行失败
      const endTime = new Date();
      result.status = TaskStatus.FAILED;
      result.endTime = endTime;
      result.duration = endTime.getTime() - startTime.getTime();
      result.error = error.message;

      this.logger.error(`翻译任务执行失败: ${error.message}`, error.stack);
      return result;
    }
  }

  /**
   * 执行主题提取任务
   * @returns 任务执行结果
   */
  private async executeThemeExtractionTask(): Promise<TaskExecutionResult> {
    const startTime = new Date();
    const result: TaskExecutionResult = {
      taskType: 'theme_extraction',
      status: TaskStatus.RUNNING,
      startTime
    };

    try {
      // 构建主题提取任务参数
      const themeExtractionParams = {
        batchSize: this.config.themeExtractionConfig.batchSize,
        maxDocs: this.config.themeExtractionConfig.maxDocs,
        forceReextract: this.config.themeExtractionConfig.forceReprocess,
        specificIndexes: this.config.themeExtractionConfig.specificIndexes,
        llmMode: this.config.themeExtractionConfig.llmMode,
        customModel: this.config.themeExtractionConfig.customModel
      };

      this.logger.debug(`主题提取和内容类型识别任务参数: ${JSON.stringify(themeExtractionParams)}`);

      // 执行主题提取和内容类型识别任务
      const statistics = await this.newsService.extractNewsThemes(themeExtractionParams);

      // 任务成功完成
      const endTime = new Date();
      result.status = TaskStatus.COMPLETED;
      result.endTime = endTime;
      result.duration = endTime.getTime() - startTime.getTime();
      result.statistics = statistics;

      return result;

    } catch (error) {
      // 任务执行失败
      const endTime = new Date();
      result.status = TaskStatus.FAILED;
      result.endTime = endTime;
      result.duration = endTime.getTime() - startTime.getTime();
      result.error = error.message;

      this.logger.error(`主题提取和内容类型识别任务执行失败: ${error.message}`, error.stack);
      return result;
    }
  }

  /**
   * 手动触发新闻处理任务
   * @returns 任务执行结果
   */
  async triggerManualExecution(): Promise<{
    success: boolean;
    message: string;
    results?: TaskExecutionResult[];
  }> {
    if (this.isRunning) {
      return {
        success: false,
        message: '新闻处理任务正在运行中，请稍后再试'
      };
    }

    this.logger.log('🔧 手动触发新闻处理任务');

    try {
      await this.executeNewsProcessingPipeline();
      
      return {
        success: true,
        message: '新闻处理任务执行成功',
        results: this.lastExecutionResults
      };
    } catch (error) {
      return {
        success: false,
        message: `新闻处理任务执行失败: ${error.message}`,
        results: this.lastExecutionResults
      };
    }
  }

  /**
   * 获取任务状态
   * @returns 当前任务状态和最近执行结果
   */
  getTaskStatus(): {
    currentStatus: TaskStatus;
    isRunning: boolean;
    config: NewsSchedulerConfig;
    lastExecutionResults: TaskExecutionResult[];
  } {
    return {
      currentStatus: this.currentStatus,
      isRunning: this.isRunning,
      config: this.config,
      lastExecutionResults: this.lastExecutionResults
    };
  }

  /**
   * 更新配置（运行时动态更新）
   * @param newConfig 新的配置
   */
  updateConfig(newConfig: Partial<NewsSchedulerConfig>): void {
    Object.assign(this.config, newConfig);
    
    try {
      validateNewsSchedulerConfig(this.config);
      this.logger.log(`配置已更新: ${JSON.stringify(newConfig)}`);
    } catch (error) {
      this.logger.error(`配置更新失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 停止正在运行的任务（紧急停止）
   */
  async stopRunningTask(): Promise<void> {
    if (!this.isRunning) {
      this.logger.warn('没有正在运行的任务');
      return;
    }

    this.logger.warn('🛑 紧急停止正在运行的新闻处理任务');
    this.isRunning = false;
    this.currentStatus = TaskStatus.FAILED;
    
    // 记录停止操作
    const stopResult: TaskExecutionResult = {
      taskType: 'translation', // 或者根据当前执行的任务类型
      status: TaskStatus.FAILED,
      startTime: new Date(),
      endTime: new Date(),
      error: '任务被手动停止'
    };
    
    this.lastExecutionResults.push(stopResult);
  }
} 