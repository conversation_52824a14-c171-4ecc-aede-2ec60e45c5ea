import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Satellite } from '../entities/satellite.entity';
import { ConstellationsWithTleResponseDto, ConstellationWithTleDto } from '../dto/constellation-with-tle.dto';

/**
 * 卫星星座服务
 * 处理与卫星星座相关的查询和业务逻辑
 */
@Injectable()
export class ConstellationService {
  private readonly logger = new Logger(ConstellationService.name);

  constructor(
    @InjectRepository(Satellite)
    private readonly satelliteRepository: Repository<Satellite>,
  ) {}

  /**
   * 获取具有TLE轨道信息的卫星星座列表
   * @returns 包含星座名称和卫星数量的响应DTO
   */
  async getConstellationsWithTle(): Promise<ConstellationsWithTleResponseDto> {
    try {
      this.logger.log('查询具有TLE轨道信息的卫星星座');
      
      // 查询所有包含TLE轨道信息的卫星，不限制constellation字段
      const query = `
        SELECT s.* 
        FROM satellites s 
        WHERE s.orbit_info IS NOT NULL 
          AND s.orbit_info::text != '[]'
          AND (
            -- 检查orbit_info中是否包含orbital_tle来源
            s.orbit_info::text ILIKE '%orbital_tle%'
            -- 或使用JSON路径匹配sources数组中的orbital_tle
            OR jsonb_path_exists(s.orbit_info, '$[*].sources[*] ? (@ == "orbital_tle")')
          )
      `;
      
      this.logger.debug(`执行查询SQL: ${query}`);
      
      const satellites = await this.satelliteRepository.query(query);
      
      this.logger.debug(`找到${satellites.length}个具有TLE轨道信息的卫星记录`);
      
      // 获取数据库中卫星总数，用于比较
      const totalCount = await this.satelliteRepository.count();
      this.logger.debug(`数据库中卫星总数: ${totalCount}`);
      
      // 记录前几条记录的详细信息用于调试
      if (satellites.length > 0) {
        for (let i = 0; i < Math.min(5, satellites.length); i++) {
          this.logger.debug(`第${i+1}条记录ID: ${satellites[i].id}`);
          if (satellites[i].satellite_name) {
            this.logger.debug(`卫星名称: ${JSON.stringify(satellites[i].satellite_name)}`);
          }
          this.logger.debug(`星座信息: ${JSON.stringify(satellites[i].constellation)}`);
        }
      }
      
      // 已知的主要星座名称，用于子串匹配
      const constellationNames = [
        'Starlink',
        'OneWeb',
        'Planet Dove',
        'Planet Flock',
        'Planet',
        'Iridium',
        'Globalstar',
        'GaoFen',
        'Galileo',
        'BeiDou',
        'Compass',
        'GOES',
        'O3b',
        'SES',
        'Eutelsat',
        'Intelsat',
        'Inmarsat',
        'SkySat',
        'Orbcomm',
        'GLONASS',
        'GPS',
        'NavIC',
        'IRNSS',
        'Sentinel',
        'Landsat',
        'Meteosat',
        'FengYun',
        'Himawari',
        'Kuiper',
        'Jilin'   // 加入吉林卫星作为高分系列识别
      ];
      
      // 统计每个星座的卫星数量
      const constellationCount = new Map<string, number>();
      let processedCount = 0; // 成功处理的记录计数
      
      for (const satellite of satellites) {
        try {
          let constellationFound = false;
          
          // 1. 首先尝试从constellation字段提取星座信息
          if (satellite.constellation) {
            let constellationArray;
            
            if (typeof satellite.constellation === 'string') {
              try {
                constellationArray = JSON.parse(satellite.constellation);
              } catch (e) {
                this.logger.warn(`无法解析星座信息(${satellite.id}): ${e.message}`);
              }
            } else if (Array.isArray(satellite.constellation)) {
              constellationArray = satellite.constellation;
            } else if (typeof satellite.constellation === 'object' && satellite.constellation !== null) {
              constellationArray = [satellite.constellation];
            }
            
            if (Array.isArray(constellationArray) && constellationArray.length > 0) {
              for (const item of constellationArray) {
                if (item && typeof item === 'object' && 'value' in item && item.value) {
                  const constellationName = item.value.trim();
                  if (constellationName) {
                    const count = constellationCount.get(constellationName) || 0;
                    constellationCount.set(constellationName, count + 1);
                    constellationFound = true;
                  }
                }
              }
            }
          }
          
          // 获取卫星所有可能的名称，包括主名称和别名
          const getAllSatelliteNames = (field: any): string[] => {
            const names: string[] = [];
            
            if (!field) return names;
            
            if (typeof field === 'string') {
              try {
                const parsed = JSON.parse(field);
                if (Array.isArray(parsed)) {
                  parsed.forEach(item => {
                    if (item && item.value) names.push(item.value.toString());
                  });
                }
              } catch (e) {
                names.push(field);
              }
            } else if (Array.isArray(field)) {
              field.forEach(item => {
                if (item && item.value) names.push(item.value.toString());
              });
            } else if (typeof field === 'object' && field !== null) {
              if (field.value) names.push(field.value.toString());
            }
            
            return names;
          };
          
          // 星座名称映射（处理某些需要特殊映射的星座名称）
          const constellationMapping: Record<string, string> = {
            'Jilin': 'GaoFen',  // 吉林卫星归为高分系列
            'Planet Flock': 'Planet Dove',  // Planet Flock归为Planet Dove
            'SkySat': 'Planet SkySat',
            'Compass': 'BeiDou',  // Compass映射到BeiDou
            'BeiDou Compass': 'BeiDou',  // BeiDou Compass也映射到BeiDou
          };
          
          // 2. 如果constellation字段没有有效的星座信息，尝试从卫星名称和别名中提取
          if (!constellationFound) {
            // 获取所有可能的卫星名称，包括主名称和别名
            const allNames = [
              ...getAllSatelliteNames(satellite.satellite_name),
              ...getAllSatelliteNames(satellite.alternative_name)
            ];
            
            if (allNames.length > 0) {
              this.logger.debug(`卫星 ${satellite.id} 所有名称: ${allNames.join(', ')}`);
            
              for (const satelliteName of allNames) {
                if (!satelliteName) continue;
                
                // 使用子串匹配查找星座名称
                for (const name of constellationNames) {
                  if (satelliteName.toLowerCase().includes(name.toLowerCase())) {
                    // 获取映射后的星座名（如果有映射规则的话）
                    const mappedName = constellationMapping[name] || name;
                    const count = constellationCount.get(mappedName) || 0;
                    constellationCount.set(mappedName, count + 1);
                    constellationFound = true;
                    
                    if ((name === 'GaoFen' || name === 'Jilin') && processedCount < 5) {
                      this.logger.debug(`GaoFen星座卫星: ID=${satellite.id}, 名称=${satelliteName}, 匹配=${name}`);
                    }
                    
                    break; // 找到匹配的星座后停止
                  }
                }
                
                if (constellationFound) break;
              }
            }
          }
          
          if (constellationFound) {
            processedCount++;
          }
        } catch (error) {
          this.logger.warn(`处理卫星${satellite.id}的星座信息时出错: ${error.message}`);
        }
      }
      
      this.logger.debug(`成功处理了${processedCount}条星座信息`);
      this.logger.debug(`找到了${constellationCount.size}个不同的星座`);
      
      // 记录找到的所有星座名称用于调试
      const constellationNamesForDisplay = Array.from(constellationCount.keys()).join(', ');
      this.logger.debug(`找到的星座: ${constellationNamesForDisplay}`);
      
      // 构建响应DTO
      const constellations: ConstellationWithTleDto[] = [];
      constellationCount.forEach((count, name) => {
        // 过滤掉BeiDou Compass和Compass星座，因为已经合并到BeiDou中
        if (name !== 'BeiDou Compass' && name !== 'Compass') {
          constellations.push({
            name,
            satelliteCount: count,
          });
        }
      });
      
      // 按卫星数量降序排序
      constellations.sort((a, b) => b.satelliteCount - a.satelliteCount);
      
      const response = new ConstellationsWithTleResponseDto();
      response.total = constellations.length;
      response.constellations = constellations;
      
      return response;
    } catch (error) {
      this.logger.error(`查询具有TLE轨道信息的卫星星座列表失败: ${error.message}`, error.stack);
      throw new Error(`查询具有TLE轨道信息的卫星星座失败: ${error.message}`);
    }
  }
} 