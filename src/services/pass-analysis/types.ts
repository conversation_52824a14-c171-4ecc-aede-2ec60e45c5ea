/**
 * 地面站位置信息接口
 */
export interface IGroundStation {
  name: string;      // 地面站名称
  longitude: number; // 经度(度)
  latitude: number;  // 纬度(度)
  altitude: number;  // 海拔高度(米)
}

/**
 * 卫星信息接口
 */
export interface ISatelliteInfo {
  satId: string;           // 卫星编号
  name: string;           // 卫星名称
  organization: string;   // 所属机构
  company: string;       // 所属公司
  country: string;       // 所属国家
  type: string;         // 卫星类型
  line1: string;        // TLE数据第一行
  line2: string;        // TLE数据第二行
}

/**
 * 过境点信息接口
 */
export interface IPassPoint {
  time: Date;        // 时间点
  azimuth: number;   // 方位角(度)
  elevation: number; // 仰角(度)
  range: number;     // 距离(千米)
}

/**
 * 过境信息接口
 */
export interface IPassInfo {
  satellite: {
    satId: string;        // 卫星编号
    name: string;        // 卫星名称
    organization: string; // 所属机构
    company: string;     // 所属公司
    country: string;     // 所属国家
    type: string;       // 卫星类型
  };
  startPass: IPassPoint;  // 入境点
  endPass: IPassPoint;    // 离境点
  duration: number;       // 过境时长(秒)
}

/**
 * 过境分析请求参数接口
 */
export interface IPassAnalysisParams {
  groundStation: IGroundStation;  // 地面站信息
  satellites: ISatelliteInfo[];   // 卫星信息列表
  startTime: Date;               // 开始时间
  endTime: Date;                // 结束时间
  minElevation?: number;        // 最小仰角(度)，默认为0
  timeStep?: number;            // 时间步长(秒)，默认为60
} 