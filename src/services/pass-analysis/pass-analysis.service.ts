import { Injectable } from '@nestjs/common';
import { OrbitCalculator } from '../orbit-calculator/OrbitCalculator';
import { IGroundStation, ISatelliteInfo, IPassInfo, IPassPoint, IPassAnalysisParams } from './types';

@Injectable()
export class PassAnalysisService {
  private readonly earthRadius = 6378.137; // WGS84地球半径(km)

  constructor(private readonly orbitCalculator: OrbitCalculator) {}

  /**
   * 将角度转换为弧度
   * @param degrees 角度值
   * @returns 弧度值
   */
  private deg2rad(degrees: number): number {
    return degrees * Math.PI / 180;
  }

  /**
   * 将弧度转换为角度
   * @param radians 弧度值
   * @returns 角度值
   */
  private rad2deg(radians: number): number {
    return radians * 180 / Math.PI;
  }

  /**
   * 计算地面站的ECI坐标
   * @param groundStation 地面站信息
   * @param time UTC时间
   * @returns ECI坐标
   */
  private calculateGroundStationECI(
    groundStation: IGroundStation,
    time: Date
  ): { x: number; y: number; z: number } {
    // 计算地球自转角度(GMST)
    const msPerDay = 86400000; // 每天的毫秒数
    const j2000 = new Date('2000-01-01T12:00:00Z');
    const daysSinceJ2000 = (time.getTime() - j2000.getTime()) / msPerDay;
    const gmst = (280.4606 + 360.9856473 * daysSinceJ2000) % 360;
    
    // 计算地面站的经度(相对于GMST)
    const longitude = groundStation.longitude;
    const lst = (gmst + longitude) % 360;
    const lstRad = this.deg2rad(lst);
    
    // 计算地面站的地心距离(考虑海拔)
    const latRad = this.deg2rad(groundStation.latitude);
    const r = this.earthRadius + groundStation.altitude / 1000; // 转换为km
    
    // 计算ECI坐标
    return {
      x: r * Math.cos(latRad) * Math.cos(lstRad),
      y: r * Math.cos(latRad) * Math.sin(lstRad),
      z: r * Math.sin(latRad)
    };
  }

  /**
   * 计算地面站到卫星的方位角、仰角和距离
   * @param groundStation 地面站信息
   * @param satPosition 卫星ECI位置
   * @param time UTC时间
   * @returns 方位角、仰角和距离
   */
  private calculateLookAngles(
    groundStation: IGroundStation,
    satPosition: { x: number; y: number; z: number },
    time: Date
  ): { azimuth: number; elevation: number; range: number } {
    // 1. 计算地面站的ECI坐标
    const groundECI = this.calculateGroundStationECI(groundStation, time);
    
    // 2. 计算地面站到卫星的向量
    const dx = satPosition.x - groundECI.x;
    const dy = satPosition.y - groundECI.y;
    const dz = satPosition.z - groundECI.z;
    
    // 3. 计算距离
    const range = Math.sqrt(dx * dx + dy * dy + dz * dz);
    
    // 4. 计算地面站的GMST角度
    const msPerDay = 86400000;
    const j2000 = new Date('2000-01-01T12:00:00Z');
    const daysSinceJ2000 = (time.getTime() - j2000.getTime()) / msPerDay;
    const gmst = (280.4606 + 360.9856473 * daysSinceJ2000) % 360;
    const gstRad = this.deg2rad(gmst);
    
    // 5. 转换到地面站的局部坐标系(SEZ: South-East-Zenith)
    const lonRad = this.deg2rad(groundStation.longitude);
    const latRad = this.deg2rad(groundStation.latitude);
    
    // 旋转矩阵变换
    const rotLon = gstRad + lonRad;
    const sinLat = Math.sin(latRad);
    const cosLat = Math.cos(latRad);
    const sinLon = Math.sin(rotLon);
    const cosLon = Math.cos(rotLon);
    
    // 计算SEZ坐标
    const s = sinLat * cosLon * dx + sinLat * sinLon * dy - cosLat * dz;
    const e = -sinLon * dx + cosLon * dy;
    const z = cosLat * cosLon * dx + cosLat * sinLon * dy + sinLat * dz;
    
    // 6. 计算方位角和仰角
    let azimuth = this.rad2deg(Math.atan2(e, s)) % 360;
    if (azimuth < 0) azimuth += 360;
    
    const elevation = this.rad2deg(Math.asin(z / range));
    
    return {
      azimuth,
      elevation,
      range
    };
  }

  /**
   * 判断卫星是否可见
   * @param elevation 仰角
   * @param minElevation 最小仰角
   * @returns 是否可见
   */
  private isVisible(elevation: number, minElevation: number = 0): boolean {
    return elevation >= minElevation;
  }

  /**
   * 分析单个卫星的过境情况
   * @param params 分析参数
   * @param satellite 卫星信息
   * @returns 过境信息列表
   */
  private async analyzeSingleSatellite(
    params: IPassAnalysisParams,
    satellite: ISatelliteInfo
  ): Promise<IPassInfo[]> {
    const passes: IPassInfo[] = [];
    let currentPass: {
      startPass?: IPassPoint;
      positions: IPassPoint[];
    } | null = null;

    const timeStep = params.timeStep || 60; // 默认60秒
    const minElevation = params.minElevation || 0;

    // 计算时间序列
    const totalSeconds = (params.endTime.getTime() - params.startTime.getTime()) / 1000;
    const steps = Math.ceil(totalSeconds / timeStep);

    for (let i = 0; i <= steps; i++) {
      const currentTime = new Date(params.startTime.getTime() + i * timeStep * 1000);

      // 计算卫星位置
      const satPosition = await this.orbitCalculator.calculatePosition({
        satId: satellite.satId,
        name: satellite.name,
        line1: satellite.line1,
        line2: satellite.line2
      }, currentTime);

      if (!satPosition) continue;

      // 计算观测角度
      const lookAngles = this.calculateLookAngles(
        params.groundStation,
        satPosition.position,
        currentTime
      );

      const passPoint: IPassPoint = {
        time: currentTime,
        ...lookAngles
      };

      // 判断可见性
      const isVisible = this.isVisible(lookAngles.elevation, minElevation);

      if (isVisible) {
        if (!currentPass) {
          // 开始新的过境
          currentPass = {
            startPass: passPoint,
            positions: [passPoint]
          };
        } else {
          // 继续当前过境
          currentPass.positions.push(passPoint);
        }
      } else if (currentPass) {
        // 结束当前过境
        const endPass = currentPass.positions[currentPass.positions.length - 1];
        passes.push({
          satellite: {
            satId: satellite.satId,
            name: satellite.name,
            organization: satellite.organization,
            company: satellite.company,
            country: satellite.country,
            type: satellite.type
          },
          startPass: currentPass.startPass!,
          endPass: endPass,
          duration: (endPass.time.getTime() - currentPass.startPass!.time.getTime()) / 1000
        });
        currentPass = null;
      }
    }

    return passes;
  }

  /**
   * 分析多个卫星的过境情况
   * @param params 分析参数
   * @returns 过境信息列表
   */
  public async analyzeMultipleSatellites(params: IPassAnalysisParams): Promise<IPassInfo[]> {
    const allPasses: IPassInfo[] = [];

    // 并行处理多个卫星
    const passPromises = params.satellites.map(satellite =>
      this.analyzeSingleSatellite(params, satellite)
    );

    const results = await Promise.all(passPromises);

    // 合并所有过境信息
    results.forEach(passes => {
      allPasses.push(...passes);
    });

    // 按入境时间排序
    return allPasses.sort((a, b) => a.startPass.time.getTime() - b.startPass.time.getTime());
  }
} 