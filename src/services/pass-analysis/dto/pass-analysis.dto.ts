import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsString, IsNumber, IsDateString, IsArray, ValidateNested, IsO<PERSON>al, <PERSON>, Max } from 'class-validator';

export class GroundStationDto {
  @ApiProperty({
    description: '地面站名称',
    example: '北京站',
    required: true
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: '经度(度)',
    example: 116.3833,
    required: true
  })
  @IsNumber()
  longitude: number;

  @ApiProperty({
    description: '纬度(度)',
    example: 39.9167,
    required: true
  })
  @IsNumber()
  latitude: number;

  @ApiProperty({
    description: '海拔高度(米)',
    example: 43.5,
    required: true
  })
  @IsNumber()
  altitude: number;
}

export class SatelliteInfoDto {
  @ApiProperty({
    description: '卫星编号',
    example: '25544',
    required: true
  })
  @IsString()
  satId: string;

  @ApiProperty({
    description: '卫星名称',
    example: 'ISS (ZARYA)',
    required: true
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: '所属机构',
    example: 'NASA',
    required: true
  })
  @IsString()
  organization: string;

  @ApiProperty({
    description: '所属公司',
    example: 'NASA',
    required: true
  })
  @IsString()
  company: string;

  @ApiProperty({
    description: '所属国家',
    example: 'USA',
    required: true
  })
  @IsString()
  country: string;

  @ApiProperty({
    description: '卫星类型',
    example: '空间站',
    required: true
  })
  @IsString()
  type: string;

  @ApiProperty({
    description: 'TLE数据第一行',
    example: '1 25544U 98067A   24054.88088078  .00014558  00000+0  26729-3 0  9990',
    required: true
  })
  @IsString()
  line1: string;

  @ApiProperty({
    description: 'TLE数据第二行',
    example: '2 25544  51.6416 150.2506 0004789  89.9089  32.8809 15.49512781434577',
    required: true
  })
  @IsString()
  line2: string;
}

export class PassAnalysisRequestDto {
  @ApiProperty({
    description: '地面站信息',
    type: GroundStationDto,
    required: true
  })
  @ValidateNested()
  @Type(() => GroundStationDto)
  groundStation: GroundStationDto;

  @ApiProperty({
    description: '卫星信息列表',
    type: [SatelliteInfoDto],
    required: true
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SatelliteInfoDto)
  satellites: SatelliteInfoDto[];

  @ApiProperty({
    description: '开始时间(UTC)',
    example: '2024-02-24T00:00:00Z',
    required: true
  })
  @IsDateString()
  startTime: string;

  @ApiProperty({
    description: '结束时间(UTC)',
    example: '2024-02-25T00:00:00Z',
    required: true
  })
  @IsDateString()
  endTime: string;

  @ApiProperty({
    description: '最小仰角(度)',
    example: 10,
    required: false,
    minimum: 0,
    maximum: 90,
    default: 0
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(90)
  minElevation?: number;

  @ApiProperty({
    description: '时间步长(秒)',
    example: 60,
    required: false,
    minimum: 1,
    maximum: 3600,
    default: 60
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(3600)
  timeStep?: number;
}

export class PassPointDto {
  @ApiProperty({
    description: '时间(UTC)',
    example: '2024-02-24T02:30:00Z'
  })
  time: Date;

  @ApiProperty({
    description: '方位角(度)',
    example: 123.45
  })
  azimuth: number;

  @ApiProperty({
    description: '仰角(度)',
    example: 45.67
  })
  elevation: number;

  @ApiProperty({
    description: '距离(千米)',
    example: 789.12
  })
  range: number;
}

export class SatelliteBasicInfoDto {
  @ApiProperty({
    description: '卫星编号',
    example: '25544'
  })
  @IsString()
  satId: string;

  @ApiProperty({
    description: '卫星名称',
    example: 'ISS (ZARYA)'
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: '所属机构',
    example: 'NASA'
  })
  @IsString()
  organization: string;

  @ApiProperty({
    description: '所属公司',
    example: 'NASA'
  })
  @IsString()
  company: string;

  @ApiProperty({
    description: '所属国家',
    example: 'USA'
  })
  @IsString()
  country: string;

  @ApiProperty({
    description: '卫星类型',
    example: '空间站'
  })
  @IsString()
  type: string;
}

export class PassInfoDto {
  @ApiProperty({
    description: '卫星信息',
    type: SatelliteBasicInfoDto
  })
  @ValidateNested()
  @Type(() => SatelliteBasicInfoDto)
  satellite: SatelliteBasicInfoDto;

  @ApiProperty({
    description: '入境信息',
    type: PassPointDto
  })
  @ValidateNested()
  @Type(() => PassPointDto)
  startPass: PassPointDto;

  @ApiProperty({
    description: '离境信息',
    type: PassPointDto
  })
  @ValidateNested()
  @Type(() => PassPointDto)
  endPass: PassPointDto;

  @ApiProperty({
    description: '过境时长(秒)',
    example: 600
  })
  @IsNumber()
  duration: number;
}

export class PassAnalysisResponseDto {
  @ApiProperty({
    description: '过境信息列表',
    type: [PassInfoDto]
  })
  passes: PassInfoDto[];
} 