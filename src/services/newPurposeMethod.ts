import { Injectable, Logger, InternalServerErrorException } from '@nestjs/common';

@Injectable()
export class SatellitePurposeService {
  private readonly logger = new Logger(SatellitePurposeService.name);

  /**
   * 模拟executeQuery方法，实际项目中应该调用数据库服务的方法
   */
  private async executeQuery(query: string): Promise<any> {
    // 这里模拟返回查询结果
    return {
      success: true,
      rows: []
    };
  }

  /**
   * 获取卫星主要用途集合
   * 提供标准化的卫星用途分类列表
   * @returns 主要用途数组，包含中英文表示
   */
  async getSatellitePurposes(): Promise<any> {
    try {
      // 定义主要用途类别及其中文翻译
      const mainCategories = [
        { en: 'Amateur Radio', cn: '业余无线电' },
        { en: 'Astronomy', cn: '天文学' },
        { en: 'Communications', cn: '通信' },
        { en: 'Data Relay', cn: '数据中继' },
        { en: 'Earth Observation', cn: '地球观测' },
        { en: 'Earth Science', cn: '地球科学' },
        { en: 'Education', cn: '教育' },
        { en: 'Geodesy', cn: '大地测量' },
        { en: 'Ionospheric Research', cn: '电离层研究' },
        { en: 'Magnetospheric Research', cn: '磁层研究' },
        { en: 'Meteorology', cn: '气象' },
        { en: 'Microgravity Research', cn: '微重力研究' },
        { en: 'Military', cn: '军事' },
        { en: 'Navigation', cn: '导航' },
        { en: 'Optical', cn: '光学' },
        { en: 'Platform', cn: '平台' },
        { en: 'Radar', cn: '雷达' },
        { en: 'Radar Calibration', cn: '雷达校准' },
        { en: 'Remote Sensing', cn: '遥感' },
        { en: 'Satellite Servicing', cn: '卫星服务' },
        { en: 'Science', cn: '科学' },
        { en: 'Search & Rescue', cn: '搜索救援' },
        { en: 'Solar Research', cn: '太阳研究' },
        { en: 'Space Burial', cn: '太空安葬' },
        { en: 'Space Exploration', cn: '太空探索' },
        { en: 'Space Station', cn: '空间站' },
        { en: 'Technology Demonstration', cn: '技术验证' },
        { en: 'Unknown', cn: '未知' },
        { en: 'UV Research', cn: '紫外线研究' },
        { en: 'X-ray Research', cn: 'X射线研究' }
      ];
      
      this.logger.log(`获取到 ${mainCategories.length} 个卫星用途`);
      
      // 结果已按字母顺序排序
      return {
        success: true,
        purposes: mainCategories
      };
    } catch (error) {
      this.logger.error(`Error fetching satellite purposes: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to fetch satellite purposes');
    }
  }
}

// 使用示例
async function testSatellitePurposes() {
  const service = new SatellitePurposeService();
  const result = await service.getSatellitePurposes();
  console.log('获取到卫星用途:', result.purposes.length);
  console.log(JSON.stringify(result, null, 2));
}

// 取消注释以下行可直接运行此文件测试
// testSatellitePurposes().catch(console.error); 