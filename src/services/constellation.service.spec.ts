import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConstellationService } from './constellation.service';
import { Satellite } from '../entities/satellite.entity';
import { ConstellationsWithTleResponseDto } from '../dto/constellation-with-tle.dto';

describe('ConstellationService', () => {
  let service: ConstellationService;
  let satelliteRepo: Repository<Satellite>;

  // 创建一个模拟的查询构建器
  const mockQueryBuilder = {
    select: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    getMany: jest.fn().mockResolvedValue([
      { constellation: 'Starlink', id: 1 },
      { constellation: 'Starlink', id: 2 },
      { constellation: 'OneWeb', id: 3 },
      { constellation: 'Starlink', id: 4 },
      { constellation: null, id: 5 },
      { constellation: 'OneWeb', id: 6 },
    ]),
  };

  const mockSatelliteRepo = {
    createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ConstellationService,
        {
          provide: getRepositoryToken(Satellite),
          useValue: mockSatelliteRepo,
        },
      ],
    }).compile();

    service = module.get<ConstellationService>(ConstellationService);
    satelliteRepo = module.get<Repository<Satellite>>(getRepositoryToken(Satellite));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getConstellationsWithTle', () => {
    it('should return constellations with TLE information', async () => {
      // Act
      const result = await service.getConstellationsWithTle();

      // Assert
      expect(result).toBeInstanceOf(ConstellationsWithTleResponseDto);
      expect(result.total).toBe(2);
      expect(result.constellations).toHaveLength(2);
      
      // 验证星座按卫星数量降序排序
      expect(result.constellations[0].name).toBe('Starlink');
      expect(result.constellations[0].satelliteCount).toBe(3);
      expect(result.constellations[1].name).toBe('OneWeb');
      expect(result.constellations[1].satelliteCount).toBe(2);
      
      // 验证查询构建器被正确调用
      expect(satelliteRepo.createQueryBuilder).toHaveBeenCalled();
      expect(mockQueryBuilder.select).toHaveBeenCalledWith('satellite.constellation');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('satellite.constellation IS NOT NULL');
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(expect.stringContaining('sources'));
    });

    it('should handle errors during query', async () => {
      // Arrange
      mockQueryBuilder.getMany.mockRejectedValueOnce(new Error('Database error'));

      // Act & Assert
      await expect(service.getConstellationsWithTle()).rejects.toThrow(
        '查询具有TLE轨道信息的卫星星座失败: Database error',
      );
    });
  });
}); 