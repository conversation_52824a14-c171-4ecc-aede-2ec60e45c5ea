import { Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { SatelliteService } from '../satellite.service';
import { 
  SatelliteSchedulerConfig, 
  SatelliteTaskStatus, 
  SatelliteTaskExecutionResult,
  getSatelliteSchedulerConfig,
  validateSatelliteSchedulerConfig
} from '../../../config/satellite-scheduler.config';

/**
 * 卫星数据定时任务服务
 * 负责定时执行卫星数据增量聚合的定时任务逻辑
 */
@Injectable()
export class SatelliteSchedulerService {
  private readonly logger = new Logger(SatelliteSchedulerService.name);
  private readonly config: SatelliteSchedulerConfig;
  
  // 任务状态管理
  private currentStatus: SatelliteTaskStatus = SatelliteTaskStatus.IDLE;
  private lastExecutionResult: SatelliteTaskExecutionResult | null = null;
  private isRunning = false;

  constructor(
    private readonly satelliteService: SatelliteService
  ) {
    // 加载配置
    this.config = getSatelliteSchedulerConfig();
    
    // 验证配置
    try {
      validateSatelliteSchedulerConfig(this.config);
      this.logger.log(`卫星数据定时任务服务初始化完成，配置: ${JSON.stringify({
        enabled: this.config.enabled,
        cronExpression: this.config.cronExpression,
        timezone: this.config.timezone,
        saveToDatabase: this.config.saveToDatabase
      })}`);
    } catch (error) {
      this.logger.error(`卫星数据定时任务配置验证失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 定时任务：执行卫星数据增量聚合
   * 默认每天3:00执行，可通过配置文件自定义
   */
  @Cron('0 3 * * *', {
    name: 'satelliteIncrementalAggregateTask',
    timeZone: 'Asia/Shanghai'
  })
  async scheduledSatelliteIncrementalAggregate(): Promise<void> {
    // 检查是否启用定时任务
    if (!this.config.enabled) {
      this.logger.debug('卫星数据定时任务已禁用，跳过执行');
      return;
    }

    // 检查是否有任务正在运行
    if (this.isRunning) {
      this.logger.warn('卫星数据增量聚合任务正在运行中，跳过本次执行');
      return;
    }

    this.logger.log('🕐 卫星数据定时任务触发：开始执行增量聚合');
    
    try {
      await this.executeSatelliteIncrementalAggregate();
    } catch (error) {
      this.logger.error('❌ 卫星数据定时任务执行失败', error.stack);
    }
  }

  /**
   * 执行卫星数据增量聚合任务
   * @returns 任务执行结果
   */
  async executeSatelliteIncrementalAggregate(): Promise<SatelliteTaskExecutionResult> {
    const startTime = new Date();
    let result: SatelliteTaskExecutionResult = {
      taskType: 'satellite_incremental_aggregate',
      status: SatelliteTaskStatus.RUNNING,
      startTime
    };

    try {
      // 设置任务状态
      this.isRunning = true;
      this.currentStatus = SatelliteTaskStatus.RUNNING;
      
      this.logger.log('🚀 开始执行卫星数据增量聚合任务');

      // 调用卫星服务的增量聚合方法
      const aggregateResult = await this.satelliteService.incrementalAggregateSatelliteData({
        saveToDatabase: this.config.saveToDatabase
      });

      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();

      // 更新结果
      result = {
        ...result,
        status: SatelliteTaskStatus.COMPLETED,
        endTime,
        duration,
        totalNewAggregated: aggregateResult.totalNewAggregated
      };

      this.logger.log(`✅ 卫星数据增量聚合任务完成: ${aggregateResult.message}, 耗时: ${duration}ms`);

    } catch (error) {
      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();

      // 更新结果
      result = {
        ...result,
        status: SatelliteTaskStatus.FAILED,
        endTime,
        duration,
        error: error.message
      };

      this.logger.error(`❌ 卫星数据增量聚合任务失败: ${error.message}, 耗时: ${duration}ms`, error.stack);
      
      // 如果配置了重试，可以在这里实现重试逻辑
      // 这里暂时不实现重试，因为卫星服务内部已经有错误处理
      
    } finally {
      // 重置任务状态
      this.isRunning = false;
      this.currentStatus = result.status;
      this.lastExecutionResult = result;
    }

    return result;
  }

  /**
   * 手动触发卫星数据增量聚合任务
   * @returns 任务执行结果
   */
  async triggerManualExecution(): Promise<{
    success: boolean;
    message: string;
    result?: SatelliteTaskExecutionResult;
  }> {
    this.logger.log('收到手动触发卫星数据增量聚合任务请求');

    // 检查是否有任务正在运行
    if (this.isRunning) {
      return {
        success: false,
        message: '卫星数据增量聚合任务正在运行中，请稍后再试'
      };
    }

    try {
      const result = await this.executeSatelliteIncrementalAggregate();

      return {
        success: result.status === SatelliteTaskStatus.COMPLETED,
        message: result.status === SatelliteTaskStatus.COMPLETED 
          ? `手动触发任务执行成功，新增聚合 ${result.totalNewAggregated} 条卫星数据`
          : `手动触发任务执行失败: ${result.error}`,
        result
      };
    } catch (error: any) {
      this.logger.error(`手动触发任务失败: ${error.message}`, error.stack);

      return {
        success: false,
        message: `手动触发任务失败: ${error.message}`
      };
    }
  }

  /**
   * 获取任务状态
   * @returns 任务状态信息
   */
  getTaskStatus(): {
    currentStatus: SatelliteTaskStatus;
    isRunning: boolean;
    config: SatelliteSchedulerConfig;
    lastExecutionResult: SatelliteTaskExecutionResult | null;
  } {
    return {
      currentStatus: this.currentStatus,
      isRunning: this.isRunning,
      config: this.config,
      lastExecutionResult: this.lastExecutionResult
    };
  }

  /**
   * 更新任务配置
   * @param configUpdate 配置更新参数
   */
  updateConfig(configUpdate: Partial<SatelliteSchedulerConfig>): void {
    this.logger.log(`更新卫星数据定时任务配置: ${JSON.stringify(configUpdate)}`);

    // 更新配置（这里简化处理，实际项目中可能需要更复杂的配置管理）
    Object.assign(this.config, configUpdate);

    // 验证更新后的配置
    try {
      validateSatelliteSchedulerConfig(this.config);
      this.logger.log('配置更新成功');
    } catch (error) {
      this.logger.error(`配置更新失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 停止正在运行的任务
   */
  async stopRunningTask(): Promise<void> {
    if (!this.isRunning) {
      this.logger.log('当前没有正在运行的任务');
      return;
    }

    this.logger.log('停止正在运行的卫星数据增量聚合任务');
    
    // 这里可以实现任务停止逻辑
    // 由于卫星聚合任务可能需要较长时间，实际停止可能需要等待当前批次完成
    // 这里暂时只是标记状态，实际项目中可能需要更复杂的停止机制
    
    this.logger.warn('任务停止请求已发送，任务将在当前批次完成后停止');
  }
} 