import { Controller, Get, Post, Body, Logger, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags, ApiBody, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { SatelliteSchedulerService } from '../services/satellite-scheduler/satellite-scheduler.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { 
  SatelliteSchedulerConfig, 
  SatelliteTaskStatus, 
  SatelliteTaskExecutionResult,
  SATELLITE_PREDEFINED_CRON_EXPRESSIONS 
} from '../../config/satellite-scheduler.config';

/**
 * 卫星数据定时任务控制器
 * 提供卫星数据增量聚合定时任务的管理API接口
 */
@Controller('api/satellite-scheduler')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('卫星数据定时任务')
export class SatelliteSchedulerController {
  private readonly logger = new Logger(SatelliteSchedulerController.name);

  constructor(private readonly schedulerService: SatelliteSchedulerService) {}

  /**
   * 获取定时任务状态
   */
  @Get('status')
  @ApiOperation({
    summary: '获取卫星数据定时任务状态',
    description: '获取当前卫星数据增量聚合定时任务的运行状态、配置信息和最近执行结果'
  })
  @ApiResponse({
    status: 200,
    description: '成功获取任务状态',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '获取任务状态成功' },
        data: {
          type: 'object',
          properties: {
            currentStatus: { type: 'string', enum: ['idle', 'running', 'completed', 'failed'] },
            isRunning: { type: 'boolean' },
            config: {
              type: 'object',
              properties: {
                enabled: { type: 'boolean' },
                cronExpression: { type: 'string' },
                timezone: { type: 'string' },
                maxRetries: { type: 'number' },
                retryDelay: { type: 'number' },
                saveToDatabase: { type: 'boolean' }
              }
            },
            lastExecutionResult: {
              type: 'object',
              properties: {
                taskType: { type: 'string', example: 'satellite_incremental_aggregate' },
                status: { type: 'string', enum: ['idle', 'running', 'completed', 'failed'] },
                startTime: { type: 'string', format: 'date-time' },
                endTime: { type: 'string', format: 'date-time' },
                duration: { type: 'number' },
                totalNewAggregated: { type: 'number' },
                error: { type: 'string' }
              }
            }
          }
        },
        timestamp: { type: 'string', format: 'date-time' }
      }
    }
  })
  getTaskStatus(): {
    success: boolean;
    message: string;
    data: {
      currentStatus: SatelliteTaskStatus;
      isRunning: boolean;
      config: SatelliteSchedulerConfig;
      lastExecutionResult: SatelliteTaskExecutionResult | null;
    };
    timestamp: string;
  } {
    this.logger.log('获取卫星数据定时任务状态');

    const status = this.schedulerService.getTaskStatus();

    return {
      success: true,
      message: '获取任务状态成功',
      data: status,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 手动触发卫星数据增量聚合任务
   */
  @Post('trigger')
  @ApiOperation({
    summary: '手动触发卫星数据增量聚合任务',
    description: '手动触发卫星数据增量聚合任务的执行，任务将在后台异步执行。该任务会调用 /local/satellite/incremental-aggregate 接口更新本地卫星数据。'
  })
  @ApiResponse({
    status: 200,
    description: '任务触发成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '手动触发任务执行成功，新增聚合 10 条卫星数据' },
        result: {
          type: 'object',
          properties: {
            taskType: { type: 'string', example: 'satellite_incremental_aggregate' },
            status: { type: 'string', enum: ['idle', 'running', 'completed', 'failed'] },
            startTime: { type: 'string', format: 'date-time' },
            endTime: { type: 'string', format: 'date-time' },
            duration: { type: 'number' },
            totalNewAggregated: { type: 'number' },
            error: { type: 'string' }
          }
        },
        timestamp: { type: 'string', format: 'date-time' }
      }
    }
  })
  async triggerManualExecution(): Promise<{
    success: boolean;
    message: string;
    result?: SatelliteTaskExecutionResult;
    timestamp: string;
  }> {
    this.logger.log('收到手动触发卫星数据增量聚合任务请求');

    try {
      const result = await this.schedulerService.triggerManualExecution();

      return {
        success: result.success,
        message: result.message,
        result: result.result,
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      this.logger.error(`手动触发任务失败: ${error.message}`, error.stack);

      return {
        success: false,
        message: `手动触发任务失败: ${error.message}`,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 更新定时任务配置
   */
  @Post('config')
  @ApiOperation({
    summary: '更新卫星数据定时任务配置',
    description: '动态更新卫星数据增量聚合定时任务的配置，包括执行时间、重试次数等参数'
  })
  @ApiBody({
    description: '配置更新参数',
    schema: {
      type: 'object',
      properties: {
        enabled: { type: 'boolean', description: '是否启用定时任务' },
        cronExpression: { type: 'string', description: 'cron表达式，定义执行时间' },
        timezone: { type: 'string', description: '时区' },
        maxRetries: { type: 'number', description: '最大重试次数' },
        retryDelay: { type: 'number', description: '重试延迟时间（毫秒）' },
        saveToDatabase: { type: 'boolean', description: '是否保存到数据库' }
      }
    },
    examples: {
      '更新执行时间': {
        summary: '更新定时任务执行时间',
        description: '将执行时间改为每天2:00',
        value: {
          cronExpression: '0 2 * * *'
        }
      },
      '更新为每12小时执行': {
        summary: '更新为每12小时执行',
        description: '将执行时间改为每天3:00和15:00',
        value: {
          cronExpression: '0 3,15 * * *'
        }
      },
      '禁用定时任务': {
        summary: '禁用定时任务',
        description: '临时禁用定时任务的自动执行',
        value: {
          enabled: false
        }
      },
      '更新重试配置': {
        summary: '更新重试配置',
        description: '调整最大重试次数和重试延迟',
        value: {
          maxRetries: 5,
          retryDelay: 600000
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '配置更新成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '配置更新成功' },
        timestamp: { type: 'string', format: 'date-time' }
      }
    }
  })
  updateConfig(@Body() configUpdate: Partial<SatelliteSchedulerConfig>): {
    success: boolean;
    message: string;
    timestamp: string;
  } {
    this.logger.log(`收到配置更新请求: ${JSON.stringify(configUpdate)}`);

    try {
      this.schedulerService.updateConfig(configUpdate);

      return {
        success: true,
        message: '配置更新成功',
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      this.logger.error(`配置更新失败: ${error.message}`, error.stack);

      return {
        success: false,
        message: `配置更新失败: ${error.message}`,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 停止正在运行的任务
   */
  @Post('stop')
  @ApiOperation({
    summary: '停止正在运行的卫星数据增量聚合任务',
    description: '紧急停止当前正在运行的卫星数据增量聚合任务'
  })
  @ApiResponse({
    status: 200,
    description: '任务停止成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '任务已停止' },
        timestamp: { type: 'string', format: 'date-time' }
      }
    }
  })
  async stopRunningTask(): Promise<{
    success: boolean;
    message: string;
    timestamp: string;
  }> {
    this.logger.log('收到停止任务请求');

    try {
      await this.schedulerService.stopRunningTask();

      return {
        success: true,
        message: '任务已停止',
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      this.logger.error(`停止任务失败: ${error.message}`, error.stack);

      return {
        success: false,
        message: `停止任务失败: ${error.message}`,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 获取预定义的cron表达式
   */
  @Get('cron-expressions')
  @ApiOperation({
    summary: '获取预定义的cron表达式',
    description: '获取系统预定义的常用cron表达式，方便用户选择合适的执行时间'
  })
  @ApiResponse({
    status: 200,
    description: '成功获取cron表达式列表',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '获取cron表达式成功' },
        data: {
          type: 'object',
          additionalProperties: { type: 'string' },
          example: {
            'DAILY_3AM': '0 3 * * *',
            'DAILY_2AM': '0 2 * * *',
            'EVERY_12_HOURS': '0 3,15 * * *'
          }
        },
        timestamp: { type: 'string', format: 'date-time' }
      }
    }
  })
  getPredefinedCronExpressions(): {
    success: boolean;
    message: string;
    data: typeof SATELLITE_PREDEFINED_CRON_EXPRESSIONS;
    timestamp: string;
  } {
    this.logger.log('获取预定义cron表达式');

    return {
      success: true,
      message: '获取cron表达式成功',
      data: SATELLITE_PREDEFINED_CRON_EXPRESSIONS,
      timestamp: new Date().toISOString()
    };
  }
} 