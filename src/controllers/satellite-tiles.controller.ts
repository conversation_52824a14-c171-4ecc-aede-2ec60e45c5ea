import { Controller, Get, Post, Body, UseGuards, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiBearerAuth, ApiTags, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { SatelliteTilesService } from '../services/satellite-tiles/satellite-tiles.service';
import { 
  GenerateSatelliteTilesDto, 
  GenerateTilesResponseDto, 
  TilesStatusResponseDto 
} from '../services/satellite-tiles/dto/generate-tiles.dto';

/**
 * 卫星3D Tiles点云控制器
 * 提供卫星点云数据的生成、查询和管理接口
 */
@Controller('tiles')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('卫星3D Tiles点云')
export class SatelliteTilesController {
  private readonly logger = new Logger(SatelliteTilesController.name);

  constructor(private readonly tilesService: SatelliteTilesService) {}

  @Post('generate')
  @ApiOperation({
    summary: '手动生成卫星点云数据',
    description: `
    手动触发卫星点云数据生成任务。该接口会：
    1. 获取所有卫星的最新TLE数据
    2. 计算当前时刻所有卫星的位置
    3. 生成3D点云数据文件
    4. 保存到public/tiles目录供前端使用
    
    ## 功能特性
    - 支持按轨道类型自动着色
    - 包含详细的统计信息
    - 支持笛卡尔和地理坐标系
    - 自动生成元数据文件
    
    ## 注意事项
    - 生成过程可能需要几分钟时间
    - 同时只能有一个生成任务运行
    - 生成的文件会覆盖之前的数据
    `
  })
  @ApiBody({
    type: GenerateSatelliteTilesDto,
    examples: {
      default: {
        summary: '默认配置生成',
        value: {
          forceRegenerate: false,
          colorByOrbitClass: true,
          includeStatistics: true,
          coordinateSystem: 'cartesian',
          compressionLevel: 6
        }
      },
      force_regenerate: {
        summary: '强制重新生成',
        value: {
          forceRegenerate: true,
          colorByOrbitClass: true,
          includeStatistics: true,
          coordinateSystem: 'cartesian',
          compressionLevel: 9
        }
      },
      geographic_coords: {
        summary: '使用地理坐标系',
        value: {
          forceRegenerate: false,
          colorByOrbitClass: true,
          includeStatistics: true,
          coordinateSystem: 'geographic',
          compressionLevel: 6
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '生成成功',
    type: GenerateTilesResponseDto,
    schema: {
      example: {
        success: true,
        message: '成功生成 5000 颗卫星的点云数据',
        taskId: 'task_2025-01-06T14_00_00_000Z',
        totalSatellites: 5000,
        generationDuration: 15000,
        outputPath: '/tiles/satellites.json'
      }
    }
  })
  @ApiResponse({
    status: 409,
    description: '已有任务正在运行',
    schema: {
      example: {
        success: false,
        message: '已有点云生成任务正在运行中，请稍后再试',
        statusCode: 409
      }
    }
  })
  @ApiResponse({
    status: 500,
    description: '生成失败',
    schema: {
      example: {
        success: false,
        message: '获取卫星TLE数据失败',
        statusCode: 500
      }
    }
  })
  async generateSatelliteTiles(
    @Body() generateDto: GenerateSatelliteTilesDto
  ): Promise<GenerateTilesResponseDto> {
    this.logger.log(`接收到手动生成卫星点云请求: ${JSON.stringify(generateDto)}`);
    
    try {
      const config = {
        outputPath: 'public/tiles',
        colorByOrbitClass: generateDto.colorByOrbitClass ?? true,
        includeStatistics: generateDto.includeStatistics ?? true,
        coordinateSystem: generateDto.coordinateSystem ?? 'cartesian',
        compressionLevel: generateDto.compressionLevel ?? 6
      };

      const result = await this.tilesService.generateSatellitePointCloudWithPush(generateDto);
      
      this.logger.log(`卫星点云生成成功: ${result.message}`);
      
      return {
        success: result.success,
        message: result.message,
        taskId: result.taskId,
        totalSatellites: result.totalSatellites,
        generationDuration: result.generationDuration,
        outputPath: result.outputPath,
        pushResults: result.pushResults,
        generatedFiles: result.generatedFiles,
        fileSizes: result.fileSizes
      };
      
    } catch (error) {
      this.logger.error(`卫星点云生成失败: ${error.message}`, error.stack);
      
      if (error.message.includes('正在运行中')) {
        throw new HttpException(
          {
            success: false,
            message: error.message,
            statusCode: HttpStatus.CONFLICT
          },
          HttpStatus.CONFLICT
        );
      }
      
      throw new HttpException(
        {
          success: false,
          message: error.message,
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('status')
  @ApiOperation({
    summary: '获取点云生成状态',
    description: `
    获取当前卫星点云数据的生成状态和元数据信息。
    
    ## 返回信息
    - 生成状态（idle/generating/error）
    - 最后生成时间
    - 下次计划生成时间
    - 卫星总数
    - 生成耗时
    - 错误信息（如果有）
    - 数据版本
    
    ## 用途
    - 监控定时任务执行状态
    - 获取数据更新时间
    - 检查系统健康状态
    `
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: TilesStatusResponseDto,
    schema: {
      example: {
        status: 'idle',
        lastGenerated: '2025-01-06T14:00:00Z',
        nextScheduled: '2025-01-06T18:00:00Z',
        totalSatellites: 5000,
        generationDuration: 15000,
        errorMessage: null,
        version: '1.0.0'
      }
    }
  })
  async getTilesStatus(): Promise<TilesStatusResponseDto> {
    this.logger.debug('获取卫星点云生成状态');
    
    try {
      // 获取当前任务状态
      const currentTask = this.tilesService.getCurrentTaskStatus();
      
      // 获取元数据
      const metadata = await this.tilesService.getMetadata();
      
      if (currentTask && currentTask.status === 'running') {
        // 有正在运行的任务
        return {
          status: 'generating',
          lastGenerated: metadata?.lastGenerated || '',
          nextScheduled: metadata?.nextScheduled || '',
          totalSatellites: currentTask.totalSatellites,
          generationDuration: metadata?.generationDuration || 0,
          version: metadata?.version || '1.0.0'
        };
      } else if (currentTask && currentTask.status === 'failed') {
        // 任务失败
        return {
          status: 'error',
          lastGenerated: metadata?.lastGenerated || '',
          nextScheduled: metadata?.nextScheduled || '',
          totalSatellites: metadata?.totalSatellites || 0,
          generationDuration: metadata?.generationDuration || 0,
          errorMessage: currentTask.errorMessage,
          version: metadata?.version || '1.0.0'
        };
      } else if (metadata) {
        // 正常状态，有元数据
        return {
          status: 'idle',
          lastGenerated: metadata.lastGenerated,
          nextScheduled: metadata.nextScheduled,
          totalSatellites: metadata.totalSatellites,
          generationDuration: metadata.generationDuration,
          errorMessage: metadata.errorMessage,
          version: metadata.version
        };
      } else {
        // 没有元数据，可能是首次运行
        return {
          status: 'idle',
          lastGenerated: '',
          nextScheduled: '',
          totalSatellites: 0,
          generationDuration: 0,
          version: '1.0.0'
        };
      }
    } catch (error) {
      this.logger.error(`获取点云状态失败: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          message: '获取点云状态失败',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('satellites')
  @ApiOperation({
    summary: '获取卫星点云数据',
    description: `
    获取最新生成的卫星点云数据文件内容。
    
    ## 数据格式
    返回包含所有卫星位置、属性和颜色信息的JSON数据：
    - 生成时间和计算时间
    - 卫星总数
    - 卫星列表（位置、属性、颜色）
    - 统计信息（轨道类型分布、高度范围）
    
    ## 用途
    - 前端3D可视化渲染
    - 数据分析和统计
    - 第三方应用集成
    
    ## 注意事项
    - 数据量可能较大，建议使用压缩传输
    - 数据每4小时更新一次
    - 如果没有生成过数据，返回404
    `
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        generatedAt: { type: 'string', format: 'date-time', example: '2025-01-06T14:00:00Z' },
        totalSatellites: { type: 'number', example: 5000 },
        calculationTime: { type: 'string', format: 'date-time', example: '2025-01-06T14:00:00Z' },
        satellites: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', example: '25544' },
              name: { type: 'string', example: 'ISS (ZARYA)' },
              position: {
                type: 'object',
                properties: {
                  x: { type: 'number', example: 1234567.89 },
                  y: { type: 'number', example: 2345678.90 },
                  z: { type: 'number', example: 3456789.01 }
                }
              },
              geographic: {
                type: 'object',
                properties: {
                  longitude: { type: 'number', example: 120.5 },
                  latitude: { type: 'number', example: 30.2 },
                  altitude: { type: 'number', example: 408000 }
                }
              },
              properties: {
                type: 'object',
                properties: {
                  noradId: { type: 'number', example: 25544 },
                  orbitClass: { type: 'string', example: 'LEO' },
                  inclination: { type: 'number', example: 51.64 }
                }
              },
              color: {
                type: 'object',
                properties: {
                  r: { type: 'number', example: 0 },
                  g: { type: 'number', example: 255 },
                  b: { type: 'number', example: 0 }
                }
              }
            }
          }
        },
        statistics: {
          type: 'object',
          properties: {
            orbitClassDistribution: {
              type: 'object',
              example: { 'LEO': 3000, 'MEO': 1500, 'GEO': 500 }
            },
            altitudeRange: {
              type: 'object',
              properties: {
                min: { type: 'number', example: 200000 },
                max: { type: 'number', example: 36000000 },
                average: { type: 'number', example: 800000 }
              }
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: '数据不存在',
    schema: {
      example: {
        success: false,
        message: '卫星点云数据文件不存在，请先生成数据',
        statusCode: 404
      }
    }
  })
  async getSatellitePointCloudData() {
    this.logger.debug('获取卫星点云数据');
    
    try {
      const pointCloudData = await this.tilesService.getSatellitePointCloudData();
      
      if (!pointCloudData) {
        throw new HttpException(
          {
            success: false,
            message: '卫星点云数据文件不存在，请先生成数据',
            statusCode: HttpStatus.NOT_FOUND
          },
          HttpStatus.NOT_FOUND
        );
      }
      
      return pointCloudData;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      this.logger.error(`获取卫星点云数据失败: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          message: '获取卫星点云数据失败',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('metadata')
  @ApiOperation({
    summary: '获取点云元数据',
    description: `
    获取卫星点云数据的元数据信息，包括生成时间、状态等。
    
    ## 返回信息
    - 最后生成时间
    - 下次计划生成时间
    - 卫星总数
    - 生成耗时
    - 生成状态
    - 错误信息（如果有）
    - 数据版本
    
    ## 用途
    - 检查数据是否为最新
    - 监控系统运行状态
    - 获取数据统计信息
    `
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        lastGenerated: { type: 'string', format: 'date-time', example: '2025-01-06T14:00:00Z' },
        nextScheduled: { type: 'string', format: 'date-time', example: '2025-01-06T18:00:00Z' },
        totalSatellites: { type: 'number', example: 5000 },
        generationDuration: { type: 'number', example: 15000 },
        status: { type: 'string', enum: ['idle', 'generating', 'error'], example: 'idle' },
        errorMessage: { type: 'string', example: null },
        version: { type: 'string', example: '1.0.0' }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: '元数据不存在',
    schema: {
      example: {
        success: false,
        message: '元数据文件不存在',
        statusCode: 404
      }
    }
  })
  async getMetadata() {
    this.logger.debug('获取卫星点云元数据');
    
    try {
      const metadata = await this.tilesService.getMetadata();
      
      if (!metadata) {
        throw new HttpException(
          {
            success: false,
            message: '元数据文件不存在',
            statusCode: HttpStatus.NOT_FOUND
          },
          HttpStatus.NOT_FOUND
        );
      }
      
      return metadata;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      this.logger.error(`获取元数据失败: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          message: '获取元数据失败',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
} 