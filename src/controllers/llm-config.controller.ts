import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query,
  HttpCode,
  HttpStatus
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth, 
  ApiBody,
  ApiParam,
  ApiQuery
} from '@nestjs/swagger';
import { 
  UpdateLLMConfigDto, 
  LLMConfigResponseDto, 
  ConfigType, 
  TestConnectionDto, 
  TestConnectionResponseDto,
  ResetConfigDto,
  ConfigStatsDto
} from '../dto/llm-config.dto';
import { LLMConfigService } from '../services/llm-config.service';

/**
 * 大模型配置管理控制器
 * 提供大模型配置的增删改查、测试连接、统计信息等API接口
 */
@ApiTags('大模型配置')
@Controller('llm-config')
@ApiBearerAuth('JWT-auth')
export class LLMConfigController {
  constructor(private readonly llmConfigService: LLMConfigService) {}

  /**
   * 获取所有配置
   */
  @Get()
  @ApiOperation({ 
    summary: '获取所有大模型配置',
    description: '获取翻译和主题提取的所有大模型配置信息，API密钥会被脱敏处理'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: [LLMConfigResponseDto]
  })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async getAllConfigs(): Promise<LLMConfigResponseDto[]> {
    return await this.llmConfigService.getAllConfigs();
  }

  /**
   * 获取指定类型的配置
   */
  @Get(':configType')
  @ApiOperation({ 
    summary: '获取指定类型的大模型配置',
    description: '根据配置类型获取对应的大模型配置信息'
  })
  @ApiParam({
    name: 'configType',
    description: '配置类型',
    enum: ConfigType,
    example: ConfigType.TRANSLATION
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: LLMConfigResponseDto
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 404, description: '配置不存在' })
  async getConfig(@Param('configType') configType: ConfigType): Promise<LLMConfigResponseDto> {
    return await this.llmConfigService.getConfig(configType);
  }

  /**
   * 更新配置
   */
  @Put()
  @ApiOperation({ 
    summary: '更新大模型配置',
    description: '更新指定类型的大模型配置，只更新提供的字段，其他字段保持不变'
  })
  @ApiBody({
    type: UpdateLLMConfigDto,
    description: '配置更新数据',
    examples: {
      updateTranslation: {
        summary: '更新翻译配置示例',
        value: {
          configType: 'translation',
          model: 'qwen-max-latest',
          apiKey: 'sk-new-api-key',
          systemPrompt: '你是一位专业的航空航天翻译专家...',
          temperature: 0.05
        }
      },
      updateThemeExtraction: {
        summary: '更新主题提取配置示例',
        value: {
          configType: 'theme_extraction',
          model: 'qwen-turbo',
          maxTokens: 2000,
          temperature: 0.2
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    type: LLMConfigResponseDto
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 401, description: '未授权' })
  async updateConfig(@Body() updateDto: UpdateLLMConfigDto): Promise<LLMConfigResponseDto> {
    return await this.llmConfigService.updateConfig(updateDto);
  }

  /**
   * 测试配置连接
   */
  @Post('test')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: '测试大模型配置连接',
    description: '测试指定配置类型的大模型连接是否正常，可选择提供测试文本'
  })
  @ApiBody({
    type: TestConnectionDto,
    description: '测试连接参数',
    examples: {
      testTranslation: {
        summary: '测试翻译配置',
        value: {
          configType: 'translation',
          testText: 'Hello, world!'
        }
      },
      testThemeExtraction: {
        summary: '测试主题提取配置',
        value: {
          configType: 'theme_extraction',
          testText: 'This is a news article about space technology...'
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '测试完成',
    type: TestConnectionResponseDto,
    examples: {
      success: {
        summary: '测试成功',
        value: {
          success: true,
          responseTime: 1234,
          result: '你好，世界！',
          timestamp: '2024-01-01T00:00:00.000Z'
        }
      },
      failure: {
        summary: '测试失败',
        value: {
          success: false,
          responseTime: 5000,
          error: 'API连接超时',
          timestamp: '2024-01-01T00:00:00.000Z'
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 401, description: '未授权' })
  async testConnection(@Body() testDto: TestConnectionDto): Promise<TestConnectionResponseDto> {
    return await this.llmConfigService.testConnection(testDto);
  }

  /**
   * 重置配置到默认值
   */
  @Post('reset')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: '重置大模型配置到默认值',
    description: '将指定类型的配置重置为系统默认值，需要确认操作'
  })
  @ApiBody({
    type: ResetConfigDto,
    description: '重置配置参数',
    examples: {
      resetTranslation: {
        summary: '重置翻译配置',
        value: {
          configType: 'translation',
          confirm: true
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '重置成功',
    type: LLMConfigResponseDto
  })
  @ApiResponse({ status: 400, description: '请求参数错误或未确认操作' })
  @ApiResponse({ status: 401, description: '未授权' })
  async resetConfig(@Body() resetDto: ResetConfigDto): Promise<LLMConfigResponseDto> {
    return await this.llmConfigService.resetConfig(resetDto);
  }

  /**
   * 获取统计信息
   */
  @Get('stats/summary')
  @ApiOperation({ 
    summary: '获取大模型配置统计信息',
    description: '获取翻译和主题提取功能的使用统计信息，包括请求次数、成功率、平均响应时间等'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: ConfigStatsDto,
    examples: {
      success: {
        summary: '统计信息示例',
        value: {
          translation: {
            totalRequests: 1000,
            successfulRequests: 950,
            failedRequests: 50,
            averageResponseTime: 1500,
            lastUsed: '2024-01-01T00:00:00.000Z'
          },
          themeExtraction: {
            totalRequests: 500,
            successfulRequests: 480,
            failedRequests: 20,
            averageResponseTime: 2000,
            lastUsed: '2024-01-01T00:00:00.000Z'
          },
          timestamp: '2024-01-01T00:00:00.000Z'
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async getStats(): Promise<ConfigStatsDto> {
    return await this.llmConfigService.getStats();
  }

  /**
   * 清除统计信息
   */
  @Delete('stats')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ 
    summary: '清除统计信息',
    description: '清除所有大模型配置的统计信息，将计数器重置为零'
  })
  @ApiResponse({ status: 204, description: '清除成功' })
  @ApiResponse({ status: 401, description: '未授权' })
  async clearStats(): Promise<void> {
    return await this.llmConfigService.clearStats();
  }

  /**
   * 批量操作：导出配置
   */
  @Get('export/all')
  @ApiOperation({ 
    summary: '导出所有配置',
    description: '导出所有大模型配置为JSON格式，方便备份和迁移'
  })
  @ApiQuery({
    name: 'includeSensitive',
    description: '是否包含敏感信息（API密钥）',
    type: Boolean,
    required: false,
    example: false
  })
  @ApiResponse({
    status: 200,
    description: '导出成功',
    schema: {
      type: 'object',
      properties: {
        exportTime: { type: 'string', description: '导出时间' },
        configs: {
          type: 'array',
          items: { $ref: '#/components/schemas/LLMConfigResponseDto' }
        },
        metadata: {
          type: 'object',
          properties: {
            version: { type: 'string' },
            totalConfigs: { type: 'number' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: '未授权' })
  async exportConfigs(@Query('includeSensitive') includeSensitive: boolean = false): Promise<any> {
    const configs = await this.llmConfigService.getAllConfigs();
    
    return {
      exportTime: new Date().toISOString(),
      configs: configs.map(config => ({
        ...config,
        apiKey: includeSensitive ? '***EXPORTED***' : config.apiKey
      })),
      metadata: {
        version: '1.0.0',
        totalConfigs: configs.length
      }
    };
  }
} 