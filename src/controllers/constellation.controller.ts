import { Controller, Post, Body, UseGuards, Logger, Get } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { SatelliteService } from '../services/satellite.service';
import { ConstellationService } from '../services/constellation.service';
import { ConstellationsWithTleResponseDto } from '../dto/constellation-with-tle.dto';

/**
 * 星座信息控制器
 * 处理卫星星座信息的查询和更新
 */
@Controller('local/constellation')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('本地卫星信息')
export class ConstellationController {
  private readonly logger = new Logger(ConstellationController.name);

  constructor(
    private readonly satelliteService: SatelliteService,
    private readonly constellationService: ConstellationService
  ) {}

  /**
   * 获取卫星星座集合（本地数据库）
   * @returns 星座集合，包含英文和中文名称
   */
  @Get()
  @ApiOperation({
    summary: '获取卫星星座集合',
    description: `
    从本地数据库中获取卫星星座集合。
    
    ## 返回数据说明
    返回星座集合，每个星座包含英文名称和中文名称。
    
    ## 示例
    \`\`\`json
    [
      {
        "en": "Starlink",
        "zh": "星链"
      },
      {
        "en": "OneWeb",
        "zh": "万维网"
      }
    ]
    \`\`\`
    `
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          en: { type: 'string', description: '英文名称', example: 'Starlink' },
          zh: { type: 'string', description: '中文名称', example: '星链' }
        }
      }
    }
  })
  async getConstellations(): Promise<Array<{en: string, zh: string}>> {
    this.logger.log('接收到获取卫星星座集合请求（本地数据库）');
    return this.satelliteService.getSatelliteConstellationsLocal();
  }

  /**
   * 更新卫星星座信息
   * @returns 更新结果
   */
  @Post('update')
  @ApiOperation({
    summary: '更新卫星星座信息',
    description: `
    从ES数据库获取星座信息并更新本地数据库中的卫星星座字段。
    
    ## 更新方式
    1. 从卫星名称中提取星座信息
    2. 从ES数据库中的constell_n2yo索引获取星座成员信息，通过norad_id、cospar_id、name进行匹配
    
    ## 返回数据说明
    - success: 是否成功
    - message: 更新结果消息
    - updated: 更新的卫星数量
    `
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '成功更新100个卫星的星座信息' },
        updated: { type: 'number', example: 100 }
      }
    }
  })
  async updateConstellationInfo(): Promise<{ success: boolean; message: string; updated: number }> {
    this.logger.log('接收到更新卫星星座信息请求');
    return this.satelliteService.updateSatelliteConstellationInfo();
  }

  @Get('with-tle')
  @ApiOperation({
    summary: '获取具有TLE轨道信息的卫星星座列表',
    description: '从本地PostgreSQL数据库中查询所有具有TLE轨道信息的卫星星座及其包含的卫星数量'
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: ConstellationsWithTleResponseDto
  })
  @ApiTags('数据库查询')
  async getConstellationsWithTle(): Promise<ConstellationsWithTleResponseDto> {
    this.logger.log('接收到获取具有TLE轨道信息的卫星星座列表请求');
    const result = await this.constellationService.getConstellationsWithTle();
    this.logger.debug(`查询结果: 找到${result.total}个星座`);
    return result;
  }
} 