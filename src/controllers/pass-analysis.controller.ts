import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { PassAnalysisService } from '../services/pass-analysis/pass-analysis.service';
import {
  PassAnalysisRequestDto,
  PassAnalysisResponseDto,
  PassInfoDto
} from '../services/pass-analysis/dto/pass-analysis.dto';

/**
 * 过境分析控制器
 * 提供卫星过境分析相关的API接口
 */
@Controller('api/pass-analysis')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('轨道数据')
export class PassAnalysisController {
  constructor(private readonly passAnalysisService: PassAnalysisService) {}

  @Post('pass-analysis')
  @ApiOperation({
    summary: '卫星过境分析',
    description: `
      分析指定时间段内卫星相对于地面站的过境情况。
      
      功能说明：
      - 支持批量分析多颗卫星的过境情况
      - 可设置最小仰角过滤条件
      - 可调整时间步长以平衡精度和性能
      - 返回每次过境的入境和离境信息
      
      注意事项：
      - 时间使用UTC时间
      - 角度单位为度
      - 距离单位为千米
      - 时间步长建议60秒，可根据需要调整
      - 分析时间段建议不超过7天
    `
  })
  @ApiResponse({
    status: 200,
    description: '过境分析结果',
    type: PassAnalysisResponseDto,
    content: {
      'application/json': {
        example: {
          passes: [
            {
              satellite: {
                satId: '25544',
                name: 'ISS (ZARYA)',
                organization: 'NASA',
                company: 'NASA',
                country: 'USA',
                type: '空间站'
              },
              startPass: {
                time: '2024-02-24T02:30:00Z',
                azimuth: 123.45,
                elevation: 10.0,
                range: 1200.5
              },
              endPass: {
                time: '2024-02-24T02:40:00Z',
                azimuth: 67.89,
                elevation: 10.0,
                range: 1150.3
              },
              duration: 600
            }
          ]
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
    content: {
      'application/json': {
        example: {
          statusCode: 400,
          message: ['时间步长必须在1到3600秒之间'],
          error: 'Bad Request'
        }
      }
    }
  })
  async analyzePass(
    @Body() request: PassAnalysisRequestDto
  ): Promise<PassAnalysisResponseDto> {
    const passes = await this.passAnalysisService.analyzeMultipleSatellites({
      groundStation: request.groundStation,
      satellites: request.satellites,
      startTime: new Date(request.startTime),
      endTime: new Date(request.endTime),
      minElevation: request.minElevation,
      timeStep: request.timeStep
    });

    return { passes };
  }
} 