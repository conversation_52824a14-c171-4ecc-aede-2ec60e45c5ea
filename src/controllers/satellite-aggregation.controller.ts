import { Controller, Get, UseGuards, Logger } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AggregationTaskService } from '../services/aggregation-task.service';

/**
 * 卫星聚合任务控制器
 * 处理卫星数据聚合任务的查询请求
 */
@Controller('local/satellite-aggregation')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('卫星聚合任务')
export class SatelliteAggregationController {
  private readonly logger = new Logger(SatelliteAggregationController.name);

  constructor(
    private readonly aggregationTaskService: AggregationTaskService
  ) {}

  @Get('status')
  @ApiOperation({
    summary: '查询卫星聚合任务状态',
    description: `
    获取最新的卫星数据聚合任务状态。
    
    ## 返回数据说明
    - id: 任务ID
    - task_type: 任务类型（full: 全量聚合, partial: 部分聚合, keyword: 关键词聚合）
    - status: 任务状态（pending: 等待执行, running: 执行中, completed: 已完成, failed: 失败）
    - progress: 任务进度百分比
    - processed_records: 已处理的记录数
    - aggregated_records: 已聚合的记录数
    - start_time: 开始时间
    - end_time: 结束时间（如果已完成）
    - parameters: 任务参数
    - error_message: 错误信息（如果失败）
    `
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', description: '任务ID' },
        task_type: { type: 'string', description: '任务类型' },
        status: { type: 'string', description: '任务状态' },
        progress: { type: 'number', description: '进度百分比' },
        processed_records: { type: 'number', description: '已处理的记录数' },
        aggregated_records: { type: 'number', description: '已聚合的记录数' },
        start_time: { type: 'string', format: 'date-time', description: '开始时间' },
        end_time: { type: 'string', format: 'date-time', description: '结束时间' },
        parameters: { type: 'object', description: '任务参数' },
        error_message: { type: 'string', description: '错误信息' }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: '未找到任何任务',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: '未找到任何聚合任务' }
      }
    }
  })
  async getAggregationStatus(): Promise<any> {
    this.logger.debug('接收到查询卫星聚合任务状态请求');
    
    try {
      // 直接从聚合任务服务获取最新任务
      const task = await this.aggregationTaskService.getLatestTask();
      
      if (!task) {
        return {
          success: false,
          message: '未找到任何聚合任务'
        };
      }
      
      // 返回任务状态，不引用任何卫星ID
      return {
        success: true,
        id: task.id,
        task_type: task.task_type,
        status: task.status,
        progress: task.progress,
        processed_records: task.processed_records,
        aggregated_records: task.aggregated_records,
        start_time: task.start_time,
        end_time: task.end_time,
        parameters: task.parameters,
        error_message: task.error_message,
        createdAt: task.createdAt,
        updatedAt: task.updatedAt
      };
    } catch (error) {
      this.logger.error(`查询聚合任务状态失败: ${error.message}`, error.stack);
      return {
        success: false,
        message: `查询失败: ${error.message}`
      };
    }
  }

  @Get('running')
  @ApiOperation({
    summary: '查询正在运行的聚合任务',
    description: `
    查询所有正在运行（状态为running）的聚合任务列表。
    
    ## 用途
    用于监控当前系统中正在执行的聚合任务，了解系统负载情况。
    `
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number', description: '任务ID' },
          task_type: { type: 'string', description: '任务类型' },
          status: { type: 'string', description: '任务状态' },
          start_time: { type: 'string', format: 'date-time', description: '开始时间' },
          processed_records: { type: 'number', description: '处理的记录数' },
          progress: { type: 'number', description: '进度百分比' }
        }
      }
    }
  })
  async getRunningAggregations(): Promise<any> {
    this.logger.debug('接收到查询正在运行的聚合任务请求');
    
    try {
      // 直接从聚合任务服务获取运行中的任务
      const tasks = await this.aggregationTaskService.getRunningTasks();
      
      if (!tasks || tasks.length === 0) {
        return {
          success: true,
          data: [],
          message: '当前没有正在运行的聚合任务'
        };
      }
      
      // 返回任务列表，不引用任何卫星ID
      return {
        success: true,
        data: tasks.map(task => ({
          id: task.id,
          task_type: task.task_type,
          status: task.status,
          progress: task.progress,
          processed_records: task.processed_records,
          start_time: task.start_time,
          parameters: task.parameters,
          createdAt: task.createdAt
        }))
      };
    } catch (error) {
      this.logger.error(`查询正在运行的聚合任务失败: ${error.message}`, error.stack);
      return {
        success: false,
        message: `查询失败: ${error.message}`
      };
    }
  }
} 