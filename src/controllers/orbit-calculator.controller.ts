import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiBearerAuth, ApiTags, ApiExtraModels } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { OrbitCalculator } from '../services/orbit-calculator/OrbitCalculator';
import {
  CalculatePositionsRequestDto,
  CalculatePositionsResponseDto,
  SatellitePositionDto,
  CalculationErrorDto,
  TLESatelliteDto
} from '../services/orbit-calculator/dto/calculate-positions.dto';
import { Logger } from '@nestjs/common';

/**
 * 轨道计算控制器
 * 提供轨道计算相关的API接口
 */
@Controller('api/orbit-calculator')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('轨道计算')
@ApiExtraModels(TLESatelliteDto, SatellitePositionDto, CalculationErrorDto)
export class OrbitCalculatorController {
  private readonly logger = new Logger(OrbitCalculatorController.name);
  
  constructor(private readonly orbitCalculator: OrbitCalculator) {}

  @Post('calculate-positions')
  @ApiOperation({
    summary: '批量计算卫星位置',
    description: `
      根据提供的TLE数据计算多颗卫星在指定时间的位置。
      
      功能特点：
      - 支持LEO和深空轨道卫星
      - 自动选择SGP4或SDP4算法
      - 返回ECI坐标系和地理坐标系的位置信息
      - 支持批量计算，自动并行处理
      
      参数说明：
      - satellites: 卫星TLE数据列表
      - time: 计算时间点(UTC)，可选，默认为当前时间
      - continueOnError: 发生错误时是否继续计算，可选，默认true
      - batchSize: 每批处理的卫星数量，可选，默认100
      
      注意事项：
      - TLE数据应保证时效性，建议使用最新的数据
      - 时间格式应为ISO 8601标准
      - 所有距离单位为千米(km)
      - 所有速度单位为千米/秒(km/s)
      - 所有角度单位为度
    `
  })
  @ApiResponse({
    status: 200,
    description: '计算成功',
    type: CalculatePositionsResponseDto,
    content: {
      'application/json': {
        example: {
          positions: [{
            satId: 'NORAD_25544',
            name: 'ISS',
            epoch: '2021-06-05T07:19:36.000Z',
            position: {
              x: -2345.124,
              y: 4567.890,
              z: 3456.123
            },
            velocity: {
              x: -5.678,
              y: -4.321,
              z: 2.345
            },
            latitude: 45.678,
            longitude: 123.456,
            altitude: 408.789,
            algorithm: 'SGP4'
          }],
          errors: []
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'TLE数据格式错误或参数无效',
    content: {
      'application/json': {
        example: {
          statusCode: 400,
          message: ['TLE数据格式无效', '时间格式无效'],
          error: 'Bad Request'
        }
      }
    }
  })
  async calculatePositions(
    @Body() request: CalculatePositionsRequestDto
  ): Promise<CalculatePositionsResponseDto> {
    const time = request.time ? new Date(request.time) : new Date();
    
    // 设置批处理大小限制，确保即使前端发送大量数据也能处理
    // 如果请求没有指定batchSize或指定值过大，则使用默认值
    const maxBatchSize = 500; // 最大批处理大小
    const batchSize = request.batchSize && request.batchSize <= maxBatchSize 
      ? request.batchSize 
      : 100; // 默认批处理大小
    
    // 记录卫星数量便于调试
    const satelliteCount = request.satellites?.length || 0;
    this.logger.log(`正在计算${satelliteCount}颗卫星的位置，批处理大小: ${batchSize}`);

    const { positions, errors } = await this.orbitCalculator.calculatePositions(
      request.satellites,
      time,
      {
        continueOnError: request.continueOnError !== false, // 默认为true
        batchSize: batchSize
      }
    );

    return {
      positions,
      errors: errors.map(err => ({
        satId: err.satId,
        error: err.error.message
      }))
    };
  }
} 