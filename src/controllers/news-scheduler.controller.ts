import { Controller, Get, Post, Body, Logger, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags, ApiBody, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { NewsSchedulerService } from '../services/news-scheduler/news-scheduler.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { 
  NewsSchedulerConfig, 
  TaskStatus, 
  TaskExecutionResult,
  PREDEFINED_CRON_EXPRESSIONS 
} from '../../config/news-scheduler.config';

/**
 * 新闻定时任务控制器
 * 提供新闻处理定时任务的管理API接口
 */
@Controller('api/news-scheduler')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('新闻定时任务')
export class NewsSchedulerController {
  private readonly logger = new Logger(NewsSchedulerController.name);

  constructor(private readonly schedulerService: NewsSchedulerService) {}

  /**
   * 获取定时任务状态
   */
  @Get('status')
  @ApiOperation({
    summary: '获取定时任务状态',
    description: '获取当前新闻处理定时任务的运行状态、配置信息和最近执行结果'
  })
  @ApiResponse({
    status: 200,
    description: '成功获取任务状态',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '获取任务状态成功' },
        data: {
          type: 'object',
          properties: {
            currentStatus: { type: 'string', enum: ['idle', 'running', 'completed', 'failed'] },
            isRunning: { type: 'boolean' },
            config: {
              type: 'object',
              properties: {
                enabled: { type: 'boolean' },
                cronExpression: { type: 'string' },
                timezone: { type: 'string' },
                translationConfig: { type: 'object' },
                themeExtractionConfig: { type: 'object' }
              }
            },
            lastExecutionResults: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  taskType: { type: 'string', enum: ['translation', 'theme_extraction'] },
                  status: { type: 'string', enum: ['idle', 'running', 'completed', 'failed'] },
                  startTime: { type: 'string', format: 'date-time' },
                  endTime: { type: 'string', format: 'date-time' },
                  duration: { type: 'number' },
                  statistics: { type: 'object' },
                  error: { type: 'string' }
                }
              }
            }
          }
        },
        timestamp: { type: 'string', format: 'date-time' }
      }
    }
  })
  getTaskStatus(): {
    success: boolean;
    message: string;
    data: {
      currentStatus: TaskStatus;
      isRunning: boolean;
      config: NewsSchedulerConfig;
      lastExecutionResults: TaskExecutionResult[];
    };
    timestamp: string;
  } {
    this.logger.log('获取新闻定时任务状态');

    try {
      const status = this.schedulerService.getTaskStatus();

      return {
        success: true,
        message: '获取任务状态成功',
        data: status,
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      this.logger.error(`获取任务状态失败: ${error.message}`, error.stack);

      return {
        success: false,
        message: `获取任务状态失败: ${error.message}`,
        data: {
          currentStatus: TaskStatus.FAILED,
          isRunning: false,
          config: {} as NewsSchedulerConfig,
          lastExecutionResults: []
        },
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 手动触发新闻处理任务
   */
  @Post('trigger')
  @ApiOperation({
    summary: '手动触发新闻处理任务',
    description: '手动触发新闻翻译和主题提取任务的执行，任务将在后台异步执行'
  })
  @ApiResponse({
    status: 200,
    description: '任务触发成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '新闻处理任务执行成功' },
        results: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              taskType: { type: 'string', enum: ['translation', 'theme_extraction'] },
              status: { type: 'string', enum: ['idle', 'running', 'completed', 'failed'] },
              startTime: { type: 'string', format: 'date-time' },
              endTime: { type: 'string', format: 'date-time' },
              duration: { type: 'number' },
              statistics: { type: 'object' },
              error: { type: 'string' }
            }
          }
        },
        timestamp: { type: 'string', format: 'date-time' }
      }
    }
  })
  async triggerManualExecution(): Promise<{
    success: boolean;
    message: string;
    results?: TaskExecutionResult[];
    timestamp: string;
  }> {
    this.logger.log('收到手动触发新闻处理任务请求');

    try {
      const result = await this.schedulerService.triggerManualExecution();

      return {
        success: result.success,
        message: result.message,
        results: result.results,
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      this.logger.error(`手动触发任务失败: ${error.message}`, error.stack);

      return {
        success: false,
        message: `手动触发任务失败: ${error.message}`,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 更新定时任务配置
   */
  @Post('config')
  @ApiOperation({
    summary: '更新定时任务配置',
    description: '动态更新新闻处理定时任务的配置，包括执行时间、批次大小等参数'
  })
  @ApiBody({
    description: '配置更新参数',
    schema: {
      type: 'object',
      properties: {
        enabled: { type: 'boolean', description: '是否启用定时任务' },
        cronExpression: { type: 'string', description: 'cron表达式，定义执行时间' },
        timezone: { type: 'string', description: '时区' },
        translationConfig: {
          type: 'object',
          properties: {
            batchSize: { type: 'number', description: '翻译批次大小' },
            maxDocs: { type: 'number', description: '最大处理文档数' },
            forceReprocess: { type: 'boolean', description: '是否强制重新处理' },
            specificIndexes: { type: 'array', items: { type: 'string' }, description: '指定处理的索引' },
            llmMode: { type: 'string', enum: ['default', 'high_quality', 'fast'], description: '大模型模式' },
            customModel: { type: 'string', description: '自定义模型名称' }
          }
        },
        themeExtractionConfig: {
          type: 'object',
          properties: {
            batchSize: { type: 'number', description: '主题提取批次大小' },
            maxDocs: { type: 'number', description: '最大处理文档数' },
            forceReprocess: { type: 'boolean', description: '是否强制重新处理' },
            specificIndexes: { type: 'array', items: { type: 'string' }, description: '指定处理的索引' },
            llmMode: { type: 'string', enum: ['default', 'high_quality', 'fast'], description: '大模型模式' },
            customModel: { type: 'string', description: '自定义模型名称' }
          }
        },
        maxRetries: { type: 'number', description: '最大重试次数' },
        retryDelay: { type: 'number', description: '重试延迟时间（毫秒）' }
      }
    },
    examples: {
      '更新执行时间': {
        summary: '更新定时任务执行时间',
        description: '将执行时间改为每天6:00、14:00、22:00',
        value: {
          cronExpression: '0 6,14,22 * * *'
        }
      },
      '更新批次配置': {
        summary: '更新批次处理配置',
        description: '调整翻译和主题提取的批次大小',
        value: {
          translationConfig: {
            batchSize: 30,
            llmMode: 'fast'
          },
          themeExtractionConfig: {
            batchSize: 25,
            llmMode: 'high_quality'
          }
        }
      },
      '禁用定时任务': {
        summary: '禁用定时任务',
        description: '临时禁用定时任务的自动执行',
        value: {
          enabled: false
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '配置更新成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '配置更新成功' },
        timestamp: { type: 'string', format: 'date-time' }
      }
    }
  })
  updateConfig(@Body() configUpdate: Partial<NewsSchedulerConfig>): {
    success: boolean;
    message: string;
    timestamp: string;
  } {
    this.logger.log(`收到配置更新请求: ${JSON.stringify(configUpdate)}`);

    try {
      this.schedulerService.updateConfig(configUpdate);

      return {
        success: true,
        message: '配置更新成功',
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      this.logger.error(`配置更新失败: ${error.message}`, error.stack);

      return {
        success: false,
        message: `配置更新失败: ${error.message}`,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 停止正在运行的任务
   */
  @Post('stop')
  @ApiOperation({
    summary: '停止正在运行的任务',
    description: '紧急停止当前正在运行的新闻处理任务'
  })
  @ApiResponse({
    status: 200,
    description: '任务停止成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '任务已停止' },
        timestamp: { type: 'string', format: 'date-time' }
      }
    }
  })
  async stopRunningTask(): Promise<{
    success: boolean;
    message: string;
    timestamp: string;
  }> {
    this.logger.log('收到停止任务请求');

    try {
      await this.schedulerService.stopRunningTask();

      return {
        success: true,
        message: '任务已停止',
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      this.logger.error(`停止任务失败: ${error.message}`, error.stack);

      return {
        success: false,
        message: `停止任务失败: ${error.message}`,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 获取预定义的cron表达式
   */
  @Get('cron-expressions')
  @ApiOperation({
    summary: '获取预定义的cron表达式',
    description: '获取系统预定义的常用cron表达式，方便用户选择合适的执行时间'
  })
  @ApiResponse({
    status: 200,
    description: '成功获取cron表达式列表',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '获取cron表达式成功' },
        data: {
          type: 'object',
          additionalProperties: { type: 'string' }
        },
        timestamp: { type: 'string', format: 'date-time' }
      }
    }
  })
  getPredefinedCronExpressions(): {
    success: boolean;
    message: string;
    data: typeof PREDEFINED_CRON_EXPRESSIONS;
    timestamp: string;
  } {
    this.logger.log('获取预定义cron表达式');

    return {
      success: true,
      message: '获取cron表达式成功',
      data: PREDEFINED_CRON_EXPRESSIONS,
      timestamp: new Date().toISOString()
    };
  }
} 