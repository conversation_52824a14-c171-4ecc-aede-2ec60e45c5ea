import { Controller, Post, Body, UseGuards, Logger, BadRequestException, Get, SetMetadata } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiBearerAuth, ApiTags, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { DatabaseService } from '../services/database.service';
import { IsOptional, IsString, IsNumber, IsArray, IsDate, Min, Max, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * 数字范围筛选条件
 */
class NumberRangeDto {
  @IsOptional()
  @IsNumber()
  @Min(0)
  min?: number;

  @IsOptional()
  @IsNumber()
  max?: number;
}

/**
 * 日期范围筛选条件
 */
class DateRangeDto {
  @IsOptional()
  @IsString()
  start?: string;

  @IsOptional()
  @IsString()
  end?: string;
}

/**
 * 卫星筛选条件DTO
 */
class SatelliteFilterDto {
  @IsOptional()
  @IsString()
  keyword?: string;

  @IsOptional()
  @IsString()
  satelliteName?: string;

  @IsOptional()
  @IsString()
  noradId?: string;

  @IsOptional()
  @IsString()
  cosparId?: string;

  @IsOptional()
  @IsString()
  constellationName?: string;

  @IsOptional()
  @IsString()
  status?: string;

  @IsOptional()
  @IsString()
  countryOfOwner?: string;

  @IsOptional()
  @IsString()
  owner?: string;

  @IsOptional()
  @IsString()
  users?: string;

  @IsOptional()
  @IsString()
  purpose?: string;

  @IsOptional()
  @IsString()
  orbitClass?: string;

  @IsOptional()
  @IsString()
  orbitType?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => NumberRangeDto)
  perigeeKm?: NumberRangeDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => NumberRangeDto)
  apogeeKm?: NumberRangeDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => NumberRangeDto)
  eccentricity?: NumberRangeDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => NumberRangeDto)
  inclDegrees?: NumberRangeDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => NumberRangeDto)
  periodMinutes?: NumberRangeDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => DateRangeDto)
  decayDate?: DateRangeDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => DateRangeDto)
  deployedDate?: DateRangeDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => DateRangeDto)
  launchDate?: DateRangeDto;

  @IsOptional()
  @IsString()
  contractor?: string;

  @IsOptional()
  @IsString()
  countryOfContractor?: string;

  @IsOptional()
  @IsString()
  launchSite?: string;

  @IsOptional()
  @IsString()
  launchVehicle?: string;

  @IsOptional()
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;
}

/**
 * 数据库查询控制器
 * 提供执行SQL查询的API端点
 */
@Controller('api/v1/database')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('数据库查询')
export class DatabaseController {
  private readonly logger = new Logger(DatabaseController.name);

  constructor(private readonly databaseService: DatabaseService) {}

  @Post('query')
  @ApiOperation({
    summary: '执行SQL查询',
    description: `
    执行自定义SQL查询语句，查询卫星信息数据库。
    
    ## 安全限制
    - 仅支持SELECT查询
    - 不允许执行修改数据的操作（INSERT, UPDATE, DELETE等）
    - 不允许执行多条语句
    - 不允许执行数据定义语言（DDL）操作
    
    ## 卫星信息表结构
    主表: satellites
    字段说明:
    - id: 主键
    - satellite_name: 卫星名称，JSONB类型，格式: [{ value: string, sources: string[] }]
    - alternative_name: 卫星别名，JSONB类型
    - cospar_id: COSPAR ID，JSONB类型
    - country_of_registry: 注册国家，JSONB类型
    - owner: 所有者，JSONB类型
    - status: 状态，JSONB类型
    - norad_id: NORAD ID，JSONB类型
    - launch_info: 发射信息，JSONB类型
    - orbit_info: 轨道信息，JSONB类型
    - mass_kg: 质量(kg)，JSONB类型
    - power_watts: 功率(W)，JSONB类型
    - lifetime_years: 寿命(年)，JSONB类型
    - contractor: 承包商，JSONB类型
    - purpose: 用途，JSONB类型
    - detailed_purpose: 详细用途，JSONB类型
    - payload: 有效载荷，JSONB类型
    - payload_description: 有效载荷描述，JSONB类型
    - update_time: 更新时间，JSONB类型
    - _sources: 数据来源，JSONB类型，格式: string[]
    - _similarity_info: 相似度信息，JSONB类型
    - created_at: 创建时间
    - updated_at: 更新时间
    
    ## 查询示例
    
    ### 1. 查询卫星总数
    \`\`\`sql
    SELECT COUNT(*) FROM satellites;
    \`\`\`
    
    ### 2. 查询不同数据源的卫星数量
    \`\`\`sql
    SELECT jsonb_array_elements_text(_sources) as source, COUNT(*) 
    FROM satellites 
    GROUP BY source 
    ORDER BY COUNT(*) DESC;
    \`\`\`
    
    ### 3. 查询包含特定NORAD ID的卫星
    \`\`\`sql
    SELECT id, satellite_name 
    FROM satellites 
    WHERE EXISTS (
      SELECT FROM jsonb_array_elements(norad_id) AS n 
      WHERE (n->>'value')::text = '25544'
    );
    \`\`\`
    
    ### 4. 查询特定国家的卫星数量
    \`\`\`sql
    SELECT jsonb_array_elements(country_of_registry) ->> 'value' as country, COUNT(*) 
    FROM satellites 
    GROUP BY country 
    ORDER BY COUNT(*) DESC 
    LIMIT 10;
    \`\`\`
    
    ### 5. 查询特定状态的卫星
    \`\`\`sql
    SELECT id, satellite_name 
    FROM satellites 
    WHERE EXISTS (
      SELECT FROM jsonb_array_elements(status) AS s 
      WHERE (s->>'value')::text ILIKE '%active%'
    )
    LIMIT 20;
    \`\`\`
    
    ### 6. 查询特定时间段发射的卫星
    \`\`\`sql
    SELECT id, satellite_name 
    FROM satellites 
    WHERE EXISTS (
      SELECT FROM jsonb_array_elements(launch_info) AS l 
      WHERE (l->>'value')::jsonb ->> 'launch_date' LIKE '2020%'
    )
    LIMIT 20;
    \`\`\`
    
    ### 7. 查询多个数据源都有记录的卫星
    \`\`\`sql
    SELECT id, satellite_name, _sources 
    FROM satellites 
    WHERE jsonb_array_length(_sources) > 2
    LIMIT 20;
    \`\`\`
    
    ### 8. 查询轨道类型分布
    \`\`\`sql
    SELECT 
      jsonb_path_query(orbit_info, '$[*].value.orbit_class') ->> 0 as orbit_class, 
      COUNT(*) 
    FROM satellites 
    WHERE orbit_info IS NOT NULL
    GROUP BY orbit_class 
    ORDER BY COUNT(*) DESC;
    \`\`\`
    
    ### 9. 查询卫星质量分布
    \`\`\`sql
    SELECT 
      CASE 
        WHEN (jsonb_array_elements(mass_kg) ->> 'value')::numeric < 10 THEN '< 10 kg'
        WHEN (jsonb_array_elements(mass_kg) ->> 'value')::numeric < 100 THEN '10-100 kg'
        WHEN (jsonb_array_elements(mass_kg) ->> 'value')::numeric < 1000 THEN '100-1000 kg'
        ELSE '> 1000 kg'
      END as mass_range,
      COUNT(*) 
    FROM satellites 
    WHERE mass_kg IS NOT NULL AND jsonb_array_length(mass_kg) > 0
    GROUP BY mass_range 
    ORDER BY mass_range;
    \`\`\`
    
    ### 10. 查询卫星名称包含特定关键词的卫星
    \`\`\`sql
    SELECT id, satellite_name 
    FROM satellites 
    WHERE EXISTS (
      SELECT FROM jsonb_array_elements(satellite_name) AS name 
      WHERE (name->>'value')::text ILIKE '%starlink%'
    )
    LIMIT 20;
    \`\`\`
    `
  })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['sql'],
      properties: {
        sql: {
          type: 'string',
          description: 'SQL查询语句',
          example: 'SELECT COUNT(*) FROM satellites;'
        },
        parameters: {
          type: 'array',
          description: '查询参数（可选）',
          items: {
            type: 'string'
          },
          example: []
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        rows: { 
          type: 'array', 
          description: '查询结果行',
          items: {
            type: 'object',
            additionalProperties: true
          },
          example: [{ "count": "64868" }]
        },
        rowCount: { type: 'number', description: '结果行数', example: 1 },
        query: { type: 'string', description: '执行的查询', example: 'SELECT COUNT(*) FROM satellites;' }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: '查询无效',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: { type: 'string', example: '只允许SELECT查询' },
        error: { type: 'string', example: 'Bad Request' }
      }
    }
  })
  @ApiResponse({
    status: 500,
    description: '查询执行失败',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 500 },
        message: { type: 'string', example: 'SQL查询执行失败: relation "unknown_table" does not exist' },
        error: { type: 'string', example: 'Internal Server Error' }
      }
    }
  })
  async executeQuery(
    @Body() body: { sql: string; parameters?: any[] }
  ): Promise<any> {
    this.logger.debug(`接收到SQL查询请求: ${body.sql}`);
    
    // 验证查询
    const validation = this.databaseService.validateQuery(body.sql);
    if (!validation.isValid) {
      this.logger.warn(`SQL查询验证失败: ${validation.message}`);
      throw new BadRequestException(validation.message);
    }
    
    try {
      // 执行查询
      const result = await this.databaseService.executeQuery(
        body.sql, 
        body.parameters || []
      );
      
      return result;
    } catch (error) {
      this.logger.error(`SQL查询执行失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Post('filter-satellites')
  @ApiOperation({
    summary: '高级筛选卫星信息',
    description: `
    根据多种条件筛选卫星信息，支持多个条件的组合查询。
    
    ## 筛选条件说明
    支持以下筛选条件，可以组合使用：
    
    1. keyword: 关键词全字段模糊匹配，可以匹配卫星信息中的所有字段，包括卫星名称、别名、NORAD ID、COSPAR ID、状态、用途、所有者、星座、发射信息、轨道信息等所有文本和数值字段
    2. satelliteName: 卫星名称相似性匹配satellite_name、alternative_name字段
    3. noradId: 卫星NORAD ID精确匹配
    4. cosparId: 卫星COSPAR ID精确匹配
    5. constellationName: 所属星座子串匹配（匹配constellation、satellite_name和alternative_name字段）
    6. status: 运营状态精确匹配
    7. countryOfOwner: 所属国家/组织精确匹配
    8. owner: 操作方或所有者精确匹配（特殊说明：当owner为SpaceX时，会同时返回所有名称或别名中包含"starlink"的卫星）
    9. users: 使用者子串匹配（不区分大小写，匹配users字段中包含输入值的记录）
    10. purpose: 主要用途精确匹配
    11. orbitClass: 轨道高度精确匹配
    12. orbitType: 轨道类型精确匹配
    13. perigeeKm: 近地点高度范围查询，格式: { "min": 数值, "max": 数值 }
    14. apogeeKm: 远地点高度范围查询，格式: { "min": 数值, "max": 数值 }
    15. eccentricity: 轨道偏心率范围查询，格式: { "min": 数值, "max": 数值 }
    16. inclDegrees: 轨道倾角范围查询，格式: { "min": 数值, "max": 数值 }
    17. periodMinutes: 轨道周期范围查询，格式: { "min": 数值, "max": 数值 }
    18. decayDate: 衰变日期时间范围查询，格式: { "start": "YYYY-MM-DD", "end": "YYYY-MM-DD" }
    19. deployedDate: 部署/入轨日期时间范围查询，格式: { "start": "YYYY-MM-DD", "end": "YYYY-MM-DD" }
    20. launchDate: 发射日期时间范围查询，格式: { "start": "YYYY-MM-DD", "end": "YYYY-MM-DD" }
    21. contractor: 发射承包商精确匹配
    22. countryOfContractor: 承包商所在国家精确匹配
    23. launchSite: 发射地点精确匹配
    24. launchVehicle: 发射载具精确匹配
    
    ## 分页参数
    - page: 页码，默认为1
    - limit: 每页记录数，默认为10，最大为100
    
    ## 返回结果
    返回符合条件的卫星信息列表，包含完整的字段信息以及匹配条件的相似度（如适用）。
    `
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        keyword: { type: 'string', description: '关键词（全字段模糊匹配，包括卫星名称、别名、NORAD ID、COSPAR ID、状态等所有字段）' },
        satelliteName: { type: 'string', description: '卫星名称（相似性匹配）' },
        noradId: { type: 'string', description: 'NORAD ID（精确匹配）' },
        cosparId: { type: 'string', description: 'COSPAR ID（精确匹配）' },
        constellationName: { type: "string", description: "所属星座（子串匹配：匹配constellation、satellite_name和alternative_name字段）" },
        status: { type: 'string', description: '运营状态（精确匹配）' },
        countryOfOwner: { type: 'string', description: '所属国家/组织（精确匹配）' },
        owner: { type: 'string', description: '操作方或所有者（精确匹配）' },
        users: { type: 'string', description: '使用者（精确匹配）' },
        purpose: { type: 'string', description: '主要用途（精确匹配）' },
        orbitClass: { type: 'string', description: '轨道高度（精确匹配）' },
        orbitType: { type: 'string', description: '轨道类型（精确匹配）' },
        perigeeKm: { 
          type: 'object', 
          description: '近地点高度（范围查询）',
          properties: {
            min: { type: 'number' },
            max: { type: 'number' }
          }
        },
        apogeeKm: { 
          type: 'object', 
          description: '远地点高度（范围查询）',
          properties: {
            min: { type: 'number' },
            max: { type: 'number' }
          }
        },
        eccentricity: { 
          type: 'object', 
          description: '轨道偏心率（范围查询）',
          properties: {
            min: { type: 'number' },
            max: { type: 'number' }
          }
        },
        inclDegrees: { 
          type: 'object', 
          description: '轨道倾角（范围查询）',
          properties: {
            min: { type: 'number' },
            max: { type: 'number' }
          }
        },
        periodMinutes: { 
          type: 'object', 
          description: '轨道周期（范围查询）',
          properties: {
            min: { type: 'number' },
            max: { type: 'number' }
          }
        },
        decayDate: { 
          type: 'object', 
          description: '衰变日期（范围查询）',
          properties: {
            start: { type: 'string', format: 'date' },
            end: { type: 'string', format: 'date' }
          }
        },
        deployedDate: { 
          type: 'object', 
          description: '部署/入轨日期（范围查询）',
          properties: {
            start: { type: 'string', format: 'date' },
            end: { type: 'string', format: 'date' }
          }
        },
        launchDate: { 
          type: 'object', 
          description: '发射日期（范围查询）',
          properties: {
            start: { type: 'string', format: 'date' },
            end: { type: 'string', format: 'date' }
          }
        },
        contractor: { type: 'string', description: '发射承包商（精确匹配）' },
        countryOfContractor: { type: 'string', description: '承包商所在国家（精确匹配）' },
        launchSite: { type: 'string', description: '发射地点（精确匹配）' },
        launchVehicle: { type: 'string', description: '发射载具（精确匹配）' },
        page: { type: 'number', description: '页码，默认为1', default: 1 },
        limit: { type: 'number', description: '每页记录数，默认为10，最大为100', default: 10 }
      },
      example: {
        "keyword": "通信",
        "satelliteName": "Starlink",
        "noradId": "25544",
        "cosparId": "1998-067A",
        "constellationName": "Starlink",
        "status": "ACTIVE",
        "countryOfOwner": "United States",
        "owner": "SpaceX",
        "users": "Commercial",
        "purpose": "Communications",
        "orbitClass": "LEO",
        "orbitType": "Non-Polar Inclined",
        "perigeeKm": {
          "min": 400,
          "max": 600
        },
        "apogeeKm": {
          "min": 400,
          "max": 600
        },
        "eccentricity": {
          "min": 0,
          "max": 0.01
        },
        "inclDegrees": {
          "min": 50,
          "max": 60
        },
        "periodMinutes": {
          "min": 90,
          "max": 100
        },
        "deployedDate": {
          "start": "2019-01-01",
          "end": "2023-12-31"
        },
        "launchDate": {
          "start": "2019-01-01",
          "end": "2023-12-31"
        },
        "contractor": "SpaceX",
        "launchSite": "Cape Canaveral",
        "launchVehicle": "Falcon 9",
        "page": 1,
        "limit": 10
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        total: { type: 'number', description: '符合条件的总记录数', example: 125 },
        page: { type: 'number', description: '当前页码', example: 1 },
        limit: { type: 'number', description: '每页记录数', example: 10 },
        results: { 
          type: 'array',
          description: '查询结果',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number', description: '卫星ID' },
              satellite_name: { type: 'array', description: '卫星名称' },
              alternative_name: { type: 'array', description: '卫星别名' },
              norad_id: { type: 'array', description: 'NORAD ID' },
              cospar_id: { type: 'array', description: 'COSPAR ID' },
              constellation: { type: 'array', description: '星座信息' },
              country_of_registry: { type: 'array', description: '注册国家' },
              owner: { type: 'array', description: '所有者' },
              status: { type: 'array', description: '状态' },
              orbit_info: { type: 'array', description: '轨道信息' },
              launch_info: { type: 'array', description: '发射信息' },
              purpose: { type: 'array', description: '用途' },
              detailed_purpose: { type: 'array', description: '详细用途' },
              _sources: { type: 'array', description: '数据来源' },
              _match_info: { 
                type: 'object', 
                description: '匹配信息',
                properties: {
                  similarity: { type: 'number', description: '相似度', example: 0.85 },
                  matched_fields: { type: 'array', items: { type: 'string' }, description: '匹配的字段', example: ["satellite_name", "purpose"] }
                }
              }
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: '查询参数无效',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: '至少需要提供一个筛选条件' }
      }
    }
  })
  async filterSatellites(@Body() filterDto: SatelliteFilterDto): Promise<any> {
    this.logger.debug(`接收到卫星筛选请求: ${JSON.stringify(filterDto)}`);
    
    // 验证至少有一个筛选条件
    if (this.isEmptyFilter(filterDto)) {
      // 临时注释不再需要，恢复正常验证
      throw new BadRequestException('至少需要提供一个筛选条件');
    }
    
    try {
      // 调用数据库服务进行筛选
      const result = await this.databaseService.filterSatellites(filterDto);
      
      return {
        success: true,
        total: result.total,
        page: filterDto.page || 1,
        limit: filterDto.limit || 10,
        results: result.results
      };
    } catch (error) {
      this.logger.error(`卫星筛选失败: ${error.message}`, error.stack);
      throw new BadRequestException(`卫星筛选失败: ${error.message}`);
    }
  }
  
  /**
   * 检查筛选条件是否为空
   * @param filter 筛选条件
   * @returns 是否为空
   */
  private isEmptyFilter(filter: SatelliteFilterDto): boolean {
    // 排除分页参数
    const { page, limit, ...conditions } = filter;
    
    // 检查是否有任何筛选条件
    return Object.keys(conditions).length === 0;
  }

  @Get('satellite-users')
  @ApiOperation({
    summary: '获取卫星使用者集合',
    description: `
    从本地卫星信息中获取所有卫星使用者的唯一集合。
    
    ## 说明
    - 从satellites表的users字段获取使用者集合
    - users字段是JSONB类型，值存在于字段内数组项的value字段中
    - 如果一个value字段包含多个用标点符号分隔的所有者，则将它们分开返回
    - 处理括号和特殊符号，确保无标点符号的正确内容
    - 每个使用者同时提供中文和英文表示
    - 返回按英文字母排序的唯一使用者列表
    
    ## 返回示例
    \`\`\`json
    {
      "success": true,
      "users": [
        {"cn": "学术", "en": "Academic"},
        {"cn": "民用", "en": "Civil"},
        {"cn": "商业", "en": "Commercial"},
        {"cn": "公司", "en": "Company"},
        {"cn": "教育", "en": "Education"},
        {"cn": "政府", "en": "Government"},
        {"cn": "个人", "en": "Individual"},
        {"cn": "研究所", "en": "Institute"},
        {"cn": "军事", "en": "Military"}
      ]
    }
    \`\`\`
    `
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        users: { 
          type: 'array', 
          description: '唯一使用者列表，包含中英文表示',
          items: {
            type: 'object',
            properties: {
              cn: { type: 'string', description: '中文表示', example: '军事' },
              en: { type: 'string', description: '英文表示', example: 'Military' }
            }
          },
          example: [
            {"cn": "学术", "en": "Academic"}, 
            {"cn": "民用", "en": "Civil"}, 
            {"cn": "商业", "en": "Commercial"}
          ]
        }
      }
    }
  })
  @ApiResponse({
    status: 500,
    description: '查询执行失败',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 500 },
        message: { type: 'string', example: '获取卫星使用者集合失败' },
        error: { type: 'string', example: 'Internal Server Error' }
      }
    }
  })
  async getSatelliteUsers(): Promise<any> {
    this.logger.debug('执行获取卫星使用者集合操作');
    
    try {
      // 执行查询
      const result = await this.databaseService.getSatelliteUsers();
      
      return result;
    } catch (error) {
      this.logger.error(`获取卫星使用者集合失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Get('satellite-purposes')
  @ApiOperation({
    summary: '获取卫星主要用途集合',
    description: `
    从本地卫星信息中获取所有卫星主要用途的唯一集合。
    
    ## 说明
    - 从satellites表的purpose字段获取主要用途集合
    - purpose字段是JSONB类型，值存在于字段内数组项的value字段中
    - 处理括号和特殊符号，确保无标点符号的正确内容
    - 每个用途同时提供中文和英文表示
    - 返回按英文字母排序的唯一用途列表
    
    ## 返回示例
    \`\`\`json
    {
      "success": true,
      "purposes": [
        {"cn": "通信", "en": "Communications"},
        {"cn": "地球观测", "en": "Earth Observation"},
        {"cn": "导航", "en": "Navigation"},
        {"cn": "科学", "en": "Science"},
        {"cn": "技术验证", "en": "Technology Demonstration"},
        {"cn": "太空探索", "en": "Space Exploration"}
      ]
    }
    \`\`\`
    `
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        purposes: { 
          type: 'array', 
          description: '唯一用途列表，包含中英文表示',
          items: {
            type: 'object',
            properties: {
              cn: { type: 'string', description: '中文表示', example: '通信' },
              en: { type: 'string', description: '英文表示', example: 'Communications' }
            }
          },
          example: [
            {"cn": "通信", "en": "Communications"}, 
            {"cn": "地球观测", "en": "Earth Observation"}, 
            {"cn": "导航", "en": "Navigation"}
          ]
        }
      }
    }
  })
  @ApiResponse({
    status: 500,
    description: '查询执行失败',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 500 },
        message: { type: 'string', example: '获取卫星主要用途集合失败' },
        error: { type: 'string', example: 'Internal Server Error' }
      }
    }
  })
  async getSatellitePurposes(): Promise<any> {
    this.logger.debug('执行获取卫星主要用途集合操作');
    
    try {
      // 执行查询
      const result = await this.databaseService.getSatellitePurposes();
      
      return result;
    } catch (error) {
      this.logger.error(`获取卫星主要用途集合失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Get('launch-contractors')
  @ApiOperation({
    summary: '获取发射承包商集合',
    description: '获取所有不同的发射承包商名称，用于筛选界面。'
  })
  @ApiResponse({
    status: 200,
    description: '成功获取发射承包商集合',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        contractors: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              cn: { type: 'string', example: '中国航天科技集团' },
              en: { type: 'string', example: 'China Aerospace Science and Technology Corporation' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 500,
    description: '获取发射承包商集合失败',
    schema: {
      properties: {
        statusCode: { type: 'number', example: 500 },
        message: { type: 'string', example: '获取发射承包商集合失败' },
        error: { type: 'string', example: 'Internal Server Error' }
      }
    }
  })
  @SetMetadata('isPublic', true)
  async getLaunchContractors(): Promise<any> {
    this.logger.debug('执行获取发射承包商集合操作');
    
    try {
      // 执行查询
      const result = await this.databaseService.getLaunchContractors();
      
      return result;
    } catch (error) {
      this.logger.error(`获取发射承包商集合失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Get('launch-sites')
  @ApiOperation({
    summary: '获取发射地点集合',
    description: '获取所有不同的发射地点名称，用于筛选界面。'
  })
  @ApiResponse({
    status: 200,
    description: '成功获取发射地点集合',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        launch_sites: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              cn: { type: 'string', example: '酒泉卫星发射中心' },
              en: { type: 'string', example: 'JIUQUAN SATELLITE LAUNCH CENTER' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 500,
    description: '获取发射地点集合失败',
    schema: {
      properties: {
        statusCode: { type: 'number', example: 500 },
        message: { type: 'string', example: '获取发射地点集合失败' },
        error: { type: 'string', example: 'Internal Server Error' }
      }
    }
  })
  @SetMetadata('isPublic', true)
  async getLaunchSites(): Promise<any> {
    this.logger.debug('执行获取发射地点集合操作');
    
    try {
      // 执行查询
      const result = await this.databaseService.getLaunchSites();
      
      return result;
    } catch (error) {
      this.logger.error(`获取发射地点集合失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Get('launch-vehicles')
  @ApiOperation({
    summary: '获取发射载具集合',
    description: '获取所有不同的发射载具名称，用于筛选界面。'
  })
  @ApiResponse({
    status: 200,
    description: '成功获取发射载具集合',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
        launch_vehicles: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              cn: { type: 'string', example: '长征五号' },
              en: { type: 'string', example: 'LONG MARCH 5' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 500,
    description: '获取发射载具集合失败',
    schema: {
      properties: {
        statusCode: { type: 'number', example: 500 },
        message: { type: 'string', example: '获取发射载具集合失败' },
        error: { type: 'string', example: 'Internal Server Error' }
      }
    }
  })
  @SetMetadata('isPublic', true)
  async getLaunchVehicles(): Promise<any> {
    this.logger.debug('执行获取发射载具集合操作');
    
    try {
      // 执行查询
      const result = await this.databaseService.getLaunchVehicles();
      
      return result;
    } catch (error) {
      this.logger.error(`获取发射载具集合失败: ${error.message}`, error.stack);
      throw error;
    }
  }
} 