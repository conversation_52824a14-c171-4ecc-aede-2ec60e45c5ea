import { Test, TestingModule } from '@nestjs/testing';
import { ConstellationController } from './constellation.controller';
import { SatelliteService } from '../services/satellite.service';
import { ConstellationService } from '../services/constellation.service';
import { ConstellationsWithTleResponseDto } from '../dto/constellation-with-tle.dto';
import { Logger } from '@nestjs/common';

describe('ConstellationController', () => {
  let controller: ConstellationController;
  let constellationService: ConstellationService;
  let satelliteService: SatelliteService;

  // 模拟日志记录器，避免在测试中生成日志
  const mockLogger = {
    log: jest.fn(),
    debug: jest.fn(),
    error: jest.fn(),
  };

  // 模拟卫星服务
  const mockSatelliteService = {
    getSatelliteConstellationsLocal: jest.fn().mockResolvedValue([
      { en: 'Starlink', zh: '星链' },
      { en: 'OneWeb', zh: '万维网' },
    ]),
    updateSatelliteConstellationInfo: jest.fn().mockResolvedValue({
      success: true,
      message: '成功更新100个卫星的星座信息',
      updated: 100,
    }),
  };

  // 模拟星座服务
  const mockConstellationService = {
    getConstellationsWithTle: jest.fn().mockResolvedValue({
      total: 2,
      constellations: [
        {
          name: 'Starlink',
          satelliteCount: 42,
        },
        {
          name: 'OneWeb',
          satelliteCount: 36,
        },
      ],
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ConstellationController],
      providers: [
        {
          provide: SatelliteService,
          useValue: mockSatelliteService,
        },
        {
          provide: ConstellationService,
          useValue: mockConstellationService,
        },
      ],
    }).compile();

    controller = module.get<ConstellationController>(ConstellationController);
    constellationService = module.get<ConstellationService>(ConstellationService);
    satelliteService = module.get<SatelliteService>(SatelliteService);

    // 替换控制器中的日志记录器
    (controller as any).logger = mockLogger;
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getConstellations', () => {
    it('should return an array of constellations', async () => {
      // Act
      const result = await controller.getConstellations();
      
      // Assert
      expect(result).toEqual([
        { en: 'Starlink', zh: '星链' },
        { en: 'OneWeb', zh: '万维网' },
      ]);
      expect(satelliteService.getSatelliteConstellationsLocal).toHaveBeenCalled();
      expect(mockLogger.log).toHaveBeenCalledWith('接收到获取卫星星座集合请求（本地数据库）');
    });
  });

  describe('updateConstellationInfo', () => {
    it('should update constellation information', async () => {
      // Act
      const result = await controller.updateConstellationInfo();
      
      // Assert
      expect(result).toEqual({
        success: true,
        message: '成功更新100个卫星的星座信息',
        updated: 100,
      });
      expect(satelliteService.updateSatelliteConstellationInfo).toHaveBeenCalled();
      expect(mockLogger.log).toHaveBeenCalledWith('接收到更新卫星星座信息请求');
    });
  });

  describe('getConstellationsWithTle', () => {
    it('should return constellations with TLE information', async () => {
      // Act
      const result = await controller.getConstellationsWithTle();
      
      // Assert
      expect(result).toBeInstanceOf(ConstellationsWithTleResponseDto);
      expect(result.total).toBe(2);
      expect(result.constellations).toHaveLength(2);
      expect(result.constellations[0].name).toBe('Starlink');
      expect(result.constellations[0].satelliteCount).toBe(42);
      
      expect(constellationService.getConstellationsWithTle).toHaveBeenCalled();
      expect(mockLogger.log).toHaveBeenCalledWith('接收到获取具有TLE轨道信息的卫星星座列表请求');
      expect(mockLogger.debug).toHaveBeenCalledWith('查询结果: 找到2个星座');
    });
  });
}); 