import { Controller, Post, Body, UseGuards, Logger, Get, Param, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiBearerAuth, ApiBody, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { SatelliteService } from '../services/satellite.service';
import { SatelliteQueryDto } from '../elasticsearch/dto/satellite-query.dto';
import { SatelliteSearchResponse } from '../elasticsearch/types/satellite.types';
import { AggregationTaskService } from '../services/aggregation-task.service';
import { ElasticsearchSatelliteService } from '../elasticsearch/services/elasticsearch.satellite.service';

// 扩展查询DTO，添加排序字段
class ExtendedSatelliteQueryDto extends SatelliteQueryDto {
  sort_by?: string;
  sort_dir?: 'asc' | 'desc';
}

/**
 * 卫星查询控制器
 * 处理本地数据库中卫星信息的查询请求
 */
@Controller('local/satellite')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('本地卫星信息')
export class SatelliteController {
  private readonly logger = new Logger(SatelliteController.name);

  constructor(
    private readonly satelliteService: SatelliteService,
    private readonly aggregationTaskService: AggregationTaskService,
    private readonly elasticsearchSatelliteService: ElasticsearchSatelliteService
  ) {}

  @Post('search')
  @ApiOperation({
    summary: '在本地数据库中搜索卫星信息（旧版API，推荐使用GET方法）',
    description: `
    此接口保留用于向后兼容，推荐使用GET /local/satellite 接口替代。
    
    根据提供的查询条件在本地数据库中搜索卫星信息。支持多种查询条件组合，包括关键词搜索和精确匹配。
    
    ## 查询参数说明
    - page: 页码，默认为1
    - limit: 每页数量，默认为10
    - keyword: 关键词搜索，会匹配卫星名称、别名等字段
    - sort_by: 排序字段，如satellite_name, norad_id等
    - sort_dir: 排序方向，asc或desc
    - 其他字段支持精确匹配
    `
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number', description: '总结果数' },
        page: { type: 'number', description: '当前页码' },
        limit: { type: 'number', description: '每页数量' },
        hits: {
          type: 'array',
          description: '查询结果',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number', description: '记录ID' },
              satellite_name: { type: 'array', description: '卫星名称' },
              alternative_name: { type: 'array', description: '卫星别名' },
              cospar_id: { type: 'array', description: 'COSPAR ID' },
              country_of_registry: { type: 'array', description: '注册国家' },
              owner: { type: 'array', description: '所有者' },
              status: { type: 'array', description: '状态' },
              norad_id: { type: 'array', description: 'NORAD ID' },
              launch_info: { type: 'array', description: '发射信息' },
              orbit_info: { type: 'array', description: '轨道信息' },
              update_time: { type: 'array', description: '更新时间' },
              _sources: { type: 'array', description: '数据来源' }
            }
          }
        }
      }
    }
  })
  async searchSatelliteInfo(@Body() query: ExtendedSatelliteQueryDto): Promise<SatelliteSearchResponse> {
    this.logger.debug(`接收到本地卫星信息查询请求(POST方法): ${JSON.stringify(query)}`);
    // 直接调用GET方法的实现
    return this.getAggregatedSatellites(query);
  }

  @Get('names')
  @ApiOperation({
    summary: '获取本地数据库中的卫星名称集合',
    description: `
    从本地数据库中提取卫星名称（satellite_name）和别名（alternative_name）字段的值，合并为一个集合并返回。
    
    ## 数据处理
    1. 从本地数据库中提取卫星名称和别名
    2. 过滤掉空值、"None"值、"null"值和"undefined"值
    3. 去重并排序
    
    ## 用途
    该API主要用于：
    1. 提供卫星名称的自动补全功能
    2. 展示系统中所有已知的卫星名称
    3. 辅助用户进行卫星信息查询
    `
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'array',
      items: {
        type: 'string'
      },
      example: [
        "AEHF-1",
        "AEHF-2",
        "AEHF-3",
        "AEHF-4",
        "AEHF-5",
        "AEHF-6"
      ]
    }
  })
  async getSatelliteNames(): Promise<string[]> {
    this.logger.debug('接收到获取本地卫星名称集合请求');
    const result = await this.satelliteService.getSatelliteNamesLocal();
    this.logger.debug(`获取到 ${result.length} 个本地卫星名称`);
    return result;
  }

  @Get('statuses')
  @ApiOperation({
    summary: '获取本地数据库中的卫星状态集合',
    description: `
    从本地数据库中提取卫星状态（status）字段的值，合并为一个集合并返回。
    
    ## 数据处理
    1. 从本地数据库中提取status字段的值
    2. 过滤掉空值、"None"值、"null"值和"undefined"值
    3. 添加中文翻译
    4. 去重并排序
    
    ## 用途
    该API主要用于：
    1. 提供卫星状态的筛选条件
    2. 展示系统中所有已知的卫星状态
    3. 辅助用户进行卫星信息查询
    `
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          en: {
            type: 'string',
            description: '英文状态'
          },
          zh: {
            type: 'string',
            description: '中文状态'
          }
        }
      },
      example: [
        { "en": "active", "zh": "在轨运行" },
        { "en": "inactive", "zh": "停止运行" },
        { "en": "decayed", "zh": "已坠毁" }
      ]
    }
  })
  async getSatelliteStatuses(): Promise<Array<{en: string, zh: string}>> {
    this.logger.debug('接收到获取卫星状态集合请求');
    const result = await this.satelliteService.getSatelliteStatusesLocal();
    this.logger.debug(`获取到 ${result.length} 种卫星状态`);
    return result;
  }

  @Post('sync')
  @ApiOperation({
    summary: '同步单个卫星数据',
    description: `
    根据卫星名称从Elasticsearch同步单个卫星数据到本地数据库。
    
    ## 处理流程
    1. 根据卫星名称在Elasticsearch中查询卫星数据
    2. 将查询结果保存到本地数据库
    3. 如果本地数据库中已存在该卫星，则更新数据
    
    ## 用途
    该API主要用于：
    1. 手动同步单个卫星数据
    2. 测试同步功能
    `
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          description: '卫星名称'
        }
      },
      required: ['name']
    }
  })
  @ApiResponse({
    status: 200,
    description: '同步成功',
    schema: {
      type: 'object',
      properties: {
        success: {
          type: 'boolean',
          example: true
        },
        message: {
          type: 'string',
          example: '卫星数据同步成功'
        }
      }
    }
  })
  async syncSatellite(@Body('name') name: string): Promise<{success: boolean, message: string}> {
    this.logger.debug(`接收到同步卫星数据请求: ${name}`);
    
    try {
      await this.satelliteService.syncSatelliteByName(name);
      return {
        success: true,
        message: `卫星 ${name} 数据同步成功`
      };
    } catch (error) {
      this.logger.error(`同步卫星数据失败: ${error.message}`, error.stack);
      return {
        success: false,
        message: `同步失败: ${error.message}`
      };
    }
  }

  @Post('sync-all')
  @ApiOperation({
    summary: '同步所有卫星数据',
    description: `
    从Elasticsearch同步所有卫星数据到本地数据库。
    
    ## 处理流程
    1. 从Elasticsearch中查询所有卫星数据
    2. 将查询结果保存到本地数据库
    3. 如果本地数据库中已存在相同卫星，则更新数据
    
    ## 用途
    该API主要用于：
    1. 初始化本地数据库
    2. 全量更新本地数据库
    
    ## 注意事项
    此操作可能耗时较长，建议在非高峰期执行。
    `
  })
  @ApiResponse({
    status: 200,
    description: '同步成功',
    schema: {
      type: 'object',
      properties: {
        success: {
          type: 'boolean',
          example: true
        },
        message: {
          type: 'string',
          example: '所有卫星数据同步成功'
        }
      }
    }
  })
  async syncAllSatellites(): Promise<{success: boolean, message: string}> {
    this.logger.debug('接收到同步所有卫星数据请求');
    
    try {
      await this.satelliteService.syncAllSatellitesFromES();
      return {
        success: true,
        message: '所有卫星数据同步成功'
      };
    } catch (error) {
      this.logger.error(`同步所有卫星数据失败: ${error.message}`, error.stack);
      return {
        success: false,
        message: `同步失败: ${error.message}`
      };
    }
  }

  @Post('aggregate')
  @ApiOperation({
    summary: '聚合卫星数据',
    description: `
    根据提供的参数聚合卫星数据。
    
    ## 参数说明
    - keyword: 关键词，用于筛选要聚合的卫星数据
    - limit: 限制聚合的卫星数量
    - saveToDatabase: 是否将聚合结果保存到数据库
    
    ## 处理流程
    1. 根据参数从Elasticsearch中查询卫星数据
    2. 对查询结果进行聚合处理
    3. 如果saveToDatabase为true，则将聚合结果保存到本地数据库
    
    ## 用途
    该API主要用于：
    1. 测试聚合算法
    2. 针对特定卫星进行聚合
    3. 增量更新本地数据库
    `
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        keyword: {
          type: 'string',
          description: '关键词'
        },
        limit: {
          type: 'number',
          description: '限制数量'
        },
        saveToDatabase: {
          type: 'boolean',
          description: '是否保存到数据库'
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '聚合成功',
    schema: {
      type: 'object',
      properties: {
        success: {
          type: 'boolean',
          example: true
        },
        totalAggregated: {
          type: 'number',
          example: 100
        },
        message: {
          type: 'string',
          example: '卫星数据聚合成功'
        }
      }
    }
  })
  async aggregateSatelliteData(
    @Body() options: { keyword?: string; limit?: number; saveToDatabase?: boolean }
  ): Promise<{ success: boolean; totalAggregated: number; message: string }> {
    this.logger.debug(`接收到聚合卫星数据请求: ${JSON.stringify(options)}`);
    
    try {
      const result = await this.satelliteService.aggregateSatelliteData(options);
      return result;
    } catch (error) {
      this.logger.error(`聚合卫星数据失败: ${error.message}`, error.stack);
      return {
        success: false,
        totalAggregated: 0,
        message: `聚合失败: ${error.message}`
      };
    }
  }

  @Post('clear')
  @ApiOperation({
    summary: '清空卫星数据',
    description: `
    清空本地数据库中的所有卫星数据。
    
    ## 处理流程
    1. 删除本地数据库中的所有卫星记录
    
    ## 用途
    该API主要用于：
    1. 测试环境重置
    2. 数据迁移前的准备工作
    
    ## 注意事项
    此操作会删除所有卫星数据，请谨慎使用。
    `
  })
  @ApiResponse({
    status: 200,
    description: '清空成功',
    schema: {
      type: 'object',
      properties: {
        success: {
          type: 'boolean',
          example: true
        },
        message: {
          type: 'string',
          example: '卫星数据已清空'
        }
      }
    }
  })
  async clearSatelliteData(): Promise<{ success: boolean; message: string }> {
    this.logger.debug('接收到清空卫星数据请求');
    
    try {
      const result = await this.satelliteService.clearSatelliteData();
      return result;
    } catch (error) {
      this.logger.error(`清空卫星数据失败: ${error.message}`, error.stack);
      return {
        success: false,
        message: `清空失败: ${error.message}`
      };
    }
  }

  @Get('orbit-classes-local')
  @ApiOperation({
    summary: '获取本地数据库中卫星轨道高度集合',
    description: `
    从本地数据库中提取卫星轨道高度（orbit_class）字段的值，合并为一个集合并返回。
    
    ## 数据处理
    1. 从本地数据库中提取orbit_info.orbit_class字段的值
    2. 过滤掉空值、"None"值、"null"值和"undefined"值
    3. 添加中文翻译
    4. 去重并排序
    
    ## 用途
    该API主要用于：
    1. 提供卫星轨道高度的筛选条件
    2. 展示系统中所有已知的卫星轨道高度
    3. 辅助用户进行卫星信息查询
    `
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          en: {
            type: 'string',
            description: '英文轨道高度'
          },
          zh: {
            type: 'string',
            description: '中文轨道高度'
          }
        }
      },
      example: [
        { "en": "GEO", "zh": "地球同步轨道" },
        { "en": "LEO", "zh": "低地球轨道" },
        { "en": "MEO", "zh": "中地球轨道" }
      ]
    }
  })
  async getOrbitClassesLocal(): Promise<Array<{en: string, zh: string}>> {
    this.logger.debug('接收到获取本地卫星轨道高度集合请求');
    const result = await this.satelliteService.getSatelliteOrbitClassesLocal();
    this.logger.debug(`获取到 ${result.length} 种本地卫星轨道高度`);
    return result;
  }

  @Get('orbit-types-local')
  @ApiOperation({
    summary: '获取本地数据库中卫星轨道类型集合',
    description: `
    从本地数据库中提取卫星轨道类型（orbit_type）字段的值，合并为一个集合并返回。
    
    ## 数据处理
    1. 从本地数据库中提取orbit_info.orbit_type字段的值
    2. 过滤掉空值、"None"值、"null"值和"undefined"值
    3. 添加中文翻译
    4. 去重并排序
    
    ## 用途
    该API主要用于：
    1. 提供卫星轨道类型的筛选条件
    2. 展示系统中所有已知的卫星轨道类型
    3. 辅助用户进行卫星信息查询
    `
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          en: {
            type: 'string',
            description: '英文轨道类型'
          },
          zh: {
            type: 'string',
            description: '中文轨道类型'
          }
        }
      },
      example: [
        { "en": "Polar", "zh": "极地轨道" },
        { "en": "Elliptical", "zh": "椭圆轨道" },
        { "en": "Equatorial", "zh": "赤道轨道" }
      ]
    }
  })
  async getOrbitTypesLocal(): Promise<Array<{en: string, zh: string}>> {
    this.logger.debug('接收到获取本地卫星轨道类型集合请求');
    const result = await this.satelliteService.getSatelliteOrbitTypesLocal();
    this.logger.debug(`获取到 ${result.length} 种本地卫星轨道类型`);
    return result;
  }

  @Get(':id')
  @ApiOperation({
    summary: '获取单个卫星的详细信息',
    description: `
    根据卫星ID获取其在本地数据库中的详细信息。
    
    ## 返回数据格式
    返回的是单个卫星的完整信息，包括所有字段及其来源。每个字段都是一个数组，
    数组中的每个元素包含value（字段值）和sources（数据来源）两部分。
    
    例如：
    \`\`\`json
    {
      "satellite_name": [
        {
          "value": "ISS (ZARYA)",
          "sources": ["ucs", "n2yo"]
        },
        {
          "value": "INTERNATIONAL SPACE STATION",
          "sources": ["satnogs"]
        }
      ],
      "norad_id": [
        {
          "value": "25544",
          "sources": ["ucs", "n2yo", "satnogs"]
        }
      ],
      // 其他字段...
    }
    \`\`\`
    
    ## 数据聚合说明
    卫星信息是通过聚合多个数据源（包括gunter、n2yo、nanosats、satnogs、ucs）的数据生成的。
    聚合规则是基于norad_id、cospar_id和satellite_name字段进行匹配，只要其中一个字段匹配，
    就认为是同一颗卫星的不同数据源。
    `
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', description: '记录ID' },
        satellite_name: { type: 'array', description: '卫星名称' },
        alternative_name: { type: 'array', description: '卫星别名' },
        cospar_id: { type: 'array', description: 'COSPAR ID' },
        country_of_registry: { type: 'array', description: '注册国家' },
        owner: { type: 'array', description: '所有者' },
        status: { type: 'array', description: '状态' },
        norad_id: { type: 'array', description: 'NORAD ID' },
        launch_info: { type: 'array', description: '发射信息' },
        orbit_info: { type: 'array', description: '轨道信息' },
        update_time: { type: 'array', description: '更新时间' },
        _sources: { type: 'array', description: '数据来源' }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: '未找到卫星',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: '未找到ID为123的卫星' }
      }
    }
  })
  async getSatelliteById(@Param('id') id: number): Promise<any> {
    this.logger.debug(`接收到获取卫星详细信息请求: ID=${id}`);
    
    try {
      const satellite = await this.satelliteService.getSatelliteById(id);
      return satellite;
    } catch (error) {
      this.logger.error(`获取卫星详细信息失败: ${error.message}`, error.stack);
      return {
        success: false,
        message: `获取失败: ${error.message}`
      };
    }
  }

  @Get()
  @ApiOperation({
    summary: '查询聚合卫星列表',
    description: `
    获取本地数据库中已聚合的卫星信息列表，支持分页、关键词搜索、各种字段的精确匹配以及排序。
    
    ## 查询参数说明
    - page: 页码，默认为1
    - limit: 每页数量，默认为10
    - keyword: 关键词搜索，会匹配卫星名称、别名等字段
    - norad_id: NORAD ID精确匹配
    - cospar_id: COSPAR ID精确匹配
    - status: 卫星状态模糊匹配
    - sort_by: 排序字段，如satellite_name, norad_id等
    - sort_dir: 排序方向，asc或desc
    
    ## 结果格式
    返回的是包含分页信息和卫星列表的对象，每个卫星对象包含所有聚合字段。
    `
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number', description: '总结果数' },
        page: { type: 'number', description: '当前页码' },
        limit: { type: 'number', description: '每页数量' },
        hits: {
          type: 'array',
          description: '查询结果',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number', description: '记录ID' },
              satellite_name: { type: 'array', description: '卫星名称' },
              alternative_name: { type: 'array', description: '卫星别名' },
              cospar_id: { type: 'array', description: 'COSPAR ID' },
              norad_id: { type: 'array', description: 'NORAD ID' },
              status: { type: 'array', description: '卫星状态' },
              _sources: { type: 'array', description: '数据来源列表' }
            }
          }
        }
      }
    }
  })
  async getAggregatedSatellites(@Query() query: ExtendedSatelliteQueryDto): Promise<SatelliteSearchResponse> {
    this.logger.debug(`接收到查询聚合卫星列表请求: ${JSON.stringify(query)}`);
    
    // 直接复用现有的searchSatelliteInfoLocal方法
    const result = await this.satelliteService.searchSatelliteInfoLocal(query);
    
    this.logger.debug(`查询结果: 共${result.total}条记录，当前第${result.page}页，每页${result.limit}条`);
    return result;
  }

  @Post('test-aggregation')
  @ApiOperation({
    summary: '测试卫星数据聚合逻辑',
    description: `
    测试卫星数据聚合逻辑是否正确工作。
    
    此接口会：
    1. 清空数据库
    2. 创建测试数据
    3. 执行聚合操作
    4. 验证聚合结果
    
    ## 注意事项
    此接口仅用于测试和调试，不应在生产环境中使用。
    `
  })
  @ApiResponse({
    status: 200,
    description: '测试完成',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '测试完成，请查看日志了解详细结果' }
      }
    }
  })
  async testAggregationLogic(): Promise<any> {
    this.logger.debug('接收到测试卫星数据聚合逻辑请求');
    
    try {
      const result = await this.satelliteService.testAggregationLogic();
      this.logger.debug(`测试完成: ${result.message}`);
      return result;
    } catch (error) {
      this.logger.error(`测试失败: ${error.message}`, error.stack);
      return { 
        success: false, 
        message: `测试失败: ${error.message}` 
      };
    }
  }

  @Post('test-custom-aggregation')
  @ApiOperation({
    summary: '测试自定义卫星数据聚合逻辑',
    description: '使用提供的测试数据文件测试卫星数据聚合逻辑。此端点仅用于测试和调试目的。'
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        testDataFile: {
          type: 'string',
          description: '包含测试数据的JSON文件路径'
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '测试完成',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' }
      }
    }
  })
  async testCustomAggregation(@Body() body: { testDataFile: string }): Promise<any> {
    this.logger.log(`收到自定义聚合测试请求，使用文件: ${body.testDataFile}`);
    
    try {
      const result = await this.satelliteService.testCustomAggregation(body.testDataFile);
      this.logger.log(`自定义聚合测试完成: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      this.logger.error(`自定义聚合测试失败: ${error.message}`, error.stack);
      return {
        success: false,
        message: `自定义聚合测试失败: ${error.message}`
      };
    }
  }

  @Post('aggregate-all')
  @ApiOperation({
    summary: '全量卫星信息聚合',
    description: `
    聚合所有索引中的卫星信息并存储到本地数据库。
    
    ## 聚合规则
    1. 从satsinfo_gunter、satsinfo_n2yo、satsinfo_nanosats、satsinfo_satnogs、satsinfo_ucs五个索引中获取所有卫星信息
    2. 通过norad_id、cospar_id、satellite_name进行匹配（三个字段中只要有一个能精确匹配即可）
    3. 聚合后的字段为五个数据源中包含字段的并集，同时保留每个字段值的数据来源
    4. 同一个字段的相同值只保留一次，不同值按数据源显示
    5. 结果去重，确保不同文档的norad_id、cospar_id、satellite_name不重复
    6. 将聚合结果保存到本地PostgreSQL数据库
    
    ## 参数说明
    - limit: 最大处理数量，默认为0（不限制）
    - saveToDatabase: 是否保存到数据库，默认为true
    
    ## 注意事项
    该操作非常耗时，建议在后台执行。API会立即返回确认任务已启动的响应，实际聚合过程会在后台继续执行。
    `
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        limit: {
          type: 'number',
          description: '最大处理数量，默认为0（不限制）'
        },
        saveToDatabase: {
          type: 'boolean',
          description: '是否保存到数据库，默认为true'
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '聚合任务已启动',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '聚合任务已启动，请耐心等待完成' }
      }
    }
  })
  async aggregateAllSatelliteData(
    @Body() options: { limit?: number; saveToDatabase?: boolean }
  ): Promise<{ success: boolean; message: string }> {
    this.logger.log(`接收到全量卫星信息聚合请求: ${JSON.stringify(options)}`);
    
    // 在后台执行聚合任务
    setTimeout(async () => {
      try {
        // 先清空数据库
        await this.satelliteService.clearSatelliteData();
        this.logger.log('已清空卫星数据表，开始全量聚合');
        
        // 执行聚合
        const result = await this.satelliteService.aggregateSatelliteData({
          limit: options.limit || 0, // 0表示不限制
          saveToDatabase: options.saveToDatabase !== false
        });
        
        this.logger.log(`全量卫星信息聚合完成: ${result.message}`);
      } catch (error) {
        this.logger.error(`全量卫星信息聚合失败: ${error.message}`, error.stack);
      }
    }, 0);
    
    return { 
      success: true, 
      message: '全量聚合任务已启动，请耐心等待完成。该过程可能需要较长时间，您可以稍后查询数据库以获取结果。' 
    };
  }

  @Post('incremental-aggregate')
  @ApiOperation({
    summary: '增量聚合卫星数据',
    description: `
    执行增量聚合操作，只聚合ES中有但本地数据库中没有的卫星信息。
    
    ## 功能说明
    1. 首先查找ES卫星信息索引中有但本地PostgreSQL数据库中没有的卫星信息
    2. 使用norad_id、cospar_id、satellite_name、alternative_name字段进行匹配
    3. 如果ES中这几个字段的值在本地数据库中都没有，则视为新的卫星信息
    4. 将新的卫星信息聚合并写入本地数据库
    
    ## 参数说明
    - saveToDatabase: 是否保存到数据库，默认为true
    
    ## 返回数据说明
    - success: 是否成功
    - totalNewAggregated: 新增聚合的卫星数量
    - message: 操作结果描述
    
    ## 注意事项
    - 该操作可能需要较长时间，建议在后台运行
    - 可以通过聚合任务API查询任务执行状态
    `
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        saveToDatabase: {
          type: 'boolean',
          description: '是否保存到数据库，默认为true',
          default: true
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '操作已启动',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '增量聚合任务已启动，请耐心等待完成。该过程可能需要较长时间，您可以稍后查询数据库以获取结果。' }
      }
    }
  })
  async incrementalAggregateSatelliteData(
    @Body() options: { saveToDatabase?: boolean }
  ): Promise<{ success: boolean; message: string }> {
    this.logger.log(`接收到增量卫星信息聚合请求: ${JSON.stringify(options)}`);
    
    // 在后台执行聚合任务
    setTimeout(async () => {
      try {
        // 执行增量聚合
        const result = await this.satelliteService.incrementalAggregateSatelliteData({
          saveToDatabase: options.saveToDatabase !== false
        });
        
        this.logger.log(`增量卫星信息聚合完成: ${result.message}`);
      } catch (error) {
        this.logger.error(`增量卫星信息聚合失败: ${error.message}`, error.stack);
      }
    }, 0);
    
    return { 
      success: true, 
      message: '增量聚合任务已启动，请耐心等待完成。该过程可能需要较长时间，您可以稍后查询数据库以获取结果。' 
    };
  }

  /**
   * 更新卫星轨道信息
   * @returns 更新结果
   */
  @Post('update-orbit-info')
  @ApiOperation({
    summary: '更新卫星轨道信息',
    description: `
    从ES数据库的orbital_tle索引获取轨道信息并更新本地数据库中的卫星轨道信息字段。
    
    ## 更新字段
    - 轨道概览 (orbit_overview)
    - 轨道高度 (orbit_class): LEO, MEO, GEO, HEO等
    - 轨道类型 (orbit_type): Polar, Equatorial, Inclined, Circular, Elliptical等
    - 地球同步轨道经度 (longitude_of_geo_degrees)
    - 近地点高度 (perigee_km)
    - 远地点高度 (apogee_km)
    - 轨道偏心率 (eccentricity)
    - 轨道倾角 (incl_degrees)
    - 轨道周期 (period_minutes)
    - 衰变日期 (decay_date)
    - 部署/入轨日期 (deployed_date)
    
    ## 匹配方式
    通过norad_id、cospar_id、satellite_name进行匹配，按照这个优先级顺序，只要有一个字段匹配即视为同一颗卫星。
    
    ## 返回数据说明
    - success: 是否成功
    - message: 更新结果消息
    - updated: 更新的卫星数量
    `
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '成功更新50个卫星的轨道信息' },
        updated: { type: 'number', example: 50 }
      }
    }
  })
  async updateOrbitInfo(): Promise<{ success: boolean; message: string; updated: number }> {
    this.logger.log('接收到更新卫星轨道信息请求');
    return this.satelliteService.updateSatelliteOrbitInfoFromTLE();
  }

  @Post('update-users')
  @ApiOperation({
    summary: '从ES更新卫星users字段',
    description: `
    从ES中提取卫星users字段并更新到本地数据库中。
    
    ## 处理流程
    1. 从ES中的所有卫星索引查询有users字段的记录
    2. 在本地数据库中找到对应的卫星记录
    3. 更新本地记录的users字段，保留来源信息
    
    ## 返回结果
    返回更新的记录数量和处理结果。
    `
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', description: '是否成功' },
        updated: { type: 'number', description: '更新的记录数量' },
        message: { type: 'string', description: '处理结果描述' }
      }
    }
  })
  async updateUsersFromES(): Promise<{ success: boolean; message: string; updated: number }> {
    this.logger.log('接收到更新卫星users字段请求');
    return await this.satelliteService.updateSatelliteUsersFromES();
  }
} 