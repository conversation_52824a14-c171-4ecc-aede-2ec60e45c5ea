/**
 * 密码验证工具
 */
export class PasswordValidator {
  /**
   * 验证密码强度
   * @param password 待验证的密码
   * @returns 验证结果对象
   */
  static validate(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 检查长度
    if (password.length < 8 || password.length > 20) {
      errors.push('密码长度必须在8-20个字符之间');
    }

    // 检查是否包含大写字母
    if (!/[A-Z]/.test(password)) {
      errors.push('密码必须包含至少一个大写字母');
    }

    // 检查是否包含小写字母
    if (!/[a-z]/.test(password)) {
      errors.push('密码必须包含至少一个小写字母');
    }

    // 检查是否包含数字
    if (!/[0-9]/.test(password)) {
      errors.push('密码必须包含至少一个数字');
    }

    // 检查是否包含特殊字符
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('密码必须包含至少一个特殊字符(!@#$%^&*(),.?":{}|<>)');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
} 