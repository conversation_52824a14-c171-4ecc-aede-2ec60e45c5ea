#!/bin/bash

# 快速启动脚本 - 完成构建和启动

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 构建应用
build_app() {
    log_info "构建应用..."
    
    # 创建dist目录
    mkdir -p dist
    
    # 编译TypeScript文件
    npx tsc --target ES2020 --module commonjs --outDir dist --experimentalDecorators --emitDecoratorMetadata src-minimal/*.ts
    
    if [[ ! -f "dist/main.js" ]]; then
        log_error "构建失败"
        return 1
    fi
    
    log_success "构建完成"
}

# 启动应用
start_app() {
    log_info "启动应用..."
    
    # 检查端口
    if lsof -Pi :3001 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口3001被占用，尝试停止..."
        sudo kill -9 $(lsof -t -i:3001) 2>/dev/null || true
        sleep 2
    fi
    
    # 设置环境变量
    export NODE_ENV=production
    export PORT=3001
    
    # 启动应用
    nohup node dist/main.js > app-minimal.log 2>&1 &
    echo $! > app-minimal.pid
    
    log_success "应用已启动，PID: $(cat app-minimal.pid)"
    
    # 等待启动
    sleep 3
    
    # 检查状态
    for i in {1..10}; do
        if curl -f http://localhost:3001/api/health &>/dev/null; then
            log_success "应用启动成功！"
            log_info "访问地址: http://localhost:3001/api"
            log_info "健康检查: http://localhost:3001/api/health"
            return 0
        fi
        sleep 1
    done
    
    log_warning "应用可能还在启动中..."
    log_info "查看日志: tail -f app-minimal.log"
}

# 显示状态
show_status() {
    log_info "应用状态:"
    
    if [[ -f "app-minimal.pid" ]] && kill -0 $(cat app-minimal.pid) 2>/dev/null; then
        log_success "应用进程运行正常 (PID: $(cat app-minimal.pid))"
    else
        log_error "应用进程未运行"
    fi
    
    echo ""
    log_info "管理命令:"
    echo "  查看日志: tail -f app-minimal.log"
    echo "  停止应用: kill \$(cat app-minimal.pid)"
    echo "  重启应用: ./quick-start.sh"
    echo "  查看状态: curl http://localhost:3001/api/health"
    
    echo ""
    log_info "测试命令:"
    echo "  curl http://localhost:3001/api"
    echo "  curl http://localhost:3001/api/health"
}

# 主函数
main() {
    log_info "开始快速启动..."
    
    build_app
    start_app
    show_status
    
    log_success "快速启动完成！🎉"
}

# 执行主函数
main "$@" 