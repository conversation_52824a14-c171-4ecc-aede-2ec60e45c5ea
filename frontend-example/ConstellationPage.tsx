import React, { useState, useEffect } from 'react';
import { Table, Card, Typography, Spin, message } from 'antd';
import ConstellationSearchForm from './ConstellationSearchForm';
import { searchConstellations, ConstellationSearchParams, ConstellationSearchResponse } from './constellationService';
import './ConstellationSearchForm.css';

const { Title } = Typography;

const ConstellationPage: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [searchResults, setSearchResults] = useState<ConstellationSearchResponse | null>(null);
  const [searchParams, setSearchParams] = useState<ConstellationSearchParams>({
    page: 1,
    limit: 10
  });

  // 处理搜索
  const handleSearch = async (values: ConstellationSearchParams) => {
    try {
      setLoading(true);
      const params = {
        ...values,
        page: values.page || 1,
        limit: values.limit || 10
      };
      setSearchParams(params);
      const results = await searchConstellations(params);
      setSearchResults(results);
    } catch (error) {
      message.error('搜索失败，请稍后重试');
      console.error('Search error:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理分页
  const handlePageChange = (page: number, pageSize?: number) => {
    const newParams = {
      ...searchParams,
      page,
      limit: pageSize || 10
    };
    handleSearch(newParams);
  };

  // 表格列定义
  const columns = [
    {
      title: '星座名称',
      dataIndex: 'constellation_name',
      key: 'constellation_name',
      render: (names: any[]) => names?.map(item => item.value).join(', ') || '-'
    },
    {
      title: '所属机构',
      dataIndex: 'company',
      key: 'company',
      render: (companies: any[]) => companies?.map(item => item.value).join(', ') || '-'
    },
    {
      title: '所属国家',
      dataIndex: 'country',
      key: 'country',
      render: (countries: any[]) => {
        if (!countries || countries.length === 0) return '-';
        const countryValues = countries.map(item => {
          if (Array.isArray(item.value)) {
            return item.value.join(', ');
          }
          return item.value;
        });
        return countryValues.join(', ');
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (statuses: any[]) => statuses?.map(item => item.value).join(', ') || '-'
    },
    {
      title: '卫星数量',
      dataIndex: 'satellites',
      key: 'satellites',
      render: (satellites: any) => satellites?.total_count || '-'
    }
  ];

  return (
    <div className="constellation-page">
      <Title level={2}>星座信息查询</Title>
      
      <ConstellationSearchForm onSearch={handleSearch} />
      
      <Card>
        <Spin spinning={loading}>
          {searchResults && (
            <Table
              dataSource={searchResults.hits}
              columns={columns}
              rowKey={(record) => {
                const names = record.constellation_name;
                return names && names.length > 0 ? names[0].value : Math.random().toString();
              }}
              pagination={{
                current: searchResults.page,
                pageSize: searchResults.limit,
                total: searchResults.total,
                onChange: handlePageChange,
                showSizeChanger: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          )}
        </Spin>
      </Card>
    </div>
  );
};

export default ConstellationPage; 