import React, { useState } from 'react';
import { Button, Input, Select, Form, Space, Card } from 'antd';
import { SearchOutlined } from '@ant-design/icons';

interface ConstellationSearchFormProps {
  onSearch: (values: any) => void;
}

const ConstellationSearchForm: React.FC<ConstellationSearchFormProps> = ({ onSearch }) => {
  const [form] = Form.useForm();
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleSearch = (values: any) => {
    onSearch(values);
  };

  const handleReset = () => {
    form.resetFields();
  };

  return (
    <Card className="search-card">
      <Form
        form={form}
        name="constellation_search"
        onFinish={handleSearch}
        layout="vertical"
      >
        <div className="search-bar">
          <Form.Item name="keyword" className="search-input">
            <Input 
              placeholder="请输入关键词搜索" 
              prefix={<SearchOutlined />} 
              size="large"
            />
          </Form.Item>
          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              size="large"
              icon={<SearchOutlined />}
            >
              搜索
            </Button>
          </Form.Item>
          <Form.Item>
            <Button 
              type="link" 
              onClick={() => setShowAdvanced(!showAdvanced)}
              size="large"
            >
              {showAdvanced ? '收起' : '精确搜索'}
            </Button>
          </Form.Item>
        </div>

        {showAdvanced && (
          <div className="advanced-search">
            <div className="search-row">
              <Form.Item 
                label="目标名称" 
                name="targetName" 
                className="search-item"
              >
                <Input placeholder="请输入" />
              </Form.Item>
              
              <Form.Item 
                label="目标编号" 
                name="targetId" 
                className="search-item"
              >
                <Input placeholder="请输入" />
              </Form.Item>
            </div>

            <div className="search-row">
              <Form.Item 
                label="所属国家" 
                name="country" 
                className="search-item"
              >
                <Select
                  placeholder="请选择"
                  allowClear
                  showSearch
                  options={[
                    { value: 'USA', label: '美国' },
                    { value: 'China', label: '中国' },
                    { value: 'Russia', label: '俄罗斯' },
                    { value: 'EU', label: '欧盟' },
                    { value: 'Japan', label: '日本' },
                    { value: 'India', label: '印度' },
                    // 可以根据需要添加更多国家
                  ]}
                />
              </Form.Item>
              
              <Form.Item 
                label="所属机构" 
                name="organization" 
                className="search-item"
              >
                <Select
                  placeholder="请选择"
                  allowClear
                  showSearch
                  options={[
                    { value: 'SpaceX', label: 'SpaceX' },
                    { value: 'NASA', label: 'NASA' },
                    { value: 'ESA', label: '欧洲航天局' },
                    { value: 'CNSA', label: '中国航天局' },
                    { value: 'Roscosmos', label: '俄罗斯航天局' },
                    { value: 'JAXA', label: '日本宇宙航空研究开发机构' },
                    // 可以根据需要添加更多机构
                  ]}
                />
              </Form.Item>
            </div>

            <div className="search-row">
              <Form.Item 
                label="运营状态" 
                name="status" 
                className="search-item"
              >
                <Select
                  placeholder="请选择"
                  allowClear
                  options={[
                    { value: 'operational', label: '运行中' },
                    { value: 'planned', label: '计划中' },
                    { value: 'in_development', label: '开发中' },
                    { value: 'decommissioned', label: '已退役' },
                  ]}
                />
              </Form.Item>
              
              <Form.Item className="search-item">
                <Space>
                  <Button type="primary" htmlType="submit">
                    搜索
                  </Button>
                  <Button onClick={handleReset}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </div>
          </div>
        )}
      </Form>
    </Card>
  );
};

export default ConstellationSearchForm;

// 相关CSS样式
/*
.search-card {
  margin-bottom: 20px;
}

.search-bar {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.search-input {
  flex: 1;
  margin-right: 16px;
  margin-bottom: 0 !important;
}

.advanced-search {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
}

.search-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.search-item {
  flex: 1;
  min-width: 200px;
  margin-right: 16px;
}

@media (max-width: 768px) {
  .search-row {
    flex-direction: column;
  }
  
  .search-item {
    width: 100%;
    margin-right: 0;
  }
}
*/ 