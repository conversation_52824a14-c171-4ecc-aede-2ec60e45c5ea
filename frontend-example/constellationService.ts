import axios from 'axios';

const API_BASE_URL = '/api';

export interface ConstellationSearchParams {
  keyword?: string;
  targetName?: string;
  targetId?: string;
  country?: string;
  organization?: string;
  page?: number;
  limit?: number;
}

export interface ConstellationSearchResponse {
  total: number;
  page: number;
  limit: number;
  hits: any[];
}

export interface ConstellationNamesResponse {
  constellationNames: string[];
  count: number;
}

export interface ConstellationOrganizationsResponse {
  organizations: string[];
  count: number;
}

/**
 * 搜索星座信息
 * @param params 搜索参数
 * @returns 搜索结果
 */
export const searchConstellations = async (
  params: ConstellationSearchParams
): Promise<ConstellationSearchResponse> => {
  try {
    const response = await axios.post<ConstellationSearchResponse>(
      `${API_BASE_URL}/search/constellation`,
      params,
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error('Error searching constellations:', error);
    throw error;
  }
};

/**
 * 获取星座名称集合
 * @returns 星座名称集合
 */
export const getConstellationNames = async (): Promise<ConstellationNamesResponse> => {
  try {
    const response = await axios.get<ConstellationNamesResponse>(
      `${API_BASE_URL}/search/constellation/names`,
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching constellation names:', error);
    throw error;
  }
};

/**
 * 获取国家列表
 * @returns 国家列表
 */
export const getCountries = async (): Promise<string[]> => {
  // 这里可以实现从后端获取国家列表的逻辑
  // 目前返回静态数据
  return [
    'USA',
    'China',
    'Russia',
    'EU',
    'Japan',
    'India',
    'Canada',
    'UK',
    'France',
    'Germany',
    'Italy',
    'Brazil',
    'Australia',
  ];
};

/**
 * 获取机构列表
 * @returns 机构列表
 */
export const getOrganizations = async (): Promise<string[]> => {
  try {
    const response = await axios.get<ConstellationOrganizationsResponse>(
      `${API_BASE_URL}/search/constellation/organizations`,
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      }
    );
    return response.data.organizations;
  } catch (error) {
    console.error('Error fetching organizations:', error);
    throw error;
  }
}; 