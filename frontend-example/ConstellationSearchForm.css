.search-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-bar {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.search-input {
  flex: 1;
  margin-right: 16px;
  margin-bottom: 0 !important;
}

.advanced-search {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  margin-top: 8px;
}

.search-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.search-item {
  flex: 1;
  min-width: 200px;
  margin-right: 16px;
}

.search-item:last-child {
  margin-right: 0;
}

@media (max-width: 768px) {
  .search-row {
    flex-direction: column;
  }
  
  .search-item {
    width: 100%;
    margin-right: 0;
    margin-bottom: 16px;
  }
} 