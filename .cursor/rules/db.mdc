---
description: 数据库访问规则
globs: 
alwaysApply: false
---
# 数据分类存储规则
| 数据类型 | 存储方案 | 说明 |
|------------------|-----------------------------------|----------------------------------------------------------------------|
| 空间实体数据 | ES集群（分片存储） | 卫星/碎片/发射场等核心数据 |
| 用户数据 | PostgreSQL（关系型） | 账户信息、权限配置、会员等级 |
| 用户行为数据 | ES集群（日志专用索引） | 搜索记录、下载记录、访问日志 |
| 缓存数据 | Redis Cluster | 热点查询结果、模型推理结果、权限信息 |
| 模型训练数据 | MinIO对象存储 | 用户查询样本、标注数据、模型版本 |

# 空间实体数据ES索引与配置
##索引
- 卫星信息：satsinfo_gunter、satsinfo_n2yo、satsinfo_nanosats、satsinfo_satnogs、satsinfo_ucs
- 轨道信息：orbital_tle
- 碎片信息：debris_discos、debris_spacetrack
- 碎片事件：event_discos、
- 太空漏洞库：loophole_sat
- 频率信息：freq_itu
- 星座信息：constell_newspace、constell_n2yo
- 发射信息：launch_jonathan、launch_spacenow_*
- 带载荷的发射信息：launch_gunter
- 火箭信息：veh_discos、veh_jonathan
- 发射场信息：launchsites_wiki
##配置
- 用户：web_readonly
- 密码：web@readonly4all
- 地址：http://**************:9200

#本地PostgreSQL数据库
##配置在.env文件中
   DB_HOST=localhost
   DB_PORT=5432
   DB_USERNAME=linger
   DB_PASSWORD=postgres
   DB_DATABASE=spacedata


