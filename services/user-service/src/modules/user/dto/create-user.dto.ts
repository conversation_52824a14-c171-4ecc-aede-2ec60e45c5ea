import { IsString, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, IsBoolean, IsA<PERSON>y, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateUserDto {
  @IsString()
  @ApiProperty({ description: '用户名' })
  username: string;

  @IsEmail()
  @ApiProperty({ description: '电子邮箱' })
  email: string;

  @IsString()
  @MinLength(6)
  @ApiProperty({ description: '密码' })
  password: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ description: '手机号码', required: false })
  phone?: string;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ description: '是否启用', default: true, required: false })
  isActive?: boolean;

  @IsArray()
  @IsOptional()
  @ApiProperty({ description: '角色ID列表', type: [String], required: false })
  roleIds?: string[];

  @IsOptional()
  @ApiProperty({ description: '用户设置', required: false })
  settings?: Record<string, any>;

  @IsOptional()
  @ApiProperty({ description: '用户元数据', required: false })
  metadata?: Record<string, any>;
} 