import { <PERSON><PERSON><PERSON>, IsEmail, <PERSON><PERSON><PERSON>al, IsBoolean, <PERSON>A<PERSON>y, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { CreateUserDto } from './create-user.dto';
import { PartialType } from '@nestjs/swagger';

export class UpdateUserDto extends PartialType(CreateUserDto) {
  @IsString()
  @IsOptional()
  @MinLength(6)
  @ApiProperty({ description: '密码', required: false })
  password?: string;

  @IsArray()
  @IsOptional()
  @ApiProperty({ description: '角色ID列表', type: [String], required: false })
  roleIds?: string[];

  @IsOptional()
  @ApiProperty({ description: '用户设置', required: false })
  settings?: Record<string, any>;

  @IsOptional()
  @ApiProperty({ description: '用户元数据', required: false })
  metadata?: Record<string, any>;
} 