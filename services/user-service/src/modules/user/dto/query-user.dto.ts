import { IsString, IsE<PERSON>, Is<PERSON>ptional, IsBoolean, IsN<PERSON>ber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class QueryUserDto {
  @IsString()
  @IsOptional()
  @ApiProperty({ description: '用户名', required: false })
  username?: string;

  @IsEmail()
  @IsOptional()
  @ApiProperty({ description: '电子邮箱', required: false })
  email?: string;

  @IsBoolean()
  @IsOptional()
  @Type(() => Boolean)
  @ApiProperty({ description: '是否启用', required: false })
  isActive?: boolean;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  @ApiProperty({ description: '跳过记录数', required: false, default: 0 })
  skip?: number;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  @ApiProperty({ description: '获取记录数', required: false, default: 10 })
  take?: number;
} 