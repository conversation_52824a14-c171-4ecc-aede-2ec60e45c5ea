import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, DeleteDateColumn, ManyToMany, JoinTable } from 'typeorm';
import { Permission } from './permission.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity('roles')
export class Role {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: '角色ID' })
  id: string;

  @Column({ unique: true })
  @ApiProperty({ description: '角色名称' })
  name: string;

  @Column({ nullable: true })
  @ApiProperty({ description: '角色描述' })
  description?: string;

  @Column({ default: true })
  @ApiProperty({ description: '是否启用' })
  isActive: boolean;

  @ManyToMany(() => Permission)
  @JoinTable({
    name: 'role_permissions',
    joinColumn: { name: 'role_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'permission_id', referencedColumnName: 'id' },
  })
  @ApiProperty({ description: '角色权限' })
  permissions: Permission[];

  @CreateDateColumn()
  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @DeleteDateColumn()
  @ApiProperty({ description: '删除时间' })
  deletedAt?: Date;

  @Column({ type: 'jsonb', nullable: true })
  @ApiProperty({ description: '角色元数据' })
  metadata?: Record<string, any>;
} 