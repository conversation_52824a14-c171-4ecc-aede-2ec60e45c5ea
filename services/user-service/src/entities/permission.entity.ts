import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, DeleteDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('permissions')
export class Permission {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: '权限ID' })
  id: string;

  @Column({ unique: true })
  @ApiProperty({ description: '权限名称' })
  name: string;

  @Column({ nullable: true })
  @ApiProperty({ description: '权限描述' })
  description?: string;

  @Column({ default: true })
  @ApiProperty({ description: '是否启用' })
  isActive: boolean;

  @Column({ type: 'varchar', array: true, default: [] })
  @ApiProperty({ description: '资源类型' })
  resourceTypes: string[];

  @Column({ type: 'varchar', array: true, default: [] })
  @ApiProperty({ description: '操作类型' })
  actions: string[];

  @Column({ type: 'jsonb', nullable: true })
  @ApiProperty({ description: '权限条件' })
  conditions?: Record<string, any>;

  @CreateDateColumn()
  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @DeleteDateColumn()
  @ApiProperty({ description: '删除时间' })
  deletedAt?: Date;

  @Column({ type: 'jsonb', nullable: true })
  @ApiProperty({ description: '权限元数据' })
  metadata?: Record<string, any>;
} 