import { <PERSON>ti<PERSON>, PrimaryGeneratedC<PERSON>umn, Column, CreateDateColumn, UpdateDateColumn, DeleteDateColumn, ManyToMany, JoinTable } from 'typeorm';
import { Role } from './role.entity';
import { Exclude } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  @ApiProperty({ description: '用户ID' })
  id: string;

  @Column({ unique: true })
  @ApiProperty({ description: '用户名' })
  username: string;

  @Column({ unique: true })
  @ApiProperty({ description: '电子邮箱' })
  email: string;

  @Column()
  @Exclude()
  password: string;

  @Column({ nullable: true })
  @ApiProperty({ description: '手机号码' })
  phone?: string;

  @Column({ default: false })
  @ApiProperty({ description: '是否验证邮箱' })
  isEmailVerified: boolean;

  @Column({ default: false })
  @ApiProperty({ description: '是否验证手机' })
  isPhoneVerified: boolean;

  @Column({ default: true })
  @ApiProperty({ description: '是否启用' })
  isActive: boolean;

  @ManyToMany(() => Role)
  @JoinTable({
    name: 'user_roles',
    joinColumn: { name: 'user_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'role_id', referencedColumnName: 'id' },
  })
  @ApiProperty({ description: '用户角色' })
  roles: Role[];

  @CreateDateColumn()
  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn()
  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @DeleteDateColumn()
  @ApiProperty({ description: '删除时间' })
  deletedAt?: Date;

  @Column({ type: 'jsonb', nullable: true })
  @ApiProperty({ description: '用户设置' })
  settings?: Record<string, any>;

  @Column({ type: 'jsonb', nullable: true })
  @ApiProperty({ description: '用户元数据' })
  metadata?: Record<string, any>;
} 