# Ubuntu服务器部署指南

## 快速部署

### 1. 准备工作
- Ubuntu 18.04/20.04/22.04 服务器
- 具有sudo权限的用户账户
- 服务器能够访问互联网

### 2. 一键部署
```bash
# 进入项目目录
cd spacedata后端-cursor

# 运行部署脚本
./ubuntu-deploy.sh
```

### 3. 部署内容
脚本会自动完成以下操作：
- ✅ 更新系统包
- ✅ 安装Node.js 18.x
- ✅ 安装编译工具和Python
- ✅ 安装PM2进程管理器
- ✅ 安装项目依赖
- ✅ 编译项目
- ✅ 创建环境变量模板
- ✅ 配置PM2启动
- ✅ 启动应用

## 部署后访问

- **应用地址**: http://your-server-ip:3001
- **API文档**: http://your-server-ip:3001/api-docs

## 常用命令

### PM2进程管理
```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs spacedata-backend

# 重启应用
pm2 restart spacedata-backend

# 停止应用
pm2 stop spacedata-backend

# 开机自启动
pm2 startup
pm2 save
```

### 直接启动（开发模式）
```bash
# 使用项目自带的启动脚本
./start.sh
```

## 配置说明

### 环境变量配置
编辑 `.env` 文件修改配置：
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=linger
DB_PASSWORD=postgres
DB_DATABASE=spacedata

# JWT配置
JWT_SECRET=your_jwt_secret_key_here

# 大模型API配置
OPENAI_API_KEY=your_openai_api_key
QIANWEN_API_KEY=your_qianwen_api_key
```

### 防火墙配置
如需外部访问，开放3001端口：
```bash
sudo ufw allow 3001
```

##中间件数据库数据更新
首先要根据现有数据库结构创建对应的表
1、调用增量聚合卫星数据API
2、调用更新卫星轨道信息API
3、调用更新卫星星座信息API

## 故障排除

### 查看日志
```bash
# PM2日志
pm2 logs spacedata-backend

# 应用日志
tail -f logs/combined.log
```

### 重新部署
如需重新部署，直接运行：
```bash
./ubuntu-deploy.sh
```

### 端口占用
如果3001端口被占用：
```bash
# 查看端口占用
sudo netstat -tlnp | grep 3001

# 杀死占用进程
sudo kill -9 PID
```

## 注意事项

1. **数据库配置**: 需要根据实际情况配置数据库连接
2. **安全设置**: 生产环境需要修改默认的JWT密钥
3. **文件权限**: 确保应用有权限访问日志目录
4. **系统资源**: 建议服务器至少2GB内存

## 技术支持

如遇问题，请检查：
- 系统版本是否支持
- 网络连接是否正常
- 端口是否被占用
- 依赖安装是否完整 
