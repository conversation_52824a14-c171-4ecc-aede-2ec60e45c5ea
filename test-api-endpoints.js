/**
 * 测试实体识别相关API端点的脚本
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:3000';
const JWT_TOKEN = process.env.JWT_TOKEN || '';

// API端点列表
const endpoints = [
  {
    name: '卫星名称',
    url: '/satellite/names',
    method: 'GET'
  },
  {
    name: '星座名称',
    url: '/local/constellation',
    method: 'GET'
  },
  {
    name: '发射场名称',
    url: '/api/es/launch/site-names',
    method: 'GET'
  },
  {
    name: '火箭名称',
    url: '/api/es/launch/rocket-names',
    method: 'GET'
  },
  {
    name: '发射服务商',
    url: '/api/es/launch/providers',
    method: 'GET'
  }
];

async function testEndpoint(endpoint) {
  try {
    console.log(`\n🔍 测试 ${endpoint.name} API: ${endpoint.url}`);
    
    const config = {
      method: endpoint.method,
      url: `${BASE_URL}${endpoint.url}`,
      timeout: 10000
    };

    // 如果有JWT token，添加认证头
    if (JWT_TOKEN) {
      config.headers = {
        'Authorization': `Bearer ${JWT_TOKEN}`
      };
    } else {
      console.log('⚠️  未找到JWT_TOKEN环境变量');
    }

    const response = await axios(config);
    
    console.log(`✅ ${endpoint.name} API 调用成功`);
    console.log(`   状态码: ${response.status}`);
    
    if (response.data) {
      if (Array.isArray(response.data)) {
        console.log(`   返回数据: 数组，长度 ${response.data.length}`);
        if (response.data.length > 0) {
          console.log(`   示例数据: ${JSON.stringify(response.data.slice(0, 3))}`);
        }
      } else if (response.data.data && Array.isArray(response.data.data)) {
        console.log(`   返回数据: 对象包含数组，长度 ${response.data.data.length}`);
        if (response.data.data.length > 0) {
          console.log(`   示例数据: ${JSON.stringify(response.data.data.slice(0, 3))}`);
        }
      } else {
        console.log(`   返回数据类型: ${typeof response.data}`);
        console.log(`   数据结构: ${JSON.stringify(response.data).substring(0, 200)}...`);
      }
    }
    
    return { success: true, data: response.data };
  } catch (error) {
    console.log(`❌ ${endpoint.name} API 调用失败`);
    console.log(`   错误信息: ${error.message}`);
    if (error.response) {
      console.log(`   状态码: ${error.response.status}`);
      console.log(`   响应数据: ${JSON.stringify(error.response.data)}`);
    }
    return { success: false, error: error.message };
  }
}

async function testAllEndpoints() {
  console.log('🚀 开始测试实体识别相关API端点...');
  console.log(`📡 基础URL: ${BASE_URL}`);
  console.log(`🔑 JWT Token: ${JWT_TOKEN ? '已配置' : '未配置'}`);
  
  const results = [];
  
  for (const endpoint of endpoints) {
    const result = await testEndpoint(endpoint);
    results.push({
      name: endpoint.name,
      url: endpoint.url,
      ...result
    });
  }
  
  console.log('\n📊 测试结果汇总:');
  console.log('='.repeat(50));
  
  results.forEach(result => {
    const status = result.success ? '✅ 成功' : '❌ 失败';
    console.log(`${result.name}: ${status}`);
  });
  
  const successCount = results.filter(r => r.success).length;
  console.log(`\n总计: ${successCount}/${results.length} 个API端点可用`);
  
  if (successCount === 0) {
    console.log('\n💡 建议检查:');
    console.log('1. 服务是否正常启动');
    console.log('2. JWT_TOKEN环境变量是否正确配置');
    console.log('3. API端点路径是否正确');
  }
}

// 运行测试
testAllEndpoints().catch(console.error);
