const axios = require('axios');
const fs = require('fs');

const TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwic3ViIjozLCJpYXQiOjE3NTA1MDE5NzYsImV4cCI6MTc1MDU4ODM3Nn0.ltAVJ5xI7ut6Khe5ncsMpZzgNFjlpa3b_qYWE7mld2o";
const API_URL = 'http://localhost:3001/api/v1/database/filter-satellites';

async function investigateApiStability() {
  console.log('🔍 调查 API 分页查询稳定性...\n');
  
  // 进行两次完全相同的查询，比较结果
  const results = [];
  
  for (let round = 1; round <= 2; round++) {
    console.log(`🔄 第 ${round} 轮查询:`);
    
    const roundResults = [];
    const noradIdTracker = new Map();
    
    // 查询所有页面
    let page = 1;
    
    while (true) {
      try {
        const { data } = await axios.post(
          API_URL,
          { constellationName: 'Starlink', page, limit: 100 },
          {
            headers: {
              Accept: 'application/json',
              Authorization: `Bearer ${TOKEN}`,
              'Content-Type': 'application/json',
            },
          },
        );

        if (!data?.success) break;
        
        if (page <= 5 || page % 10 === 0) {
          console.log(`  第 ${page} 页: ${data.results.length} 条记录`);
        }
        
        data.results.forEach((item, index) => {
          const noradId = item.norad_id?.[0]?.value;
          if (noradId) {
            if (!noradIdTracker.has(noradId)) {
              noradIdTracker.set(noradId, []);
            }
            noradIdTracker.get(noradId).push({
              page,
              index,
              dbId: item.id
            });
            
            roundResults.push({
              page,
              index,
              noradId,
              dbId: item.id
            });
          }
        });

        if (data.results.length < 100) break;
        page += 1;
        
        // 限制查询范围
        if (page > 76) break;
        
      } catch (error) {
        console.error(`  第 ${page} 页查询失败:`, error.message);
        break;
      }
    }
    
    // 统计重复
    const duplicates = Array.from(noradIdTracker.entries())
      .filter(([noradId, occurrences]) => occurrences.length > 1);
    
    console.log(`  总记录数: ${roundResults.length}`);
    console.log(`  唯一 NORAD_ID: ${noradIdTracker.size}`);
    console.log(`  重复 NORAD_ID: ${duplicates.length}`);
    
    results.push({
      round,
      totalRecords: roundResults.length,
      uniqueNoradIds: noradIdTracker.size,
      duplicates: duplicates.length,
      duplicateDetails: duplicates.slice(0, 5).map(([noradId, occurrences]) => ({
        noradId,
        count: occurrences.length,
        pages: occurrences.map(o => o.page)
      }))
    });
    
    console.log(`  完成第 ${round} 轮查询\n`);
    
    // 两轮查询之间稍作延迟
    if (round === 1) {
      console.log('⏳ 等待5秒后进行第二轮查询...\n');
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }
  
  // 比较两轮结果
  console.log('📊 两轮查询结果比较:');
  console.log(`第1轮: ${results[0].totalRecords} 条记录, ${results[0].uniqueNoradIds} 个唯一ID, ${results[0].duplicates} 个重复`);
  console.log(`第2轮: ${results[1].totalRecords} 条记录, ${results[1].uniqueNoradIds} 个唯一ID, ${results[1].duplicates} 个重复`);
  
  if (results[0].totalRecords !== results[1].totalRecords || 
      results[0].duplicates !== results[1].duplicates) {
    console.log('\n⚠️  两轮查询结果不一致，说明 API 分页查询不稳定！');
  } else {
    console.log('\n✅ 两轮查询结果一致');
  }
  
  // 保存详细结果
  fs.writeFileSync('api_stability_test.json', JSON.stringify(results, null, 2));
  console.log('\n📄 详细结果已保存到 api_stability_test.json');
}

investigateApiStability(); 