# 服务配置
NODE_ENV=development
PORT=3000
API_PREFIX=/api/v1

# 数据库配置
DB_TYPE=postgres
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=spacedata
DB_SCHEMA=public

# MongoDB配置
MONGODB_URI=mongodb://localhost:27017/spacedata

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Elasticsearch配置
ES_NODE=http://localhost:9200
ES_USERNAME=elastic
ES_PASSWORD=changeme

# JWT配置
JWT_SECRET=your-jwt-secret
JWT_EXPIRATION=1d

# RabbitMQ配置
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_QUEUE=spacedata_queue

# 服务发现
CONSUL_HOST=localhost
CONSUL_PORT=8500

# 监控配置
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# 日志配置
LOG_LEVEL=debug
LOG_PATH=./logs

# 外部服务API配置
SATELLITE_API_KEY=your-api-key
WEATHER_API_KEY=your-api-key

# 文件存储配置
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET=spacedata

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_ITEMS=1000

# 限流配置
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# CORS配置
CORS_ORIGIN=http://localhost:8080
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_CREDENTIALS=true

# 安全配置
ENCRYPTION_KEY=your-encryption-key
COOKIE_SECRET=your-cookie-secret

# 邮件服务配置
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-smtp-password

# 大模型配置 - Qwen
QWEN_API_KEY=sk-1b581fd6f319419d9d0e3f2cc855c10d
QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# 翻译服务配置
TRANSLATION_MODEL=qwen-turbo
TRANSLATION_MODE=default
TRANSLATION_MAX_CONCURRENT=3
TRANSLATION_TIMEOUT=30000
TRANSLATION_MAX_RETRIES=2

# 主题提取服务配置
THEME_EXTRACTION_MODEL=qwen-turbo
THEME_EXTRACTION_MODE=default
THEME_EXTRACTION_MAX_CONCURRENT=3
THEME_EXTRACTION_TIMEOUT=30000
THEME_EXTRACTION_MAX_RETRIES=2

# 新闻定时任务配置
NEWS_SCHEDULER_ENABLED=true
NEWS_SCHEDULER_CRON=0 5,12,18 * * *
NEWS_SCHEDULER_TIMEZONE=Asia/Shanghai
NEWS_SCHEDULER_MAX_RETRIES=3
NEWS_SCHEDULER_RETRY_DELAY=60000

# 新闻翻译定时任务配置
NEWS_TRANSLATION_BATCH_SIZE=20
NEWS_TRANSLATION_MAX_DOCS=0
NEWS_TRANSLATION_FORCE_REPROCESS=false
NEWS_TRANSLATION_LLM_MODE=default
NEWS_TRANSLATION_CUSTOM_MODEL=

# 新闻主题提取定时任务配置
NEWS_THEME_BATCH_SIZE=15
NEWS_THEME_MAX_DOCS=0
NEWS_THEME_FORCE_REPROCESS=false
NEWS_THEME_LLM_MODE=default
NEWS_THEME_CUSTOM_MODEL=

# TLE查询配置
TLE_QUERY_TIME_WINDOW_MINUTES=30
TLE_QUERY_MAX_RECORDS=50000
TLE_QUERY_TIMEOUT=300000
TLE_QUERY_SCROLL_SIZE=10000
TLE_QUERY_SCROLL_TIMEOUT=10m

# 服务器超时配置
SERVER_TIMEOUT=300000
SERVER_KEEP_ALIVE_TIMEOUT=305000
SERVER_HEADERS_TIMEOUT=306000 