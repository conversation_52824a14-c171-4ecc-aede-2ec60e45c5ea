/**
 * TLE查询配置文件
 * 用于管理批量TLE数据查询的配置项
 */

/**
 * TLE查询配置接口
 */
export interface TleQueryConfig {
  timeWindowMinutes: number;     // 时间窗口（分钟）
  maxRecords: number;           // 最大记录数
  timeout: number;              // 查询超时时间（毫秒）
  scrollSize: number;           // 滚动查询每批次大小
  scrollTimeout: string;        // 滚动查询超时时间
}

/**
 * 默认TLE查询配置
 */
const DEFAULT_TLE_QUERY_CONFIG: TleQueryConfig = {
  timeWindowMinutes: 30,        // 默认30分钟时间窗口
  maxRecords: 50000,           // 默认最大50000条记录
  timeout: 300000,             // 默认300秒超时（5分钟）
  scrollSize: 10000,           // 默认每批次10000条
  scrollTimeout: '10m'         // 默认滚动10分钟超时
};

/**
 * 获取TLE查询配置
 * 从环境变量加载配置，如果环境变量不存在则使用默认值
 */
export function getTleQueryConfig(): TleQueryConfig {
  return {
    timeWindowMinutes: parseInt(process.env.TLE_QUERY_TIME_WINDOW_MINUTES || '30', 10),
    maxRecords: parseInt(process.env.TLE_QUERY_MAX_RECORDS || '50000', 10),
    timeout: parseInt(process.env.TLE_QUERY_TIMEOUT || '300000', 10),
    scrollSize: parseInt(process.env.TLE_QUERY_SCROLL_SIZE || '10000', 10),
    scrollTimeout: process.env.TLE_QUERY_SCROLL_TIMEOUT || '10m'
  };
}

/**
 * 验证TLE查询配置
 * @param config TLE查询配置
 */
export function validateTleQueryConfig(config: TleQueryConfig): void {
  if (config.timeWindowMinutes <= 0 || config.timeWindowMinutes > 1440) {
    throw new Error(`时间窗口必须在1-1440分钟之间，当前值: ${config.timeWindowMinutes}`);
  }
  
  if (config.maxRecords <= 0 || config.maxRecords > 100000) {
    throw new Error(`最大记录数必须在1-100000之间，当前值: ${config.maxRecords}`);
  }
  
  if (config.timeout <= 0 || config.timeout > 600000) {
    throw new Error(`超时时间必须在1-600000毫秒之间（1毫秒到10分钟），当前值: ${config.timeout}`);
  }
  
  if (config.scrollSize <= 0 || config.scrollSize > 10000) {
    throw new Error(`滚动查询批次大小必须在1-10000之间，当前值: ${config.scrollSize}`);
  }
}

/**
 * 合并配置
 * @param baseConfig 基础配置
 * @param overrides 覆盖配置
 */
export function mergeTleQueryConfig(
  baseConfig: TleQueryConfig, 
  overrides: Partial<TleQueryConfig>
): TleQueryConfig {
  return {
    ...baseConfig,
    ...overrides
  };
}

export { DEFAULT_TLE_QUERY_CONFIG }; 