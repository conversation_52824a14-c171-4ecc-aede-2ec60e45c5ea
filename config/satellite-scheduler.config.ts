/**
 * 卫星数据定时任务配置文件
 * 用于管理卫星数据增量聚合定时任务的配置项
 */

/**
 * 卫星数据定时任务配置接口
 */
export interface SatelliteSchedulerConfig {
  enabled: boolean;                    // 是否启用定时任务
  cronExpression: string;              // cron表达式，定义执行时间
  timezone: string;                    // 时区
  maxRetries: number;                  // 最大重试次数
  retryDelay: number;                  // 重试延迟时间（毫秒）
  saveToDatabase: boolean;             // 是否保存到数据库
}

/**
 * 任务状态枚举
 */
export enum SatelliteTaskStatus {
  IDLE = 'idle',                       // 空闲
  RUNNING = 'running',                 // 运行中
  COMPLETED = 'completed',             // 已完成
  FAILED = 'failed'                    // 失败
}

/**
 * 任务执行结果接口
 */
export interface SatelliteTaskExecutionResult {
  taskType: 'satellite_incremental_aggregate';
  status: SatelliteTaskStatus;
  startTime: Date;
  endTime?: Date;
  duration?: number;                   // 执行时长（毫秒）
  totalNewAggregated?: number;         // 新增聚合的卫星数量
  error?: string;                      // 错误信息
}

/**
 * 默认的卫星数据定时任务配置
 */
export const DEFAULT_SATELLITE_SCHEDULER_CONFIG: SatelliteSchedulerConfig = {
  enabled: true,
  cronExpression: '0 3 * * *',         // 每天3:00执行
  timezone: 'Asia/Shanghai',            // 中国时区
  maxRetries: 3,                        // 最大重试3次
  retryDelay: 300000,                   // 重试延迟5分钟
  saveToDatabase: true                  // 默认保存到数据库
};

/**
 * 从环境变量获取配置
 * @returns 卫星数据定时任务配置
 */
export function getSatelliteSchedulerConfig(): SatelliteSchedulerConfig {
  return {
    enabled: process.env.SATELLITE_SCHEDULER_ENABLED !== 'false',
    cronExpression: process.env.SATELLITE_SCHEDULER_CRON || DEFAULT_SATELLITE_SCHEDULER_CONFIG.cronExpression,
    timezone: process.env.SATELLITE_SCHEDULER_TIMEZONE || DEFAULT_SATELLITE_SCHEDULER_CONFIG.timezone,
    maxRetries: parseInt(process.env.SATELLITE_SCHEDULER_MAX_RETRIES || '3'),
    retryDelay: parseInt(process.env.SATELLITE_SCHEDULER_RETRY_DELAY || '300000'),
    saveToDatabase: process.env.SATELLITE_SCHEDULER_SAVE_TO_DB !== 'false'
  };
}

/**
 * 验证配置有效性
 * @param config 配置对象
 */
export function validateSatelliteSchedulerConfig(config: SatelliteSchedulerConfig): void {
  if (!config.cronExpression) {
    throw new Error('cron表达式不能为空');
  }
  
  if (config.maxRetries < 0) {
    throw new Error('最大重试次数不能小于0');
  }
  
  if (config.retryDelay <= 0) {
    throw new Error('重试延迟时间必须大于0');
  }
}

/**
 * 预定义的cron表达式
 */
export const SATELLITE_PREDEFINED_CRON_EXPRESSIONS = {
  // 每天执行
  DAILY_3AM: '0 3 * * *',              // 每天3:00（默认）
  DAILY_2AM: '0 2 * * *',              // 每天2:00
  DAILY_4AM: '0 4 * * *',              // 每天4:00
  DAILY_MIDNIGHT: '0 0 * * *',         // 每天午夜
  DAILY_6AM: '0 6 * * *',              // 每天6:00
  
  // 每12小时执行
  EVERY_12_HOURS: '0 3,15 * * *',      // 每天3:00和15:00
  
  // 每周执行
  WEEKLY_SUNDAY_3AM: '0 3 * * 0',      // 每周日3:00
  WEEKLY_MONDAY_3AM: '0 3 * * 1',      // 每周一3:00
  
  // 测试用（每小时）
  HOURLY: '0 * * * *',                 // 每小时（仅用于测试）
  EVERY_30_MIN: '*/30 * * * *',        // 每30分钟（仅用于测试）
}; 