import { LLMMode, TranslationStatistics } from '../src/elasticsearch/types/news.types';

/**
 * 新闻处理定时任务配置文件
 * 用于管理新闻翻译和主题提取定时任务的配置项
 */

/**
 * 定时任务配置接口
 */
export interface NewsSchedulerConfig {
  enabled: boolean;                    // 是否启用定时任务
  cronExpression: string;              // cron表达式，定义执行时间
  timezone: string;                    // 时区
  translationConfig: TaskConfig;       // 翻译任务配置
  themeExtractionConfig: TaskConfig;   // 主题提取任务配置
  maxRetries: number;                  // 最大重试次数
  retryDelay: number;                  // 重试延迟时间（毫秒）
}

/**
 * 任务配置接口
 */
export interface TaskConfig {
  batchSize: number;                   // 批次大小
  maxDocs: number;                     // 最大处理文档数，0表示不限制
  forceReprocess: boolean;             // 是否强制重新处理已处理的文档
  specificIndexes?: string[];          // 指定要处理的索引，不指定则处理所有
  llmMode?: LLMMode;                   // 大模型模式
  customModel?: string;                // 自定义模型名称
}

/**
 * 任务状态枚举
 */
export enum TaskStatus {
  IDLE = 'idle',                       // 空闲
  RUNNING = 'running',                 // 运行中
  COMPLETED = 'completed',             // 已完成
  FAILED = 'failed'                    // 失败
}

/**
 * 任务执行结果接口
 */
export interface TaskExecutionResult {
  taskType: 'translation' | 'theme_extraction';
  status: TaskStatus;
  startTime: Date;
  endTime?: Date;
  duration?: number;                   // 执行时长（毫秒）
  statistics?: TranslationStatistics;  // 使用完整的TranslationStatistics类型
  error?: string;
}

/**
 * 默认的新闻定时任务配置
 */
export const DEFAULT_NEWS_SCHEDULER_CONFIG: NewsSchedulerConfig = {
  enabled: true,
  cronExpression: '0 5,12,18 * * *',   // 每天5:00、12:00、18:00执行
  timezone: 'Asia/Shanghai',            // 中国时区
  translationConfig: {
    batchSize: 20,                      // 翻译批次大小
    maxDocs: 0,                         // 不限制处理文档数
    forceReprocess: false,              // 不强制重新翻译
    llmMode: LLMMode.DEFAULT            // 使用默认模式
  },
  themeExtractionConfig: {
    batchSize: 15,                      // 主题提取批次大小
    maxDocs: 0,                         // 不限制处理文档数
    forceReprocess: false,              // 不强制重新提取
    llmMode: LLMMode.DEFAULT            // 使用默认模式
  },
  maxRetries: 3,                        // 最大重试3次
  retryDelay: 60000                     // 重试延迟1分钟
};

/**
 * 从环境变量获取配置
 * @returns 新闻定时任务配置
 */
export function getNewsSchedulerConfig(): NewsSchedulerConfig {
  return {
    enabled: process.env.NEWS_SCHEDULER_ENABLED !== 'false',
    cronExpression: process.env.NEWS_SCHEDULER_CRON || DEFAULT_NEWS_SCHEDULER_CONFIG.cronExpression,
    timezone: process.env.NEWS_SCHEDULER_TIMEZONE || DEFAULT_NEWS_SCHEDULER_CONFIG.timezone,
    translationConfig: {
      batchSize: parseInt(process.env.NEWS_TRANSLATION_BATCH_SIZE || '20'),
      maxDocs: parseInt(process.env.NEWS_TRANSLATION_MAX_DOCS || '0'),
      forceReprocess: process.env.NEWS_TRANSLATION_FORCE_REPROCESS === 'true',
      llmMode: (process.env.NEWS_TRANSLATION_LLM_MODE as LLMMode) || LLMMode.DEFAULT,
      customModel: process.env.NEWS_TRANSLATION_CUSTOM_MODEL
    },
    themeExtractionConfig: {
      batchSize: parseInt(process.env.NEWS_THEME_BATCH_SIZE || '15'),
      maxDocs: parseInt(process.env.NEWS_THEME_MAX_DOCS || '0'),
      forceReprocess: process.env.NEWS_THEME_FORCE_REPROCESS === 'true',
      llmMode: (process.env.NEWS_THEME_LLM_MODE as LLMMode) || LLMMode.DEFAULT,
      customModel: process.env.NEWS_THEME_CUSTOM_MODEL
    },
    maxRetries: parseInt(process.env.NEWS_SCHEDULER_MAX_RETRIES || '3'),
    retryDelay: parseInt(process.env.NEWS_SCHEDULER_RETRY_DELAY || '60000')
  };
}

/**
 * 验证配置
 * @param config 配置对象
 * @throws 如果配置无效则抛出错误
 */
export function validateNewsSchedulerConfig(config: NewsSchedulerConfig): void {
  if (!config.cronExpression) {
    throw new Error('cron表达式不能为空');
  }
  
  if (config.maxRetries < 0) {
    throw new Error('最大重试次数不能小于0');
  }
  
  if (config.retryDelay <= 0) {
    throw new Error('重试延迟时间必须大于0');
  }
  
  if (config.translationConfig.batchSize <= 0) {
    throw new Error('翻译批次大小必须大于0');
  }
  
  if (config.themeExtractionConfig.batchSize <= 0) {
    throw new Error('主题提取批次大小必须大于0');
  }
}

/**
 * 预定义的cron表达式
 */
export const PREDEFINED_CRON_EXPRESSIONS = {
  // 每天执行
  DAILY_5_12_18: '0 5,12,18 * * *',    // 每天5:00、12:00、18:00
  DAILY_6_14_22: '0 6,14,22 * * *',    // 每天6:00、14:00、22:00
  DAILY_8_16: '0 8,16 * * *',          // 每天8:00、16:00
  DAILY_MIDNIGHT: '0 0 * * *',         // 每天午夜
  
  // 每小时执行
  HOURLY: '0 * * * *',                 // 每小时
  EVERY_2_HOURS: '0 */2 * * *',        // 每2小时
  EVERY_4_HOURS: '0 */4 * * *',        // 每4小时
  EVERY_6_HOURS: '0 */6 * * *',        // 每6小时
  
  // 工作日执行
  WEEKDAYS_9_17: '0 9,17 * * 1-5',     // 工作日9:00、17:00
  WEEKDAYS_NOON: '0 12 * * 1-5',       // 工作日中午12:00
}; 