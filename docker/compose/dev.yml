version: '3.8'

services:
  # API网关
  api-gateway:
    build:
      context: ../../api-gateway
      dockerfile: ../../docker/services/gateway.Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
    depends_on:
      - user-service
      - data-service
      - auth-service
      - analysis-service
    networks:
      - spacedata-network

  # 用户服务
  user-service:
    build:
      context: ../../services/user-service
      dockerfile: ../../docker/services/service.Dockerfile
    environment:
      - NODE_ENV=development
      - SERVICE_NAME=user-service
    depends_on:
      - postgres
      - redis
    networks:
      - spacedata-network

  # 数据服务
  data-service:
    build:
      context: ../../services/data-service
      dockerfile: ../../docker/services/service.Dockerfile
    environment:
      - NODE_ENV=development
      - SERVICE_NAME=data-service
    depends_on:
      - mongodb
      - elasticsearch
    networks:
      - spacedata-network

  # 认证服务
  auth-service:
    build:
      context: ../../services/auth-service
      dockerfile: ../../docker/services/service.Dockerfile
    environment:
      - NODE_ENV=development
      - SERVICE_NAME=auth-service
    depends_on:
      - redis
    networks:
      - spacedata-network

  # 分析服务
  analysis-service:
    build:
      context: ../../services/analysis-service
      dockerfile: ../../docker/services/service.Dockerfile
    environment:
      - NODE_ENV=development
      - SERVICE_NAME=analysis-service
    depends_on:
      - mongodb
      - rabbitmq
    networks:
      - spacedata-network

  # PostgreSQL
  postgres:
    image: postgres:14-alpine
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=spacedata
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - spacedata-network

  # MongoDB
  mongodb:
    image: mongo:5
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - spacedata-network

  # Redis
  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - spacedata-network

  # Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.17.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - spacedata-network

  # RabbitMQ
  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - spacedata-network

  # Consul
  consul:
    image: consul:1.11
    ports:
      - "8500:8500"
    networks:
      - spacedata-network

  # Prometheus
  prometheus:
    image: prom/prometheus:v2.32.1
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - spacedata-network

  # Grafana
  grafana:
    image: grafana/grafana:8.3.3
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - spacedata-network

volumes:
  postgres_data:
  mongodb_data:
  redis_data:
  elasticsearch_data:
  rabbitmq_data:
  grafana_data:

networks:
  spacedata-network:
    driver: bridge 