const axios = require('axios');
const express = require('express');
const fs = require('fs');
const path = require('path');

// 创建简单的前端接收服务器
function createTestServer() {
  const app = express();
  
  // 解析二进制数据
  app.use('/api/tiles/upload', express.raw({ 
    type: 'application/octet-stream', 
    limit: '100mb' 
  }));
  
  // 解析JSON数据
  app.use(express.json());
  
  // 接收文件上传
  app.post('/api/tiles/upload', (req, res) => {
    const fileName = req.headers['x-file-name'];
    const fileSize = parseInt(req.headers['x-file-size']);
    
    console.log(`📥 接收到文件: ${fileName}, 大小: ${(fileSize / 1024 / 1024).toFixed(2)}MB`);
    
    if (req.body.length !== fileSize) {
      console.error(`❌ 文件大小不匹配: 期望${fileSize}, 实际${req.body.length}`);
      return res.status(400).json({ error: '文件大小不匹配' });
    }
    
    // 保存文件到测试目录
    const testDir = './test-received-files';
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }
    
    const filePath = path.join(testDir, fileName);
    fs.writeFileSync(filePath, req.body);
    
    console.log(`✅ 文件保存成功: ${filePath}`);
    res.json({ success: true, message: '文件接收成功', fileName, fileSize });
  });
  
  // 接收回调通知
  app.post('/api/tiles/callback', (req, res) => {
    console.log('\n📞 收到推送完成回调:');
    console.log(JSON.stringify(req.body, null, 2));
    
    const { results, summary } = req.body;
    console.log(`\n📊 推送摘要: 成功${summary.success}/${summary.total}, 总大小: ${(summary.totalDataSize / 1024 / 1024).toFixed(2)}MB`);
    
    res.json({ success: true });
  });
  
  const server = app.listen(3001, () => {
    console.log('🚀 测试接收服务器启动在端口 3001');
  });
  
  return server;
}

// 测试不同的推送配置
async function testPushConfigurations() {
  const baseUrl = 'http://localhost:3000/api/tiles';
  
  console.log('🧪 开始测试卫星点云文件推送功能\n');
  
  // 测试配置列表
  const testConfigs = [
    {
      name: '基础HTTP推送',
      config: {
        colorByOrbitClass: true,
        includeStatistics: true,
        enableGzip: true,
        pushTargets: [
          {
            type: 'http',
            target: 'http://localhost:3001/api/tiles/upload',
            options: {
              timeout: 60000,
              retries: 2
            }
          }
        ]
      }
    },
    {
      name: '本地推送',
      config: {
        colorByOrbitClass: true,
        includeStatistics: true,
        enableGzip: false,
        pushTargets: [
          {
            type: 'local',
            target: './test-local-push'
          }
        ]
      }
    },
    {
      name: '多目标推送',
      config: {
        colorByOrbitClass: true,
        includeStatistics: true,
        enableGzip: true,
        pushTargets: [
          {
            type: 'http',
            target: 'http://localhost:3001/api/tiles/upload',
            options: { timeout: 60000, retries: 2 }
          },
          {
            type: 'local',
            target: './test-multi-push'
          }
        ],
        callbackUrl: 'http://localhost:3001/api/tiles/callback'
      }
    },
    {
      name: '文件分片推送',
      config: {
        colorByOrbitClass: true,
        includeStatistics: true,
        enableGzip: true,
        chunking: {
          enabled: true,
          chunkSize: 5, // 5MB分片
          namePattern: 'satellites_part_{index}.json'
        },
        pushTargets: [
          {
            type: 'http',
            target: 'http://localhost:3001/api/tiles/upload',
            options: { timeout: 60000, retries: 2 }
          }
        ],
        callbackUrl: 'http://localhost:3001/api/tiles/callback'
      }
    }
  ];
  
  for (let i = 0; i < testConfigs.length; i++) {
    const { name, config } = testConfigs[i];
    
    console.log(`\n🔬 测试 ${i + 1}/${testConfigs.length}: ${name}`);
    console.log('配置:', JSON.stringify(config, null, 2));
    
    try {
      const startTime = Date.now();
      
      const response = await axios.post(`${baseUrl}/generate`, config, {
        timeout: 300000 // 5分钟超时
      });
      
      const duration = Date.now() - startTime;
      
      if (response.data.success) {
        console.log(`✅ ${name} 测试成功!`);
        console.log(`   耗时: ${duration}ms`);
        console.log(`   卫星数量: ${response.data.totalSatellites}`);
        console.log(`   生成耗时: ${response.data.generationDuration}ms`);
        
        if (response.data.pushResults) {
          console.log(`   推送结果:`);
          response.data.pushResults.forEach((result, idx) => {
            const status = result.success ? '✅' : '❌';
            console.log(`     ${status} ${result.target}: ${result.files.join(', ')} (${(result.dataSize / 1024 / 1024).toFixed(2)}MB, ${result.duration}ms)`);
            if (result.error) {
              console.log(`       错误: ${result.error}`);
            }
          });
        }
        
        if (response.data.fileSizes) {
          console.log(`   文件大小:`);
          Object.entries(response.data.fileSizes).forEach(([file, size]) => {
            console.log(`     ${file}: ${(size / 1024 / 1024).toFixed(2)}MB`);
          });
        }
      } else {
        console.log(`❌ ${name} 测试失败: ${response.data.message}`);
      }
      
    } catch (error) {
      console.log(`❌ ${name} 测试出错:`, error.message);
      if (error.response?.data) {
        console.log('   响应数据:', error.response.data);
      }
    }
    
    // 测试间隔
    if (i < testConfigs.length - 1) {
      console.log('\n⏳ 等待5秒后进行下一个测试...');
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }
}

// 测试状态API
async function testStatusAPI() {
  const baseUrl = 'http://localhost:3000/api/tiles';
  
  console.log('\n📊 测试状态API');
  
  try {
    const response = await axios.get(`${baseUrl}/status`);
    console.log('状态信息:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.log('❌ 获取状态失败:', error.message);
  }
}

// 清理测试文件
function cleanupTestFiles() {
  const testDirs = [
    './test-received-files',
    './test-local-push',
    './test-multi-push'
  ];
  
  console.log('\n🧹 清理测试文件...');
  
  testDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true, force: true });
      console.log(`   删除目录: ${dir}`);
    }
  });
}

// 主测试函数
async function runTests() {
  let server = null;
  
  try {
    // 清理之前的测试文件
    cleanupTestFiles();
    
    // 启动测试接收服务器
    server = createTestServer();
    
    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 测试状态API
    await testStatusAPI();
    
    // 测试推送功能
    await testPushConfigurations();
    
    console.log('\n🎉 所有测试完成!');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    // 关闭测试服务器
    if (server) {
      server.close();
      console.log('\n🛑 测试服务器已关闭');
    }
    
    // 清理测试文件
    setTimeout(() => {
      cleanupTestFiles();
      console.log('✨ 测试环境清理完成');
      process.exit(0);
    }, 2000);
  }
}

// 检查后端服务是否运行
async function checkBackendService() {
  try {
    await axios.get('http://localhost:3000/api/tiles/status', { timeout: 5000 });
    return true;
  } catch (error) {
    return false;
  }
}

// 启动测试
async function main() {
  console.log('🔍 检查后端服务状态...');
  
  const isBackendRunning = await checkBackendService();
  
  if (!isBackendRunning) {
    console.log('❌ 后端服务未运行，请先启动后端服务:');
    console.log('   npm run start:dev');
    process.exit(1);
  }
  
  console.log('✅ 后端服务正在运行');
  
  await runTests();
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n\n🛑 收到退出信号，正在清理...');
  cleanupTestFiles();
  process.exit(0);
});

// 运行测试
main().catch(error => {
  console.error('❌ 测试启动失败:', error);
  process.exit(1);
}); 