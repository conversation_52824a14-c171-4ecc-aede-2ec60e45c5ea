#!/bin/bash

# 修复 AggregationTask 元数据错误的一键脚本
# Usage: ./fix-aggregation-task-error.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印函数
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "======================================"
echo "修复 AggregationTask 元数据错误"
echo "======================================"

# 步骤1: 检查当前目录
print_status "检查当前目录..."
if [ ! -f "package.json" ]; then
    print_error "未找到package.json文件，请确保在项目根目录运行此脚本"
    exit 1
fi

if [ ! -f "src/entities/aggregation-task.entity.ts" ]; then
    print_error "未找到AggregationTask实体文件，请确保代码已更新"
    exit 1
fi

print_status "当前目录确认正确"

# 步骤2: 拉取最新代码
print_status "拉取最新代码..."
git pull origin main || {
    print_warning "Git拉取失败，请手动检查Git状态"
}

# 步骤3: 检查迁移文件是否存在
print_status "检查迁移文件..."
if [ ! -f "src/migrations/1748600000000-CreateAggregationTasksTable.ts" ]; then
    print_error "迁移文件不存在，请确保代码已正确更新"
    exit 1
fi

print_status "迁移文件存在，继续执行..."

# 步骤4: 备份当前运行状态
print_status "备份当前PM2状态..."
pm2 save 2>/dev/null || print_warning "PM2状态保存失败，继续执行..."

# 步骤5: 停止应用（如果在运行）
print_status "停止当前应用..."
pm2 stop spacedata-backend 2>/dev/null || print_warning "PM2应用可能未运行"

# 也停止可能直接运行的Node进程
pkill -f "node.*spacedata" 2>/dev/null || print_warning "未找到直接运行的Node进程"

# 步骤6: 清理并重新构建
print_status "清理旧的构建文件..."
rm -rf dist

print_status "重新安装依赖（如果需要）..."
# 检查node_modules是否存在且完整
if [ ! -d "node_modules" ] || [ ! -f "node_modules/.bin/nest" ]; then
    print_status "重新安装项目依赖..."
    npm install --legacy-peer-deps
fi

print_status "重新构建项目..."
npm run build

# 步骤7: 运行数据库迁移
print_status "运行数据库迁移..."

# 检查数据库连接
print_status "检查数据库连接..."
if ! npm run migration:run; then
    print_error "数据库迁移失败！"
    print_status "请检查以下配置："
    echo "1. 数据库是否正在运行："
    echo "   sudo systemctl status postgresql"
    echo "2. 检查环境变量配置："
    echo "   cat .env | grep DB_"
    echo "3. 手动测试数据库连接："
    echo "   psql -h \$DB_HOST -U \$DB_USERNAME -d \$DB_DATABASE"
    exit 1
fi

print_status "数据库迁移成功完成！"

# 步骤8: 启动应用
print_status "启动应用..."

# 检查PM2是否安装
if command -v pm2 &> /dev/null; then
    print_status "使用PM2启动应用..."
    pm2 start dist/main.js --name spacedata-backend 2>/dev/null || pm2 restart spacedata-backend
    
    # 等待应用启动
    sleep 3
    
    print_status "检查应用状态..."
    pm2 status
    
    print_status "显示最近日志..."
    pm2 logs spacedata-backend --lines 20
else
    print_warning "PM2未安装，使用直接启动模式..."
    nohup npm run start:prod > logs/app.log 2>&1 &
    APP_PID=$!
    print_status "应用已启动，PID: $APP_PID"
    
    # 等待应用启动
    sleep 5
    
    print_status "检查应用是否正常运行..."
    if ps -p $APP_PID > /dev/null; then
        print_status "应用正常运行"
        tail -n 20 logs/app.log
    else
        print_error "应用启动失败"
        cat logs/app.log
        exit 1
    fi
fi

# 步骤9: 验证修复
print_status "验证修复效果..."

# 等待应用完全启动
sleep 5

# 检查应用是否在监听端口
if netstat -tlnp 2>/dev/null | grep :3001 > /dev/null; then
    print_status "应用已成功启动并监听3001端口"
else
    print_warning "应用可能未完全启动，请手动检查"
fi

# 显示验证命令
echo ""
print_status "修复完成！您可以使用以下命令验证："
echo ""
echo "1. 检查应用状态："
echo "   pm2 status"
echo ""
echo "2. 查看应用日志："
echo "   pm2 logs spacedata-backend"
echo "   # 或者"
echo "   tail -f logs/combined.log"
echo ""
echo "3. 测试增量聚合API（需要JWT token）："
echo "   curl -X POST http://localhost:3001/api/satellite/incremental-aggregate \\"
echo "        -H \"Content-Type: application/json\" \\"
echo "        -H \"Authorization: Bearer YOUR_JWT_TOKEN\" \\"
echo "        -d '{\"saveToDatabase\": true}'"
echo ""
echo "4. 检查数据库表是否创建成功："
echo "   psql -h \$DB_HOST -U \$DB_USERNAME -d \$DB_DATABASE -c \"\\dt aggregation_tasks\""
echo ""

print_status "如果仍有问题，请查看详细日志并提供错误信息" 