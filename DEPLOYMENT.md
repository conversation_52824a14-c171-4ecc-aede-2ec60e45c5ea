# 太空大数据平台一键部署方案

## 🚀 快速部署

### 系统要求
- Ubuntu 18.04+ 
- 4GB+ 内存
- 20GB+ 磁盘空间
- 具有sudo权限的非root用户

### 一键部署命令

```bash
# 1. 克隆项目
git clone <your-repo-url>
cd spacedata-backend

# 2. 给脚本添加执行权限
chmod +x deploy.sh scripts/deployment/*.sh

# 3. 执行一键部署
./deploy.sh

# 4. 指定域名部署（推荐生产环境）
./deploy.sh --domain your-domain.com
```

## 📋 部署选项

| 命令 | 说明 |
|------|------|
| `./deploy.sh` | 默认生产环境部署 |
| `./deploy.sh --dev` | 开发环境部署 |
| `./deploy.sh --domain example.com` | 指定域名部署 |
| `./deploy.sh --clean` | 清理之前的部署 |
| `./deploy.sh --help` | 查看所有选项 |

## 🏗️ 部署架构

### 核心服务
- **应用服务**: SpaceData Backend (端口: 3001)
- **Web服务**: Nginx (端口: 80/443)
- **数据库**: PostgreSQL, MongoDB, Redis, Elasticsearch
- **消息队列**: RabbitMQ
- **监控**: Prometheus + Grafana

### 部署方式
- **Docker Compose** (推荐): 容器化部署
- **PM2**: 进程管理部署
- **systemd**: 系统服务部署

## 🔧 配置文件

### 环境配置
- 生产环境: `config/env/production.env`
- 开发环境: `config/env/development.env`

### 重要配置项
```bash
# 数据库
DB_PASSWORD=your_secure_password
REDIS_PASSWORD=your_redis_password

# 安全
JWT_SECRET=your_jwt_secret

# 大模型API
QWEN_API_KEY=your_api_key
```

## 🌐 访问地址

部署完成后可访问：

- **应用主页**: http://your-domain.com
- **API文档**: http://your-domain.com/api-docs
- **健康检查**: http://your-domain.com/health
- **监控面板**: http://your-domain.com:3000
- **RabbitMQ管理**: http://your-domain.com:15672

## 🔍 健康检查

```bash
# 运行健康检查
./scripts/deployment/health-check.sh --domain your-domain.com

# 检查服务状态
docker-compose -f docker/compose/production.yml ps
```

## 📊 监控管理

### 查看日志
```bash
# 应用日志
docker-compose logs spacedata-app

# 系统日志
tail -f /var/log/spacedata/combined.log
```

### 服务管理
```bash
# 重启服务
docker-compose restart spacedata-app

# 查看资源使用
docker stats
```

## 🛠️ 运维操作

### 更新应用
```bash
git pull origin main
./deploy.sh --skip-deps
```

### 备份数据
```bash
# 数据库备份
docker-compose exec postgres pg_dump -U postgres spacedata > backup.sql

# 配置备份
tar -czf config_backup.tar.gz config/
```

### 清理部署
```bash
./deploy.sh --clean
```

## 🔒 安全配置

### 防火墙
```bash
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
```

### SSL证书
```bash
# 自动获取Let's Encrypt证书
sudo certbot --nginx -d your-domain.com
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   netstat -tulpn | grep :3001
   sudo kill -9 <PID>
   ```

2. **内存不足**
   ```bash
   free -h
   docker system prune -f
   ```

3. **服务启动失败**
   ```bash
   docker-compose logs spacedata-app
   sudo systemctl status spacedata
   ```

### 性能优化
- 调整Docker内存限制
- 优化数据库连接池
- 启用Redis缓存
- 配置Nginx缓存

## 📁 文件结构

```
├── deploy.sh                          # 主部署脚本
├── scripts/deployment/
│   ├── prepare-environment.sh         # 环境准备
│   ├── build-app.sh                  # 应用构建
│   ├── deploy-app.sh                 # 应用部署
│   ├── setup-nginx.sh                # Nginx配置
│   ├── setup-monitoring.sh           # 监控配置
│   ├── start-services.sh             # 服务启动
│   ├── health-check.sh               # 健康检查
│   └── cleanup.sh                    # 清理脚本
├── docker/compose/
│   └── production.yml                 # 生产环境Docker配置
├── config/env/
│   └── production.env                 # 生产环境变量
└── docs/
    └── deployment-guide.md            # 详细部署文档
```

## 📞 技术支持

- **详细文档**: [docs/deployment-guide.md](docs/deployment-guide.md)
- **问题反馈**: 创建GitHub Issue
- **技术交流**: 联系开发团队

---

**注意**: 首次部署前请仔细阅读 [详细部署文档](docs/deployment-guide.md) 并根据实际环境修改配置文件。
