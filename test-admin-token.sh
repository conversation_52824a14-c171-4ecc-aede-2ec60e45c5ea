#!/bin/bash

# 测试admin token认证
echo "=== 测试Admin Token认证 ==="

# 从token.json文件读取token（如果存在）
if [ -f "token.json" ]; then
    TOKEN=$(cat token.json | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
    echo "从token.json文件读取到token: ${TOKEN:0:50}..."
else
    echo "未找到token.json文件，请先登录获取token"
    exit 1
fi

echo ""
echo "1. 测试获取当前用户信息 (GET /auth/profile)"
curl -X 'GET' \
  'http://localhost:3001/auth/profile' \
  -H 'accept: application/json' \
  -H "Authorization: Bearer $TOKEN" \
  -w "\nHTTP状态码: %{http_code}\n\n"

echo ""
echo "2. 测试获取用户列表 (GET /auth/users)"
curl -X 'GET' \
  'http://localhost:3001/auth/users?page=1&limit=10' \
  -H 'accept: application/json' \
  -H "Authorization: Bearer $TOKEN" \
  -w "\nHTTP状态码: %{http_code}\n\n"

echo ""
echo "3. 测试获取用户权限信息 (GET /auth/users/1/permissions)"
curl -X 'GET' \
  'http://localhost:3001/auth/users/1/permissions' \
  -H 'accept: application/json' \
  -H "Authorization: Bearer $TOKEN" \
  -w "\nHTTP状态码: %{http_code}\n\n"

echo ""
echo "=== 测试完成 ===" 