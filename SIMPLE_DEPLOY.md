# 太空大数据平台 - 超简单部署指南

## 🚀 一键部署（推荐）

在远程服务器上执行以下命令：

```bash
# 1. 克隆项目
git clone <your-repo-url> spacedata-backend
cd spacedata-backend

# 2. 运行一键部署脚本
./quick-deploy.sh
```

就这么简单！脚本会自动：
- 安装Docker和Docker Compose
- 构建并启动所有服务
- 等待服务就绪
- 显示访问地址

## 📋 手动部署（3个命令）

如果你更喜欢手动控制：

```bash
# 1. 安装Docker（如果没有）
curl -fsSL https://get.docker.com -o get-docker.sh && sudo sh get-docker.sh

# 2. 启动所有服务
docker compose -f docker/compose/production.yml up -d --build

# 3. 查看状态
docker compose -f docker/compose/production.yml ps
```

## 🌐 访问地址

部署完成后，你可以访问：

- **主应用**: http://your-server-ip:3001
- **API文档**: http://your-server-ip:3001/api-docs  
- **监控面板**: http://your-server-ip:3000 (admin/admin123)
- **消息队列管理**: http://your-server-ip:15672 (admin/admin123)

## 🔧 常用命令

```bash
# 查看所有服务状态
docker-compose -f docker/compose/production.yml ps

# 查看应用日志
docker-compose -f docker/compose/production.yml logs -f spacedata-app

# 重启应用
docker-compose -f docker/compose/production.yml restart spacedata-app

# 停止所有服务
docker-compose -f docker/compose/production.yml down

# 完全重新部署
docker-compose -f docker/compose/production.yml down
docker-compose -f docker/compose/production.yml up -d --build
```

## 🛠️ 故障排除

### 如果端口被占用
```bash
# 查看端口占用
sudo netstat -tlnp | grep :3001

# 停止占用端口的进程
sudo kill -9 <PID>
```

### 如果Docker权限问题
```bash
# 将用户添加到docker组
sudo usermod -aG docker $USER

# 重新登录或执行
newgrp docker
```

### 如果服务启动失败
```bash
# 查看详细日志
docker-compose -f docker/compose/production.yml logs

# 重新构建镜像
docker-compose -f docker/compose/production.yml build --no-cache
```

## 📝 环境配置

如需修改数据库密码等配置，编辑：
```bash
nano config/env/production.env
```

然后重启服务：
```bash
docker-compose -f docker/compose/production.yml restart
```

## 🎯 最小系统要求

- **操作系统**: Ubuntu 18.04+ / CentOS 7+ / Debian 9+
- **内存**: 4GB+
- **磁盘**: 20GB+
- **网络**: 能访问Docker Hub

## ❓ 需要帮助？

如果遇到问题，请提供以下信息：
1. 操作系统版本：`cat /etc/os-release`
2. Docker版本：`docker --version`
3. 错误日志：`docker-compose -f docker/compose/production.yml logs` 