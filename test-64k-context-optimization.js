/**
 * 64K上下文优化验证测试脚本
 * 用于测试优化后的翻译配置能否处理超长文本
 */

const axios = require('axios');
const fs = require('fs');

// 测试配置
const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000',
  // 使用JWT token进行认证，请根据实际情况替换
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwiaWF0IjoxNzM2MTQ2ODM3LCJleHAiOjE3MzYxNTA0Mzd9.nX8dNrNP5kU_P9XVxL0D7s7MNx3VBE1n0YIiE3f3cDE'
};

// 生成不同长度的测试文本
function generateTestText(targetLength, topic = 'space technology') {
  const baseTexts = {
    'space technology': `
Space technology represents one of humanity's greatest achievements in pushing the boundaries of scientific knowledge and engineering capability. The development of rocket propulsion systems has evolved from early experiments with gunpowder-based rockets in ancient China to modern sophisticated multi-stage launch vehicles capable of delivering payloads to various orbital trajectories. The fundamental principles of rocket science rely on <PERSON>'s third law of motion, where the expulsion of high-velocity exhaust gases creates an equal and opposite reaction force that propels the spacecraft forward through the vacuum of space.

The complexity of modern spacecraft systems encompasses multiple interconnected subsystems including propulsion, guidance and navigation, thermal control, power generation and distribution, communications, and life support systems for crewed missions. Each of these subsystems must operate reliably in the harsh environment of space, where temperatures can vary from hundreds of degrees above zero in direct sunlight to hundreds of degrees below zero in shadow, radiation levels are significantly higher than on Earth's surface, and the vacuum of space presents unique challenges for heat dissipation and material behavior.

Satellite technology has revolutionized global communications, weather forecasting, navigation systems, and Earth observation capabilities. Modern satellites employ sophisticated onboard computers, high-resolution imaging sensors, and advanced communication systems that enable real-time data transmission across vast distances. The Global Positioning System (GPS) constellation consists of multiple satellites in precise orbital configurations that allow for accurate positioning determination anywhere on Earth's surface with remarkable precision.

Deep space exploration missions require even more advanced technologies, including autonomous navigation systems capable of operating independently for extended periods due to the significant communication delays between Earth and distant spacecraft. These missions often employ innovative propulsion technologies such as ion drives, which provide highly efficient thrust over extended periods, though with much lower thrust levels compared to chemical rockets.
`,
    'military defense': `
Modern military defense systems incorporate advanced technologies developed through decades of research and development in aerospace, electronics, and materials science. Air defense systems are designed to detect, track, and intercept incoming threats ranging from aircraft and helicopters to ballistic missiles and cruise missiles. These systems typically consist of multiple layers of defense, including long-range surveillance radars, medium-range tracking radars, short-range terminal defense systems, and various types of interceptor missiles optimized for different threat categories.

The integration of artificial intelligence and machine learning algorithms has significantly enhanced the capabilities of modern defense systems, enabling faster threat identification, improved tracking accuracy, and more effective countermeasure deployment. Advanced signal processing techniques allow radar systems to distinguish between actual threats and various forms of electronic countermeasures or natural phenomena that might otherwise cause false alarms.

Missile defense technology represents one of the most challenging aspects of modern military engineering, requiring precise coordination between detection systems, tracking radars, and interceptor missiles to successfully engage targets traveling at extremely high velocities. The physics of ballistic missile interception involves complex calculations of trajectory prediction, considering factors such as atmospheric effects, gravitational influences, and the dynamic behavior of both the threat and the interceptor.

Electronic warfare capabilities have become increasingly important in modern military operations, encompassing both offensive and defensive measures designed to control the electromagnetic spectrum. These systems include radar jamming equipment, communication disruption technologies, and sophisticated countermeasures designed to protect friendly forces from enemy electronic attacks.
`
  };

  let text = baseTexts[topic] || baseTexts['space technology'];
  
  // 重复文本直到达到目标长度
  while (text.length < targetLength) {
    text += '\n\n' + text;
  }
  
  // 截取到目标长度
  return text.substring(0, targetLength);
}

// 测试翻译API
async function testTranslation(testName, textLength, useHighQuality = false) {
  console.log(`\n🧪 测试 ${testName} (长度: ${textLength} 字符)`);
  console.log('='.repeat(50));
  
  try {
    const testText = generateTestText(textLength);
    const testTitle = `Test Article - ${testName}`;
    const testSummary = `This is a test summary for ${testName} with ${textLength} characters.`;
    
    const requestData = {
      indices: ['test'],
      translationType: useHighQuality ? 'high_quality' : 'default',
      news: [
        {
          id: `test-${Date.now()}`,
          title: testTitle,
          summary: testSummary,
          content: testText,
          info_source: 'test-source'
        }
      ]
    };
    
    console.log(`📊 请求数据统计:`);
    console.log(`  - 标题长度: ${testTitle.length} 字符`);
    console.log(`  - 摘要长度: ${testSummary.length} 字符`);
    console.log(`  - 内容长度: ${testText.length} 字符`);
    console.log(`  - 总长度: ${testTitle.length + testSummary.length + testText.length} 字符`);
    console.log(`  - 翻译模式: ${useHighQuality ? '高质量模式' : '默认模式'}`);
    
    const startTime = Date.now();
    
    const response = await axios.post(
      `${TEST_CONFIG.baseUrl}/api/es/news/translate`,
      requestData,
      {
        headers: {
          'Authorization': `Bearer ${TEST_CONFIG.token}`,
          'Content-Type': 'application/json'
        },
        timeout: 300000 // 5分钟超时
      }
    );
    
    const endTime = Date.now();
    const processingTime = endTime - startTime;
    
    console.log(`✅ 翻译成功!`);
    console.log(`⏱️  处理时间: ${processingTime}ms (${(processingTime / 1000).toFixed(2)}秒)`);
    
    if (response.data && response.data.results) {
      const result = response.data.results[0];
      if (result) {
        console.log(`📝 翻译结果统计:`);
        console.log(`  - 中文标题长度: ${result.title_cn ? result.title_cn.length : 0} 字符`);
        console.log(`  - 中文摘要长度: ${result.summary_cn ? result.summary_cn.length : 0} 字符`);
        console.log(`  - 中文内容长度: ${result.content_cn ? result.content_cn.length : 0} 字符`);
        console.log(`  - 主题词: ${result.themes_cn || '无'}`);
        
        // 计算翻译完整性
        const originalLength = testTitle.length + testSummary.length + testText.length;
        const translatedLength = (result.title_cn?.length || 0) + (result.summary_cn?.length || 0) + (result.content_cn?.length || 0);
        const completionRate = ((translatedLength / originalLength) * 100).toFixed(1);
        
        console.log(`📊 翻译完整性: ${completionRate}% (译文/原文长度比)`);
        
        // 检查翻译质量
        if (result.content_cn && result.content_cn.includes('...') || result.content_cn.includes('[省略]')) {
          console.log(`⚠️  检测到内容截断标记`);
        }
        
        if (parseFloat(completionRate) < 30) {
          console.log(`⚠️  翻译完整性偏低，可能存在截断问题`);
        } else if (parseFloat(completionRate) > 80) {
          console.log(`✨ 翻译完整性良好`);
        }
      }
    }
    
    return {
      success: true,
      processingTime,
      result: response.data
    };
    
  } catch (error) {
    console.log(`❌ 翻译失败: ${error.message}`);
    
    if (error.response) {
      console.log(`📡 HTTP状态: ${error.response.status}`);
      console.log(`📄 错误详情: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    
    return {
      success: false,
      error: error.message
    };
  }
}

// 测试当前配置信息
async function testCurrentConfig() {
  console.log(`\n📋 获取当前大模型配置`);
  console.log('='.repeat(50));
  
  try {
    const response = await axios.get(
      `${TEST_CONFIG.baseUrl}/api/llm-config`,
      {
        headers: {
          'Authorization': `Bearer ${TEST_CONFIG.token}`
        }
      }
    );
    
    console.log(`✅ 配置获取成功`);
    
    if (response.data.translation) {
      const config = response.data.translation;
      console.log(`🔧 翻译配置:`);
      console.log(`  - 模型: ${config.model}`);
      console.log(`  - 最大文本长度: ${config.maxTextLength} 字符`);
      console.log(`  - 最大令牌数: ${config.maxTokens}`);
      console.log(`  - 超时时间: ${config.timeout}ms`);
      console.log(`  - 并发请求数: ${config.maxConcurrentRequests}`);
    }
    
    if (response.data.themeExtraction) {
      const config = response.data.themeExtraction;
      console.log(`🏷️  主题提取配置:`);
      console.log(`  - 模型: ${config.model}`);
      console.log(`  - 最大内容长度: ${config.maxContentLength} 字符`);
      console.log(`  - 最大令牌数: ${config.maxTokens}`);
    }
    
  } catch (error) {
    console.log(`❌ 配置获取失败: ${error.message}`);
  }
}

// 主测试函数
async function runTests() {
  console.log(`🚀 64K上下文优化验证测试`);
  console.log(`⏰ 测试开始时间: ${new Date().toLocaleString()}`);
  
  // 获取当前配置
  await testCurrentConfig();
  
  // 测试不同长度的文本
  const tests = [
    { name: '短文本测试', length: 1000, highQuality: false },
    { name: '中等文本测试', length: 5000, highQuality: false },
    { name: '长文本测试', length: 15000, highQuality: false },
    { name: '超长文本测试(默认模式)', length: 35000, highQuality: false },
    { name: '超长文本测试(高质量模式)', length: 40000, highQuality: true }
  ];
  
  const results = [];
  
  for (const test of tests) {
    const result = await testTranslation(test.name, test.length, test.highQuality);
    results.push({
      ...test,
      ...result
    });
    
    // 测试间隔，避免API限制
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // 汇总测试结果
  console.log(`\n📊 测试结果汇总`);
  console.log('='.repeat(50));
  
  let successCount = 0;
  let totalProcessingTime = 0;
  
  results.forEach((result, index) => {
    console.log(`${index + 1}. ${result.name}:`);
    console.log(`   - 文本长度: ${result.length} 字符`);
    console.log(`   - 结果: ${result.success ? '✅ 成功' : '❌ 失败'}`);
    
    if (result.success) {
      successCount++;
      totalProcessingTime += result.processingTime;
      console.log(`   - 处理时间: ${result.processingTime}ms`);
    } else {
      console.log(`   - 错误: ${result.error}`);
    }
    console.log('');
  });
  
  console.log(`📈 总体统计:`);
  console.log(`  - 成功率: ${((successCount / results.length) * 100).toFixed(1)}% (${successCount}/${results.length})`);
  console.log(`  - 平均处理时间: ${successCount > 0 ? Math.round(totalProcessingTime / successCount) : 0}ms`);
  console.log(`  - 最大处理文本: ${Math.max(...results.filter(r => r.success).map(r => r.length))} 字符`);
  
  console.log(`\n🎯 64K上下文优化验证结论:`);
  
  const successfulLongTexts = results.filter(r => r.success && r.length >= 15000);
  if (successfulLongTexts.length > 0) {
    console.log(`✅ 成功处理超长文本 (≥15K字符): ${successfulLongTexts.length} 个`);
    console.log(`📏 最大成功处理长度: ${Math.max(...successfulLongTexts.map(r => r.length))} 字符`);
    console.log(`🚀 64K上下文优化有效，显著提升了长文本处理能力！`);
  } else {
    console.log(`⚠️  未能成功处理超长文本，可能需要进一步优化配置`);
  }
  
  console.log(`\n✅ 测试完成时间: ${new Date().toLocaleString()}`);
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  runTests,
  testTranslation,
  testCurrentConfig
}; 