#!/bin/bash

echo "=== 设置Admin用户并测试权限 ==="

# 首先获取admin用户的token
echo "1. 获取admin用户token..."
response=$(curl -s -X 'POST' \
  'http://localhost:3001/auth/login' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "admin",
    "password": "admin123"
  }')

if ! echo "$response" | grep -q "access_token"; then
    echo "登录失败，退出"
    exit 1
fi

token=$(echo "$response" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
admin_id=$(echo "$response" | grep -o '"id":[^,]*' | cut -d':' -f2)

echo "Admin用户ID: $admin_id"
echo "Token获取成功"

# 创建一个临时的admin用户来设置角色（通过直接创建一个已知的admin用户）
echo ""
echo "2. 创建临时超级管理员用户..."
temp_admin_response=$(curl -s -X 'POST' \
  'http://localhost:3001/auth/register' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "superadmin",
    "password": "superadmin123",
    "email": "<EMAIL>"
  }')

echo "临时管理员创建响应: $temp_admin_response"

# 需要直接修改数据库来设置admin角色
echo ""
echo "3. 请手动执行以下SQL命令来设置admin角色："
echo "UPDATE users SET role = 'admin' WHERE username = 'admin';"
echo ""
echo "或者如果您有PostgreSQL访问权限，可以运行："
echo "psql -d your_database_name -c \"UPDATE users SET role = 'admin' WHERE username = 'admin';\""
echo ""

# 提供当前用户信息
echo "4. 当前admin用户信息:"
curl -s -X 'GET' \
  'http://localhost:3001/auth/profile' \
  -H 'accept: application/json' \
  -H "Authorization: Bearer $token" | jq '.'

echo ""
echo "5. 如果您已经更新了数据库，请重新运行以下命令测试："
echo "./test-admin-token.sh"

# 保存当前token
echo "$response" > token.json
echo ""
echo "Token已保存到token.json文件" 