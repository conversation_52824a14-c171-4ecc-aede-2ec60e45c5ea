/**
 * OneWeb-0639卫星数据聚合测试脚本
 */
const axios = require('axios');

// 设置API基础URL
const API_BASE_URL = 'http://localhost:3001';

// JWT令牌 - 请替换为有效的令牌
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwic3ViIjozLCJpYXQiOjE3NDE4MjQ0NTIsImV4cCI6MTc0MTkxMDg1Mn0.q5QQcM8G-XKPAsYrKodnynz2tNSG5yHmTBzl1Q4bmdQ';

// 设置请求头
const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${JWT_TOKEN}`
};

/**
 * 清空卫星数据表
 */
async function clearSatelliteData() {
  try {
    console.log('清空卫星数据表...');
    const response = await axios.post(`${API_BASE_URL}/local/satellite/clear`, {}, { headers });
    console.log('清空结果:', response.data);
    return response.data;
  } catch (error) {
    console.error('清空卫星数据表失败:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * 测试OneWeb-0639卫星的聚合
 */
async function testOneWebAggregation() {
  try {
    console.log('开始测试OneWeb-0639卫星的聚合...');
    
    // 创建测试数据
    const testData = [
      // 来自satnogs的数据
      {
        _index: 'satsinfo_satnogs',
        _source: {
          info_source: "https://docs.google.com/document/d/1xrBAOiIx7fwpI4SMSiv13caQDvk8ukHoxWXCvGXzBEM/edit?usp=sharing",
          update_time: "2023-12-10T11:23:27.197823Z",
          satellite_name: "Oneweb-0639",
          alternative_name: "2023-029H",
          country_of_registry: "GB",
          owner: "None",
          status: "alive",
          norad_id: 55803,
          source: "",
          orbit_info: {
            decay_date: null,
            deployed_date: "2023-03-09T00:00:00Z"
          },
          launch_info: {
            launch_date: "2023-03-09T00:00:00Z"
          }
        }
      },
      
      // 来自n2yo的数据
      {
        _index: 'satsinfo_n2yo',
        _source: {
          info_source: "https://www.n2yo.com/satellite/?s=55803",
          alternative_name: "ONEWEB-0639",
          norad_id: 55803,
          launch_info: {
            launch_date: "2023-03-09",
            launch_site: "AIR FORCE EASTERN TEST RANGE (AFETR)"
          },
          status: "IN ORBIT",
          country_of_registry: "United Kingdom (UK)",
          purpose: [
            "OneWeb"
          ],
          cospar_id: "2023-029H",
          orbit_info: {
            perigee_km: 1192.2,
            apogee_km: 1193.6,
            incl_degrees: 87.9,
            period_minutes: 109.1
          }
        }
      },
      
      // 来自ucs的数据
      {
        _index: 'satsinfo_ucs',
        _source: {
          satellite_name: "OneWeb-0639                ",
          alternative_name: "OneWeb-0639                ",
          country_of_registry: "United Kingdom",
          country_of_owner: "United Kingdom",
          owner: "OneWeb Satellites",
          users: "Commercial",
          purpose: "Communications",
          cospar_id: "2023-029H",
          norad_id: 55803,
          orbit_info: {
            orbit_class: "LEO",
            orbit_type: "Polar",
            longitude_of_geo_degrees: 0,
            perigee_km: 977,
            apogee_km: 982,
            eccentricity: 0.00034015919450302743,
            incl_degrees: 87.5,
            period_minutes: 104.5
          },
          launch_info: {
            launch_mass_kg: 148,
            dry_mass_kg: 140,
            launch_date: "2023-03-09",
            expected_lifetime_yrs: 5,
            contractor: "OneWeb Satellites",
            country_of_contractor: "USA",
            launch_site: "Cape Canaveral",
            launch_vehicle: "Falcon 9"
          }
        }
      }
    ];
    
    // 将测试数据保存到临时文件
    const fs = require('fs');
    fs.writeFileSync('oneweb-test-data.json', JSON.stringify(testData, null, 2));
    
    // 创建一个自定义的测试端点请求
    const response = await axios.post(
      `${API_BASE_URL}/local/satellite/test-custom-aggregation`,
      { testDataFile: 'oneweb-test-data.json' },
      { headers }
    );
    
    console.log('测试结果:', response.data);
    return response.data;
  } catch (error) {
    console.error('OneWeb-0639聚合测试失败:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * 查询聚合结果
 */
async function querySatellites() {
  try {
    console.log('查询聚合结果...');
    const response = await axios.get(`${API_BASE_URL}/local/satellite`, { headers });
    console.log(`查询结果: 共${response.data.total}条记录`);
    
    // 打印每条记录的详细信息
    for (const satellite of response.data.hits) {
      console.log('\n卫星记录:');
      console.log(`ID: ${satellite.id}`);
      console.log(`数据源: ${JSON.stringify(satellite._sources)}`);
      
      if (satellite.norad_id && satellite.norad_id.length > 0) {
        console.log(`NORAD ID: ${JSON.stringify(satellite.norad_id)}`);
      }
      
      if (satellite.cospar_id && satellite.cospar_id.length > 0) {
        console.log(`COSPAR ID: ${JSON.stringify(satellite.cospar_id)}`);
      }
      
      if (satellite.satellite_name && satellite.satellite_name.length > 0) {
        console.log(`卫星名称: ${JSON.stringify(satellite.satellite_name)}`);
      }
    }
    
    return response.data;
  } catch (error) {
    console.error('查询聚合结果失败:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    // 清空数据表
    await clearSatelliteData();
    
    // 测试OneWeb-0639卫星的聚合
    await testOneWebAggregation();
    
    // 查询聚合结果
    await querySatellites();
    
    console.log('\n测试完成!');
  } catch (error) {
    console.error('测试过程中出错:', error);
  }
}

// 执行主函数
main(); 