/**
 * 卫星数据聚合测试脚本
 */
const axios = require('axios');

// 设置API基础URL
const API_BASE_URL = 'http://localhost:3001';

// JWT令牌 - 请替换为有效的令牌
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwic3ViIjozLCJpYXQiOjE3NDE4MjQ0NTIsImV4cCI6MTc0MTkxMDg1Mn0.q5QQcM8G-XKPAsYrKodnynz2tNSG5yHmTBzl1Q4bmdQ';

// 设置请求头
const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${JWT_TOKEN}`
};

/**
 * 清空卫星数据表
 */
async function clearSatelliteData() {
  try {
    console.log('清空卫星数据表...');
    const response = await axios.post(`${API_BASE_URL}/local/satellite/clear`, {}, { headers });
    console.log('清空结果:', response.data);
    return response.data;
  } catch (error) {
    console.error('清空卫星数据表失败:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * 运行聚合测试
 */
async function runAggregationTest() {
  try {
    console.log('运行聚合测试...');
    const response = await axios.post(`${API_BASE_URL}/local/satellite/test-aggregation`, {}, { headers });
    console.log('测试结果:', response.data);
    return response.data;
  } catch (error) {
    console.error('聚合测试失败:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * 查询聚合结果
 */
async function querySatellites() {
  try {
    console.log('查询聚合结果...');
    const response = await axios.get(`${API_BASE_URL}/local/satellite`, { headers });
    console.log(`查询结果: 共${response.data.total}条记录`);
    
    // 打印每条记录的详细信息
    for (const satellite of response.data.hits) {
      console.log('\n卫星记录:');
      console.log(`ID: ${satellite.id}`);
      console.log(`数据源: ${JSON.stringify(satellite._sources)}`);
      
      if (satellite.norad_id && satellite.norad_id.length > 0) {
        console.log(`NORAD ID: ${JSON.stringify(satellite.norad_id)}`);
      }
      
      if (satellite.cospar_id && satellite.cospar_id.length > 0) {
        console.log(`COSPAR ID: ${JSON.stringify(satellite.cospar_id)}`);
      }
      
      if (satellite.satellite_name && satellite.satellite_name.length > 0) {
        console.log(`卫星名称: ${JSON.stringify(satellite.satellite_name)}`);
      }
    }
    
    return response.data;
  } catch (error) {
    console.error('查询聚合结果失败:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    // 清空数据表
    await clearSatelliteData();
    
    // 运行聚合测试
    await runAggregationTest();
    
    // 查询聚合结果
    await querySatellites();
    
    console.log('\n测试完成!');
  } catch (error) {
    console.error('测试过程中出错:', error);
  }
}

// 执行主函数
main(); 