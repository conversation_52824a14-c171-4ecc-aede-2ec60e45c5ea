#!/bin/bash

# 测试发射承包商API修改后的结果
API_URL="http://localhost:3001/api/v1/database/launch-contractors"

# 设置JWT令牌
AUTH_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwiaWQiOjEsInJvbGUiOiJhZG1pbiIsImlhdCI6MTcxMTAyMjY2MCwiZXhwIjoxODY5MjE4NjYwfQ.n8Qj1OvulI4f0N3u-Gk3ccDhHYFlC7LmCADUcDjXqVI"

echo "正在测试修改后的发射承包商API，检查中国承包商的中文翻译..."

# 等待服务启动
sleep 5

# 发送GET请求，显示详细信息
echo "API调用详情："
curl -v -X GET \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  $API_URL > api-contractors-updated.json

# 检查API响应
echo -e "\n\nAPI响应内容："
cat api-contractors-updated.json

# 如果API调用成功，检查中国承包商的中文翻译
if grep -q "success" api-contractors-updated.json; then
  echo -e "\n\n检查中国承包商的中文翻译："
  grep -A 1 "China" api-contractors-updated.json | grep -v "\"en\":" | grep "\"cn\":" | grep -v "中国" | head -n 5

  # 显示几个中国航天机构的条目作为示例
  echo -e "\n中国航天相关机构示例："
  grep -A 2 -B 2 "China Academy of Science\|China Electronic\|Chinese Research Institute" api-contractors-updated.json
fi 