const { Client } = require('@elastic/elasticsearch');

const ES_URL = 'http://123.57.173.156:9200';
const ES_USERNAME = 'web_readonly';
const ES_PASSWORD = 'web@readonly4all';
const INDEX_NAME = 'orbital_tle';

async function checkTimeFilterIssue() {
  const es = new Client({
    node: ES_URL,
    auth: { username: ES_USERNAME, password: ES_PASSWORD },
  });

  // 查询47769这个NORAD_ID在ES中的记录（不使用时间过滤）
  console.log('🔍 检查NORAD_ID 47769在ES中的记录...');
  
  const response = await es.search({
    index: INDEX_NAME,
    size: 10,
    _source: ['satellite_name', 'norad_id', 'cospar_id', 'time'],
    body: {
      query: {
        bool: {
          must: [
            { term: { norad_id: 47769 } },
            {
              wildcard: {
                satellite_name: {
                  value: 'starlink*',
                  case_insensitive: true,
                },
              },
            },
          ],
        },
      },
    },
  });

  const hits = response.hits.hits;
  console.log(`找到 ${hits.length} 条记录:`);
  
  hits.forEach((hit, index) => {
    const { satellite_name, norad_id, cospar_id, time } = hit._source;
    console.log(`${index + 1}. ${satellite_name}, ${norad_id}, ${cospar_id}, 时间: ${time}`);
  });

  // 检查时间过滤的影响
  const timeThreshold = '2025-06-21T04:00:52.163901+00:00';
  console.log(`\n⏰ 时间过滤条件: ${timeThreshold}`);
  
  const filteredResponse = await es.search({
    index: INDEX_NAME,
    size: 10,
    _source: ['satellite_name', 'norad_id', 'cospar_id', 'time'],
    body: {
      query: {
        bool: {
          must: [
            { term: { norad_id: 47769 } },
            { range: { time: { gt: timeThreshold } } },
            {
              wildcard: {
                satellite_name: {
                  value: 'starlink*',
                  case_insensitive: true,
                },
              },
            },
          ],
        },
      },
    },
  });

  const filteredHits = filteredResponse.hits.hits;
  console.log(`\n时间过滤后找到 ${filteredHits.length} 条记录:`);
  
  if (filteredHits.length === 0) {
    console.log('❌ 确认：时间过滤导致47769被排除！');
  } else {
    filteredHits.forEach((hit, index) => {
      const { satellite_name, norad_id, cospar_id, time } = hit._source;
      console.log(`${index + 1}. ${satellite_name}, ${norad_id}, ${cospar_id}, 时间: ${time}`);
    });
  }

  // 统计所有Starlink卫星数量对比
  console.log('\n📊 统计对比:');
  
  // 不使用时间过滤的总数
  const allStarlinkResponse = await es.count({
    index: INDEX_NAME,
    body: {
      query: {
        wildcard: {
          satellite_name: {
            value: 'starlink*',
            case_insensitive: true,
          },
        },
      },
    },
  });
  
  // 使用时间过滤的总数
  const filteredStarlinkResponse = await es.count({
    index: INDEX_NAME,
    body: {
      query: {
        bool: {
          must: [
            { range: { time: { gt: timeThreshold } } },
            {
              wildcard: {
                satellite_name: {
                  value: 'starlink*',
                  case_insensitive: true,
                },
              },
            },
          ],
        },
      },
    },
  });

  console.log(`所有Starlink TLE记录: ${allStarlinkResponse.count}`);
  console.log(`时间过滤后的记录: ${filteredStarlinkResponse.count}`);
  console.log(`被时间过滤排除的记录: ${allStarlinkResponse.count - filteredStarlinkResponse.count}`);
}

checkTimeFilterIssue().catch(console.error); 