#!/bin/bash

# 快速检查 AggregationTask 修复是否成功
# Usage: ./check-aggregation-fix.sh

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印函数
print_status() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

echo "======================================"
echo "检查 AggregationTask 修复状态"
echo "======================================"

# 检查1: 实体文件是否存在
echo "1. 检查实体文件..."
if [ -f "src/entities/aggregation-task.entity.ts" ]; then
    print_status "AggregationTask 实体文件存在"
else
    print_error "AggregationTask 实体文件不存在"
fi

# 检查2: 迁移文件是否存在
echo "2. 检查迁移文件..."
if [ -f "src/migrations/1748600000000-CreateAggregationTasksTable.ts" ]; then
    print_status "AggregationTask 迁移文件存在"
else
    print_error "AggregationTask 迁移文件不存在"
fi

# 检查3: data-source.ts 是否包含 AggregationTask
echo "3. 检查数据源配置..."
if grep -q "AggregationTask" src/data-source.ts; then
    print_status "data-source.ts 已包含 AggregationTask"
else
    print_error "data-source.ts 未包含 AggregationTask"
fi

# 检查4: 应用是否正在运行
echo "4. 检查应用运行状态..."
if command -v pm2 &> /dev/null; then
    if pm2 list | grep -q "spacedata-backend.*online"; then
        print_status "PM2应用正在运行"
    else
        print_warning "PM2应用未运行"
        pm2 status
    fi
else
    if pgrep -f "node.*spacedata" > /dev/null; then
        print_status "应用进程正在运行"
    else
        print_warning "未找到应用进程"
    fi
fi

# 检查5: 端口是否在监听
echo "5. 检查端口监听状态..."
if netstat -tlnp 2>/dev/null | grep :3001 > /dev/null; then
    print_status "应用正在监听3001端口"
else
    print_warning "3001端口未在监听"
fi

# 检查6: 数据库表是否存在（需要环境变量）
echo "6. 检查数据库表..."
if [ -f ".env" ]; then
    source .env
    if command -v psql &> /dev/null && [ ! -z "$DB_HOST" ] && [ ! -z "$DB_USERNAME" ] && [ ! -z "$DB_DATABASE" ]; then
        if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U $DB_USERNAME -d $DB_DATABASE -c "\dt aggregation_tasks" 2>/dev/null | grep -q "aggregation_tasks"; then
            print_status "数据库表 aggregation_tasks 存在"
        else
            print_error "数据库表 aggregation_tasks 不存在"
            echo "   请运行: npm run migration:run"
        fi
    else
        print_warning "无法连接数据库或环境变量未配置"
    fi
else
    print_warning "未找到 .env 文件"
fi

# 检查7: 最近的日志是否有错误
echo "7. 检查最近日志..."
if [ -f "logs/error.log" ]; then
    RECENT_ERRORS=$(tail -n 20 logs/error.log | grep -i "aggregationtask\|metadata.*not.*found" | wc -l)
    if [ $RECENT_ERRORS -eq 0 ]; then
        print_status "最近日志中无 AggregationTask 相关错误"
    else
        print_error "最近日志中仍有 AggregationTask 相关错误"
        echo "   最近的错误："
        tail -n 20 logs/error.log | grep -i "aggregationtask\|metadata.*not.*found" | tail -5
    fi
else
    print_warning "未找到 error.log 文件"
fi

echo ""
echo "======================================"
echo "检查完成"
echo "======================================"

# 提供建议
echo ""
echo "建议的后续操作："
echo ""
echo "如果所有检查都通过："
echo "  • 修复已成功完成"
echo "  • 您可以测试增量聚合API"
echo ""
echo "如果仍有问题："
echo "  • 运行修复脚本: ./fix-aggregation-task-error.sh"
echo "  • 查看详细日志: tail -f logs/combined.log"
echo "  • 检查PM2状态: pm2 logs spacedata-backend"
echo ""
echo "测试API命令（需要JWT token）："
echo "curl -X POST http://localhost:3001/api/satellite/incremental-aggregate \\"
echo "     -H \"Content-Type: application/json\" \\"
echo "     -H \"Authorization: Bearer YOUR_JWT_TOKEN\" \\"
echo "     -d '{\"saveToDatabase\": true}'" 