<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卫星3D点云数据示例</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .info-panel {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: white;
            border-radius: 6px;
            padding: 15px;
            border-left: 4px solid #007bff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            margin-bottom: 5px;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .orbit-colors {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        .orbit-color {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 5px 10px;
            background: white;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .color-box {
            width: 16px;
            height: 16px;
            border-radius: 2px;
        }
        .satellite-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .satellite-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .satellite-item:last-child {
            border-bottom: none;
        }
        .satellite-name {
            font-weight: 500;
            color: #333;
        }
        .satellite-id {
            color: #666;
            font-size: 12px;
        }
        .satellite-orbit {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            color: white;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .refresh-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .refresh-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛰️ 卫星3D点云数据可视化示例</h1>
        
        <div class="info-panel">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3 style="margin: 0;">数据概览</h3>
                    <p style="margin: 5px 0 0 0; color: #666;">实时卫星轨道位置数据</p>
                </div>
                <button class="refresh-btn" onclick="loadSatelliteData()">🔄 刷新数据</button>
            </div>
        </div>

        <div id="loading" class="loading">
            <p>📡 正在加载卫星数据...</p>
        </div>

        <div id="error" class="error" style="display: none;">
            <p id="error-message"></p>
        </div>

        <div id="content" style="display: none;">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-label">卫星总数</div>
                    <div class="stat-value" id="total-satellites">-</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">生成时间</div>
                    <div class="stat-value" id="generated-time" style="font-size: 14px;">-</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">最低高度</div>
                    <div class="stat-value" id="min-altitude">-</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">最高高度</div>
                    <div class="stat-value" id="max-altitude">-</div>
                </div>
            </div>

            <div class="info-panel">
                <h4>轨道类型分布</h4>
                <div id="orbit-distribution"></div>
                <div class="orbit-colors">
                    <div class="orbit-color">
                        <div class="color-box" style="background: rgb(0, 255, 0);"></div>
                        <span>LEO (低地球轨道)</span>
                    </div>
                    <div class="orbit-color">
                        <div class="color-box" style="background: rgb(255, 165, 0);"></div>
                        <span>MEO (中地球轨道)</span>
                    </div>
                    <div class="orbit-color">
                        <div class="color-box" style="background: rgb(255, 0, 0);"></div>
                        <span>GEO (地球同步轨道)</span>
                    </div>
                    <div class="orbit-color">
                        <div class="color-box" style="background: rgb(255, 0, 255);"></div>
                        <span>HEO (高椭圆轨道)</span>
                    </div>
                </div>
            </div>

            <div class="info-panel">
                <h4>卫星列表 (前50颗)</h4>
                <div id="satellite-list" class="satellite-list"></div>
            </div>

            <div class="info-panel">
                <h4>数据格式说明</h4>
                <p>本示例展示了卫星3D点云数据的基本信息。实际的点云数据包含：</p>
                <ul>
                    <li><strong>位置信息</strong>：笛卡尔坐标 (x, y, z) 和地理坐标 (经度, 纬度, 高度)</li>
                    <li><strong>卫星属性</strong>：NORAD ID, 轨道类型, 倾角, 周期等</li>
                    <li><strong>颜色信息</strong>：根据轨道类型自动分配的RGB颜色值</li>
                    <li><strong>统计数据</strong>：轨道类型分布, 高度范围等</li>
                </ul>
                <p>前端可以使用这些数据在Cesium、Three.js等3D库中渲染卫星点云。</p>
            </div>
        </div>
    </div>

    <script>
        const orbitColors = {
            'LEO': 'rgb(0, 255, 0)',
            'MEO': 'rgb(255, 165, 0)', 
            'GEO': 'rgb(255, 0, 0)',
            'HEO': 'rgb(255, 0, 255)',
            'SSO': 'rgb(0, 255, 255)',
            'Polar': 'rgb(0, 0, 255)',
            'Elliptical': 'rgb(255, 255, 0)',
            'Unknown': 'rgb(128, 128, 128)'
        };

        async function loadSatelliteData() {
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const content = document.getElementById('content');
            
            loading.style.display = 'block';
            error.style.display = 'none';
            content.style.display = 'none';

            try {
                const response = await fetch('/tiles/satellites');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                displaySatelliteData(data);
                
                loading.style.display = 'none';
                content.style.display = 'block';
                
            } catch (err) {
                console.error('加载数据失败:', err);
                loading.style.display = 'none';
                error.style.display = 'block';
                document.getElementById('error-message').textContent = 
                    `加载失败: ${err.message}. 请确保后端服务正在运行并且已生成点云数据。`;
            }
        }

        function displaySatelliteData(data) {
            // 基本统计信息
            document.getElementById('total-satellites').textContent = data.totalSatellites.toLocaleString();
            document.getElementById('generated-time').textContent = new Date(data.generatedAt).toLocaleString('zh-CN');
            
            if (data.statistics && data.statistics.altitudeRange) {
                document.getElementById('min-altitude').textContent = 
                    Math.round(data.statistics.altitudeRange.min / 1000) + ' km';
                document.getElementById('max-altitude').textContent = 
                    Math.round(data.statistics.altitudeRange.max / 1000) + ' km';
            }

            // 轨道类型分布
            if (data.statistics && data.statistics.orbitClassDistribution) {
                const distributionHtml = Object.entries(data.statistics.orbitClassDistribution)
                    .map(([orbitClass, count]) => 
                        `<div class="orbit-color">
                            <div class="color-box" style="background: ${orbitColors[orbitClass] || orbitColors['Unknown']};"></div>
                            <span>${orbitClass}: ${count.toLocaleString()} 颗</span>
                        </div>`
                    ).join('');
                document.getElementById('orbit-distribution').innerHTML = distributionHtml;
            }

            // 卫星列表 (前50颗)
            if (data.satellites && data.satellites.length > 0) {
                const satelliteListHtml = data.satellites.slice(0, 50)
                    .map(satellite => 
                        `<div class="satellite-item">
                            <div>
                                <div class="satellite-name">${satellite.name}</div>
                                <div class="satellite-id">ID: ${satellite.id}</div>
                            </div>
                            <div class="satellite-orbit" style="background: ${orbitColors[satellite.properties.orbitClass] || orbitColors['Unknown']};">
                                ${satellite.properties.orbitClass}
                            </div>
                        </div>`
                    ).join('');
                document.getElementById('satellite-list').innerHTML = satelliteListHtml;
            }
        }

        // 页面加载时自动获取数据
        document.addEventListener('DOMContentLoaded', loadSatelliteData);
    </script>
</body>
</html> 