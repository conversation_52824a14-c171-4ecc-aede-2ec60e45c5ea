#!/bin/bash

# 太空大数据平台后端Ubuntu快速部署脚本
# 跳过系统升级，专注于项目部署

set -e

echo "======================================"
echo "太空大数据平台后端 - 快速部署脚本"
echo "======================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 修复软件源问题
fix_apt_sources() {
    print_status "修复软件源问题..."
    
    # 移除有问题的Docker源
    sudo rm -f /etc/apt/sources.list.d/docker.list* 2>/dev/null || true
    
    # 更新软件源
    sudo apt update || {
        print_error "无法更新软件源，请检查网络连接"
        exit 1
    }
    
    print_status "软件源修复完成"
}

# 安装基础依赖
install_dependencies() {
    print_status "安装基础依赖..."
    
    # 先安装基础工具
    sudo apt install -y curl wget git python3 python3-pip sqlite3 ca-certificates gnupg lsb-release
    
    # 检查Ubuntu版本以安装合适的编译工具
    UBUNTU_VERSION=$(lsb_release -rs)
    if [[ "$UBUNTU_VERSION" == "20.04" ]]; then
        print_status "检测到Ubuntu 20.04，安装现代化编译工具..."
        # 添加gcc-10源
        sudo apt install -y software-properties-common
        sudo add-apt-repository -y ppa:ubuntu-toolchain-r/test
        sudo apt update
        sudo apt install -y gcc-10 g++-10 build-essential
        
        # 设置g++为默认版本
        sudo update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-10 100
        sudo update-alternatives --install /usr/bin/g++ g++ /usr/bin/g++-10 100
        
        print_status "编译工具升级完成"
    else
        print_status "安装标准编译工具..."
        sudo apt install -y build-essential
    fi
}

# 安装PostgreSQL数据库
install_postgresql() {
    print_status "安装PostgreSQL数据库..."
    
    # 检查是否已安装PostgreSQL
    if command -v psql &> /dev/null; then
        print_status "PostgreSQL已安装，跳过安装步骤"
        return
    fi
    
    # 安装PostgreSQL
    sudo apt install -y postgresql postgresql-contrib
    
    # 启动并启用PostgreSQL服务
    sudo systemctl start postgresql
    sudo systemctl enable postgresql
    
    print_status "PostgreSQL安装完成"
}

# 配置PostgreSQL数据库
configure_postgresql() {
    print_status "配置PostgreSQL数据库..."
    
    # 创建数据库和用户
    sudo -u postgres psql -c "CREATE DATABASE spacedata;" 2>/dev/null || print_warning "数据库spacedata可能已存在"
    sudo -u postgres psql -c "CREATE USER spaceuser WITH PASSWORD 'spacepass123';" 2>/dev/null || print_warning "用户spaceuser可能已存在"
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE spacedata TO spaceuser;" 2>/dev/null || true
    sudo -u postgres psql -c "ALTER USER spaceuser CREATEDB;" 2>/dev/null || true
    
    print_status "PostgreSQL配置完成"
    print_status "数据库: spacedata"
    print_status "用户: spaceuser"
    print_status "密码: spacepass123"
}

# 安装Node.js 18.x
install_nodejs() {
    print_status "安装Node.js 18.x..."
    
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node -v)
        if [[ $NODE_VERSION == v18* ]]; then
            print_status "Node.js 18.x已安装，跳过"
            return
        fi
    fi
    
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
    
    print_status "Node.js安装完成: $(node -v)"
}

# 安装PM2
install_pm2() {
    print_status "安装PM2..."
    if ! command -v pm2 &> /dev/null; then
        sudo npm install -g pm2
    fi
    print_status "PM2安装完成"
}

# 安装项目依赖
install_project() {
    print_status "安装项目依赖..."
    
    if [ ! -f "package.json" ]; then
        print_error "未找到package.json文件"
        exit 1
    fi
    
    # 清理可能存在的node_modules和package-lock.json
    if [ -d "node_modules" ]; then
        print_status "清理旧的node_modules..."
        rm -rf node_modules
    fi
    
    if [ -f "package-lock.json" ]; then
        print_status "清理package-lock.json..."
        rm -f package-lock.json
    fi
    
    # 使用--legacy-peer-deps来解决依赖冲突
    print_status "安装依赖（使用--legacy-peer-deps解决版本冲突）..."
    npm install --legacy-peer-deps || {
        print_warning "标准安装失败，尝试使用--force选项..."
        npm install --force || {
            print_warning "依赖安装失败，可能是better-sqlite3编译问题，尝试跳过problematic依赖..."
            
            # 尝试跳过better-sqlite3
            print_status "尝试跳过better-sqlite3编译..."
            npm install --legacy-peer-deps --ignore-scripts || {
                print_error "所有安装方式都失败了。请检查编译环境。"
                print_error "您可以尝试手动安装：npm install --legacy-peer-deps --ignore-scripts"
                exit 1
            }
        }
    }
    
    print_status "依赖安装完成"
}

# 编译项目
build_project() {
    print_status "编译项目..."
    rm -rf dist
    npm run build
    print_status "编译完成"
}

# 创建环境变量文件
create_env() {
    if [ ! -f ".env" ]; then
        print_status "创建环境变量文件..."
        cat > .env << EOF
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=spaceuser
DB_PASSWORD=spacepass123
DB_NAME=spacedata

# JWT配置
JWT_SECRET=$(openssl rand -base64 32)
JWT_EXPIRES_IN=24h

# 服务配置
PORT=3001
NODE_ENV=production

# Elasticsearch配置
ES_HOST=localhost
ES_PORT=9200

# Redis配置（如果使用）
REDIS_HOST=localhost
REDIS_PORT=6379

# 大模型API配置（可选）
OPENAI_API_KEY=your_openai_api_key
QIANWEN_API_KEY=your_qianwen_api_key

# 日志配置
LOG_LEVEL=info
LOG_DIR=./logs
EOF
        print_status "环境变量文件创建完成"
        print_warning "数据库已自动配置，如需修改其他配置请编辑 .env 文件"
    else
        print_status "环境变量文件已存在，跳过创建"
    fi
}

# 创建PM2配置
create_pm2_config() {
    print_status "创建PM2配置..."
    cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'spacedata-backend',
    script: 'dist/src/main.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    time: true
  }]
};
EOF
}

# 启动应用
start_app() {
    print_status "启动应用..."
    mkdir -p logs
    pm2 start ecosystem.config.js
    pm2 save
    pm2 startup
    
    print_status "======================================"
    print_status "部署完成!"
    print_status "应用地址: http://localhost:3001"
    print_status "API文档: http://localhost:3001/api-docs"
    print_status "======================================"
    print_status "常用命令:"
    print_status "查看状态: pm2 status"
    print_status "查看日志: pm2 logs spacedata-backend"
    print_status "重启应用: pm2 restart spacedata-backend"
    print_status "======================================"
}

# 主函数
main() {
    fix_apt_sources
    install_dependencies
    install_postgresql
    configure_postgresql
    install_nodejs
    install_pm2
    install_project
    build_project
    create_env
    create_pm2_config
    start_app
}

main "$@" 