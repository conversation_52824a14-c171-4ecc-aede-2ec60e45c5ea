Starlink 数据提取与对比分析最终报告
========================================

执行时间: 2025-06-21 17:45
脚本版本: scripts/extract-starlink.ts (已修复重复数据问题)

## 1. 数据源概览

### Elasticsearch TLE 数据 (orbital_tle 索引)
- 查询条件: satellite_name 以 "starlink" 开头 (不区分大小写)
- 时间过滤: time 字段晚于 "2025-06-21T04:00:52.163901+00:00"
- 提取字段: satellite_name, norad_id, cospar_id
- 结果数量: 7,766 条记录
- 输出文件: starlink_tle.txt (239KB)

### API 卫星数据库 (filter-satellites 接口)
- 查询参数: constellationName="Starlink"
- 分页获取: 76 页 x 100条/页 + 16条
- 原始数据: 7,516 条记录
- **发现问题**: API 返回大量重复记录
- **去重处理**: 去重后得到 4,933 条唯一记录
- 提取字段: satellite_name, alternative_name, norad_id, cospar_id
- 输出文件: starlink_satinfo.txt (去重后)

## 2. 重要发现

### API 数据重复问题
- 原始记录: 7,516 条
- 去重后记录: 4,933 条
- **重复率**: 约 34.4%
- 重复判断标准: 相同的 norad_id 和 cospar_id 组合

### 数据覆盖情况
- TLE 数据: 7,766 条记录（时间过滤后的最新数据）
- API 数据: 4,933 条记录（去重后的完整星座数据）
- **数据差异**: TLE 数据比 API 数据多 2,833 条记录

## 3. 差异分析结果

### 缺失统计
- **缺失的 NORAD_ID**: 686 个
- **缺失的 COSPAR_ID**: 686 个

这些 ID 存在于 API 数据库中，但在符合时间条件的 TLE 数据中找不到。

### 缺失原因分析
1. **时间过滤影响**: 这些卫星的 TLE 记录时间早于过滤条件
2. **数据同步延迟**: orbital_tle 索引可能缺少某些卫星的最新记录
3. **数据源差异**: 不同数据源的收录范围可能不同

## 4. 验证案例

以 NORAD_ID 54176 为例：
- ✅ 存在于 TLE 数据中: STARLINK-5154,54176,2022-141V
- ✅ 存在于 API 数据中: Starlink-5154 |STARLINK-5154|54176|2022-141V
- ✅ **不出现在缺失列表中** (之前的错误已修复)

## 5. 输出文件

### starlink_tle.txt (239KB)
```
格式: satellite_name,norad_id,cospar_id
示例: STARLINK-1008,44714,2019-074B
记录数: 7,766 条
```

### starlink_satinfo.txt (去重后)
```
格式: satellite_name|alternative_name|norad_id|cospar_id
示例: Starlink-5154 |STARLINK-5154|54176|2022-141V
记录数: 4,933 条 (去重后)
```

### diff_id.txt
```
缺失的 NORAD_ID:
58837, 58833, 54171, 51470, 51459...
(共 686 个)

缺失的 COSPAR_ID:
2024-012F, 2022-141R, 2021-036AE...
(共 686 个)
```

## 6. 脚本改进

### 已修复的问题
1. ✅ 修复了 API 数据重复记录问题
2. ✅ 添加了数据去重功能
3. ✅ 修正了差异分析逻辑
4. ✅ 增强了错误处理和日志输出

### 脚本特性
- 支持环境变量配置 (ES_NODE, ES_USERNAME, ES_PASSWORD, API_TOKEN)
- 支持跳过选项 (SKIP_ES, SKIP_API)
- 自动分页处理
- 完整的错误处理
- 详细的进度日志

## 7. 使用方法

```bash
# 完整运行
API_TOKEN="your_token" npx ts-node scripts/extract-starlink.ts

# 仅提取 TLE 数据
SKIP_API=true npx ts-node scripts/extract-starlink.ts

# 仅提取 API 数据
SKIP_ES=true API_TOKEN="your_token" npx ts-node scripts/extract-starlink.ts

# 自定义 ES 连接
ES_NODE="your_es_url" ES_USERNAME="user" ES_PASSWORD="pass" npx ts-node scripts/extract-starlink.ts
```

## 8. 总结

✅ **任务完成**: 成功提取并对比了 Starlink TLE 数据和 API 数据
✅ **问题修复**: 解决了 API 数据重复和差异分析错误的问题
✅ **数据质量**: 提供了准确的差异分析结果
✅ **工具完善**: 脚本具备完整的功能和良好的可维护性

**关键洞察**: API 数据中存在大量重复记录，去重后的实际有效数据量比 TLE 数据少，说明 TLE 数据源包含了更多最新的卫星轨道信息。

执行完成时间: 2025-06-21 17:45 