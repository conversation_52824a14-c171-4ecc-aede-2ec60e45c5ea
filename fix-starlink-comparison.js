const { Client } = require('@elastic/elasticsearch');
const axios = require('axios');
const fs = require('fs');

const ES_URL = 'http://123.57.173.156:9200';
const ES_USERNAME = 'web_readonly';
const ES_PASSWORD = 'web@readonly4all';
const INDEX_NAME = 'orbital_tle';
const API_URL = 'http://123.57.173.156:3000/elasticsearch/filter-satellites';
const TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwic3ViIjozLCJpYXQiOjE3NTA1MDE5NzYsImV4cCI6MTc1MDU4ODM3Nn0.ltAVJ5xI7ut6Khe5ncsMpZzgNFjlpa3b_qYWE7mld2o';

async function fetchAllTleData() {
  console.log('🚀 开始从 Elasticsearch 提取所有 Starlink TLE 数据（无时间过滤）...');
  
  const es = new Client({
    node: ES_URL,
    auth: { username: ES_USERNAME, password: ES_PASSWORD },
  });

  const results = [];
  let response = await es.search({
    index: INDEX_NAME,
    scroll: '2m',
    size: 1000,
    _source: ['satellite_name', 'norad_id', 'cospar_id'],
    body: {
      query: {
        wildcard: {
          satellite_name: {
            value: 'starlink*',
            case_insensitive: true,
          },
        },
      },
    },
  });

  // 滚动查询获取全部数据
  while (true) {
    const hits = response.hits.hits;
    hits.forEach((hit) => {
      const { satellite_name, norad_id, cospar_id } = hit._source;
      results.push({
        satellite_name,
        norad_id: norad_id ?? null,
        cospar_id: cospar_id ?? null,
      });
    });

    console.log(`已获取 ${results.length} 条记录...`);

    if (hits.length === 0) break;

    const scrollId = response._scroll_id;
    response = await es.scroll({ scroll_id: scrollId, scroll: '2m' });
    if (response.hits.hits.length === 0) break;
  }

  // 清理滚动上下文
  if (response._scroll_id) {
    await es.clearScroll({ scroll_id: response._scroll_id });
  }

  console.log(`✅ 共获取 ${results.length} 条 TLE 记录`);
  
  // 按 NORAD_ID 去重
  const uniqueResults = new Map();
  results.forEach((record) => {
    if (record.norad_id) {
      const key = Number(record.norad_id);
      uniqueResults.set(key, record);
    }
  });
  
  const deduplicatedResults = Array.from(uniqueResults.values());
  console.log(`🔄 去重后剩余 ${deduplicatedResults.length} 条唯一 TLE 记录`);
  
  return deduplicatedResults;
}

async function fetchSatInfoData() {
  console.log('🚀 开始调用 API 获取 Starlink 卫星信息...');
  
  const results = [];
  let page = 1;
  const limit = 100;

  while (true) {
    const { data } = await axios.post(
      API_URL,
      { constellationName: 'Starlink', page, limit },
      {
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer ${TOKEN}`,
          'Content-Type': 'application/json',
        },
      },
    );

    if (!data?.success) throw new Error(`API 请求失败，page=${page}`);

    const pageRecords = data.results.map((item) => ({
      satellite_name: (item.satellite_name ?? []).map((n) => n.value).join(';'),
      alternative_name: (item.alternative_name ?? []).map((n) => n.value).join(';'),
      norad_id: item.norad_id?.[0]?.value ?? null,
      cospar_id: item.cospar_id?.[0]?.value ?? null,
    }));

    results.push(...pageRecords);
    console.log(`📥 第 ${page} 页获取 ${pageRecords.length} 条记录，累计: ${results.length} 条`);

    const total = data.total ?? results.length;
    if (results.length >= total || pageRecords.length < limit) break;
    page += 1;
  }

  // 去重
  const uniqueResults = new Map();
  results.forEach((record) => {
    if (record.norad_id) {
      const key = `${record.norad_id}_${record.cospar_id}`;
      uniqueResults.set(key, record);
    }
  });
  
  const deduplicatedResults = Array.from(uniqueResults.values());
  console.log(`✅ 共获取 ${results.length} 条卫星信息记录，去重后 ${deduplicatedResults.length} 条`);
  
  return deduplicatedResults;
}

function writeFiles(tleRecords, satInfoRecords) {
  // 写入TLE文件
  const tleLines = tleRecords.map(
    (r) => `${r.satellite_name || ''},${r.norad_id || ''},${r.cospar_id || ''}`,
  );
  fs.writeFileSync('starlink_tle_fixed.txt', tleLines.join('\n'), 'utf-8');
  console.log(`📄 已写入 starlink_tle_fixed.txt，共 ${tleRecords.length} 条记录`);

  // 写入API文件
  const satInfoLines = satInfoRecords.map(
    (r) => `${r.satellite_name}|${r.alternative_name}|${r.norad_id || ''}|${r.cospar_id || ''}`,
  );
  fs.writeFileSync('starlink_satinfo_fixed.txt', satInfoLines.join('\n'), 'utf-8');
  console.log(`📄 已写入 starlink_satinfo_fixed.txt，共 ${satInfoRecords.length} 条记录`);
}

function generateDiff(tleRecords, satInfoRecords) {
  const tleNoradSet = new Set();
  const tleCosparSet = new Set();

  // 收集 TLE 文件中的 ID
  tleRecords.forEach((r) => {
    if (r.norad_id) tleNoradSet.add(Number(r.norad_id));
    if (r.cospar_id) tleCosparSet.add(String(r.cospar_id));
  });

  // 查找缺失项
  const missingNorad = new Set();
  const missingCospar = new Set();

  satInfoRecords.forEach((r) => {
    if (r.norad_id && !tleNoradSet.has(Number(r.norad_id))) {
      missingNorad.add(Number(r.norad_id));
    }
    if (r.cospar_id && !tleCosparSet.has(String(r.cospar_id))) {
      missingCospar.add(String(r.cospar_id));
    }
  });

  const lines = [];
  lines.push('缺失的 NORAD_ID:');
  missingNorad.forEach((id) => lines.push(String(id)));
  lines.push('\n缺失的 COSPAR_ID:');
  missingCospar.forEach((id) => lines.push(id));

  fs.writeFileSync('diff_id_fixed.txt', lines.join('\n'), 'utf-8');
  console.log(`📄 已写入修正的差异文件 diff_id_fixed.txt`);
  console.log(`缺失的 NORAD_ID: ${missingNorad.size} 个`);
  console.log(`缺失的 COSPAR_ID: ${missingCospar.size} 个`);
}

async function main() {
  try {
    console.log('🔧 修复 Starlink 数据比较问题...\n');
    
    const tleData = await fetchAllTleData();
    const satInfoData = await fetchSatInfoData();
    
    writeFiles(tleData, satInfoData);
    generateDiff(tleData, satInfoData);
    
    console.log('\n🎉 修复完成！生成的文件：');
    console.log('- starlink_tle_fixed.txt: 修正的TLE数据（无时间过滤，已去重）');
    console.log('- starlink_satinfo_fixed.txt: 修正的API数据（已去重）');
    console.log('- diff_id_fixed.txt: 修正的差异文件');
    
  } catch (error) {
    console.error('❌ 发生错误:', error);
    process.exit(1);
  }
}

main(); 