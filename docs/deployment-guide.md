# 太空大数据平台部署指南

## 概述

本指南提供了太空大数据平台后端服务的完整部署方案，支持Ubuntu 18.04+系统的一键自动化部署。

## 系统要求

### 硬件要求
- **CPU**: 4核心或以上
- **内存**: 8GB或以上（推荐16GB）
- **存储**: 50GB可用空间或以上
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Ubuntu 18.04 LTS或更高版本
- **用户权限**: 具有sudo权限的非root用户

## 快速开始

### 1. 下载部署脚本

```bash
# 克隆项目
git clone https://github.com/your-org/spacedata-backend.git
cd spacedata-backend

# 给脚本添加执行权限
chmod +x deploy.sh
chmod +x scripts/deployment/*.sh
```

### 2. 一键部署

```bash
# 默认生产环境部署
./deploy.sh

# 指定域名部署
./deploy.sh --domain your-domain.com

# 开发环境部署
./deploy.sh --dev

# 查看帮助
./deploy.sh --help
```

### 3. 验证部署

部署完成后，访问以下地址验证：

- **应用主页**: http://your-domain.com
- **API文档**: http://your-domain.com/api-docs
- **健康检查**: http://your-domain.com/health
- **监控面板**: http://your-domain.com:3000

## 部署选项

### 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--env` | 部署环境 (development/production) | production |
| `--port` | 应用端口 | 3001 |
| `--domain` | 域名 | localhost |
| `--skip-deps` | 跳过依赖安装 | false |
| `--skip-build` | 跳过构建步骤 | false |
| `--skip-nginx` | 跳过Nginx配置 | false |
| `--skip-ssl` | 跳过SSL证书配置 | false |
| `--dev` | 开发模式部署 | false |
| `--clean` | 清理之前的部署 | false |

### 部署示例

```bash
# 生产环境完整部署
./deploy.sh --domain example.com --ssl-email <EMAIL>

# 开发环境部署
./deploy.sh --dev --port 3000

# 跳过SSL的部署
./deploy.sh --domain example.com --skip-ssl

# 仅更新应用代码
./deploy.sh --skip-deps --domain example.com

# 清理之前的部署
./deploy.sh --clean
```

## 部署架构

### 服务组件

1. **应用服务**
   - SpaceData Backend (Node.js/NestJS)
   - 端口: 3001

2. **数据库服务**
   - PostgreSQL (端口: 5432)
   - MongoDB (端口: 27017)
   - Redis (端口: 6379)
   - Elasticsearch (端口: 9200)

3. **消息队列**
   - RabbitMQ (端口: 5672, 管理界面: 15672)

4. **Web服务**
   - Nginx (端口: 80/443)

5. **监控服务**
   - Prometheus (端口: 9090)
   - Grafana (端口: 3000)
   - Node Exporter (端口: 9100)

### 部署方式

支持三种部署方式：

1. **Docker Compose** (推荐)
   - 容器化部署
   - 服务隔离
   - 易于管理

2. **PM2**
   - 进程管理
   - 自动重启
   - 负载均衡

3. **systemd**
   - 系统服务
   - 开机自启
   - 日志管理

## 配置说明

### 环境变量配置

主要配置文件位于 `config/env/production.env`：

```bash
# 数据库配置
DB_HOST=postgres
DB_USERNAME=postgres
DB_PASSWORD=your_secure_password
DB_DATABASE=spacedata

# Redis配置
REDIS_HOST=redis
REDIS_PASSWORD=your_redis_password

# JWT配置
JWT_SECRET=your_jwt_secret_key

# 大模型配置
QWEN_API_KEY=your_qwen_api_key
```

### Nginx配置

Nginx配置文件位于 `/etc/nginx/sites-available/spacedata`，包含：

- 反向代理配置
- SSL/TLS配置
- 静态文件服务
- 安全头设置
- Gzip压缩

### 监控配置

- **Prometheus**: 监控指标收集
- **Grafana**: 可视化仪表板
- **告警规则**: 自动故障检测

## 安全配置

### 防火墙设置

```bash
# 允许HTTP/HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 允许SSH
sudo ufw allow ssh

# 允许应用端口
sudo ufw allow 3001
```

### SSL证书

自动使用Let's Encrypt获取SSL证书：

```bash
# 手动获取证书
sudo certbot --nginx -d your-domain.com
```

### 数据库安全

- 使用强密码
- 限制网络访问
- 定期备份

## 运维管理

### 服务管理

```bash
# Docker方式
docker-compose -f docker/compose/production.yml ps
docker-compose -f docker/compose/production.yml logs

# PM2方式
pm2 status
pm2 logs spacedata-app

# systemd方式
sudo systemctl status spacedata
sudo journalctl -u spacedata
```

### 健康检查

```bash
# 运行健康检查
./scripts/deployment/health-check.sh --domain your-domain.com

# 检查特定服务
curl http://localhost:3001/health
```

### 日志管理

日志位置：
- 应用日志: `/var/log/spacedata/`
- Nginx日志: `/var/log/nginx/`
- 系统日志: `journalctl`

### 备份策略

```bash
# 数据库备份
pg_dump spacedata > backup_$(date +%Y%m%d).sql
mongodump --db spacedata --out backup_$(date +%Y%m%d)

# 配置文件备份
tar -czf config_backup_$(date +%Y%m%d).tar.gz /opt/spacedata/current/config
```

## 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查日志
   docker-compose logs spacedata-app
   
   # 检查端口占用
   netstat -tulpn | grep :3001
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose ps postgres
   
   # 测试连接
   psql -h localhost -U postgres -d spacedata
   ```

3. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   
   # 清理Docker缓存
   docker system prune -f
   ```

### 性能优化

1. **数据库优化**
   - 调整连接池大小
   - 优化查询索引
   - 定期清理日志

2. **应用优化**
   - 调整Node.js内存限制
   - 启用集群模式
   - 配置缓存策略

3. **系统优化**
   - 调整文件描述符限制
   - 优化内核参数
   - 配置交换分区

## 升级指南

### 应用升级

```bash
# 拉取最新代码
git pull origin main

# 重新构建和部署
./deploy.sh --skip-deps

# 验证升级
./scripts/deployment/health-check.sh
```

### 数据库迁移

```bash
# 运行数据库迁移
npm run migration:run

# 回滚迁移（如需要）
npm run migration:revert
```

## 联系支持

如遇到问题，请联系：

- **技术支持**: <EMAIL>
- **文档**: https://docs.spacedata.com
- **GitHub**: https://github.com/your-org/spacedata-backend

---

*最后更新: 2024年12月*
