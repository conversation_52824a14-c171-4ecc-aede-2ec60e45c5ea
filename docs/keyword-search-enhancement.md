# 卫星筛选API - keyword字段功能增强

## 概述

`/api/v1/database/filter-satellites` 接口的 `keyword` 字段已经增强，现在支持对所有字段进行全文搜索，包括数值字段如 NORAD ID 和 COSPAR ID。

## 更新内容

### 之前的功能
- keyword 字段只能搜索部分文本字段
- 不支持数值字段搜索
- 搜索范围有限

### 现在的功能
- **全字段搜索**：keyword 现在可以搜索卫星信息中的所有字段
- **数值字段支持**：支持搜索 NORAD ID、COSPAR ID 等数值字段
- **智能匹配**：支持精确匹配和模糊匹配

## 支持的搜索字段

### 基本信息字段
- `satellite_name` - 卫星名称
- `alternative_name` - 卫星别名
- `norad_id` - NORAD ID（数值字段）
- `cospar_id` - COSPAR ID
- `status` - 运营状态
- `users` - 使用者

### 组织信息字段
- `owner` - 所有者/操作方
- `country_of_registry` - 注册国家
- `constellation` - 星座名称

### 用途信息字段
- `purpose` - 主要用途
- `detailed_purpose` - 详细用途
- `payload_description` - 载荷描述

### 发射信息字段
- `launch_site` - 发射地点
- `launch_vehicle` - 发射载具
- `contractor` - 发射承包商
- `country_of_contractor` - 承包商国家

### 轨道信息字段
- `orbit_class` - 轨道类别
- `orbit_type` - 轨道类型
- `orbit_overview` - 轨道概述

## 使用示例

### 1. 搜索 NORAD ID
```json
{
  "keyword": "25544",
  "page": 1,
  "limit": 10
}
```

### 2. 搜索 COSPAR ID
```json
{
  "keyword": "1998-067A",
  "page": 1,
  "limit": 10
}
```

### 3. 搜索卫星状态
```json
{
  "keyword": "ACTIVE",
  "page": 1,
  "limit": 10
}
```

### 4. 搜索卫星名称
```json
{
  "keyword": "Starlink",
  "page": 1,
  "limit": 10
}
```

### 5. 搜索所有者
```json
{
  "keyword": "SpaceX",
  "page": 1,
  "limit": 10
}
```

### 6. 搜索数字片段
```json
{
  "keyword": "255",
  "page": 1,
  "limit": 10
}
```
*这将匹配所有包含"255"的字段，包括 NORAD ID 如 25544, 25555 等*

## 搜索逻辑

### 文本字段
- 使用 `ILIKE` 进行不区分大小写的模糊匹配
- 支持部分匹配（子字符串匹配）

### 数值字段
- NORAD ID：支持完整数字或数字片段的匹配
- 转换为字符串进行模糊匹配，同时保持数值精确匹配能力

### 匹配方式
- **OR 逻辑**：keyword 会在所有支持的字段中进行搜索
- **模糊匹配**：使用 `%keyword%` 模式进行匹配
- **大小写不敏感**：自动转换为小写进行匹配

## 性能考虑

- 查询已优化，使用 EXISTS 子查询提高性能
- 支持 JSONB 数组字段的高效搜索
- 建议合理设置 `limit` 参数以控制返回结果数量

## 测试方法

使用提供的测试脚本 `test-keyword-search.sh` 来验证功能：

```bash
chmod +x test-keyword-search.sh
./test-keyword-search.sh
```

## 注意事项

1. **认证要求**：所有请求都需要有效的 JWT token
2. **分页支持**：建议使用分页参数控制返回结果
3. **组合查询**：keyword 可以与其他筛选条件组合使用
4. **性能优化**：对于大量数据，建议使用更具体的搜索条件

## 更新历史

- **2025-01-23**：增强 keyword 字段，支持全字段搜索，包括数值字段
- **之前版本**：仅支持部分文本字段搜索 