# 用户认证 API 文档

## 基本信息

- 基础路径: `/auth`
- 内容类型: `application/json`
- 字符编码: `UTF-8`

## API 端点

### 1. 用户注册

注册新用户账号。

**请求**

```http
POST /auth/register
Content-Type: application/json
```

**请求参数**

| 参数名   | 类型   | 必填 | 说明                     | 验证规则                | 示例            |
|----------|--------|------|--------------------------|-------------------------|-----------------|
| username | string | 是   | 用户名                   | 4-20个字符             | "testuser"      |
| password | string | 是   | 密码                     | 6-20个字符             | "password123"   |
| email    | string | 是   | 电子邮箱                 | 有效的邮箱格式         | "<EMAIL>" |

**请求示例**

```json
{
    "username": "testuser",
    "password": "password123",
    "email": "<EMAIL>"
}
```

**成功响应**

- 状态码: `201 Created`
- 响应体:

```json
{
    "username": "testuser",
    "email": "<EMAIL>",
    "avatarUrl": null,
    "id": 1,
    "role": "free",
    "apiCallsToday": 0,
    "downloadsToday": 0,
    "lastApiReset": "2024-02-03T12:34:56.789Z",
    "isActive": true,
    "createdAt": "2024-02-03T12:34:56.789Z",
    "updatedAt": "2024-02-03T12:34:56.789Z",
    "deletedAt": null
}
```

**错误响应**

1. 格式验证错误 (400 Bad Request)
   - 用户名格式错误:
   ```json
   {
       "statusCode": 400,
       "message": [
           "用户名长度不能小于4个字符"
           // 或
           "用户名长度不能超过20个字符"
       ],
       "error": "Bad Request"
   }
   ```

   - 密码格式错误:
   ```json
   {
       "statusCode": 400,
       "message": [
           "密码长度不能小于6个字符"
           // 或
           "密码长度不能超过20个字符"
       ],
       "error": "Bad Request"
   }
   ```

   - 邮箱格式错误:
   ```json
   {
       "statusCode": 400,
       "message": [
           "请输入有效的电子邮箱地址"
           // 或
           "电子邮箱不能为空"
       ],
       "error": "Bad Request"
   }
   ```

2. 资源冲突错误 (409 Conflict)
   - 用户名已存在:
   ```json
   {
       "statusCode": 409,
       "message": "用户名已存在",
       "error": "Conflict"
   }
   ```
   
   - 邮箱已被使用:
   ```json
   {
       "statusCode": 409,
       "message": "邮箱已被使用",
       "error": "Conflict"
   }
   ```

### 2. 用户登录

使用用户名和密码登录。

**请求**

```http
POST /auth/login
Content-Type: application/json
```

**请求参数**

| 参数名   | 类型   | 必填 | 说明     | 验证规则 | 示例          |
|----------|--------|------|----------|----------|---------------|
| username | string | 是   | 用户名   | 非空     | "testuser"    |
| password | string | 是   | 密码     | 非空     | "password123" |

**请求示例**

```json
{
    "username": "testuser",
    "password": "password123"
}
```

**成功响应**

- 状态码: `200 OK`
- 响应体:

```json
{
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "role": "free",
        "apiCallsToday": 0,
        "downloadsToday": 0,
        "lastApiReset": "2024-02-03T12:34:56.789Z",
        "avatarUrl": null,
        "isActive": true,
        "createdAt": "2024-02-03T12:34:56.789Z",
        "updatedAt": "2024-02-03T12:34:56.789Z"
    }
}
```

**错误响应**

1. 认证失败 (401 Unauthorized)
```json
{
    "statusCode": 401,
    "message": "用户名或密码错误",
    "error": "Unauthorized"
}
```

2. 参数验证错误 (400 Bad Request)
```json
{
    "statusCode": 400,
    "message": ["用户名不能为空"],
    "error": "Bad Request"
}
```

## 认证令牌使用说明

1. 登录成功后，服务器会返回一个 `access_token`（JWT格式）
2. 在后续的需要认证的 API 请求中，需要在请求头中添加该令牌：

```http
Authorization: Bearer your_access_token_here
```

## 安全说明

1. 密码要求：
   - 最少 6 个字符
   - 最多 20 个字符
   - 建议包含字母、数字和特殊字符

2. 用户名要求：
   - 最少 4 个字符
   - 最多 20 个字符
   - 必须唯一

3. 邮箱要求：
   - 必须是有效的邮箱格式
   - 必须唯一
   - 不能为空

4. 安全措施：
   - 密码在传输和存储时都经过加密处理（使用 bcrypt）
   - 使用 JWT 进行身份验证
   - 令牌有效期为 24 小时
   - 建议使用 HTTPS 进行传输

## 用户角色说明

系统支持以下用户角色：

1. FREE（免费用户）
   - API 调用次数限制：100次/天
   - 下载次数限制：3次/天
   - 单次下载大小限制：10MB

## 错误码说明

| 状态码 | 错误类型        | 说明                                          | 示例值 |
|--------|----------------|-----------------------------------------------|---------|
| 200    | -             | 请求成功                                      | - |
| 201    | -             | 创建成功                                      | - |
| 400    | Bad Request   | 请求参数验证失败（格式错误）：<br>- 用户名长度不符合要求（4-20字符）<br>- 密码长度不符合要求（6-20字符）<br>- 邮箱格式无效或为空 | ```json
{
    "statusCode": 400,
    "message": "请求参数验证失败",
    "error": "Bad Request"
}
``` |
| 401    | Unauthorized  | 认证失败（用户名或密码错误）                   | - |
| 409    | Conflict      | 资源冲突：<br>- 用户名已存在<br>- 邮箱已被使用 | ```json
{
    "statusCode": 409,
    "message": "用户名已存在",
    "error": "Conflict"
}
``` |
| 500    | Internal Error| 服务器内部错误                                | - |

## 错误响应示例

### 400 Bad Request - 请求参数验证失败
```json
{
    "statusCode": 400,
    "message": [
        "用户名长度不能小于4个字符",
        "密码长度不能小于6个字符",
        "请输入有效的电子邮箱地址"
    ],
    "error": "Bad Request"
}
```

### 409 Conflict - 资源冲突
1. 用户名已存在：
```json
{
    "statusCode": 409,
    "message": "用户名已存在",
    "error": "Conflict"
}
```

2. 邮箱已被使用：
```json
{
    "statusCode": 409,
    "message": "邮箱已被使用",
    "error": "Conflict"
}
```

## 验证规则详细说明

1. 用户名验证规则：
   - 长度：4-20个字符
   - 唯一性：不能与已存在的用户名重复
   - 验证失败时返回 400 Bad Request
   - 重复时返回 409 Conflict

2. 密码验证规则：
   - 长度：6-20个字符
   - 验证失败时返回 400 Bad Request

3. 邮箱验证规则：
   - 格式：必须是有效的电子邮箱格式
   - 必填：不能为空
   - 唯一性：不能与已存在的邮箱重复
   - 格式验证失败时返回 400 Bad Request
   - 重复时返回 409 Conflict 