# 新闻列表API - 关键词搜索功能增强

## 概述

新闻列表API (`/api/es/news/list`) 的关键词搜索功能已经增强，现在默认使用子串匹配方式，并且支持在新闻的所有字段中进行搜索。

## 更新内容

### 之前的功能
- 关键词搜索默认使用相似性匹配（similarity）
- 搜索字段相对有限
- 需要显式指定子串匹配模式

### 现在的功能
- **默认子串匹配**：关键词搜索现在默认使用子串匹配（substring）方式
- **全字段搜索**：关键词现在可以搜索新闻中的所有相关字段
- **智能匹配**：同时使用wildcard查询和match查询，确保最佳匹配效果
- **大小写不敏感**：支持大小写不敏感的搜索

## 支持的搜索字段

### 基本内容字段
- `title` - 英文标题
- `title_cn` - 中文标题
- `summary` - 英文摘要
- `summary_cn` - 中文摘要
- `content` - 英文内容
- `content_cn` - 中文内容

### 元数据字段
- `source` - 新闻来源
- `author` - 作者
- `info_source` - 信息源链接
- `url` - 新闻URL
- `themes_cn` - 中文主题词
- `published_at` - 发布时间

## 匹配方式

### 子串匹配（默认）
- **模式**: `substring`
- **特点**: 精确的子串匹配，支持部分词匹配
- **实现**: 使用wildcard查询 + match查询的组合
- **大小写**: 不敏感

### 相似性匹配
- **模式**: `similarity`
- **特点**: 模糊匹配，支持拼写错误容错
- **实现**: 使用multi_match查询，带有fuzziness
- **适用**: 当不确定准确关键词时使用

## API使用示例

### 1. 基本关键词搜索（默认子串匹配）
```json
{
  "keywords": ["satellite"],
  "page": 1,
  "limit": 10
}
```

### 2. 多关键词搜索
```json
{
  "keywords": ["satellite", "launch"],
  "page": 1,
  "limit": 10
}
```

### 3. 中文关键词搜索
```json
{
  "keywords": ["卫星", "发射"],
  "page": 1,
  "limit": 10
}
```

### 4. 显式指定匹配方式
```json
{
  "keywords": ["space"],
  "keywordMatchType": "substring",
  "page": 1,
  "limit": 10
}
```

### 5. 相似性匹配
```json
{
  "keywords": ["satelite"],
  "keywordMatchType": "similarity",
  "page": 1,
  "limit": 10
}
```

### 6. 结合其他筛选条件
```json
{
  "keywords": ["satellite"],
  "themes": ["航天"],
  "publishDateStart": "2023-01-01",
  "publishDateEnd": "2023-12-31",
  "page": 1,
  "limit": 10
}
```

## 搜索逻辑

### 多关键词处理
- 当提供多个关键词时，**每个关键词都必须匹配**（AND逻辑）
- 每个关键词可以匹配任意一个字段（OR逻辑）

### 字段匹配策略
1. **Wildcard查询**: 对`.keyword`字段进行精确子串匹配
2. **Match查询**: 对分析字段进行分词匹配
3. **组合策略**: 两种查询方式并行，提高匹配成功率

## 性能优化

### 查询优化
- 使用`minimum_should_match: 1`确保至少匹配一个字段
- 同时使用wildcard和match查询，兼顾精确性和容错性
- 支持大小写不敏感搜索

### 索引优化建议
- 确保重要字段有`.keyword`映射
- 为常用搜索字段建立适当的分析器
- 考虑为高频搜索字段建立专门的索引

## 测试验证

使用提供的测试脚本验证功能：

```bash
chmod +x test-news-keyword-search.sh
./test-news-keyword-search.sh
```

测试脚本包含以下测试用例：
1. 基本关键词搜索
2. 多关键词搜索
3. 中文关键词搜索
4. 来源字段搜索
5. 作者字段搜索
6. 子串匹配vs相似性匹配对比
7. 结合主题词搜索
8. 结合时间范围搜索

## 注意事项

1. **默认行为变更**: 关键词搜索现在默认使用子串匹配而不是相似性匹配
2. **字段覆盖**: 搜索现在覆盖更多字段，可能返回更多结果
3. **性能影响**: 更多字段的搜索可能略微影响查询性能
4. **兼容性**: 保持向后兼容，仍支持显式指定匹配方式

## 更新日志

- **2024-01-XX**: 将默认匹配方式从相似性匹配改为子串匹配
- **2024-01-XX**: 增加对url、themes_cn、published_at字段的搜索支持
- **2024-01-XX**: 优化搜索算法，使用wildcard + match组合查询
- **2024-01-XX**: 更新API文档和示例 