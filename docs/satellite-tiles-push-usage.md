# 卫星点云文件推送功能使用指南

## 🚀 功能概述

卫星点云文件推送功能解决了大数据量传输的性能问题，支持在生成点云数据后直接推送到前端指定位置，避免通过API传输大文件的延迟。

## 📋 主要特性

### 1. 多种推送方式
- **HTTP推送**: 推送到前端API接口
- **本地推送**: 复制到本地指定目录
- **FTP/SFTP**: 推送到FTP服务器（待实现）
- **云存储**: 推送到S3/OSS等云存储（待实现）

### 2. 性能优化
- **文件分片**: 大文件自动分片传输
- **Gzip压缩**: 减少传输数据量
- **并行推送**: 支持同时推送到多个目标
- **重试机制**: 失败自动重试

### 3. 监控与回调
- **推送状态**: 实时监控推送进度
- **回调通知**: 推送完成后自动通知
- **错误处理**: 详细的错误信息和日志

## 🔧 API使用方法

### 基础推送示例

```bash
curl -X POST http://localhost:3000/api/tiles/generate \
  -H "Content-Type: application/json" \
  -d '{
    "colorByOrbitClass": true,
    "includeStatistics": true,
    "enableGzip": true,
    "pushTargets": [
      {
        "type": "http",
        "target": "https://frontend.example.com/api/tiles/upload",
        "credentials": {
          "token": "your_bearer_token"
        },
        "options": {
          "timeout": 60000,
          "retries": 3
        }
      }
    ]
  }'
```

### 多目标推送示例

```bash
curl -X POST http://localhost:3000/api/tiles/generate \
  -H "Content-Type: application/json" \
  -d '{
    "colorByOrbitClass": true,
    "includeStatistics": true,
    "enableGzip": true,
    "pushTargets": [
      {
        "type": "http",
        "target": "https://frontend.example.com/api/tiles/upload",
        "credentials": {
          "token": "bearer_token_1"
        }
      },
      {
        "type": "local",
        "target": "/var/www/html/tiles"
      },
      {
        "type": "http",
        "target": "https://backup.example.com/api/tiles/upload",
        "credentials": {
          "apiKey": "api_key_2"
        }
      }
    ],
    "callbackUrl": "https://frontend.example.com/api/tiles/callback"
  }'
```

### 文件分片推送示例

```bash
curl -X POST http://localhost:3000/api/tiles/generate \
  -H "Content-Type: application/json" \
  -d '{
    "colorByOrbitClass": true,
    "includeStatistics": true,
    "enableGzip": true,
    "chunking": {
      "enabled": true,
      "chunkSize": 10,
      "namePattern": "satellites_part_{index}.json"
    },
    "pushTargets": [
      {
        "type": "http",
        "target": "https://frontend.example.com/api/tiles/upload",
        "credentials": {
          "token": "your_bearer_token"
        }
      }
    ]
  }'
```

## 📊 响应格式

### 成功响应示例

```json
{
  "success": true,
  "message": "成功生成 27000 颗卫星的点云数据",
  "taskId": "task_2025-01-06T14_00_00_000Z",
  "totalSatellites": 27000,
  "generationDuration": 15000,
  "outputPath": "/tiles/satellites.json",
  "pushResults": [
    {
      "target": "https://frontend.example.com/api/tiles/upload",
      "success": true,
      "files": ["satellites.json.gz", "metadata.json.gz"],
      "duration": 5000,
      "dataSize": 1048576
    },
    {
      "target": "/var/www/html/tiles",
      "success": true,
      "files": ["satellites.json.gz", "metadata.json.gz"],
      "duration": 1000,
      "dataSize": 1048576
    }
  ],
  "generatedFiles": ["satellites.json", "metadata.json"],
  "fileSizes": {
    "satellites.json": 5242880,
    "metadata.json": 2048
  }
}
```

### 推送失败响应示例

```json
{
  "success": true,
  "message": "成功生成 27000 颗卫星的点云数据",
  "taskId": "task_2025-01-06T14_00_00_000Z",
  "totalSatellites": 27000,
  "generationDuration": 15000,
  "outputPath": "/tiles/satellites.json",
  "pushResults": [
    {
      "target": "https://frontend.example.com/api/tiles/upload",
      "success": false,
      "files": [],
      "duration": 0,
      "dataSize": 0,
      "error": "HTTP响应错误: 500 Internal Server Error"
    }
  ]
}
```

## 🔧 推送目标配置

### HTTP推送配置

```json
{
  "type": "http",
  "target": "https://frontend.example.com/api/tiles/upload",
  "credentials": {
    "token": "bearer_token",      // Bearer Token认证
    "apiKey": "api_key"           // API Key认证
  },
  "options": {
    "timeout": 60000,             // 超时时间(毫秒)
    "retries": 3                  // 重试次数
  }
}
```

### 本地推送配置

```json
{
  "type": "local",
  "target": "/var/www/html/tiles"   // 本地目录路径
}
```

### FTP推送配置（待实现）

```json
{
  "type": "ftp",
  "target": "ftp://ftp.example.com/tiles",
  "credentials": {
    "username": "ftpuser",
    "password": "ftppass"
  }
}
```

### S3推送配置（待实现）

```json
{
  "type": "s3",
  "target": "s3://bucket-name/tiles/",
  "credentials": {
    "accessKeyId": "access_key",
    "secretAccessKey": "secret_key",
    "region": "us-east-1"
  }
}
```

## 📦 文件分片配置

当点云文件过大时，可以启用文件分片功能：

```json
{
  "chunking": {
    "enabled": true,                              // 是否启用分片
    "chunkSize": 10,                             // 分片大小(MB)
    "namePattern": "satellites_chunk_{index}.json" // 分片文件命名模式
  }
}
```

分片文件命名支持的占位符：
- `{name}`: 原文件名（不含扩展名）
- `{index}`: 分片索引（从1开始，自动补零）
- `{ext}`: 原文件扩展名

## 🗜️ 压缩配置

启用Gzip压缩可以显著减少传输数据量：

```json
{
  "enableGzip": true,                // 启用gzip压缩
  "compressionLevel": 6              // 压缩级别(0-9)
}
```

压缩效果示例：
- 原始文件: 50MB
- 压缩后: 8MB (84%压缩率)

## 📞 回调通知

推送完成后可以发送回调通知：

```json
{
  "callbackUrl": "https://frontend.example.com/api/tiles/callback"
}
```

回调请求格式：

```json
{
  "timestamp": "2025-01-06T14:05:00Z",
  "results": [
    {
      "target": "https://frontend.example.com/api/tiles/upload",
      "success": true,
      "files": ["satellites.json.gz"],
      "duration": 5000,
      "dataSize": 1048576
    }
  ],
  "summary": {
    "total": 1,
    "success": 1,
    "failed": 0,
    "totalDataSize": 1048576
  }
}
```

## 🔍 前端接收示例

### Node.js Express接收示例

```javascript
const express = require('express');
const multer = require('multer');
const fs = require('fs');
const path = require('path');

const app = express();

// 配置文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = './public/tiles';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const fileName = req.headers['x-file-name'] || file.originalname;
    cb(null, fileName);
  }
});

const upload = multer({ storage });

// 接收点云文件
app.post('/api/tiles/upload', upload.single('file'), (req, res) => {
  const fileName = req.headers['x-file-name'];
  const fileSize = req.headers['x-file-size'];
  
  console.log(`接收到文件: ${fileName}, 大小: ${fileSize} bytes`);
  
  // 处理接收到的文件
  if (req.file) {
    console.log(`文件保存到: ${req.file.path}`);
    res.json({ success: true, message: '文件接收成功' });
  } else {
    res.status(400).json({ success: false, message: '文件接收失败' });
  }
});

// 接收回调通知
app.post('/api/tiles/callback', express.json(), (req, res) => {
  console.log('收到推送完成回调:', req.body);
  
  // 处理回调逻辑
  const { results, summary } = req.body;
  console.log(`推送完成: 成功${summary.success}/${summary.total}, 总大小: ${summary.totalDataSize} bytes`);
  
  res.json({ success: true });
});

app.listen(3001, () => {
  console.log('前端服务器启动在端口 3001');
});
```

### 直接接收二进制数据

```javascript
app.post('/api/tiles/upload', express.raw({ type: 'application/octet-stream', limit: '100mb' }), (req, res) => {
  const fileName = req.headers['x-file-name'];
  const fileSize = parseInt(req.headers['x-file-size']);
  
  if (req.body.length !== fileSize) {
    return res.status(400).json({ error: '文件大小不匹配' });
  }
  
  const filePath = path.join('./public/tiles', fileName);
  fs.writeFileSync(filePath, req.body);
  
  console.log(`文件保存成功: ${fileName}`);
  res.json({ success: true, message: '文件接收成功' });
});
```

## 🚨 错误处理

### 常见错误及解决方案

1. **HTTP推送失败**
   - 检查目标URL是否可访问
   - 验证认证信息是否正确
   - 确认目标服务器支持大文件上传

2. **本地推送失败**
   - 检查目标目录权限
   - 确认磁盘空间充足

3. **文件分片失败**
   - 检查分片大小设置
   - 确认临时目录可写

4. **压缩失败**
   - 检查内存使用情况
   - 降低压缩级别

## 📈 性能优化建议

### 1. 网络优化
- 使用CDN加速文件传输
- 启用HTTP/2支持
- 配置合适的超时时间

### 2. 文件优化
- 启用Gzip压缩减少传输量
- 合理设置分片大小（推荐10-50MB）
- 使用增量更新减少数据量

### 3. 并发控制
- 限制同时推送的目标数量
- 使用队列管理推送任务
- 实现推送优先级机制

## 🔧 配置示例

### 完整配置示例

```json
{
  "forceRegenerate": false,
  "colorByOrbitClass": true,
  "includeStatistics": true,
  "coordinateSystem": "cartesian",
  "compressionLevel": 6,
  "enableGzip": true,
  "enableIncremental": false,
  "chunking": {
    "enabled": true,
    "chunkSize": 20,
    "namePattern": "satellites_part_{index}.json"
  },
  "pushTargets": [
    {
      "type": "http",
      "target": "https://frontend.example.com/api/tiles/upload",
      "credentials": {
        "token": "your_bearer_token"
      },
      "options": {
        "timeout": 120000,
        "retries": 3
      }
    },
    {
      "type": "local",
      "target": "/var/www/html/tiles"
    }
  ],
  "callbackUrl": "https://frontend.example.com/api/tiles/callback"
}
```

## 📝 注意事项

1. **安全性**
   - 使用HTTPS传输敏感数据
   - 验证推送目标的合法性
   - 定期更新认证信息

2. **性能**
   - 监控推送耗时和成功率
   - 根据网络情况调整重试策略
   - 定期清理临时文件

3. **可靠性**
   - 实现推送状态监控
   - 配置告警机制
   - 建立数据备份策略

## 🔗 相关API

- `POST /api/tiles/generate` - 生成并推送点云数据
- `GET /api/tiles/status` - 获取生成状态
- `GET /api/tiles/metadata` - 获取元数据信息

更多详细信息请参考API文档。 