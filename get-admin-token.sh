#!/bin/bash

echo "=== 获取Admin用户Token ==="

# 检查服务是否运行
echo "检查服务状态..."
if ! curl -s http://localhost:3001/api-docs > /dev/null; then
    echo "错误：服务未运行或无法访问。请确保应用在端口3001上运行。"
    exit 1
fi

echo "服务正常运行"
echo ""

# 登录获取token
echo "尝试使用admin账户登录..."
response=$(curl -s -X 'POST' \
  'http://localhost:3001/auth/login' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "admin",
    "password": "Admin123!"
  }')

echo "登录响应: $response"
echo ""

# 检查响应中是否包含access_token
if echo "$response" | grep -q "access_token"; then
    echo "登录成功！"
    echo "$response" > token.json
    
    # 提取token
    token=$(echo "$response" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
    echo "Token (前50字符): ${token:0:50}..."
    
    # 验证token
    echo ""
    echo "验证token..."
    profile_response=$(curl -s -X 'GET' \
      'http://localhost:3001/auth/profile' \
      -H 'accept: application/json' \
      -H "Authorization: Bearer $token")
    
    echo "用户信息: $profile_response"
    
else
    echo "登录失败！"
    echo "响应: $response"
    echo ""
    echo "可能的原因："
    echo "1. admin用户不存在"
    echo "2. 密码错误"
    echo "3. 数据库连接问题"
    echo ""
    echo "尝试注册admin用户..."
    
    register_response=$(curl -s -X 'POST' \
      'http://localhost:3001/auth/register' \
      -H 'accept: application/json' \
      -H 'Content-Type: application/json' \
      -d '{
        "username": "admin",
        "password": "Admin123!",
        "email": "<EMAIL>"
      }')
    
    echo "注册响应: $register_response"
    
    if echo "$register_response" | grep -q "username"; then
        echo "admin用户注册成功！"
        echo "请稍后手动将该用户设置为admin角色，然后重新运行此脚本。"
    fi
fi 