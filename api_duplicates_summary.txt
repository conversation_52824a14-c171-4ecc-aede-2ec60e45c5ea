Starlink API 重复数据概览
=======================

## 重复数据统计

- 总记录数: 7,516 条
- 唯一组合数: 4,813 个
- 重复组数: 2,040 组
- 重复记录数: 2,703 条
- **重复率: 35.96%**

## 重复类型分布

- 重复 2 次的组: 1,494 组 (占重复组的 73.2%)
- 重复 3 次的组: 445 组 (占重复组的 21.8%)
- 重复 4 次的组: 85 组 (占重复组的 4.2%)
- 重复 5 次的组: 16 组 (占重复组的 0.8%)

## 重复最严重的卫星 (重复5次)

1. STARLINK-30769 (NORAD: 58138)
2. STARLINK-30782 (NORAD: 58137)
3. STARLINK-30776 (NORAD: 58136)
4. STARLINK-30772 (NORAD: 58135)
5. STARLINK-30761 (NORAD: 58134)
6. STARLINK-30758 (NORAD: 58133)
7. STARLINK-30770 (NORAD: 58132)
8. STARLINK-30757 (NORAD: 58131)
9. STARLINK-30783 (NORAD: 58130)
10. STARLINK-30756 (NORAD: 58129)

## 重复示例

### 示例1: STARLINK-3449 (重复4次)
- NORAD_ID: 51745
- COSPAR_ID: 2022-016AH
- 出现在页码: 1, 12, 21, 68
- 原始索引: 0, 1170, 2096, 6707

### 示例2: STARLINK-3419 (重复4次)
- NORAD_ID: 51463
- COSPAR_ID: 2022-010H
- 出现在页码: 1, 12, 21, 68

## 问题分析

1. **数据源问题**: API 返回的数据中存在大量重复记录
2. **分页问题**: 同一卫星在不同页面中重复出现
3. **数据库索引**: 可能是数据库查询或索引配置导致的重复
4. **影响**: 导致实际有效数据量从 7,516 条减少到 4,813 条

## 建议

1. 检查后端 API 的查询逻辑
2. 确认数据库中是否存在重复记录
3. 在 API 层面添加去重逻辑
4. 优化分页查询以避免重复

详细的重复记录清单请查看: api_duplicates_analysis.txt (893KB) 