const axios = require('axios');

const TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwic3ViIjozLCJpYXQiOjE3NTA1MDE5NzYsImV4cCI6MTc1MDU4ODM3Nn0.ltAVJ5xI7ut6Khe5ncsMpZzgNFjlpa3b_qYWE7mld2o";
const API_URL = 'http://localhost:3001/api/v1/database/filter-satellites';

async function verifySpecificPages() {
  console.log('🔍 验证特定页面是否包含我声称的重复 NORAD_ID...\n');
  
  const testNoradIds = [50839, 50836, 50843, 50844, 50832];
  const testPages = [20, 74, 75, 76];
  
  console.log(`测试 NORAD_ID: ${testNoradIds.join(', ')}`);
  console.log(`测试页面: ${testPages.join(', ')}\n`);
  
  for (const page of testPages) {
    console.log(`📄 检查第 ${page} 页...`);
    
    try {
      const { data } = await axios.post(
        API_URL,
        { constellationName: 'Starlink', page, limit: 100 },
        {
          headers: {
            Accept: 'application/json',
            Authorization: `Bearer ${TOKEN}`,
            'Content-Type': 'application/json',
          },
        },
      );

      if (data?.success) {
        console.log(`  第 ${page} 页共有 ${data.results.length} 条记录`);
        
        const foundNoradIds = [];
        data.results.forEach((item, index) => {
          const noradId = item.norad_id?.[0]?.value;
          if (testNoradIds.includes(noradId)) {
            foundNoradIds.push({
              noradId,
              index,
              dbId: item.id,
              name: (item.alternative_name ?? []).map(n => n.value).join(';')
            });
          }
        });
        
        if (foundNoradIds.length > 0) {
          console.log(`  ✅ 在第 ${page} 页找到 ${foundNoradIds.length} 个测试 NORAD_ID:`);
          foundNoradIds.forEach(found => {
            console.log(`    NORAD_ID: ${found.noradId}, 索引: ${found.index}, DB_ID: ${found.dbId}`);
          });
        } else {
          console.log(`  ❌ 在第 ${page} 页未找到任何测试 NORAD_ID`);
        }
      } else {
        console.log(`  ❌ 第 ${page} 页请求失败`);
      }
    } catch (error) {
      console.error(`  ❌ 第 ${page} 页查询失败:`, error.message);
    }
    
    console.log('');
  }
}

async function main() {
  await verifySpecificPages();
}

main(); 