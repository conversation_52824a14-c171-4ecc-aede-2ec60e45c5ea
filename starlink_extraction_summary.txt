Starlink 数据提取总结
====================

执行时间: 2025-06-21 17:20

1. Elasticsearch TLE 数据提取
-----------------------------
- 数据源: orbital_tle 索引
- 查询条件: 
  * 卫星名称以 "starlink" 开头（不区分大小写）
  * 时间字段晚于 "2025-06-21T04:00:52.163901+00:00"
- 提取字段: satellite_name, norad_id, cospar_id
- 结果数量: 7,766 条记录
- 输出文件: starlink_tle.txt
- 格式: satellite_name,norad_id,cospar_id

数据样例:
STARLINK-1008,44714,2019-074B
STARLINK-1010,44716,2019-074D
...
STARLINK-34017,64339,2025-124Y

2. API 卫星信息提取
------------------
- API 端点: http://localhost:3001/api/v1/database/filter-satellites
- 查询参数: constellationName="Starlink"
- 状态: 需要有效的认证 token
- 问题: 提供的 token 已过期 (401 Unauthorized)
- 输出文件: starlink_satinfo.txt (空文件)

3. 差异分析
-----------
- 由于 API 调用失败，无法完成差异对比
- 输出文件: diff_id.txt (仅包含空的缺失列表)

4. 脚本使用说明
--------------
完整运行:
npx ts-node scripts/extract-starlink.ts

仅提取 TLE 数据:
SKIP_API=true npx ts-node scripts/extract-starlink.ts

使用自定义 token:
API_TOKEN=your_token_here npx ts-node scripts/extract-starlink.ts

使用自定义 ES 配置:
ES_NODE=your_es_url ES_USERNAME=user ES_PASSWORD=pass npx ts-node scripts/extract-starlink.ts

5. 待完成任务
------------
- 获取有效的 API token
- 完成 API 数据提取
- 执行 norad_id 和 cospar_id 的差异对比
- 生成最终的差异报告

当前状态: TLE 数据提取完成，等待有效 token 完成 API 部分 