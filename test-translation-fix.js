/**
 * 翻译功能修复效果测试脚本
 * 用于验证新闻翻译内容截断问题的修复
 */

const axios = require('axios');
const fs = require('fs');

// 配置
const config = {
  baseURL: 'http://localhost:3000', // 根据实际情况调整
  apiPath: '/api/es/news/translate',
  // 从环境变量或文件中读取token
  token: process.env.JWT_TOKEN || 'your-jwt-token-here'
};

// 测试用例：来自用户提供的新闻示例
const testCases = [
  {
    name: '长新闻内容测试',
    description: '测试长新闻内容是否能完整翻译',
    params: {
      batchSize: 1,
      maxDocs: 1,
      forceRetranslate: true,
      specificIndexes: ['news_breakingdefense'], // 根据实际索引名调整
      llmMode: 'default',
      autoExtractThemes: true
    }
  },
  {
    name: '高质量模式测试',
    description: '使用高质量模式测试翻译效果',
    params: {
      batchSize: 1,
      maxDocs: 1,
      forceRetranslate: true,
      specificIndexes: ['news_breakingdefense'],
      llmMode: 'high_quality',
      autoExtractThemes: true
    }
  }
];

/**
 * 执行翻译测试
 */
async function runTranslationTest() {
  console.log('🚀 开始测试翻译功能修复效果...\n');

  for (const testCase of testCases) {
    console.log(`📋 测试案例: ${testCase.name}`);
    console.log(`📝 描述: ${testCase.description}`);
    console.log(`⚙️  参数: ${JSON.stringify(testCase.params, null, 2)}`);

    try {
      const startTime = Date.now();
      
      // 发送翻译请求
      const response = await axios.post(
        `${config.baseURL}${config.apiPath}`,
        testCase.params,
        {
          headers: {
            'Authorization': `Bearer ${config.token}`,
            'Content-Type': 'application/json'
          },
          timeout: 300000 // 5分钟超时
        }
      );

      const endTime = Date.now();
      const duration = endTime - startTime;

      console.log(`✅ 测试完成，耗时: ${duration}ms`);
      console.log(`📊 翻译统计:`, response.data);

      // 分析结果
      const stats = response.data;
      if (stats.success > 0) {
        console.log(`🎉 成功翻译 ${stats.success} 个文档`);
        
        if (stats.themeExtraction) {
          console.log(`🎯 主题提取统计: 成功 ${stats.themeExtraction.success}, 失败 ${stats.themeExtraction.failed}`);
        }
      }

      if (stats.failed > 0) {
        console.log(`⚠️  失败 ${stats.failed} 个文档`);
      }

      if (stats.skipped > 0) {
        console.log(`⏭️  跳过 ${stats.skipped} 个文档`);
      }

    } catch (error) {
      console.error(`❌ 测试失败: ${error.message}`);
      if (error.response) {
        console.error(`响应状态: ${error.response.status}`);
        console.error(`响应数据:`, error.response.data);
      }
    }

    console.log('\n' + '='.repeat(50) + '\n');
  }
}

/**
 * 检查特定文档的翻译质量
 */
async function checkDocumentQuality(index, docId) {
  console.log(`🔍 检查文档翻译质量: ${index}/${docId}`);

  try {
    // 这里需要调用ES查询API来获取文档详情
    // 根据实际的API调整
    const response = await axios.get(
      `${config.baseURL}/api/es/${index}/_doc/${docId}`,
      {
        headers: {
          'Authorization': `Bearer ${config.token}`
        }
      }
    );

    const doc = response.data._source;
    
    console.log(`📄 文档信息:`);
    console.log(`标题: ${doc.title || 'N/A'}`);
    console.log(`中文标题: ${doc.title_cn || 'N/A'}`);
    console.log(`原文内容长度: ${doc.content ? doc.content.length : 0} 字符`);
    console.log(`中文内容长度: ${doc.content_cn ? doc.content_cn.length : 0} 字符`);
    
    if (doc.content && doc.content_cn) {
      const ratio = doc.content_cn.length / doc.content.length;
      console.log(`长度比例: ${(ratio * 100).toFixed(1)}%`);
      
      if (ratio < 0.3) {
        console.log(`⚠️  警告: 翻译内容可能不完整（比例过低）`);
      } else if (ratio > 0.7) {
        console.log(`✅ 翻译内容比例正常`);
      }
    }

    // 检查是否有主题词
    if (doc.themes_cn) {
      console.log(`🎯 主题词: ${doc.themes_cn}`);
    }

  } catch (error) {
    console.error(`❌ 检查失败: ${error.message}`);
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    // 运行完整测试
    await runTranslationTest();
  } else if (args.length === 2) {
    // 检查特定文档
    const [index, docId] = args;
    await checkDocumentQuality(index, docId);
  } else {
    console.log('使用方法:');
    console.log('1. 运行完整测试: node test-translation-fix.js');
    console.log('2. 检查特定文档: node test-translation-fix.js <index> <docId>');
    console.log('');
    console.log('例如: node test-translation-fix.js news_breakingdefense 454af4f2ebcfb6167bf2662276f73c03');
  }
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  runTranslationTest,
  checkDocumentQuality
}; 