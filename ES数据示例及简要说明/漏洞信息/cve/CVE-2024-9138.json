{"dataType": "CVE_RECORD", "dataVersion": "5.1", "cveMetadata": {"cveId": "CVE-2024-9138", "assignerOrgId": "2e0a0ee2-d866-482a-9f5e-ac03d156dbaa", "state": "PUBLISHED", "assignerShortName": "Moxa", "dateReserved": "2024-09-24T07:11:41.549Z", "datePublished": "2025-01-03T08:14:31.588Z", "dateUpdated": "2025-01-03T08:14:31.588Z"}, "containers": {"cna": {"affected": [{"defaultStatus": "unaffected", "product": "EDR-810 Series", "vendor": "Moxa", "versions": [{"lessThanOrEqual": "5.12.37", "status": "affected", "version": "1.0", "versionType": "custom"}]}, {"defaultStatus": "unaffected", "product": "EDR-8010 Series", "vendor": "Moxa", "versions": [{"lessThanOrEqual": "3.13.1", "status": "affected", "version": "1.0", "versionType": "custom"}]}, {"defaultStatus": "unaffected", "product": "EDR-G902 Series", "vendor": "Moxa", "versions": [{"lessThanOrEqual": "5.7.25", "status": "affected", "version": "1.0", "versionType": "custom"}]}, {"defaultStatus": "unaffected", "product": "EDR-G903 Series", "vendor": "Moxa", "versions": [{"lessThanOrEqual": "5.7.25", "status": "affected", "version": "1.0", "versionType": "custom"}]}, {"defaultStatus": "unaffected", "product": "EDR-G9004 Series", "vendor": "Moxa", "versions": [{"lessThanOrEqual": "3.13.1", "status": "affected", "version": "1.0", "versionType": "custom"}]}, {"defaultStatus": "unaffected", "product": "EDR-G9010 Series", "vendor": "Moxa", "versions": [{"lessThanOrEqual": "3.13.1", "status": "affected", "version": "1.0", "versionType": "custom"}]}, {"defaultStatus": "unaffected", "product": "EDF-G1002-BP Series", "vendor": "Moxa", "versions": [{"lessThanOrEqual": "3.13.1", "status": "affected", "version": "1.0", "versionType": "custom"}]}, {"defaultStatus": "unaffected", "product": "NAT-102 Series", "vendor": "Moxa", "versions": [{"lessThanOrEqual": "1.0.5", "status": "affected", "version": "1.0", "versionType": "custom"}]}, {"defaultStatus": "unaffected", "product": "OnCell G4302-LTE4 Series", "vendor": "Moxa", "versions": [{"lessThanOrEqual": "3.13", "status": "affected", "version": "1.0", "versionType": "custom"}]}, {"defaultStatus": "unaffected", "product": "TN-4900 Series", "vendor": "Moxa", "versions": [{"lessThanOrEqual": "3.13", "status": "affected", "version": "1.0", "versionType": "custom"}]}], "credits": [{"lang": "en", "type": "finder", "value": "<PERSON>"}], "descriptions": [{"lang": "en", "supportingMedia": [{"base64": false, "type": "text/html", "value": "<p>Moxa’s cellular routers, secure routers, and network security appliances are affected by a high-severity vulnerability, CVE-2024-9138. This vulnerability involves hard-coded credentials, enabling an authenticated user to escalate privileges and gain root-level access to the system, posing a significant security risk.</p>"}], "value": "Moxa’s cellular routers, secure routers, and network security appliances are affected by a high-severity vulnerability, CVE-2024-9138. This vulnerability involves hard-coded credentials, enabling an authenticated user to escalate privileges and gain root-level access to the system, posing a significant security risk."}], "impacts": [{"capecId": "CAPEC-37", "descriptions": [{"lang": "en", "value": "CAPEC-37: Retrieve Embedded Sensitive Data"}]}], "metrics": [{"cvssV4_0": {"Automatable": "NOT_DEFINED", "Recovery": "NOT_DEFINED", "Safety": "NOT_DEFINED", "attackComplexity": "LOW", "attackRequirements": "NONE", "attackVector": "NETWORK", "baseScore": 8.6, "baseSeverity": "HIGH", "privilegesRequired": "HIGH", "providerUrgency": "NOT_DEFINED", "subAvailabilityImpact": "NONE", "subConfidentialityImpact": "NONE", "subIntegrityImpact": "NONE", "userInteraction": "NONE", "valueDensity": "NOT_DEFINED", "vectorString": "CVSS:4.0/AV:N/AC:L/AT:N/PR:H/UI:N/VC:H/VI:H/VA:H/SC:N/SI:N/SA:N", "version": "4.0", "vulnAvailabilityImpact": "HIGH", "vulnConfidentialityImpact": "HIGH", "vulnIntegrityImpact": "HIGH", "vulnerabilityResponseEffort": "NOT_DEFINED"}, "format": "CVSS", "scenarios": [{"lang": "en", "value": "GENERAL"}]}, {"cvssV3_1": {"attackComplexity": "LOW", "attackVector": "NETWORK", "availabilityImpact": "HIGH", "baseScore": 7.2, "baseSeverity": "HIGH", "confidentialityImpact": "HIGH", "integrityImpact": "HIGH", "privilegesRequired": "HIGH", "scope": "UNCHANGED", "userInteraction": "NONE", "vectorString": "CVSS:3.1/AV:N/AC:L/PR:H/UI:N/S:U/C:H/I:H/A:H", "version": "3.1"}, "format": "CVSS", "scenarios": [{"lang": "en", "value": "GENERAL"}]}], "problemTypes": [{"descriptions": [{"cweId": "CWE-656", "description": "CWE-656: Reliance on Security Through Obscurity", "lang": "en", "type": "CWE"}]}], "providerMetadata": {"orgId": "2e0a0ee2-d866-482a-9f5e-ac03d156dbaa", "shortName": "Moxa", "dateUpdated": "2025-01-03T08:14:31.588Z"}, "references": [{"tags": ["vendor-advisory"], "url": "https://www.moxa.com/en/support/product-support/security-advisory/mpsa-241155-privilege-escalation-and-os-command-injection-vulnerabilities-in-cellular-routers,-secure-routers,-and-netwo"}], "solutions": [{"lang": "en", "supportingMedia": [{"base64": false, "type": "text/html", "value": "<p>Moxa has developed appropriate solutions to address vulnerability. The solutions for the affected products are listed below.</p><p></p><ul><li>EDR-810 Series: Upgrade to <a target=\"_blank\" rel=\"nofollow\" href=\"https://www.moxa.com/en/products/industrial-network-infrastructure/secure-routers/secure-routers/edr-810-series#resources\">the firmware version 3.14</a>&nbsp;or later</li><li>EDR-8010 Series: Upgrade to <a target=\"_blank\" rel=\"nofollow\" href=\"https://www.moxa.com/en/products/industrial-network-infrastructure/secure-routers/secure-routers/edr-8010-series#resources\">the firmware version 3.14</a><span style=\"background-color: var(--wht);\">&nbsp;or later</span></li><li>EDR-G902 Series: Upgrade to <a target=\"_blank\" rel=\"nofollow\" href=\"https://www.moxa.com/en/products/industrial-network-infrastructure/secure-routers/secure-routers/edr-g902-series#resources\">the firmware version 3.14</a><span style=\"background-color: var(--wht);\">&nbsp;or later</span></li><li>EDR-G903 Series: Upgrade to <a target=\"_blank\" rel=\"nofollow\" href=\"https://www.moxa.com/en/products/industrial-network-infrastructure/secure-routers/secure-routers/edr-g903-series#resources\">the firmware version 3.14</a><span style=\"background-color: var(--wht);\">&nbsp;or later</span></li><li>EDR-G9004 Series: Upgrade to <a target=\"_blank\" rel=\"nofollow\" href=\"https://www.moxa.com/en/products/industrial-network-infrastructure/secure-routers/secure-routers/edr-g9004-series#resources\">the firmware version 3.14</a><span style=\"background-color: var(--wht);\">&nbsp;or later</span></li><li>EDR-G9010 Series: Upgrade to <a target=\"_blank\" rel=\"nofollow\" href=\"https://www.moxa.com/en/products/industrial-network-infrastructure/secure-routers/secure-routers/edr-g9010-series#resources\">the firmware version 3.14</a><span style=\"background-color: var(--wht);\">&nbsp;or later</span></li><li>EDF-G1002-BP Series: Upgrade to <a target=\"_blank\" rel=\"nofollow\" href=\"https://www.moxa.com/en/products/industrial-network-infrastructure/network-security-appliance/edf-g1002-bp-series#resources\">the firmware version 3.14</a><span style=\"background-color: var(--wht);\">&nbsp;or later</span></li><li>NAT-102 Series: An official patch or firmware update is not currently available for this product. Please refer to the Mitigations section below for recommended measures to address the vulnerability.</li><li>OnCell G4302-LTE4 Series: Please contact <a target=\"_blank\" rel=\"nofollow\" href=\"https://www.moxa.com/support/support/technical-support\">Moxa Technical Support</a><span style=\"background-color: var(--wht);\">&nbsp;for the security patch</span></li><li><span style=\"background-color: var(--wht);\">TN-4900 Series:&nbsp;Please contact <a target=\"_blank\" rel=\"nofollow\" href=\"https://www.moxa.com/support/support/technical-support\">Moxa Technical Support</a><span style=\"background-color: var(--wht);\">&nbsp;for the security patch</span></span></li></ul><p></p><br><br>"}], "value": "Moxa has developed appropriate solutions to address vulnerability. The solutions for the affected products are listed below.\n\n\n\n  *  EDR-810 Series: Upgrade to  the firmware version 3.14 https://www.moxa.com/en/products/industrial-network-infrastructure/secure-routers/secure-routers/edr-810-series#resources  or later\n  *  EDR-8010 Series: Upgrade to  the firmware version 3.14 https://www.moxa.com/en/products/industrial-network-infrastructure/secure-routers/secure-routers/edr-8010-series#resources  or later\n  *  EDR-G902 Series: Upgrade to  the firmware version 3.14 https://www.moxa.com/en/products/industrial-network-infrastructure/secure-routers/secure-routers/edr-g902-series#resources  or later\n  *  EDR-G903 Series: Upgrade to  the firmware version 3.14 https://www.moxa.com/en/products/industrial-network-infrastructure/secure-routers/secure-routers/edr-g903-series#resources  or later\n  *  EDR-G9004 Series: Upgrade to  the firmware version 3.14 https://www.moxa.com/en/products/industrial-network-infrastructure/secure-routers/secure-routers/edr-g9004-series#resources  or later\n  *  EDR-G9010 Series: Upgrade to  the firmware version 3.14 https://www.moxa.com/en/products/industrial-network-infrastructure/secure-routers/secure-routers/edr-g9010-series#resources  or later\n  *  EDF-G1002-BP Series: Upgrade to  the firmware version 3.14 https://www.moxa.com/en/products/industrial-network-infrastructure/network-security-appliance/edf-g1002-bp-series#resources  or later\n  *  NAT-102 Series: An official patch or firmware update is not currently available for this product. Please refer to the Mitigations section below for recommended measures to address the vulnerability.\n  *  OnCell G4302-LTE4 Series: Please contact  Moxa Technical Support https://www.moxa.com/support/support/technical-support  for the security patch\n  *  TN-4900 Series: Please contact  Moxa Technical Support https://www.moxa.com/support/support/technical-support  for the security patch"}], "source": {"discovery": "EXTERNAL"}, "title": "Privilege Escalation in Cellular Router, Secure Router, and Network Security Appliances", "workarounds": [{"lang": "en", "supportingMedia": [{"base64": false, "type": "text/html", "value": "<ul><li><p>Minimize network exposure to ensure the device is not accessible from the Internet.</p></li><li><p><span style=\"background-color: var(--wht);\">Limit SSH access to trusted IP addresses and networks using firewall rules or TCP wrappers.</span></p></li><li><p><span style=\"background-color: var(--wht);\">Implement IDS or Intrusion Prevention System (IPS) to detect and prevent exploitation attempts. These systems can provide an additional layer of defense by monitoring network traffic for signs of attacks.</span></p></li></ul>"}], "value": "*  Minimize network exposure to ensure the device is not accessible from the Internet.\n\n\n  *  Limit SSH access to trusted IP addresses and networks using firewall rules or TCP wrappers.\n\n\n  *  Implement IDS or Intrusion Prevention System (IPS) to detect and prevent exploitation attempts. These systems can provide an additional layer of defense by monitoring network traffic for signs of attacks."}], "x_generator": {"engine": "Vulnogram 0.2.0"}}}}