#!/bin/bash

# Ubuntu 20.04 better-sqlite3 编译问题修复脚本

echo "======================================="
echo "修复 Ubuntu 20.04 better-sqlite3 编译问题"
echo "======================================="

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 升级编译工具链
upgrade_compiler() {
    print_status "升级编译工具链以支持C++20..."
    
    # 添加新的GCC源
    sudo apt install -y software-properties-common
    sudo add-apt-repository -y ppa:ubuntu-toolchain-r/test
    sudo apt update
    
    # 安装GCC 10
    sudo apt install -y gcc-10 g++-10
    
    # 设置为默认编译器
    sudo update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-10 100
    sudo update-alternatives --install /usr/bin/g++ g++ /usr/bin/g++-10 100
    
    print_status "编译器升级完成"
    print_status "当前GCC版本: $(gcc --version | head -n1)"
    print_status "当前G++版本: $(g++ --version | head -n1)"
}

# 清理并重新安装
reinstall_dependencies() {
    print_status "清理旧的安装文件..."
    rm -rf node_modules package-lock.json
    
    print_status "重新安装依赖..."
    npm install --legacy-peer-deps
}

# 如果还是失败，尝试跳过编译
install_without_compile() {
    print_warning "标准安装仍然失败，尝试跳过编译步骤..."
    npm install --legacy-peer-deps --ignore-scripts
    
    print_warning "注意：better-sqlite3可能无法正常工作，建议考虑替换为其他数据库库"
}

# 主函数
main() {
    print_status "开始修复better-sqlite3编译问题..."
    
    # 检查是否为Ubuntu 20.04
    if [[ $(lsb_release -rs) != "20.04" ]]; then
        print_warning "此脚本专为Ubuntu 20.04设计"
    fi
    
    # 升级编译器
    upgrade_compiler
    
    # 重新安装
    reinstall_dependencies || {
        print_warning "标准重新安装失败，尝试备用方案..."
        install_without_compile || {
            print_error "所有方案都失败了。建议："
            print_error "1. 升级到Ubuntu 22.04"
            print_error "2. 或修改项目代码，使用其他数据库库替代better-sqlite3"
            exit 1
        }
    }
    
    print_status "修复完成！"
}

main "$@" 