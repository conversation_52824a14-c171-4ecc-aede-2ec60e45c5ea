const axios = require('axios');

const TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwic3ViIjozLCJpYXQiOjE3NTA1MDAxOTAsImV4cCI6MTc1MDU4NjU5MH0.LNTSQMmbnfitsxMtxVkh9byARxRtsBQDcaQudXzvBWw";
const API_URL = 'http://localhost:3001/api/v1/database/filter-satellites';

async function testPagination() {
  console.log('🔍 测试分页查询是否导致重复数据...\n');
  
  // 收集所有记录
  const allRecords = [];
  const noradIdCounts = new Map();
  
  let page = 1;
  const limit = 100;
  
  while (true) {
    try {
      console.log(`📄 查询第 ${page} 页...`);
      
      const { data } = await axios.post(
        API_URL,
        { constellationName: 'Starlink', page, limit },
        {
          headers: {
            Accept: 'application/json',
            Authorization: `Bearer ${TOKEN}`,
            'Content-Type': 'application/json',
          },
        },
      );

      if (!data?.success) {
        console.log(`❌ 第 ${page} 页请求失败`);
        break;
      }

      console.log(`   获取 ${data.results.length} 条记录`);
      
      // 分析每条记录
      data.results.forEach((item, index) => {
        const noradId = item.norad_id?.[0]?.value;
        if (noradId) {
          // 记录 NORAD_ID 出现次数
          if (!noradIdCounts.has(noradId)) {
            noradIdCounts.set(noradId, []);
          }
          noradIdCounts.get(noradId).push({
            page,
            index,
            id: item.id,
            name: (item.alternative_name ?? []).map(n => n.value).join(';')
          });
        }
        
        allRecords.push({
          page,
          index,
          noradId,
          cosparId: item.cospar_id?.[0]?.value,
          name: (item.alternative_name ?? []).map(n => n.value).join(';'),
          dbId: item.id
        });
      });

      if (data.results.length < limit) break;
      page += 1;
      
      // 限制测试范围，避免查询太多
      if (page > 10) {
        console.log('⚠️  限制测试到前10页');
        break;
      }
      
    } catch (error) {
      console.error(`❌ 第 ${page} 页查询失败:`, error.message);
      break;
    }
  }
  
  console.log(`\n📊 分析结果:`);
  console.log(`总记录数: ${allRecords.length}`);
  console.log(`唯一 NORAD_ID 数量: ${noradIdCounts.size}`);
  
  // 查找重复的 NORAD_ID
  const duplicates = Array.from(noradIdCounts.entries())
    .filter(([noradId, occurrences]) => occurrences.length > 1)
    .sort((a, b) => b[1].length - a[1].length);
  
  console.log(`重复的 NORAD_ID 数量: ${duplicates.length}`);
  
  if (duplicates.length > 0) {
    console.log(`\n🔍 重复最多的前5个 NORAD_ID:`);
    duplicates.slice(0, 5).forEach(([noradId, occurrences]) => {
      console.log(`\nNORAD_ID: ${noradId} (重复 ${occurrences.length} 次)`);
      console.log(`名称: ${occurrences[0].name}`);
      occurrences.forEach((occ, i) => {
        console.log(`  ${i+1}. 页码: ${occ.page}, 索引: ${occ.index}, DB_ID: ${occ.id}`);
      });
    });
  } else {
    console.log(`✅ 在前${page-1}页中没有发现重复的 NORAD_ID`);
  }
}

async function testSpecificNoradId() {
  const testId = 58138;
  console.log(`\n🔍 测试特定 NORAD_ID: ${testId}`);
  
  // 1. 单独查询
  try {
    const { data } = await axios.post(
      API_URL,
      { satelliteName: `STARLINK-30769` },
      {
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer ${TOKEN}`,
          'Content-Type': 'application/json',
        },
      },
    );
    
    console.log(`单独查询结果: ${data.success ? data.total : 0} 条记录`);
    if (data.success && data.results.length > 0) {
      const record = data.results[0];
      console.log(`  NORAD_ID: ${record.norad_id?.[0]?.value}`);
      console.log(`  DB_ID: ${record.id}`);
    }
  } catch (error) {
    console.error(`单独查询失败:`, error.message);
  }
  
  // 2. 在分页结果中查找
  console.log(`\n在分页查询中查找 NORAD_ID ${testId}...`);
  let found = false;
  
  for (let page = 1; page <= 10; page++) {
    try {
      const { data } = await axios.post(
        API_URL,
        { constellationName: 'Starlink', page, limit: 100 },
        {
          headers: {
            Accept: 'application/json',
            Authorization: `Bearer ${TOKEN}`,
            'Content-Type': 'application/json',
          },
        },
      );

      if (data?.success) {
        const foundRecords = data.results.filter(item => 
          item.norad_id?.[0]?.value === testId
        );
        
        if (foundRecords.length > 0) {
          console.log(`  在第 ${page} 页找到 ${foundRecords.length} 条记录`);
          foundRecords.forEach((record, i) => {
            console.log(`    记录 ${i+1}: DB_ID=${record.id}, 名称=${(record.alternative_name ?? []).map(n => n.value).join(';')}`);
          });
          found = true;
        }
      }
    } catch (error) {
      console.error(`第 ${page} 页查询失败:`, error.message);
    }
  }
  
  if (!found) {
    console.log(`  在前10页中未找到 NORAD_ID ${testId}`);
  }
}

async function main() {
  await testPagination();
  await testSpecificNoradId();
}

main(); 