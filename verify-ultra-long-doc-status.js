/**
 * 验证超长文档翻译状态
 * 检查bbfa7e8c6c4ff26acf0e27f7fff9ba97文档的翻译字段是否已被清空
 */

const { Client } = require('@elastic/elasticsearch');

async function verifyDocumentStatus() {
  console.log('🔍 验证超长文档翻译状态...');
  
  try {
    // 连接Elasticsearch
    const client = new Client({
      node: process.env.ELASTICSEARCH_NODE || 'http://localhost:9200'
    });
    
    const documentId = 'bbfa7e8c6c4ff26acf0e27f7fff9ba97';
    
    // 获取文档
    const response = await client.get({
      index: 'news_space',
      id: documentId
    });
    
    const doc = response.body._source;
    
    console.log('\n📄 文档信息:');
    console.log(`ID: ${documentId}`);
    console.log(`标题: ${doc.title ? doc.title.substring(0, 80) + '...' : '无标题'}`);
    console.log(`内容长度: ${doc.content ? doc.content.length : 0} 字符`);
    
    console.log('\n🈴 翻译状态:');
    console.log(`标题翻译 (title_cn): ${doc.title_cn ? `"${doc.title_cn.substring(0, 50)}..."` : '空'}`);
    console.log(`摘要翻译 (summary_cn): ${doc.summary_cn ? `"${doc.summary_cn.substring(0, 50)}..."` : '空'}`);
    console.log(`内容翻译 (content_cn): ${doc.content_cn ? `长度${doc.content_cn.length}字符` : '空'}`);
    console.log(`主题词 (themes_cn): ${doc.themes_cn ? `"${doc.themes_cn}"` : '空'}`);
    
    // 检查是否按预期清空
    const isCleared = !doc.title_cn && !doc.content_cn;
    
    console.log('\n✅ 验证结果:');
    if (isCleared) {
      console.log('🎯 成功！超长文档的翻译字段已被正确清空');
    } else {
      console.log('⚠️  翻译字段未完全清空，可能需要重新处理');
    }
    
    // 显示处理建议
    if (doc.content && doc.content.length > 100000) {
      console.log('\n💡 处理建议:');
      console.log(`- 文档内容长度: ${doc.content.length} 字符`);
      console.log('- 已超过100K阈值，翻译服务会自动跳过');
      console.log('- 如需翻译，建议先进行内容摘要或分段处理');
    }
    
  } catch (error) {
    console.error('❌ 验证失败:', error.message);
  }
}

// 运行验证
verifyDocumentStatus()
  .then(() => {
    console.log('\n🎉 验证完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 验证失败:', error);
    process.exit(1);
  }); 