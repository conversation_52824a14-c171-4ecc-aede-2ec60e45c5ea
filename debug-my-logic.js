const axios = require('axios');

const TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwic3ViIjozLCJpYXQiOjE3NTA1MDE5NzYsImV4cCI6MTc1MDU4ODM3Nn0.ltAVJ5xI7ut6Khe5ncsMpZzgNFjlpa3b_qYWE7mld2o";
const API_URL = 'http://localhost:3001/api/v1/database/filter-satellites';

async function debugMyLogic() {
  console.log('🔍 调试我的逻辑错误...\n');
  
  const allRecords = [];
  const noradIdTracker = new Map();
  
  // 只测试前3页，避免过多数据
  for (let page = 1; page <= 3; page++) {
    console.log(`📄 查询第 ${page} 页...`);
    
    try {
      const { data } = await axios.post(
        API_URL,
        { constellationName: 'Starlink', page, limit: 100 },
        {
          headers: {
            Accept: 'application/json',
            Authorization: `Bearer ${TOKEN}`,
            'Content-Type': 'application/json',
          },
        },
      );

      if (data?.success) {
        console.log(`  第 ${page} 页获取 ${data.results.length} 条记录`);
        
        data.results.forEach((item, index) => {
          const noradId = item.norad_id?.[0]?.value;
          
          if (noradId) {
            // 跟踪每个 NORAD_ID
            if (!noradIdTracker.has(noradId)) {
              noradIdTracker.set(noradId, []);
            }
            noradIdTracker.get(noradId).push({
              page,
              index,
              dbId: item.id
            });
            
            allRecords.push({
              page,
              index,
              noradId,
              dbId: item.id,
              name: (item.alternative_name ?? []).map(n => n.value).join(';')
            });
          }
        });
      }
    } catch (error) {
      console.error(`第 ${page} 页查询失败:`, error.message);
    }
  }
  
  console.log(`\n📊 统计结果:`);
  console.log(`总记录数: ${allRecords.length}`);
  console.log(`唯一 NORAD_ID 数量: ${noradIdTracker.size}`);
  
  // 查找重复
  const duplicates = Array.from(noradIdTracker.entries())
    .filter(([noradId, occurrences]) => occurrences.length > 1);
  
  console.log(`重复的 NORAD_ID 数量: ${duplicates.length}`);
  
  if (duplicates.length > 0) {
    console.log(`\n❌ 发现重复数据:`);
    duplicates.forEach(([noradId, occurrences]) => {
      console.log(`  NORAD_ID ${noradId}: 出现 ${occurrences.length} 次`);
      occurrences.forEach((occ, i) => {
        console.log(`    ${i+1}. 页码: ${occ.page}, 索引: ${occ.index}, DB_ID: ${occ.dbId}`);
      });
    });
    
    // 验证第一个重复的 NORAD_ID
    if (duplicates.length > 0) {
      const firstDuplicate = duplicates[0][0];
      console.log(`\n🔍 验证 NORAD_ID ${firstDuplicate} 是否真的重复:`);
      
      try {
        const { data } = await axios.post(
          API_URL,
          { noradId: firstDuplicate.toString(), page: 1, limit: 10 },
          {
            headers: {
              Accept: 'application/json',
              Authorization: `Bearer ${TOKEN}`,
              'Content-Type': 'application/json',
            },
          },
        );
        
        console.log(`  单独查询结果: ${data.success ? data.total : 0} 条记录`);
        if (data.success && data.results.length > 0) {
          console.log(`  DB_ID: ${data.results[0].id}`);
        }
      } catch (error) {
        console.error(`  验证查询失败:`, error.message);
      }
    }
  } else {
    console.log(`✅ 在前3页中没有发现重复的 NORAD_ID`);
  }
}

debugMyLogic(); 