#!/bin/bash

# 修复 FieldMatchConfig 重复定义问题
echo "修复 FieldMatchConfig 重复定义问题..."

# 在 satellite-query.dto.ts 中替换所有 FieldMatchConfig 为 SatelliteFieldMatchConfig
perl -i -pe 's/FieldMatchConfig/SatelliteFieldMatchConfig/g' src/elasticsearch/dto/satellite-query.dto.ts

# 在 debris-query.dto.ts 中替换所有 FieldMatchConfig 为 DebrisFieldMatchConfig
perl -i -pe 's/FieldMatchConfig/DebrisFieldMatchConfig/g' src/elasticsearch/dto/debris-query.dto.ts

# 更新 debris-query.dto.ts 的导入
perl -i -pe 's/export enum MatchType {/import { MatchType, DebrisFieldMatchConfig } from ".\/base-query.dto";\n\nexport enum MatchType {/' src/elasticsearch/dto/debris-query.dto.ts

# 移除 debris-query.dto.ts 中重复的定义
perl -i -pe 'BEGIN{$remove=0} /^export enum MatchType/&&$remove==0 && ($remove=1); $remove==1 && /^}/ && ($remove=0); !$remove || /^import/' src/elasticsearch/dto/debris-query.dto.ts

echo "FieldMatchConfig 替换完成！"

# 检查替换结果
echo "检查替换结果："
echo "satellite-query.dto.ts 中的 SatelliteFieldMatchConfig 数量："
grep -c "SatelliteFieldMatchConfig" src/elasticsearch/dto/satellite-query.dto.ts

echo "debris-query.dto.ts 中的 DebrisFieldMatchConfig 数量："
grep -c "DebrisFieldMatchConfig" src/elasticsearch/dto/debris-query.dto.ts

echo "完成！" 