#!/bin/bash

# 测试发射承包商API接口
# 需要提供有效的JWT令牌

# 设置API地址
API_URL="http://localhost:3001/api/v1/database/launch-contractors"

# 设置JWT令牌 - 更新为系统中的有效令牌
AUTH_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwiaWQiOjEsInJvbGUiOiJhZG1pbiIsImlhdCI6MTcxMTAyMjY2MCwiZXhwIjoxODY5MjE4NjYwfQ.n8Qj1OvulI4f0N3u-Gk3ccDhHYFlC7LmCADUcDjXqVI"

echo "正在测试发射承包商API接口..."

# 发送GET请求
curl -s -X GET \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  $API_URL > api-launch-contractors-result.json

# 检查响应
if [ $? -eq 0 ]; then
  echo "API请求成功，结果已保存到api-launch-contractors-result.json"
  # 显示结果中的前几个承包商
  echo "结果示例:"
  cat api-launch-contractors-result.json | head -n 20
else
  echo "API请求失败"
fi 