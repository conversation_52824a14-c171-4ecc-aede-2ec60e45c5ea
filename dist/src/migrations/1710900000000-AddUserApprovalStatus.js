"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddUserApprovalStatus1710900000000 = void 0;
const typeorm_1 = require("typeorm");
class AddUserApprovalStatus1710900000000 {
    constructor() {
        this.name = 'AddUserApprovalStatus1710900000000';
    }
    async up(queryRunner) {
        await queryRunner.addColumn('users', new typeorm_1.TableColumn({
            name: 'approval_status',
            type: 'varchar',
            length: '20',
            default: "'pending'",
            comment: '用户审批状态：pending-待审批，approved-已批准，rejected-已拒绝'
        }));
        await queryRunner.addColumn('users', new typeorm_1.TableColumn({
            name: 'approved_at',
            type: 'timestamp with time zone',
            isNullable: true,
            comment: '审批通过时间'
        }));
        await queryRunner.addColumn('users', new typeorm_1.TableColumn({
            name: 'approved_by',
            type: 'integer',
            isNullable: true,
            comment: '审批人用户ID'
        }));
        await queryRunner.addColumn('users', new typeorm_1.TableColumn({
            name: 'rejection_reason',
            type: 'varchar',
            length: '500',
            isNullable: true,
            comment: '拒绝申请的原因'
        }));
        await queryRunner.query(`
      UPDATE users 
      SET approval_status = 'approved', approved_at = created_at 
      WHERE approval_status = 'pending'
    `);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('users', 'rejection_reason');
        await queryRunner.dropColumn('users', 'approved_by');
        await queryRunner.dropColumn('users', 'approved_at');
        await queryRunner.dropColumn('users', 'approval_status');
    }
}
exports.AddUserApprovalStatus1710900000000 = AddUserApprovalStatus1710900000000;
//# sourceMappingURL=1710900000000-AddUserApprovalStatus.js.map