"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateApprovalFieldComments1748500000000 = void 0;
class UpdateApprovalFieldComments1748500000000 {
    constructor() {
        this.name = 'UpdateApprovalFieldComments1748500000000';
    }
    async up(queryRunner) {
        await queryRunner.query(`
      COMMENT ON COLUMN users.approved_at IS '审批操作时间（包括批准和拒绝）'
    `);
        await queryRunner.query(`
      COMMENT ON COLUMN users.approved_by IS '执行审批操作的管理员用户ID（包括批准和拒绝）'
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      COMMENT ON COLUMN users.approved_at IS '审批通过时间'
    `);
        await queryRunner.query(`
      COMMENT ON COLUMN users.approved_by IS '审批人用户ID'
    `);
    }
}
exports.UpdateApprovalFieldComments1748500000000 = UpdateApprovalFieldComments1748500000000;
//# sourceMappingURL=1748500000000-UpdateApprovalFieldComments.js.map