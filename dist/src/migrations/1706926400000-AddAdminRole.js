"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddAdminRole1706926400000 = void 0;
const bcrypt = require("bcrypt");
class AddAdminRole1706926400000 {
    async up(queryRunner) {
        await queryRunner.query(`
      COMMENT ON COLUMN users.role IS '用户角色: admin(管理员), free(免费用户), premium(高级用户), enterprise(企业用户), government(政府用户)';
    `);
        const adminExists = await queryRunner.query(`
      SELECT id FROM users WHERE username = 'admin' LIMIT 1;
    `);
        if (adminExists.length === 0) {
            const adminPassword = 'admin123';
            const hashedPassword = await bcrypt.hash(adminPassword, 10);
            await queryRunner.query(`
        INSERT INTO users (username, password, email, role, is_active, created_at, updated_at)
        VALUES ('admin', $1, '<EMAIL>', 'admin', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
      `, [hashedPassword]);
            console.log('✅ 默认管理员用户已创建');
            console.log('   用户名: admin');
            console.log('   密码: admin123');
            console.log('   邮箱: <EMAIL>');
            console.log('   ⚠️  请在生产环境中修改默认密码！');
        }
        else {
            console.log('ℹ️  管理员用户已存在，跳过创建');
        }
        await queryRunner.query(`
      ALTER TABLE users 
      ADD CONSTRAINT chk_user_role 
      CHECK (role IN ('admin', 'free', 'premium', 'enterprise', 'government'));
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE users DROP CONSTRAINT IF EXISTS chk_user_role;
    `);
        await queryRunner.query(`
      DELETE FROM users WHERE username = 'admin' AND email = '<EMAIL>';
    `);
        await queryRunner.query(`
      COMMENT ON COLUMN users.role IS NULL;
    `);
        console.log('✅ 管理员角色支持已回滚');
    }
}
exports.AddAdminRole1706926400000 = AddAdminRole1706926400000;
//# sourceMappingURL=1706926400000-AddAdminRole.js.map