"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateUsersTable1706926300000 = void 0;
const typeorm_1 = require("typeorm");
class CreateUsersTable1706926300000 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'users',
            columns: [
                {
                    name: 'id',
                    type: 'serial',
                    isPrimary: true,
                },
                {
                    name: 'username',
                    type: 'varchar',
                    length: '100',
                    isUnique: true,
                    isNullable: false,
                },
                {
                    name: 'password',
                    type: 'varchar',
                    length: '100',
                    isNullable: false,
                },
                {
                    name: 'email',
                    type: 'varchar',
                    length: '100',
                    isUnique: true,
                    isNullable: false,
                },
                {
                    name: 'role',
                    type: 'varchar',
                    length: '20',
                    default: "'free'",
                    isNullable: false,
                },
                {
                    name: 'api_calls_today',
                    type: 'integer',
                    default: 0,
                    isNullable: false,
                },
                {
                    name: 'downloads_today',
                    type: 'integer',
                    default: 0,
                    isNullable: false,
                },
                {
                    name: 'last_api_reset',
                    type: 'timestamp with time zone',
                    default: 'CURRENT_TIMESTAMP',
                    isNullable: false,
                },
                {
                    name: 'avatar_url',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'is_active',
                    type: 'boolean',
                    default: true,
                    isNullable: false,
                },
                {
                    name: 'created_at',
                    type: 'timestamp with time zone',
                    default: 'CURRENT_TIMESTAMP',
                    isNullable: false,
                },
                {
                    name: 'updated_at',
                    type: 'timestamp with time zone',
                    default: 'CURRENT_TIMESTAMP',
                    isNullable: false,
                },
                {
                    name: 'deleted_at',
                    type: 'timestamp with time zone',
                    isNullable: true,
                },
            ],
            indices: [
                {
                    name: 'idx_username_unique',
                    columnNames: ['username'],
                    isUnique: true,
                },
                {
                    name: 'idx_email_unique',
                    columnNames: ['email'],
                    isUnique: true,
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable('users');
    }
}
exports.CreateUsersTable1706926300000 = CreateUsersTable1706926300000;
//# sourceMappingURL=1706926300000-CreateUsersTable.js.map