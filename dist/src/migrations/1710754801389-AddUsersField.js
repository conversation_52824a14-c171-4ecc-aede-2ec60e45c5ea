"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddUsersField1710754801389 = void 0;
class AddUsersField1710754801389 {
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "satellites" ADD COLUMN IF NOT EXISTS "users" JSONB DEFAULT '[]'`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "satellites" DROP COLUMN IF EXISTS "users"`);
    }
}
exports.AddUsersField1710754801389 = AddUsersField1710754801389;
//# sourceMappingURL=1710754801389-AddUsersField.js.map