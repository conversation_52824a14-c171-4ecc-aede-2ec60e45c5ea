"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const fs = require("fs");
const database_service_1 = require("./services/database.service");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule, {
        logger: ['log', 'error', 'warn', 'debug'],
    });
    try {
        console.log('调用服务方法测试...');
        const databaseService = app.get(database_service_1.DatabaseService);
        const result = await databaseService.getSatellitePurposes();
        console.log(`获取到 ${result.purposes.length} 个卫星用途`);
        fs.writeFileSync('api-satellite-purposes-result.json', JSON.stringify(result, null, 2));
        console.log('结果已写入 api-satellite-purposes-result.json 文件');
        const untranslatedItems = result.purposes.filter((p) => !/[\u4e00-\u9fa5]/.test(p.cn));
        if (untranslatedItems.length > 0) {
            console.log(`警告：发现 ${untranslatedItems.length} 个未翻译的卫星用途项:`);
            untranslatedItems.forEach((item, index) => {
                if (index < 10) {
                    console.log(`- ${item.en}: ${item.cn}`);
                }
            });
            if (untranslatedItems.length > 10) {
                console.log(`... 以及其他 ${untranslatedItems.length - 10} 个项目`);
            }
        }
        else {
            console.log('所有卫星用途项均已翻译成中文，无未翻译项');
        }
    }
    catch (error) {
        console.error('测试过程中发生错误:', error.message);
        console.error('错误堆栈:', error.stack);
    }
    finally {
        await app.close();
    }
}
bootstrap();
//# sourceMappingURL=test-api-satellite-purposes.js.map