"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const database_service_1 = require("./services/database.service");
const fs = require("fs");
async function testSatellitePurposes() {
    try {
        const app = await core_1.NestFactory.create(app_module_1.AppModule, {
            logger: ['log', 'error', 'warn'],
        });
        const databaseService = app.get(database_service_1.DatabaseService);
        console.log('调用服务方法测试...');
        const result = await databaseService.getSatellitePurposes();
        fs.writeFileSync('api-satellite-purposes-result.json', JSON.stringify(result, null, 2));
        console.log(`获取到 ${result.purposes.length} 个卫星用途`);
        console.log('结果已写入 api-satellite-purposes-result.json 文件');
        const untranslatedItems = result.purposes.filter((item) => !item.cn ||
            !/[\u4e00-\u9fa5]/.test(item.cn));
        if (untranslatedItems.length > 0) {
            console.log('警告: 发现未翻译的项目:');
            untranslatedItems.forEach((item) => {
                console.log(`  - ${item.en}: "${item.cn}"`);
            });
        }
        else {
            console.log('所有卫星用途项均已翻译成中文，无未翻译项');
        }
        await app.close();
    }
    catch (error) {
        console.error('测试过程中发生错误:', error);
    }
}
testSatellitePurposes();
//# sourceMappingURL=test-satellite-purposes.js.map