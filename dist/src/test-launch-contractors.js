"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const database_service_1 = require("./services/database.service");
const fs = require("fs");
const path = require("path");
async function bootstrap() {
    try {
        const app = await core_1.NestFactory.create(app_module_1.AppModule);
        const databaseService = app.get(database_service_1.DatabaseService);
        console.log('正在调用getLaunchContractors方法...');
        const result = await databaseService.getLaunchContractors();
        console.log(`成功获取${result.contractors.length}个发射承包商`);
        const outputPath = path.join(process.cwd(), 'launch-contractors-result.json');
        fs.writeFileSync(outputPath, JSON.stringify(result, null, 2));
        console.log(`结果已写入文件: ${outputPath}`);
        await app.close();
    }
    catch (error) {
        console.error('测试失败:', error);
        process.exit(1);
    }
}
bootstrap();
//# sourceMappingURL=test-launch-contractors.js.map