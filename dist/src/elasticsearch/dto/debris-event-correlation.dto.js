"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventToDebrisQueryDto = exports.DebrisToEventQueryDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class DebrisToEventQueryDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'COSPAR ID (格式: YYYY-XXX[A-Z])',
        example: '1999-025EZ'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Matches)(/^\d{4}-\d{3}[A-Z]*$/, {
        message: 'COSPAR ID格式必须为YYYY-XXX[A-Z]'
    }),
    __metadata("design:type", String)
], DebrisToEventQueryDto.prototype, "cospar_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '发射日期',
        example: '1999-05-20',
        required: false
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], DebrisToEventQueryDto.prototype, "launch_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '首次纪元时间',
        example: '1999-05-20T00:00:00Z',
        required: false
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], DebrisToEventQueryDto.prototype, "first_epoch", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '碎片名称',
        example: 'FENGYUN 1C DEB',
        required: false
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], DebrisToEventQueryDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最小关联置信度 (0-1)',
        example: 0.5,
        minimum: 0,
        maximum: 1
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1),
    __metadata("design:type", Number)
], DebrisToEventQueryDto.prototype, "min_confidence", void 0);
exports.DebrisToEventQueryDto = DebrisToEventQueryDto;
class EventToDebrisQueryDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '碎片标识 (格式: YYYY-XXX[A-Z])',
        example: '1964-006A'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Matches)(/^\d{4}-\d{3}[A-Z]*$/, {
        message: '碎片标识格式必须为YYYY-XXX[A-Z]'
    }),
    __metadata("design:type", String)
], EventToDebrisQueryDto.prototype, "piece", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '事件日期',
        example: '1964-01-01T00:00:00Z'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EventToDebrisQueryDto.prototype, "sdate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '事件类型',
        example: 'Collision'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EventToDebrisQueryDto.prototype, "event_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '编目碎片数量',
        example: 10
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], EventToDebrisQueryDto.prototype, "cataloguedFragments", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最小关联置信度 (0-1)',
        example: 0.5,
        minimum: 0,
        maximum: 1
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1),
    __metadata("design:type", Number)
], EventToDebrisQueryDto.prototype, "min_confidence", void 0);
exports.EventToDebrisQueryDto = EventToDebrisQueryDto;
//# sourceMappingURL=debris-event-correlation.dto.js.map