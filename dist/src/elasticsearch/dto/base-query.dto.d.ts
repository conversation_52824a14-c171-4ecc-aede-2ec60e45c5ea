export declare enum MatchType {
    EXACT = "exact",
    FUZZY = "fuzzy"
}
export declare class BaseFieldMatchConfig {
    matchType: MatchType;
    value: string | number;
}
export declare class SatelliteFieldMatchConfig extends BaseFieldMatchConfig {
    similarity_threshold?: number;
}
export declare class <PERSON>brisFieldMatchConfig extends BaseFieldMatchConfig {
}
export declare class SortConfig {
    field: string;
    order: 'asc' | 'desc';
}
export declare class BaseQueryDto {
    page?: number;
    limit?: number;
    sort?: Record<string, 'asc' | 'desc'>;
    keyword?: string;
}
