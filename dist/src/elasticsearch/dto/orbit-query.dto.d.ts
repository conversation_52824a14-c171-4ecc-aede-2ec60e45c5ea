import { BaseQueryDto } from './base-query.dto';
export declare enum OrbitType {
    LEO = "LEO",
    MEO = "MEO",
    GEO = "GEO"
}
export declare class OrbitQueryDto extends BaseQueryDto {
    satelliteId?: string;
    minAltitude?: number;
    maxAltitude?: number;
    startTime?: string;
    endTime?: string;
    satellite_name?: string;
    fuzzy_match?: boolean;
    norad_id?: number;
    cospar_id?: string;
    orbit_type?: OrbitType;
    min_altitude?: number;
    max_altitude?: number;
    min_inclination?: number;
    max_inclination?: number;
    min_raan?: number;
    max_raan?: number;
    min_eccentricity?: number;
    max_eccentricity?: number;
    min_arg_perigee?: number;
    max_arg_perigee?: number;
    min_mean_anomaly?: number;
    max_mean_anomaly?: number;
    min_revolutions_per_day?: number;
    max_revolutions_per_day?: number;
    min_orbital_period?: number;
    max_orbital_period?: number;
    min_semi_major_axis?: number;
    max_semi_major_axis?: number;
}
