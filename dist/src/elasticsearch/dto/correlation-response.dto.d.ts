import { DebrisDocument } from '../types/elasticsearch.types';
export declare class MatchScores {
    cosparMatch: number;
    timeScore: number;
    nameScore: number;
}
export declare class EventDetails {
    piece: string;
    sdate: string;
    name?: string;
    event_type: string;
    cataloguedFragments: number;
    object_class?: string;
    source: 'discos' | 'jonathan';
}
export declare class CorrelationResultDto {
    event_id: string;
    confidence: number;
    scores: MatchScores;
    event_details: EventDetails;
}
export declare class CorrelationResponseDto {
    total: number;
    hits: CorrelationResultDto[];
}
export declare class DebrisCorrelationResult {
    debris_id: string;
    confidence: number;
    scores: MatchScores;
    debris_details: DebrisDocument;
}
export declare class EventCorrelationResultDto {
    debris_list: DebrisCorrelationResult[];
}
