"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventCorrelationResultDto = exports.DebrisCorrelationResult = exports.CorrelationResponseDto = exports.CorrelationResultDto = exports.EventDetails = exports.MatchScores = void 0;
const swagger_1 = require("@nestjs/swagger");
class MatchScores {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'COSPAR ID匹配分数',
        example: 0.8
    }),
    __metadata("design:type", Number)
], MatchScores.prototype, "cosparMatch", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '时间匹配分数',
        example: 0.6
    }),
    __metadata("design:type", Number)
], MatchScores.prototype, "timeScore", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '名称相似度分数',
        example: 0.7
    }),
    __metadata("design:type", Number)
], MatchScores.prototype, "nameScore", void 0);
exports.MatchScores = MatchScores;
class EventDetails {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '碎片标识',
        example: '1999-025A'
    }),
    __metadata("design:type", String)
], EventDetails.prototype, "piece", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '事件时间',
        example: '2007-01-11T22:26:00Z'
    }),
    __metadata("design:type", String)
], EventDetails.prototype, "sdate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '事件名称',
        example: 'Fengyun 1C',
        required: false
    }),
    __metadata("design:type", String)
], EventDetails.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '事件类型',
        example: 'Collision'
    }),
    __metadata("design:type", String)
], EventDetails.prototype, "event_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '编目碎片数量',
        example: 3428
    }),
    __metadata("design:type", Number)
], EventDetails.prototype, "cataloguedFragments", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '对象类型',
        example: 'Payload',
        required: false
    }),
    __metadata("design:type", String)
], EventDetails.prototype, "object_class", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '数据来源',
        example: 'discos',
        enum: ['discos', 'jonathan']
    }),
    __metadata("design:type", String)
], EventDetails.prototype, "source", void 0);
exports.EventDetails = EventDetails;
class CorrelationResultDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '事件ID',
        example: '383'
    }),
    __metadata("design:type", String)
], CorrelationResultDto.prototype, "event_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '关联置信度',
        example: 0.85
    }),
    __metadata("design:type", Number)
], CorrelationResultDto.prototype, "confidence", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '各维度匹配分数',
        type: MatchScores
    }),
    __metadata("design:type", MatchScores)
], CorrelationResultDto.prototype, "scores", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '事件详情',
        type: EventDetails
    }),
    __metadata("design:type", EventDetails)
], CorrelationResultDto.prototype, "event_details", void 0);
exports.CorrelationResultDto = CorrelationResultDto;
class CorrelationResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '总命中数',
        example: 2
    }),
    __metadata("design:type", Number)
], CorrelationResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '事件列表',
        type: [CorrelationResultDto]
    }),
    __metadata("design:type", Array)
], CorrelationResponseDto.prototype, "hits", void 0);
exports.CorrelationResponseDto = CorrelationResponseDto;
class DebrisCorrelationResult {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '碎片ID',
        example: '1999-025EZ'
    }),
    __metadata("design:type", String)
], DebrisCorrelationResult.prototype, "debris_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '关联置信度',
        example: 0.85
    }),
    __metadata("design:type", Number)
], DebrisCorrelationResult.prototype, "confidence", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '各维度匹配分数',
        type: MatchScores
    }),
    __metadata("design:type", MatchScores)
], DebrisCorrelationResult.prototype, "scores", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '碎片详情',
        example: {
            cospar_id: '1999-025EZ',
            launch_date: '1999-05-20',
            first_epoch: '1999-05-20T00:00:00Z',
            name: 'FENGYUN 1C DEB',
            object_class: 'DEBRIS'
        }
    }),
    __metadata("design:type", Object)
], DebrisCorrelationResult.prototype, "debris_details", void 0);
exports.DebrisCorrelationResult = DebrisCorrelationResult;
class EventCorrelationResultDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '关联碎片列表',
        type: [DebrisCorrelationResult]
    }),
    __metadata("design:type", Array)
], EventCorrelationResultDto.prototype, "debris_list", void 0);
exports.EventCorrelationResultDto = EventCorrelationResultDto;
//# sourceMappingURL=correlation-response.dto.js.map