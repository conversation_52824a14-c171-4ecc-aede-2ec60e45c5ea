import { BaseQueryDto } from './base-query.dto';
export declare enum KeywordMatchType {
    SUBSTRING = "substring",
    SIMILARITY = "similarity"
}
export declare class NewsListQueryDto extends BaseQueryDto {
    keywords?: string[];
    keywordMatchType?: KeywordMatchType;
    themes?: string[];
    publishDateStart?: string;
    publishDateEnd?: string;
    indexPatterns?: string[];
    includeUntranslated?: boolean;
}
