"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DebrisEventQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class DebrisEventQueryDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '事件ID',
        example: 'EVENT123',
        required: false
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], DebrisEventQueryDto.prototype, "_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '碎片标识 (格式: YYYY-XXX[A-Z])',
        example: '1964-006A',
        required: false
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.Matches)(/^\d{4}-\d{3}[A-Z]*$/, {
        message: '碎片标识格式必须为YYYY-XXX[A-Z]'
    }),
    __metadata("design:type", String)
], DebrisEventQueryDto.prototype, "piece", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '事件开始时间',
        example: '2024-01-01T00:00:00Z',
        required: false
    }),
    (0, class_validator_1.IsDateString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], DebrisEventQueryDto.prototype, "start_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '事件结束时间',
        example: '2024-12-31T23:59:59Z',
        required: false
    }),
    (0, class_validator_1.IsDateString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], DebrisEventQueryDto.prototype, "end_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '关键词搜索',
        example: 'collision',
        required: false
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], DebrisEventQueryDto.prototype, "keyword", void 0);
exports.DebrisEventQueryDto = DebrisEventQueryDto;
//# sourceMappingURL=debris-event-query.dto.js.map