"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConstellationOrganizationsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ConstellationOrganizationsResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '星座所属机构集合',
        type: [String],
        example: ['SpaceX', 'OneWeb', 'Amazon', 'Boeing', 'Lockheed Martin']
    }),
    __metadata("design:type", Array)
], ConstellationOrganizationsResponseDto.prototype, "organizations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '机构数量',
        type: Number,
        example: 5
    }),
    __metadata("design:type", Number)
], ConstellationOrganizationsResponseDto.prototype, "count", void 0);
exports.ConstellationOrganizationsResponseDto = ConstellationOrganizationsResponseDto;
//# sourceMappingURL=constellation-organizations-response.dto.js.map