"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoopholeQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class LoopholeQueryDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '漏洞ID（CVE编号），精确匹配',
        required: false,
        example: 'CVE-2024-1234',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], LoopholeQueryDto.prototype, "_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '关键词搜索（可匹配漏洞描述、受影响组件、CVE编号等所有字段）',
        required: false,
        example: 'Cobham Sailor',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], LoopholeQueryDto.prototype, "keywords", void 0);
exports.LoopholeQueryDto = LoopholeQueryDto;
//# sourceMappingURL=loophole-query.dto.js.map