"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BulkNoradIdsQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class BulkNoradIdsQueryDto {
    constructor() {
        this.norad_ids = [];
        this.page = 1;
        this.limit = 1000;
        this.useOneTimeQuery = false;
    }
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星NORAD ID数组（最多支持100个ID）。如果为空数组，则返回所有卫星的TLE数据',
        required: false,
        type: [Number],
        example: [25544, 43552, 37849],
        default: []
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsNumber)({}, { each: true }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Array)
], BulkNoradIdsQueryDto.prototype, "norad_ids", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '页码，从1开始。仅在norad_ids为空时生效',
        required: false,
        minimum: 1,
        default: 1,
        example: 1
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], BulkNoradIdsQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '每页记录数，最大10000。仅在norad_ids为空时生效',
        required: false,
        minimum: 1,
        maximum: 10000,
        default: 1000,
        example: 1000
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(10000),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], BulkNoradIdsQueryDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否使用一次性查询模式（秒级返回全部数据）。仅在norad_ids为空时生效。true=一次性获取所有数据，false=分页查询',
        required: false,
        default: false,
        example: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Type)(() => Boolean),
    __metadata("design:type", Boolean)
], BulkNoradIdsQueryDto.prototype, "useOneTimeQuery", void 0);
exports.BulkNoradIdsQueryDto = BulkNoradIdsQueryDto;
//# sourceMappingURL=bulk-norad-ids-query.dto.js.map