"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrbitQueryDto = exports.OrbitType = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const base_query_dto_1 = require("./base-query.dto");
var OrbitType;
(function (OrbitType) {
    OrbitType["LEO"] = "LEO";
    OrbitType["MEO"] = "MEO";
    OrbitType["GEO"] = "GEO";
})(OrbitType = exports.OrbitType || (exports.OrbitType = {}));
class OrbitQueryDto extends base_query_dto_1.BaseQueryDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星编号',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OrbitQueryDto.prototype, "satelliteId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '轨道高度范围（最小值，单位：km）',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], OrbitQueryDto.prototype, "minAltitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '轨道高度范围（最大值，单位：km）',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], OrbitQueryDto.prototype, "maxAltitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '时间范围（开始）',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], OrbitQueryDto.prototype, "startTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '时间范围（结束）',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], OrbitQueryDto.prototype, "endTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星名称（支持精确匹配和模糊匹配，需配合 fuzzy_match 参数使用）',
        required: false,
        example: 'Canyon 2'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OrbitQueryDto.prototype, "satellite_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否使用模糊匹配卫星名称（true: 模糊匹配，false: 精确匹配）',
        required: false,
        default: false,
        example: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Type)(() => Boolean),
    __metadata("design:type", Boolean)
], OrbitQueryDto.prototype, "fuzzy_match", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星编号（NORAD ID，用于唯一标识卫星）',
        required: false,
        example: 3889
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], OrbitQueryDto.prototype, "norad_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '国际代号（COSPAR ID，格式：YYYY-NNNX，如 1969-036A）',
        required: false,
        example: '1969-036A'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OrbitQueryDto.prototype, "cospar_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '轨道类型（LEO: 低地轨道，MEO: 中地轨道，GEO: 地球同步轨道）',
        required: false,
        enum: OrbitType,
        enumName: 'OrbitType',
        example: OrbitType.LEO
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(OrbitType),
    __metadata("design:type", String)
], OrbitQueryDto.prototype, "orbit_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '轨道高度最小值（单位：千米，用于范围查询）',
        required: false,
        minimum: 0,
        example: 500
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], OrbitQueryDto.prototype, "min_altitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '轨道高度最大值（单位：千米，用于范围查询）',
        required: false,
        minimum: 0,
        example: 1000
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], OrbitQueryDto.prototype, "max_altitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '轨道倾角最小值（单位：度，范围：0-180，用于范围查询）',
        required: false,
        minimum: 0,
        maximum: 180,
        example: 30
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(180),
    __metadata("design:type", Number)
], OrbitQueryDto.prototype, "min_inclination", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '轨道倾角最大值（单位：度，范围：0-180，用于范围查询）',
        required: false,
        minimum: 0,
        maximum: 180,
        example: 60
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(180),
    __metadata("design:type", Number)
], OrbitQueryDto.prototype, "max_inclination", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '升交点赤经最小值（单位：度，范围：0-360，用于范围查询）',
        required: false,
        minimum: 0,
        maximum: 360,
        example: 0
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(360),
    __metadata("design:type", Number)
], OrbitQueryDto.prototype, "min_raan", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '升交点赤经最大值（单位：度，范围：0-360，用于范围查询）',
        required: false,
        minimum: 0,
        maximum: 360,
        example: 180
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(360),
    __metadata("design:type", Number)
], OrbitQueryDto.prototype, "max_raan", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '偏心率最小值（范围：0-1）',
        required: false,
        minimum: 0,
        maximum: 1,
        example: 0.001
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1),
    __metadata("design:type", Number)
], OrbitQueryDto.prototype, "min_eccentricity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '偏心率最大值（范围：0-1）',
        required: false,
        minimum: 0,
        maximum: 1,
        example: 0.01
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1),
    __metadata("design:type", Number)
], OrbitQueryDto.prototype, "max_eccentricity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '近地点幅角最小值（单位：度，范围：0-360）',
        required: false,
        minimum: 0,
        maximum: 360,
        example: 0
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(360),
    __metadata("design:type", Number)
], OrbitQueryDto.prototype, "min_arg_perigee", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '近地点幅角最大值（单位：度，范围：0-360）',
        required: false,
        minimum: 0,
        maximum: 360,
        example: 180
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(360),
    __metadata("design:type", Number)
], OrbitQueryDto.prototype, "max_arg_perigee", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '平近点角最小值（单位：度，范围：0-360）',
        required: false,
        minimum: 0,
        maximum: 360,
        example: 0
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(360),
    __metadata("design:type", Number)
], OrbitQueryDto.prototype, "min_mean_anomaly", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '平近点角最大值（单位：度，范围：0-360）',
        required: false,
        minimum: 0,
        maximum: 360,
        example: 180
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(360),
    __metadata("design:type", Number)
], OrbitQueryDto.prototype, "max_mean_anomaly", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '每天圈数最小值',
        required: false,
        minimum: 0,
        example: 12
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], OrbitQueryDto.prototype, "min_revolutions_per_day", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '每天圈数最大值',
        required: false,
        minimum: 0,
        example: 16
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], OrbitQueryDto.prototype, "max_revolutions_per_day", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '轨道周期最小值（单位：分钟）',
        required: false,
        minimum: 0,
        example: 90
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], OrbitQueryDto.prototype, "min_orbital_period", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '轨道周期最大值（单位：分钟）',
        required: false,
        minimum: 0,
        example: 120
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], OrbitQueryDto.prototype, "max_orbital_period", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '半长轴最小值（单位：千米）',
        required: false,
        minimum: 0,
        example: 6500
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], OrbitQueryDto.prototype, "min_semi_major_axis", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '半长轴最大值（单位：千米）',
        required: false,
        minimum: 0,
        example: 7500
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], OrbitQueryDto.prototype, "max_semi_major_axis", void 0);
exports.OrbitQueryDto = OrbitQueryDto;
//# sourceMappingURL=orbit-query.dto.js.map