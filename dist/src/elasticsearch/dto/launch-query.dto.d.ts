export declare enum LaunchStatus {
    UPCOMING = "\u5373\u5C06\u53D1\u5C04",
    IN_FLIGHT = "\u53D1\u5C04\u4E2D",
    SUCCESS = "\u53D1\u5C04\u6210\u529F",
    FAILURE = "\u53D1\u5C04\u5931\u8D25",
    HISTORY = "\u5386\u53F2\u53D1\u5C04"
}
export declare enum SortDirection {
    ASC = "asc",
    DESC = "desc"
}
export declare class LaunchQueryDto {
    page?: number;
    limit?: number;
    launchDateStart?: string;
    launchDateEnd?: string;
    rocketName?: string;
    siteName?: string;
    status?: LaunchStatus;
    provider?: string;
    sortDirection?: SortDirection;
}
