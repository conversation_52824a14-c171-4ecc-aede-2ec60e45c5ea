"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NewsListQueryDto = exports.KeywordMatchType = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const base_query_dto_1 = require("./base-query.dto");
var KeywordMatchType;
(function (KeywordMatchType) {
    KeywordMatchType["SUBSTRING"] = "substring";
    KeywordMatchType["SIMILARITY"] = "similarity";
})(KeywordMatchType = exports.KeywordMatchType || (exports.KeywordMatchType = {}));
class NewsListQueryDto extends base_query_dto_1.BaseQueryDto {
    constructor() {
        super(...arguments);
        this.keywordMatchType = KeywordMatchType.SUBSTRING;
        this.includeUntranslated = true;
    }
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '关键词数组，用于在新闻的所有字段中进行子串匹配搜索，包括：标题、摘要、内容、来源、作者、链接、主题词、发布时间等所有文本字段',
        required: false,
        isArray: true,
        type: [String],
        example: ['satellite', 'launch']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], NewsListQueryDto.prototype, "keywords", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '关键词匹配方式，可选值：substring(子串匹配)、similarity(相似性匹配)，默认为substring',
        required: false,
        enum: KeywordMatchType,
        default: KeywordMatchType.SUBSTRING
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(KeywordMatchType),
    __metadata("design:type", String)
], NewsListQueryDto.prototype, "keywordMatchType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '主题词数组，用于筛选包含特定主题的新闻',
        required: false,
        isArray: true,
        type: [String],
        example: ['航天', '卫星']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], NewsListQueryDto.prototype, "themes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '发布日期开始时间',
        required: false,
        type: String,
        example: '2023-01-01'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], NewsListQueryDto.prototype, "publishDateStart", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '发布日期结束时间',
        required: false,
        type: String,
        example: '2023-12-31'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], NewsListQueryDto.prototype, "publishDateEnd", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '索引名称模式数组，支持使用通配符匹配，例如：["*defence*", "*defense*"]可匹配包含defence或defense的索引',
        required: false,
        isArray: true,
        type: [String],
        example: ['news_2023_*', '*defence*']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], NewsListQueryDto.prototype, "indexPatterns", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否包含未翻译的新闻，默认为true',
        required: false,
        type: Boolean,
        default: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Boolean),
    __metadata("design:type", Boolean)
], NewsListQueryDto.prototype, "includeUntranslated", void 0);
exports.NewsListQueryDto = NewsListQueryDto;
//# sourceMappingURL=news-list-query.dto.js.map