import { MatchType, SatelliteFieldMatchConfig, SortConfig } from './base-query.dto';
export { MatchType, SatelliteFieldMatchConfig, SortConfig };
export declare enum OrbitType {
    LEO = "LEO",
    MEO = "MEO",
    GEO = "GEO"
}
export declare class SatelliteQueryDto {
    page?: number;
    limit?: number;
    keyword?: string;
    similarity_threshold?: number;
    sort?: SortConfig;
    satellite_name?: SatelliteFieldMatchConfig;
    alternative_name?: SatelliteFieldMatchConfig;
    cospar_id?: SatelliteFieldMatchConfig;
    country_of_registry?: SatelliteFieldMatchConfig;
    owner?: SatelliteFieldMatchConfig;
    status?: SatelliteFieldMatchConfig;
    norad_id?: SatelliteFieldMatchConfig;
    launch_date?: SatelliteFieldMatchConfig;
    deployed_date?: SatelliteFieldMatchConfig;
    orbit_type?: OrbitType;
    min_altitude?: number;
    max_altitude?: number;
    min_inclination?: number;
    max_inclination?: number;
    min_period?: number;
    max_period?: number;
    min_apogee?: number;
    max_apogee?: number;
    min_perigee?: number;
    max_perigee?: number;
    launch_date_start?: string;
    launch_date_end?: string;
    launch_site?: SatelliteFieldMatchConfig;
    launch_vehicle?: SatelliteFieldMatchConfig;
}
