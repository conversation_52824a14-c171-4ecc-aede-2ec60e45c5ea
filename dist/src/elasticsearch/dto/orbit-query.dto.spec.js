"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const orbit_query_dto_1 = require("./orbit-query.dto");
describe('OrbitQueryDto', () => {
    it('should pass validation with valid data', async () => {
        const dto = (0, class_transformer_1.plainToInstance)(orbit_query_dto_1.OrbitQueryDto, {
            page: 1,
            limit: 10,
            satelliteId: 'SAT123',
            minAltitude: 500,
            maxAltitude: 1000,
            startTime: '2024-01-01T00:00:00Z',
            endTime: '2024-12-31T23:59:59Z',
            sort: { altitude: 'desc' },
            keyword: 'test'
        });
        const errors = await (0, class_validator_1.validate)(dto);
        expect(errors.length).toBe(0);
    });
    it('should pass validation with empty data', async () => {
        const dto = (0, class_transformer_1.plainToInstance)(orbit_query_dto_1.OrbitQueryDto, {});
        const errors = await (0, class_validator_1.validate)(dto);
        expect(errors.length).toBe(0);
    });
    it('should fail validation with invalid page number', async () => {
        const dto = (0, class_transformer_1.plainToInstance)(orbit_query_dto_1.OrbitQueryDto, {
            page: -1
        });
        const errors = await (0, class_validator_1.validate)(dto);
        expect(errors.length).toBeGreaterThan(0);
        expect(errors[0].constraints).toHaveProperty('min');
    });
    it('should fail validation with invalid date format', async () => {
        const dto = (0, class_transformer_1.plainToInstance)(orbit_query_dto_1.OrbitQueryDto, {
            startTime: 'invalid-date'
        });
        const errors = await (0, class_validator_1.validate)(dto);
        expect(errors.length).toBeGreaterThan(0);
        expect(errors[0].constraints).toHaveProperty('isDateString');
    });
    it('should fail validation with invalid altitude values', async () => {
        const dto = (0, class_transformer_1.plainToInstance)(orbit_query_dto_1.OrbitQueryDto, {
            minAltitude: 'invalid',
            maxAltitude: 'invalid'
        });
        const errors = await (0, class_validator_1.validate)(dto);
        expect(errors.length).toBe(2);
        expect(errors[0].constraints).toHaveProperty('isNumber');
        expect(errors[1].constraints).toHaveProperty('isNumber');
    });
});
//# sourceMappingURL=orbit-query.dto.spec.js.map