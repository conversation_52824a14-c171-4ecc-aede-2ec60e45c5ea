"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LaunchQueryDto = exports.SortDirection = exports.LaunchStatus = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
var LaunchStatus;
(function (LaunchStatus) {
    LaunchStatus["UPCOMING"] = "\u5373\u5C06\u53D1\u5C04";
    LaunchStatus["IN_FLIGHT"] = "\u53D1\u5C04\u4E2D";
    LaunchStatus["SUCCESS"] = "\u53D1\u5C04\u6210\u529F";
    LaunchStatus["FAILURE"] = "\u53D1\u5C04\u5931\u8D25";
    LaunchStatus["HISTORY"] = "\u5386\u53F2\u53D1\u5C04";
})(LaunchStatus = exports.LaunchStatus || (exports.LaunchStatus = {}));
var SortDirection;
(function (SortDirection) {
    SortDirection["ASC"] = "asc";
    SortDirection["DESC"] = "desc";
})(SortDirection = exports.SortDirection || (exports.SortDirection = {}));
class LaunchQueryDto {
    constructor() {
        this.page = 1;
        this.limit = 10;
        this.sortDirection = SortDirection.DESC;
    }
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '页码',
        required: false,
        default: 1,
        minimum: 1
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], LaunchQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '每页数量',
        required: false,
        default: 10,
        minimum: 1
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], LaunchQueryDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '发射时间开始（北京时间，格式：YYYY-MM-DD）',
        required: false,
        type: String,
        example: '2023-01-01'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], LaunchQueryDto.prototype, "launchDateStart", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '发射时间结束（北京时间，格式：YYYY-MM-DD）',
        required: false,
        type: String,
        example: '2023-12-31'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], LaunchQueryDto.prototype, "launchDateEnd", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '火箭型号',
        required: false,
        type: String,
        example: 'Falcon 9'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], LaunchQueryDto.prototype, "rocketName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '发射场',
        required: false,
        type: String,
        example: 'Kennedy Space Center'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], LaunchQueryDto.prototype, "siteName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '任务状态',
        required: false,
        enum: LaunchStatus,
        example: LaunchStatus.SUCCESS
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(LaunchStatus),
    __metadata("design:type", String)
], LaunchQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '服务商',
        required: false,
        type: String,
        example: 'SpaceX'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], LaunchQueryDto.prototype, "provider", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '排序方向（按发射时间排序）',
        required: false,
        enum: SortDirection,
        default: SortDirection.DESC,
        example: SortDirection.DESC
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(SortDirection),
    __metadata("design:type", String)
], LaunchQueryDto.prototype, "sortDirection", void 0);
exports.LaunchQueryDto = LaunchQueryDto;
//# sourceMappingURL=launch-query.dto.js.map