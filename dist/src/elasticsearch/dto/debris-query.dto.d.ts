import { MatchType, DebrisFieldMatchConfig } from "./base-query.dto";
export { MatchType, DebrisFieldMatchConfig };
export declare class DebrisQueryDto {
    page?: number;
    limit?: number;
    sort?: {
        field: string;
        order: 'asc' | 'desc';
    };
    keyword?: string;
    cospar_id?: DebrisFieldMatchConfig;
    norad_id?: DebrisFieldMatchConfig;
    name?: string;
    country?: DebrisFieldMatchConfig;
    launch_date?: DebrisFieldMatchConfig;
    launch_site?: DebrisFieldMatchConfig;
    rcs_size?: DebrisFieldMatchConfig;
    decay?: DebrisFieldMatchConfig;
    first_epoch?: DebrisFieldMatchConfig;
    object_class?: DebrisFieldMatchConfig;
    mission?: string;
    period_minutes_range?: {
        min?: number;
        max?: number;
    };
    incl_degrees_range?: {
        min?: number;
        max?: number;
    };
    apogee_km_range?: {
        min?: number;
        max?: number;
    };
    perigee_km_range?: {
        min?: number;
        max?: number;
    };
}
