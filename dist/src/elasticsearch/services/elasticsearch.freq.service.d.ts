import { ElasticsearchBaseService } from './elasticsearch.base.service';
import { FreqQueryDto } from '../dto/freq-query.dto';
export declare class ElasticsearchFreqService {
    private readonly esBaseService;
    private readonly logger;
    private readonly freqIndex;
    constructor(esBaseService: ElasticsearchBaseService);
    searchFrequencies(query: FreqQueryDto & {
        freq_min?: number;
        freq_max?: number;
        service_type?: string;
        country?: string;
        limit?: number;
        page?: number;
    }): Promise<any>;
    getServiceTypes(): Promise<string[]>;
    getCountries(): Promise<string[]>;
    getSatellites(): Promise<string[]>;
    private buildFreqQueryConditions;
}
