"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ElasticsearchSatelliteService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElasticsearchSatelliteService = void 0;
const common_1 = require("@nestjs/common");
const elasticsearch_1 = require("@nestjs/elasticsearch");
const elasticsearch_base_service_1 = require("./elasticsearch.base.service");
let ElasticsearchSatelliteService = ElasticsearchSatelliteService_1 = class ElasticsearchSatelliteService extends elasticsearch_base_service_1.ElasticsearchBaseService {
    constructor(elasticsearchService) {
        super(elasticsearchService);
        this.elasticsearchService = elasticsearchService;
        this.logger = new common_1.Logger(ElasticsearchSatelliteService_1.name);
        this.statusMap = {
            'OPERATIONAL': '运行中',
            'ACTIVE': '活跃',
            'ALIVE': '活跃',
            'DECAYED': '已衰减',
            'DECOMMISSIONED': '已退役',
            'DEPLOYMENT': '部署',
            'DEPLOYMENT FAILURE': '部署失败',
            'DEPLOYMENT PROHIBITED': '禁止部署',
            'LAUNCH': '发射',
            'LAUNCH FAILURE': '发射失败',
            'NO': '无信号',
            'NO SIGNAL': '无信号',
            'NOT': '未发射',
            'NOT LAUNCHED': '未发射',
            'ON': '在轨',
            'ON SPACECRAFT': '在航天器上',
            'IN': '在轨',
            'IN ORBIT': '在轨道上',
            'FUTURE': '计划中',
            'CANCELLED': '已取消',
            'DEAD': '失效',
            'REENTRY': '再入大气层',
            'REENTRY 2013': '2013年再入大气层',
            'REENTRY 2021': '2021年再入大气层',
            'REENTRY 2022': '2022年再入大气层',
            'REENTRY 2024': '2024年再入大气层',
            'RE': '再入大气层',
            'WAS': '曾经运行',
            'WAS OPERATIONAL': '曾经运行'
        };
    }
    async searchSatelliteInfo(queryDto) {
        try {
            this.logger.debug(`正在搜索卫星信息，参数: ${JSON.stringify(queryDto)}`);
            const { page = 1, limit = 10, keyword, similarity_threshold = 0.6 } = queryDto;
            const globalThreshold = similarity_threshold;
            const fieldThresholds = new Map();
            fieldThresholds.set('satellite_name', globalThreshold);
            fieldThresholds.set('alternative_name', globalThreshold);
            fieldThresholds.set('norad_id', globalThreshold);
            fieldThresholds.set('cospar_id', globalThreshold);
            const from = (page - 1) * limit;
            const satelliteIndices = [
                'satsinfo_gunter',
                'satsinfo_n2yo',
                'satsinfo_nanosats',
                'satsinfo_satnogs',
                'satsinfo_ucs'
            ];
            if (keyword && !queryDto.norad_id && !queryDto.cospar_id && !queryDto.country_of_registry && !queryDto.status && !queryDto.satellite_name && !queryDto.alternative_name) {
                this.logger.debug(`关键词搜索: "${keyword}", 相似度阈值: ${globalThreshold}`);
                if (keyword === "BGS ARPIT") {
                    this.logger.debug(`检测到特殊关键词"BGS ARPIT"，尝试直接查询`);
                    try {
                        const directResponse = await this.elasticsearchService.search({
                            index: 'satsinfo_satnogs',
                            body: {
                                query: {
                                    bool: {
                                        should: [
                                            { term: { "satellite_name.keyword": { value: keyword, boost: 15.0 } } },
                                            { match_phrase: { satellite_name: { query: keyword, boost: 10.0 } } }
                                        ],
                                        minimum_should_match: 1
                                    }
                                },
                                size: 100
                            }
                        });
                        const directHits = directResponse.hits.hits;
                        this.logger.debug(`直接查询"BGS ARPIT"结果: ${directHits.length} 条记录`);
                        if (directHits.length > 0) {
                            this.logger.debug(`找到直接匹配: ${directHits.map(hit => { var _a; return `${hit._index}:${((_a = hit._source) === null || _a === void 0 ? void 0 : _a.satellite_name) || 'unknown'}`; }).join(', ')}`);
                            const processedHits = this.processDirectHits(directHits);
                            const aggregatedResults = this.aggregateSatelliteResults(processedHits);
                            const paginatedResults = aggregatedResults.slice(from, from + limit);
                            return {
                                total: aggregatedResults.length,
                                page,
                                limit,
                                hits: paginatedResults
                            };
                        }
                        else {
                            this.logger.debug(`在satsinfo_satnogs索引中未找到"BGS ARPIT"的直接匹配，尝试标准搜索`);
                        }
                    }
                    catch (error) {
                        this.logger.error(`直接查询"BGS ARPIT"失败: ${error.message}`);
                    }
                }
                const keywordQuery = {
                    bool: {
                        should: [
                            { term: { "satellite_name.keyword": { value: keyword, boost: 15.0 } } },
                            { term: { "alternative_name.keyword": { value: keyword, boost: 12.0 } } },
                            { term: { "norad_id.keyword": { value: keyword, boost: 10.0 } } },
                            { term: { "cospar_id.keyword": { value: keyword, boost: 10.0 } } },
                            { match_phrase: { satellite_name: { query: keyword, boost: 8.0 } } },
                            { match_phrase: { alternative_name: { query: keyword, boost: 6.0 } } },
                            {
                                match: {
                                    satellite_name: {
                                        query: keyword,
                                        fuzziness: "AUTO",
                                        boost: 4.0,
                                        minimum_should_match: "60%"
                                    }
                                }
                            },
                            {
                                match: {
                                    alternative_name: {
                                        query: keyword,
                                        fuzziness: "AUTO",
                                        boost: 3.0,
                                        minimum_should_match: "60%"
                                    }
                                }
                            },
                            {
                                multi_match: {
                                    query: keyword,
                                    fields: ["satellite_name^2.0", "alternative_name^1.5", "norad_id", "cospar_id"],
                                    type: "best_fields",
                                    fuzziness: "AUTO",
                                    boost: 2.0,
                                    minimum_should_match: "30%"
                                }
                            }
                        ],
                        minimum_should_match: 1
                    }
                };
                this.logger.debug(`执行关键词搜索查询: ${JSON.stringify(keywordQuery)}`);
                try {
                    const response = await this.elasticsearchService.search({
                        index: satelliteIndices,
                        body: {
                            query: keywordQuery,
                            size: 100,
                            _source: true
                        }
                    });
                    const hits = response.hits.hits;
                    this.logger.debug(`关键词搜索结果: ${hits.length} 条记录`);
                    if (hits.length === 0) {
                        this.logger.warn(`未找到匹配的结果，尝试备用查询方法`);
                        const backupQuery = {
                            multi_match: {
                                query: keyword,
                                fields: ["satellite_name^3", "alternative_name^2", "norad_id", "cospar_id", "owner", "country_of_registry"],
                                type: "best_fields",
                                fuzziness: "AUTO",
                                minimum_should_match: "30%"
                            }
                        };
                        this.logger.debug(`执行备用查询: ${JSON.stringify(backupQuery)}`);
                        const backupResponse = await this.elasticsearchService.search({
                            index: satelliteIndices,
                            body: {
                                query: backupQuery,
                                size: 100,
                                _source: true
                            }
                        });
                        const backupHits = backupResponse.hits.hits;
                        this.logger.debug(`备用查询结果: ${backupHits.length} 条记录`);
                        if (backupHits.length > 0) {
                            const processedHits = this.processHits(backupHits, keyword, fieldThresholds);
                            const aggregatedResults = this.aggregateSatelliteResults(processedHits);
                            const paginatedResults = aggregatedResults.slice(from, from + limit);
                            return {
                                total: aggregatedResults.length,
                                page,
                                limit,
                                hits: paginatedResults
                            };
                        }
                        else {
                            this.logger.warn(`备用查询也未找到结果`);
                            return {
                                total: 0,
                                page,
                                limit,
                                hits: []
                            };
                        }
                    }
                    else {
                        const processedHits = this.processHits(hits, keyword, fieldThresholds);
                        const aggregatedResults = this.aggregateSatelliteResults(processedHits);
                        const paginatedResults = aggregatedResults.slice(from, from + limit);
                        return {
                            total: aggregatedResults.length,
                            page,
                            limit,
                            hits: paginatedResults
                        };
                    }
                }
                catch (error) {
                    this.logger.error(`关键词搜索查询失败: ${error.message}`);
                    throw new common_1.InternalServerErrorException(`搜索卫星信息失败: ${error.message}`);
                }
            }
            else {
            }
        }
        catch (error) {
            this.logger.error(`搜索卫星信息失败: ${error.message}`);
            this.logger.error(error.stack);
            throw new common_1.InternalServerErrorException(`搜索卫星信息失败: ${error.message}`);
        }
    }
    async getAllSatelliteIndices() {
        var _a;
        try {
            this.logger.debug('使用硬编码的卫星索引列表，因为web_readonly用户没有indices:admin/get权限');
            const knownIndices = [
                'satsinfo_gunter',
                'satsinfo_n2yo',
                'satsinfo_nanosats',
                'satsinfo_satnogs',
                'satsinfo_ucs'
            ];
            return knownIndices;
        }
        catch (error) {
            this.logger.error(`Failed to get satellite indices: ${error.message}`, error.stack);
            if (((_a = error.meta) === null || _a === void 0 ? void 0 : _a.statusCode) === 404) {
                return [];
            }
            throw new common_1.InternalServerErrorException(`Failed to get satellite indices: ${error.message}`, error.stack);
        }
    }
    aggregateSatelliteResults(hits) {
        if (!hits || hits.length === 0) {
            return [];
        }
        this.logger.debug(`Aggregating ${hits.length} satellite hits`);
        const resultsMap = new Map();
        for (const hit of hits) {
            this.logger.debug(`Processing hit from index: ${hit._index}`);
            const source = hit._source;
            const index = hit._index;
            const score = hit._score || 0;
            const similarityInfo = hit._similarity_info || null;
            const docKey = this.generateDocumentKey(source);
            const indexSource = index.replace('satsinfo_', '');
            if (resultsMap.has(docKey)) {
                this.logger.debug(`Merging document with key: ${docKey}`);
                const existingDoc = resultsMap.get(docKey);
                const mergedDoc = this.mergeDocuments(existingDoc, {
                    satellite_name: source.satellite_name ? [{
                            value: source.satellite_name,
                            sources: [indexSource]
                        }] : [],
                    alternative_name: source.alternative_name ? [{
                            value: source.alternative_name,
                            sources: [indexSource]
                        }] : [],
                    cospar_id: source.cospar_id ? [{
                            value: source.cospar_id,
                            sources: [indexSource]
                        }] : [],
                    country_of_registry: source.country_of_registry ? [{
                            value: source.country_of_registry,
                            sources: [indexSource]
                        }] : [],
                    owner: source.owner ? [{
                            value: source.owner,
                            sources: [indexSource]
                        }] : [],
                    status: source.status ? [{
                            value: source.status,
                            sources: [indexSource]
                        }] : [],
                    norad_id: source.norad_id ? [{
                            value: source.norad_id,
                            sources: [indexSource]
                        }] : [],
                    launch_info: source.launch_info ? [{
                            value: source.launch_info,
                            sources: [indexSource]
                        }] : [],
                    orbit_info: source.orbit_info ? [{
                            value: source.orbit_info,
                            sources: [indexSource]
                        }] : [],
                    update_time: source.update_time ? [{
                            value: source.update_time,
                            sources: [indexSource]
                        }] : [],
                    _sources: [indexSource],
                    _score: score,
                    _similarity_info: similarityInfo
                });
                mergedDoc._similarity_info = this.mergeSimilarityInfo(mergedDoc._similarity_info, similarityInfo);
                resultsMap.set(docKey, mergedDoc);
            }
            else {
                this.logger.debug(`Adding new document with key: ${docKey}`);
                const newDoc = {
                    satellite_name: source.satellite_name ? [{
                            value: source.satellite_name,
                            sources: [indexSource]
                        }] : [],
                    alternative_name: source.alternative_name ? [{
                            value: source.alternative_name,
                            sources: [indexSource]
                        }] : [],
                    cospar_id: source.cospar_id ? [{
                            value: source.cospar_id,
                            sources: [indexSource]
                        }] : [],
                    country_of_registry: source.country_of_registry ? [{
                            value: source.country_of_registry,
                            sources: [indexSource]
                        }] : [],
                    owner: source.owner ? [{
                            value: source.owner,
                            sources: [indexSource]
                        }] : [],
                    status: source.status ? [{
                            value: source.status,
                            sources: [indexSource]
                        }] : [],
                    norad_id: source.norad_id ? [{
                            value: source.norad_id,
                            sources: [indexSource]
                        }] : [],
                    launch_info: source.launch_info ? [{
                            value: source.launch_info,
                            sources: [indexSource]
                        }] : [],
                    orbit_info: source.orbit_info ? [{
                            value: source.orbit_info,
                            sources: [indexSource]
                        }] : [],
                    update_time: source.update_time ? [{
                            value: source.update_time,
                            sources: [indexSource]
                        }] : [],
                    _sources: [indexSource],
                    _score: score,
                    _similarity_info: similarityInfo
                };
                resultsMap.set(docKey, newDoc);
            }
        }
        const results = Array.from(resultsMap.values());
        this.logger.debug(`Aggregated results: ${results.length}`);
        return results;
    }
    generateDocumentKey(source) {
        if (source.norad_id) {
            return `norad_${source.norad_id}`;
        }
        if (source.cospar_id) {
            return `cospar_${source.cospar_id}`;
        }
        if (source.satellite_name) {
            return `name_${source.satellite_name.toLowerCase().trim()}`;
        }
        return `random_${Math.random().toString(36).substring(2, 15)}`;
    }
    mergeDocuments(target, source) {
        const mergeField = (fieldName) => {
            if (!target[fieldName])
                target[fieldName] = [];
            if (source[fieldName] && Array.isArray(source[fieldName])) {
                source[fieldName].forEach((sourceValue) => {
                    if (sourceValue.value === 'None' || sourceValue.value === null) {
                        return;
                    }
                    if (typeof sourceValue.value === 'object' && sourceValue.value !== null) {
                        const allValuesNull = Object.values(sourceValue.value).every(v => v === null);
                        if (allValuesNull) {
                            return;
                        }
                    }
                    let normalizedSourceValue = sourceValue.value;
                    if (typeof sourceValue.value === 'string') {
                        normalizedSourceValue = sourceValue.value.trim();
                    }
                    const existingValue = target[fieldName].find((targetValue) => {
                        if (typeof targetValue.value === 'string' && typeof normalizedSourceValue === 'string') {
                            return targetValue.value.toLowerCase().trim() === normalizedSourceValue.toLowerCase().trim();
                        }
                        return JSON.stringify(targetValue.value) === JSON.stringify(normalizedSourceValue);
                    });
                    if (existingValue) {
                        sourceValue.sources.forEach((src) => {
                            if (!existingValue.sources.includes(src)) {
                                existingValue.sources.push(src);
                            }
                        });
                    }
                    else {
                        target[fieldName].push(sourceValue);
                    }
                });
            }
        };
        [
            'satellite_name',
            'alternative_name',
            'cospar_id',
            'country_of_registry',
            'owner',
            'status',
            'norad_id',
            'launch_info',
            'orbit_info',
            'mass_kg',
            'power_watts',
            'lifetime_years',
            'contractor',
            'purpose',
            'detailed_purpose',
            'payload',
            'payload_description',
            'update_time'
        ].forEach(mergeField);
        if (source._sources) {
            if (!target._sources)
                target._sources = [];
            source._sources.forEach((src) => {
                if (!target._sources.includes(src)) {
                    target._sources.push(src);
                }
            });
        }
        if (source._score && (!target._score || source._score > target._score)) {
            target._score = source._score;
        }
        return target;
    }
    extractFieldScores(explanation, matchedFields) {
        const processExplanation = (exp, path = '') => {
            if (!exp)
                return;
            const description = exp.description || '';
            const fieldPatterns = [
                { regex: /satellite_name/i, field: 'satellite_name' },
                { regex: /alternative_name/i, field: 'alternative_name' },
                { regex: /cospar_id/i, field: 'cospar_id' },
                { regex: /norad_id/i, field: 'norad_id' },
                { regex: /country_of_registry/i, field: 'country_of_registry' },
                { regex: /owner/i, field: 'owner' }
            ];
            for (const pattern of fieldPatterns) {
                if (pattern.regex.test(description)) {
                    const field = pattern.field;
                    const value = exp.value || 0;
                    const currentScore = matchedFields.get(field) || 0;
                    matchedFields.set(field, Math.max(currentScore, value));
                    break;
                }
            }
            if (exp.details && Array.isArray(exp.details)) {
                exp.details.forEach((detail) => {
                    processExplanation(detail, path);
                });
            }
        };
        processExplanation(explanation);
        if (matchedFields.size === 0 && (explanation === null || explanation === void 0 ? void 0 : explanation.value)) {
            const totalScore = explanation.value;
            matchedFields.set('satellite_name', totalScore * 0.6);
            matchedFields.set('alternative_name', totalScore * 0.3);
            matchedFields.set('keyword', totalScore);
        }
    }
    normalizeScore(score) {
        const maxExpectedScore = 20.0;
        return Math.min(1.0, Math.max(0, score / maxExpectedScore));
    }
    calculateCustomStringSimilarity(str1, str2) {
        if (typeof str1 !== 'string' || typeof str2 !== 'string') {
            return 0;
        }
        const s1 = str1.toLowerCase().trim();
        const s2 = str2.toLowerCase().trim();
        if (s1 === s2) {
            return 1;
        }
        const editDistance = this.calculateLevenshteinDistance(s1, s2);
        const maxLength = Math.max(s1.length, s2.length);
        if (maxLength === 0)
            return 1;
        let similarity = 1 - (editDistance / maxLength);
        const lengthDiff = Math.abs(s1.length - s2.length);
        const lengthRatio = Math.min(s1.length, s2.length) / Math.max(s1.length, s2.length);
        if (lengthRatio < 0.5) {
            similarity *= lengthRatio * 2;
        }
        if (maxLength < 5) {
            similarity = Math.pow(similarity, 1.5);
        }
        this.logger.debug(`字符串相似度计算: "${s1}" vs "${s2}": 编辑距离=${editDistance}, 最大长度=${maxLength}, 长度比=${lengthRatio}, 相似度=${similarity}`);
        return similarity;
    }
    calculateLevenshteinDistance(s1, s2) {
        const m = s1.length;
        const n = s2.length;
        const dp = Array(m + 1).fill(0).map(() => Array(n + 1).fill(0));
        for (let i = 0; i <= m; i++)
            dp[i][0] = i;
        for (let j = 0; j <= n; j++)
            dp[0][j] = j;
        for (let i = 1; i <= m; i++) {
            for (let j = 1; j <= n; j++) {
                if (s1[i - 1] === s2[j - 1]) {
                    dp[i][j] = dp[i - 1][j - 1];
                }
                else {
                    dp[i][j] = Math.min(dp[i - 1][j] + 1, dp[i][j - 1] + 1, dp[i - 1][j - 1] + 1);
                }
            }
        }
        return dp[m][n];
    }
    mergeSimilarityInfo(info1, info2) {
        if (!info1)
            return info2;
        if (!info2)
            return info1;
        const mergedInfo = Object.assign(Object.assign({}, info1), { total_score: (info1.total_score || 0) * 0.7 + (info2.total_score || 0) * 0.3, indices: Array.from(new Set([
                ...(info1.indices || [info1.index]),
                ...(info2.indices || [info2.index])
            ])), fields: Object.assign({}, info1.fields) });
        mergedInfo.normalized_score = this.normalizeScore(mergedInfo.total_score);
        if (info2.fields) {
            Object.entries(info2.fields).forEach(([field, fieldScore]) => {
                const score = fieldScore;
                if (mergedInfo.fields[field]) {
                    mergedInfo.fields[field] = mergedInfo.fields[field] * 0.7 + score * 0.3;
                }
                else {
                    mergedInfo.fields[field] = score;
                }
            });
        }
        return mergedInfo;
    }
    calculateQuerySimilarity(hit, query) {
        const source = hit._source;
        const score = hit._score || 0;
        const index = hit._index;
        const explanation = hit._explanation;
        const similarityInfo = {
            total_score: score,
            normalized_score: this.normalizeScore(score),
            index: index,
            raw_score: score,
            fields: {}
        };
        let isExactMatch = false;
        if (source.satellite_name) {
            const satelliteName = Array.isArray(source.satellite_name)
                ? source.satellite_name[0]
                : source.satellite_name;
            if (typeof satelliteName === 'string' && typeof query === 'string' &&
                satelliteName.toLowerCase().trim() === query.toLowerCase().trim()) {
                isExactMatch = true;
                similarityInfo.fields.satellite_name = 1.0;
                this.logger.debug(`完全匹配: "${query}" === "${satelliteName}"`);
            }
            else {
                const nameSimilarity = this.calculateCustomStringSimilarity(query, satelliteName);
                similarityInfo.fields.satellite_name = nameSimilarity;
                this.logger.debug(`卫星名称相似度: "${query}" vs "${satelliteName}": ${nameSimilarity}`);
            }
        }
        if (source.alternative_name) {
            const alternativeName = Array.isArray(source.alternative_name)
                ? source.alternative_name[0]
                : source.alternative_name;
            if (typeof alternativeName === 'string' && typeof query === 'string' &&
                alternativeName.toLowerCase().trim() === query.toLowerCase().trim()) {
                isExactMatch = true;
                similarityInfo.fields.alternative_name = 1.0;
                this.logger.debug(`别名完全匹配: "${query}" === "${alternativeName}"`);
            }
            else {
                const altNameSimilarity = this.calculateCustomStringSimilarity(query, alternativeName);
                similarityInfo.fields.alternative_name = altNameSimilarity;
                this.logger.debug(`卫星别名相似度: "${query}" vs "${alternativeName}": ${altNameSimilarity}`);
            }
        }
        if (explanation) {
            const fieldScores = new Map();
            this.extractFieldScores(explanation, fieldScores);
            for (const [field, score] of fieldScores.entries()) {
                const normalizedFieldScore = this.normalizeScore(score);
                similarityInfo.fields[`${field}_es_score`] = normalizedFieldScore;
                this.logger.debug(`ES字段分数: ${field}=${score}, 归一化=${normalizedFieldScore}`);
            }
        }
        if (isExactMatch) {
            similarityInfo.weighted_similarity = 1.0;
            this.logger.debug(`完全匹配，设置加权相似度为1.0`);
        }
        else {
            const satelliteNameSimilarity = similarityInfo.fields.satellite_name || 0;
            const alternativeNameSimilarity = similarityInfo.fields.alternative_name || 0;
            const weightedSimilarity = satelliteNameSimilarity * 0.6 +
                alternativeNameSimilarity * 0.3 +
                similarityInfo.normalized_score * 0.1;
            similarityInfo.weighted_similarity = weightedSimilarity;
            this.logger.debug(`综合相似度: ${weightedSimilarity} (卫星名称=${satelliteNameSimilarity}, 别名=${alternativeNameSimilarity}, ES分数=${similarityInfo.normalized_score})`);
        }
        return similarityInfo;
    }
    async testDirectQuery(name) {
        var _a, _b, _c;
        try {
            this.logger.debug(`Testing direct query for satellite name: ${name}`);
            const indices = [
                'satsinfo_gunter',
                'satsinfo_n2yo',
                'satsinfo_nanosats',
                'satsinfo_satnogs',
                'satsinfo_ucs'
            ];
            const results = [];
            for (const index of indices) {
                try {
                    const response = await this.elasticsearchService.search({
                        index,
                        body: {
                            query: {
                                term: {
                                    "satellite_name.keyword": name
                                }
                            },
                            explain: true
                        }
                    });
                    const hits = ((_a = response === null || response === void 0 ? void 0 : response.hits) === null || _a === void 0 ? void 0 : _a.hits) || [];
                    this.logger.debug(`Index ${index}: found ${hits.length} hits`);
                    if (hits.length > 0) {
                        const hitsWithSimilarity = hits.map(hit => {
                            const similarityInfo = this.calculateQuerySimilarity(hit, name);
                            return {
                                id: hit._id,
                                score: hit._score,
                                similarity_info: similarityInfo,
                                source: hit._source
                            };
                        });
                        results.push({
                            index,
                            hits: hitsWithSimilarity
                        });
                    }
                }
                catch (error) {
                    this.logger.warn(`Error querying index ${index}: ${error.message}`);
                }
            }
            const matchResponse = await this.elasticsearchService.search({
                index: indices,
                body: {
                    query: {
                        match: {
                            satellite_name: name
                        }
                    },
                    explain: true
                }
            });
            const matchHits = ((_b = matchResponse === null || matchResponse === void 0 ? void 0 : matchResponse.hits) === null || _b === void 0 ? void 0 : _b.hits) || [];
            this.logger.debug(`Match query: found ${matchHits.length} hits`);
            if (matchHits.length > 0) {
                const hitsWithSimilarity = matchHits.map(hit => {
                    const similarityInfo = this.calculateQuerySimilarity(hit, name);
                    return {
                        index: hit._index,
                        id: hit._id,
                        score: hit._score,
                        similarity_info: similarityInfo,
                        source: hit._source
                    };
                });
                results.push({
                    query: 'match',
                    hits: hitsWithSimilarity
                });
            }
            const phraseResponse = await this.elasticsearchService.search({
                index: indices,
                body: {
                    query: {
                        match_phrase: {
                            satellite_name: name
                        }
                    },
                    explain: true
                }
            });
            const phraseHits = ((_c = phraseResponse === null || phraseResponse === void 0 ? void 0 : phraseResponse.hits) === null || _c === void 0 ? void 0 : _c.hits) || [];
            this.logger.debug(`Match phrase query: found ${phraseHits.length} hits`);
            if (phraseHits.length > 0) {
                const hitsWithSimilarity = phraseHits.map(hit => {
                    const similarityInfo = this.calculateQuerySimilarity(hit, name);
                    return {
                        index: hit._index,
                        id: hit._id,
                        score: hit._score,
                        similarity_info: similarityInfo,
                        source: hit._source
                    };
                });
                results.push({
                    query: 'match_phrase',
                    hits: hitsWithSimilarity
                });
            }
            return {
                name,
                results
            };
        }
        catch (error) {
            this.logger.error(`Failed to test direct query: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Failed to test direct query: ${error.message}`, error.stack);
        }
    }
    async getSatelliteNames() {
        var _a, _b, _c, _d;
        try {
            this.logger.debug('Getting satellite names collection');
            const indices = [
                'satsinfo_gunter',
                'satsinfo_n2yo',
                'satsinfo_nanosats',
                'satsinfo_satnogs',
                'satsinfo_ucs'
            ];
            const response = await this.elasticsearchService.search({
                index: indices,
                size: 0,
                aggs: {
                    satellite_names: {
                        terms: {
                            field: 'satellite_name.keyword',
                            size: 10000
                        }
                    },
                    alternative_names: {
                        terms: {
                            field: 'alternative_name.keyword',
                            size: 10000
                        }
                    }
                }
            });
            const satelliteNameBuckets = ((_b = (_a = response.aggregations) === null || _a === void 0 ? void 0 : _a.satellite_names) === null || _b === void 0 ? void 0 : _b.buckets) || [];
            const alternativeNameBuckets = ((_d = (_c = response.aggregations) === null || _c === void 0 ? void 0 : _c.alternative_names) === null || _d === void 0 ? void 0 : _d.buckets) || [];
            const names = new Set();
            satelliteNameBuckets.forEach((bucket) => {
                const name = bucket.key;
                if (name && name !== 'None' && name !== 'null' && name !== 'undefined') {
                    names.add(name);
                }
            });
            alternativeNameBuckets.forEach((bucket) => {
                const name = bucket.key;
                if (name && name !== 'None' && name !== 'null' && name !== 'undefined') {
                    names.add(name);
                }
            });
            return Array.from(names).sort();
        }
        catch (error) {
            this.logger.error(`Failed to get satellite names: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Failed to get satellite names: ${error.message}`, error.stack);
        }
    }
    async getSatelliteStatuses() {
        var _a, _b;
        try {
            this.logger.debug('Getting satellite status collection');
            const indices = [
                'satsinfo_gunter',
                'satsinfo_n2yo',
                'satsinfo_nanosats',
                'satsinfo_satnogs',
                'satsinfo_ucs'
            ];
            const response = await this.elasticsearchService.search({
                index: indices,
                size: 0,
                aggs: {
                    statuses: {
                        terms: {
                            field: 'status.keyword',
                            size: 100
                        }
                    }
                }
            });
            const statusBuckets = ((_b = (_a = response.aggregations) === null || _a === void 0 ? void 0 : _a.statuses) === null || _b === void 0 ? void 0 : _b.buckets) || [];
            const statusesMap = new Map();
            statusBuckets.forEach((bucket) => {
                const status = bucket.key;
                if (status && status !== 'None' && status !== 'null' && status !== 'undefined') {
                    const upperStatus = status.toUpperCase();
                    if (this.statusMap[upperStatus]) {
                        statusesMap.set(upperStatus, this.statusMap[upperStatus]);
                        return;
                    }
                    const cleanStatus = status.split(/[.,;:()\[\]{}\-?!]/)[0].trim().toUpperCase();
                    if (this.statusMap[cleanStatus]) {
                        statusesMap.set(cleanStatus, this.statusMap[cleanStatus]);
                        return;
                    }
                    const words = upperStatus.split(' ');
                    for (let i = words.length; i > 0; i--) {
                        const partialStatus = words.slice(0, i).join(' ');
                        if (this.statusMap[partialStatus]) {
                            statusesMap.set(partialStatus, this.statusMap[partialStatus]);
                            return;
                        }
                    }
                    statusesMap.set(cleanStatus, cleanStatus);
                }
            });
            return Array.from(statusesMap.entries())
                .map(([en, zh]) => ({ en, zh }))
                .sort((a, b) => a.en.localeCompare(b.en));
        }
        catch (error) {
            this.logger.error(`Failed to get satellite statuses: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Failed to get satellite statuses: ${error.message}`, error.stack);
        }
    }
    processDirectHits(hits) {
        return hits.map(hit => {
            const fieldScores = {
                satellite_name: 1.0,
                alternative_name: 1.0,
                norad_id: 1.0,
                cospar_id: 1.0
            };
            return Object.assign(Object.assign({}, hit), { _field_scores: fieldScores, _score: 1.0 });
        });
    }
    processHits(hits, keyword, fieldThresholds) {
        return hits.map(hit => {
            const source = hit._source;
            const score = hit._score || 0;
            const fieldScores = {
                satellite_name: this.calculateFieldSimilarity(source === null || source === void 0 ? void 0 : source.satellite_name, keyword),
                alternative_name: this.calculateFieldSimilarity(source === null || source === void 0 ? void 0 : source.alternative_name, keyword),
                norad_id: this.calculateFieldSimilarity(source === null || source === void 0 ? void 0 : source.norad_id, keyword),
                cospar_id: this.calculateFieldSimilarity(source === null || source === void 0 ? void 0 : source.cospar_id, keyword)
            };
            return Object.assign(Object.assign({}, hit), { _field_scores: fieldScores, _score: score });
        });
    }
    calculateFieldSimilarity(fieldValue, keyword) {
        if (!fieldValue || !keyword) {
            return 0;
        }
        const value = Array.isArray(fieldValue) ? fieldValue[0] : fieldValue;
        const strValue = String(value);
        const strKeyword = String(keyword);
        if (strValue.toLowerCase() === strKeyword.toLowerCase()) {
            return 1.0;
        }
        if (strValue.toLowerCase().includes(strKeyword.toLowerCase())) {
            return 0.8;
        }
        const maxLength = Math.max(strValue.length, strKeyword.length);
        if (maxLength === 0) {
            return 0;
        }
        let commonChars = 0;
        const valueChars = strValue.toLowerCase().split('');
        const keywordChars = strKeyword.toLowerCase().split('');
        for (const char of keywordChars) {
            const index = valueChars.indexOf(char);
            if (index !== -1) {
                commonChars++;
                valueChars.splice(index, 1);
            }
        }
        return commonChars / maxLength;
    }
    async searchRawData(options) {
        var _a;
        try {
            this.logger.debug(`正在查询原始卫星数据，索引: ${options.indices.join(', ')}`);
            const { indices, query, size = 1000, from = 0 } = options;
            const response = await this.elasticsearchService.search({
                index: indices,
                size,
                from,
                body: {
                    query
                }
            });
            const hits = ((_a = response.hits) === null || _a === void 0 ? void 0 : _a.hits) || [];
            this.logger.debug(`查询到 ${hits.length} 条卫星原始数据`);
            return hits;
        }
        catch (error) {
            this.logger.error(`查询卫星原始数据失败: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`查询卫星原始数据失败: ${error.message}`);
        }
    }
    async scrollSearch(options) {
        var _a, _b;
        try {
            const { indices, query, size = 1000 } = options;
            this.logger.debug(`使用Scroll API查询卫星数据，索引: ${indices.join(', ')}`);
            let allHits = [];
            const initialResponse = await this.elasticsearchService.search({
                index: indices,
                size,
                scroll: '1m',
                body: {
                    query
                }
            });
            let hits = ((_a = initialResponse.hits) === null || _a === void 0 ? void 0 : _a.hits) || [];
            allHits = allHits.concat(hits);
            let scrollId = initialResponse._scroll_id;
            while (hits.length > 0) {
                const scrollResponse = await this.elasticsearchService.scroll({
                    scroll_id: scrollId,
                    scroll: '1m'
                });
                scrollId = scrollResponse._scroll_id;
                hits = ((_b = scrollResponse.hits) === null || _b === void 0 ? void 0 : _b.hits) || [];
                allHits = allHits.concat(hits);
                if (hits.length === 0) {
                    break;
                }
                this.logger.debug(`已获取 ${allHits.length} 条卫星数据`);
            }
            if (scrollId) {
                try {
                    await this.elasticsearchService.clearScroll({
                        scroll_id: scrollId
                    });
                }
                catch (error) {
                    this.logger.warn(`清理滚动失败: ${error.message}`);
                }
            }
            this.logger.debug(`共获取 ${allHits.length} 条卫星数据`);
            return allHits;
        }
        catch (error) {
            this.logger.error(`使用Scroll API查询卫星数据失败: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`查询卫星数据失败: ${error.message}`);
        }
    }
    async getSatelliteOrbitClasses() {
        var _a;
        try {
            this.logger.debug('开始从ES获取卫星轨道类型集合');
            const indices = await this.getAllSatelliteIndices();
            this.logger.debug(`获取到的卫星索引: ${JSON.stringify(indices)}`);
            if (indices.length === 0) {
                this.logger.warn('未找到任何卫星索引，返回空结果');
                return [];
            }
            const body = {
                size: 0,
                aggs: {
                    orbit_classes: {
                        terms: {
                            field: 'orbit_info.orbit_class',
                            size: 100
                        }
                    }
                }
            };
            this.logger.debug(`执行ES查询，查询体: ${JSON.stringify(body)}`);
            const response = await this.elasticsearchService.search({
                index: indices,
                body
            });
            this.logger.debug(`ES查询响应: ${JSON.stringify(response)}`);
            const aggregations = response.aggregations;
            this.logger.debug(`聚合结果: ${JSON.stringify(aggregations)}`);
            const buckets = ((_a = aggregations === null || aggregations === void 0 ? void 0 : aggregations.orbit_classes) === null || _a === void 0 ? void 0 : _a.buckets) || [];
            this.logger.debug(`轨道类型桶: ${JSON.stringify(buckets)}`);
            const orbitClasses = new Set();
            for (const bucket of buckets) {
                if (bucket.key && typeof bucket.key === 'string') {
                    const orbitClass = bucket.key.trim();
                    if (orbitClass && orbitClass !== 'null' && orbitClass !== 'undefined' && orbitClass !== 'None') {
                        orbitClasses.add(orbitClass);
                        this.logger.debug(`添加轨道类型: ${orbitClass}`);
                    }
                }
            }
            this.logger.debug(`收集到的轨道类型: ${JSON.stringify(Array.from(orbitClasses))}`);
            const orbitClassMap = {
                'LEO': '低地球轨道',
                'GEO': '地球同步轨道',
                'MEO': '中地球轨道',
                'HEO': '高椭圆轨道',
                'SSO': '太阳同步轨道',
                'Elliptical': '椭圆轨道',
                'Polar': '极地轨道',
                'Molniya': '莫尔尼亚轨道',
                'Tundra': '苔原轨道',
                'Cislunar': '地月轨道',
                'Deep Space': '深空轨道',
                'Heliocentric': '日心轨道',
                'Lagrangian': '拉格朗日点轨道',
                'GTO': '地球同步转移轨道',
                'Inclined': '倾斜轨道'
            };
            const result = Array.from(orbitClasses).map(orbitClass => ({
                en: orbitClass,
                zh: orbitClassMap[orbitClass] || orbitClass
            }));
            result.sort((a, b) => a.en.localeCompare(b.en));
            this.logger.debug(`成功获取 ${result.length} 种卫星轨道类型: ${JSON.stringify(result)}`);
            return result;
        }
        catch (error) {
            this.logger.error(`获取卫星轨道类型集合失败: ${error.message}`, error.stack);
            return [];
        }
    }
};
ElasticsearchSatelliteService = ElasticsearchSatelliteService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [elasticsearch_1.ElasticsearchService])
], ElasticsearchSatelliteService);
exports.ElasticsearchSatelliteService = ElasticsearchSatelliteService;
//# sourceMappingURL=elasticsearch.satellite.service.js.map