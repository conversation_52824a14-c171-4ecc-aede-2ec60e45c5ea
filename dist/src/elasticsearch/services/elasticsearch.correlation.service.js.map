{"version": 3, "file": "elasticsearch.correlation.service.js", "sourceRoot": "", "sources": ["../../../../src/elasticsearch/services/elasticsearch.correlation.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,yDAA6D;AAC7D,6EAAwE;AAYjE,IAAM,+BAA+B,uCAArC,MAAM,+BAA+B;IAY1C,YACmB,oBAA0C,EAC1C,aAAuC;QADvC,yBAAoB,GAApB,oBAAoB,CAAsB;QAC1C,kBAAa,GAAb,aAAa,CAA0B;QAbzC,WAAM,GAAG,IAAI,eAAM,CAAC,iCAA+B,CAAC,IAAI,CAAC,CAAC;QAC1D,WAAM,GAAG;YACxB,eAAe,EAAE,CAAC;YAClB,iBAAiB,EAAE,CAAC;YACpB,OAAO,EAAE;gBACP,YAAY,EAAE,GAAG;gBACjB,UAAU,EAAE,IAAI;gBAChB,UAAU,EAAE,IAAI;aACjB;SACF,CAAC;IAKC,CAAC;IAQI,oBAAoB,CAAC,SAAiB,EAAE,SAAiB;QAC/D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,SAAS,MAAM,SAAS,EAAE,CAAC,CAAC;QAG9D,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE;YAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;YACtC,OAAO,CAAC,CAAC;SACV;QAGD,IAAI,SAAS,KAAK,SAAS,EAAE;YAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;YACnC,OAAO,CAAC,CAAC;SACV;QAGD,MAAM,OAAO,GAAG,2BAA2B,CAAC;QAE5C,IAAI;YACF,MAAM,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxC,MAAM,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAExC,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE;gBACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;gBACxE,OAAO,CAAC,CAAC;aACV;YAGD,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,MAAM,CAAC,CAAC,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACzD,OAAO,CAAC,CAAC;aACV;YAGD,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,MAAM,CAAC,CAAC,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACzD,OAAO,CAAC,CAAC;aACV;YAGD,MAAM,SAAS,GAAG,GAAG,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,SAAS,EAAE,CAAC,CAAC;YAG1C,MAAM,WAAW,GAAG,GAAG,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,WAAW,EAAE,CAAC,CAAC;YAG5C,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE;gBAE1B,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,WAAW,EAAE,CAAC,CAAC;aAC7C;YAED,MAAM,UAAU,GAAG,SAAS,GAAG,WAAW,GAAG,WAAW,CAAC;YACzD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,UAAU,EAAE,CAAC,CAAC;YAEhD,OAAO,UAAU,CAAC;SACnB;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,KAAK,EAAE,CAAC,CAAC;YAC7C,OAAO,CAAC,CAAC;SACV;IACH,CAAC;IAQO,kBAAkB,CAAC,SAAiB,EAAE,UAAkB;QAC9D,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC;QAChD,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,CAAC;QAGjD,IAAI,SAAS,IAAI,SAAS,EAAE;YAE1B,MAAM,QAAQ,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAGvE,IAAI,QAAQ,IAAI,CAAC,EAAE;gBACjB,OAAO,CAAC,CAAC;aACV;YAGD,IAAI,QAAQ,IAAI,EAAE,EAAE;gBAClB,OAAO,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;aAC/B;SACF;QAED,OAAO,CAAC,CAAC;IACX,CAAC;IAOO,kBAAkB,CAAC,WAA+B;QAExD,IAAI,CAAC,WAAW,EAAE;YAChB,OAAO,GAAG,CAAC;SACZ;QAGD,IAAI,WAAW,KAAK,8BAA8B,EAAE;YAClD,OAAO,GAAG,CAAC;SACZ;aAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACzC,OAAO,GAAG,CAAC;SACZ;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAOO,mBAAmB,CAAC,MAAW;QACrC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC;QACrD,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QAErE,MAAM,MAAM,GAAG,CACb,WAAW,GAAG,YAAY;YAC1B,SAAS,GAAG,UAAU;YACtB,SAAS,GAAG,UAAU,CACvB,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;qBACD,WAAW,MAAM,YAAY,MAAM,WAAW,GAAG,YAAY;mBAC/D,SAAS,MAAM,UAAU,MAAM,SAAS,GAAG,UAAU;mBACrD,SAAS,MAAM,UAAU,MAAM,SAAS,GAAG,UAAU;YAC5D,MAAM;KACb,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAOD,KAAK,CAAC,iBAAiB,CAAC,KAA4B;;QAClD,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,KAAK,CAAC;QAE/D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;mBACH,SAAS;qBACP,WAAW,IAAI,KAAK;cAC3B,IAAI,IAAI,KAAK;wBACH,cAAc;KACjC,CAAC,CAAC;QAEH,IAAI;YAEF,MAAM,WAAW,GAAG;gBAClB,KAAK,EAAE,cAAc;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE;wBACL,SAAS,EAAE,EAAE;qBACd;iBACF;aACF,CAAC;YAEF,MAAM,aAAa,GAAG;gBACpB,KAAK,EAAE,gBAAgB;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE;wBACL,SAAS,EAAE,EAAE;qBACd;iBACF;aACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE;gBACpC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC5C,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;aACjD,CAAC,CAAC;YAGH,MAAM,CAAC,YAAY,EAAE,cAAc,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACvD,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAgC,WAAW,CAAC;gBAC5E,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAgC,aAAa,CAAC;aAC/E,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,CAAA,MAAA,MAAA,YAAY,CAAC,IAAI,0CAAE,IAAI,0CAAE,MAAM,KAAI,CAAC,QAAQ,CAAC,CAAC;YAC9E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAA,MAAA,MAAA,cAAc,CAAC,IAAI,0CAAE,IAAI,0CAAE,MAAM,KAAI,CAAC,QAAQ,CAAC,CAAC;YAElF,MAAM,YAAY,GAA2B,EAAE,CAAC;YAGhD,MAAM,UAAU,GAAG,CAAA,MAAA,YAAY,CAAC,IAAI,0CAAE,IAAI,KAAI,EAAE,CAAC;YACjD,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE;gBAC5B,MAAM,MAAM,GAAG,GAAG,CAAC,OAAoC,CAAC;gBACxD,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;oBACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,GAAG,IAAI,SAAS,YAAY,CAAC,CAAC;oBAClE,SAAS;iBACV;gBAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;gBAG7E,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;gBAGvE,IAAI,WAAW,KAAK,CAAC,EAAE;oBACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,GAAG,iBAAiB,CAAC,CAAC;oBAC1D,SAAS;iBACV;gBAED,MAAM,MAAM,GAAQ;oBAClB,WAAW;oBACX,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG;oBACjF,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,YAAY,CAAC;iBACxD,CAAC;gBAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,GAAG,OAAO,EAAE,MAAM,CAAC,CAAC;gBAEpD,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;gBACpD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,UAAU,EAAE,CAAC,CAAC;gBAE3D,IAAI,UAAU,IAAI,cAAc,EAAE;oBAChC,YAAY,CAAC,IAAI,CAAC;wBAChB,QAAQ,EAAE,GAAG,CAAC,GAAG;wBACjB,UAAU;wBACV,MAAM,EAAE;4BACN,WAAW,EAAE,MAAM,CAAC,WAAW;4BAC/B,SAAS,EAAE,MAAM,CAAC,SAAS;4BAC3B,SAAS,EAAE,MAAM,CAAC,SAAS;yBAC5B;wBACD,aAAa,kCACR,MAAM,KACT,MAAM,EAAE,QAAQ,GACjB;qBACF,CAAC,CAAC;iBACJ;aACF;YAGD,MAAM,YAAY,GAAG,CAAA,MAAA,cAAc,CAAC,IAAI,0CAAE,IAAI,KAAI,EAAE,CAAC;YACrD,KAAK,MAAM,GAAG,IAAI,YAAY,EAAE;gBAC9B,MAAM,MAAM,GAAG,GAAG,CAAC,OAAoC,CAAC;gBACxD,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;oBACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,GAAG,CAAC,GAAG,IAAI,SAAS,YAAY,CAAC,CAAC;oBACpE,SAAS;iBACV;gBAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,GAAG,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;gBAG/E,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;gBAGvE,IAAI,WAAW,KAAK,CAAC,EAAE;oBACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,GAAG,CAAC,GAAG,iBAAiB,CAAC,CAAC;oBAC5D,SAAS;iBACV;gBAED,MAAM,MAAM,GAAQ;oBAClB,WAAW;oBACX,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG;oBACjF,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,YAAY,CAAC;iBACxD,CAAC;gBAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,GAAG,OAAO,EAAE,MAAM,CAAC,CAAC;gBAEtD,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;gBACpD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,GAAG,UAAU,UAAU,EAAE,CAAC,CAAC;gBAE7D,IAAI,UAAU,IAAI,cAAc,EAAE;oBAChC,YAAY,CAAC,IAAI,CAAC;wBAChB,QAAQ,EAAE,GAAG,CAAC,GAAG;wBACjB,UAAU;wBACV,MAAM,EAAE;4BACN,WAAW,EAAE,MAAM,CAAC,WAAW;4BAC/B,SAAS,EAAE,MAAM,CAAC,SAAS;4BAC3B,SAAS,EAAE,MAAM,CAAC,SAAS;yBAC5B;wBACD,aAAa,kCACR,MAAM,KACT,MAAM,EAAE,UAAU,GACnB;qBACF,CAAC,CAAC;iBACJ;aACF;YAGD,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;YAEzD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,YAAY,CAAC,MAAM,QAAQ,CAAC,CAAC;YACrD,OAAO;gBACL,KAAK,EAAE,YAAY,CAAC,MAAM;gBAC1B,IAAI,EAAE,YAAY;aACnB,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC7D,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAOD,KAAK,CAAC,iBAAiB,CAAC,KAA4B;;QAClD,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,KAAK,CAAC;QAE3D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;eACP,KAAK;eACL,KAAK;oBACA,UAAU;wBACN,cAAc;KACjC,CAAC,CAAC;QAEH,IAAI;YAEF,MAAM,WAAW,GAAG;gBAClB,KAAK,EAAE,aAAa;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE;wBACL,IAAI,EAAE;4BACJ,MAAM,EAAE;gCAEN;oCACE,KAAK,EAAE;wCACL,SAAS,EAAE;4CACT,KAAK,EAAE,KAAK;4CACZ,KAAK,EAAE,GAAG;yCACX;qCACF;iCACF;gCAED;oCACE,KAAK,EAAE;wCACL,IAAI,EAAE;4CACJ,KAAK,EAAE,KAAK;4CACZ,SAAS,EAAE,MAAM;4CACjB,KAAK,EAAE,GAAG;yCACX;qCACF;iCACF;6BACF;4BACD,oBAAoB,EAAE,CAAC;yBACxB;qBACF;iBACF;aACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;YAGrE,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAiC,WAAW,CAAC,CAAC;YAEzG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAA,MAAA,MAAA,YAAY,CAAC,IAAI,0CAAE,IAAI,0CAAE,MAAM,KAAI,CAAC,WAAW,CAAC,CAAC;YAEzE,MAAM,YAAY,GAA8B,EAAE,CAAC;YAGnD,MAAM,IAAI,GAAG,CAAA,MAAA,YAAY,CAAC,IAAI,0CAAE,IAAI,KAAI,EAAE,CAAC;YAC3C,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;gBACtB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAqC,CAAC;gBACzD,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;oBACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,GAAG,IAAI,SAAS,YAAY,CAAC,CAAC;oBAC5D,SAAS;iBACV;gBAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;gBAGvE,MAAM,MAAM,GAAQ;oBAClB,WAAW,EAAE,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC;oBAC/D,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,MAAM,CAAC,WAAW,CAAC;oBAC7D,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,YAAY,CAAC;iBACxD,CAAC;gBAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,OAAO,EAAE,MAAM,CAAC,CAAC;gBAGhD,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;gBACpD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,UAAU,UAAU,EAAE,CAAC,CAAC;gBAGvD,IAAI,UAAU,IAAI,cAAc,EAAE;oBAChC,YAAY,CAAC,IAAI,CAAC;wBAChB,SAAS,EAAE,GAAG,CAAC,GAAG;wBAClB,UAAU;wBACV,MAAM,EAAE;4BACN,WAAW,EAAE,MAAM,CAAC,WAAW;4BAC/B,SAAS,EAAE,MAAM,CAAC,SAAS;4BAC3B,SAAS,EAAE,MAAM,CAAC,SAAS;yBAC5B;wBACD,cAAc,EAAE,MAAM;qBACvB,CAAC,CAAC;iBACJ;aACF;YAGD,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;YAEzD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,YAAY,CAAC,MAAM,QAAQ,CAAC,CAAC;YACrD,OAAO;gBACL,WAAW,EAAE,YAAY;aAC1B,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC7D,MAAM,KAAK,CAAC;SACb;IACH,CAAC;CACF,CAAA;AAnbY,+BAA+B;IAD3C,IAAA,mBAAU,GAAE;qCAc8B,oCAAoB;QAC3B,qDAAwB;GAd/C,+BAA+B,CAmb3C;AAnbY,0EAA+B"}