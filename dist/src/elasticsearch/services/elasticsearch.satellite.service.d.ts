import { Logger } from '@nestjs/common';
import { ElasticsearchService as NestElasticsearchService } from '@nestjs/elasticsearch';
import { ElasticsearchBaseService } from './elasticsearch.base.service';
import { SatelliteQueryDto } from '../dto/satellite-query.dto';
export declare class ElasticsearchSatelliteService extends ElasticsearchBaseService {
    protected readonly elasticsearchService: NestElasticsearchService;
    protected readonly logger: Logger;
    constructor(elasticsearchService: NestElasticsearchService);
    searchSatelliteInfo(queryDto: SatelliteQueryDto): Promise<{
        total: number;
        page: number;
        limit: number;
        hits: any[];
    } | undefined>;
    private getAllSatelliteIndices;
    private aggregateSatelliteResults;
    private generateDocumentKey;
    private mergeDocuments;
    private extractFieldScores;
    private normalizeScore;
    private calculateCustomStringSimilarity;
    private calculateLevenshteinDistance;
    private mergeSimilarityInfo;
    private calculateQuerySimilarity;
    testDirectQuery(name: string): Promise<any>;
    getSatelliteNames(): Promise<string[]>;
    getSatelliteStatuses(): Promise<Array<{
        en: string;
        zh: string;
    }>>;
    private processDirectHits;
    private processHits;
    private calculateFieldSimilarity;
    private readonly statusMap;
    searchRawData(options: {
        indices: string[];
        query: any;
        size?: number;
        from?: number;
    }): Promise<any[]>;
    scrollSearch(options: {
        indices: string[];
        query: any;
        size?: number;
    }): Promise<any[]>;
    getSatelliteOrbitClasses(): Promise<Array<{
        en: string;
        zh: string;
    }>>;
}
