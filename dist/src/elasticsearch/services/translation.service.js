"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TranslationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TranslationService = void 0;
const common_1 = require("@nestjs/common");
const openai_1 = require("openai");
const llm_config_1 = require("../../../config/llm.config");
let TranslationService = TranslationService_1 = class TranslationService {
    constructor() {
        this.logger = new common_1.Logger(TranslationService_1.name);
        this.translationCache = new Map();
        this.apiCallCount = 0;
        this.apiTotalTime = 0;
        this.activeRequests = 0;
        this.requestQueue = [];
        this.paragraphSeparators = ["\n\n", "\n", ". ", "! ", "? "];
        this.consecutiveFailures = 0;
        this.failureStats = {
            contentFilter: 0,
            timeout: 0,
            rateLimit: 0,
            networkError: 0,
            other: 0,
            totalAttempts: 0,
            successfulProcessing: 0
        };
        this.sensitiveReplacements = [
            { pattern: /\bterrorist(s)?\b/gi, replacement: 'extremist $1' },
            { pattern: /\bterrorism\b/gi, replacement: 'extremism' },
            { pattern: /\bexplosive(s)?\b/gi, replacement: 'explosive material' },
            { pattern: /\bbomb(s)?\b/gi, replacement: 'explosive device$1' },
            { pattern: /\bnuclear weapon(s)?\b/gi, replacement: 'special weapon$1' },
            { pattern: /\bmissile(s)?\b/gi, replacement: 'projectile$1' },
            { pattern: /炸弹/g, replacement: '爆炸装置' },
            { pattern: /核武器/g, replacement: '特殊武器' },
            { pattern: /恐怖分子/g, replacement: '极端分子' },
            { pattern: /恐怖主义/g, replacement: '极端主义' }
        ];
        this.translationConfig = this.loadTranslationConfig();
        this.themeExtractionConfig = this.loadThemeExtractionConfig();
        this.openai = new openai_1.default({
            apiKey: this.translationConfig.apiKey,
            baseURL: this.translationConfig.baseURL
        });
        this.logger.log(`翻译服务已初始化 - 翻译模型: ${this.translationConfig.model}, 主题提取模型: ${this.themeExtractionConfig.model}`);
    }
    selectOptimalConfig(textLength) {
        if (textLength > 150000) {
            this.logger.warn(`检测到极长文档 (${textLength}字符)，使用ultra_long模式，可能需要较长处理时间`);
            return 'ultra_long';
        }
        else if (textLength > 100000) {
            this.logger.warn(`检测到超长文档 (${textLength}字符)，自动使用ultra_long模式`);
            return 'ultra_long';
        }
        else if (textLength > 40000) {
            this.logger.debug(`检测到长文档 (${textLength}字符)，使用ultra_long模式`);
            return 'ultra_long';
        }
        else if (textLength > 25000) {
            this.logger.debug(`检测到中等长度文档 (${textLength}字符)，使用high_quality模式`);
            return 'high_quality';
        }
        else if (textLength < 10000) {
            return 'fast';
        }
        else {
            return 'default';
        }
    }
    switchToOptimalConfig(textLength) {
        const originalConfig = Object.assign({}, this.translationConfig);
        const optimalMode = this.selectOptimalConfig(textLength);
        if (optimalMode !== 'default') {
            const newConfig = (0, llm_config_1.getTranslationConfig)(optimalMode);
            this.updateTranslationConfig(newConfig);
            this.logger.log(`已临时切换到${optimalMode}模式，原模型: ${originalConfig.model} -> 新模型: ${newConfig.model}`);
        }
        return originalConfig;
    }
    restoreOriginalConfig(originalConfig) {
        this.updateTranslationConfig(originalConfig);
        this.logger.debug(`已恢复原始配置，模型: ${originalConfig.model}`);
    }
    loadTranslationConfig() {
        const mode = process.env.TRANSLATION_MODE || 'default';
        let config = (0, llm_config_1.getTranslationConfig)(mode);
        const envOverrides = {};
        if (process.env.TRANSLATION_MODEL) {
            envOverrides.model = process.env.TRANSLATION_MODEL;
        }
        if (process.env.QWEN_API_KEY) {
            envOverrides.apiKey = process.env.QWEN_API_KEY;
        }
        if (process.env.QWEN_BASE_URL) {
            envOverrides.baseURL = process.env.QWEN_BASE_URL;
        }
        if (process.env.TRANSLATION_MAX_CONCURRENT) {
            envOverrides.maxConcurrentRequests = parseInt(process.env.TRANSLATION_MAX_CONCURRENT);
        }
        if (process.env.TRANSLATION_TIMEOUT) {
            envOverrides.timeout = parseInt(process.env.TRANSLATION_TIMEOUT);
        }
        if (process.env.TRANSLATION_MAX_RETRIES) {
            envOverrides.maxRetries = parseInt(process.env.TRANSLATION_MAX_RETRIES);
        }
        config = (0, llm_config_1.mergeConfig)(config, envOverrides);
        this.logger.log(`加载翻译配置 - 模式: ${mode}, 模型: ${config.model}`);
        return config;
    }
    loadThemeExtractionConfig() {
        const mode = process.env.THEME_EXTRACTION_MODE || 'default';
        let config = (0, llm_config_1.getThemeExtractionConfig)(mode);
        const envOverrides = {};
        if (process.env.THEME_EXTRACTION_MODEL) {
            envOverrides.model = process.env.THEME_EXTRACTION_MODEL;
        }
        if (process.env.QWEN_API_KEY) {
            envOverrides.apiKey = process.env.QWEN_API_KEY;
        }
        if (process.env.QWEN_BASE_URL) {
            envOverrides.baseURL = process.env.QWEN_BASE_URL;
        }
        if (process.env.THEME_EXTRACTION_MAX_CONCURRENT) {
            envOverrides.maxConcurrentRequests = parseInt(process.env.THEME_EXTRACTION_MAX_CONCURRENT);
        }
        if (process.env.THEME_EXTRACTION_TIMEOUT) {
            envOverrides.timeout = parseInt(process.env.THEME_EXTRACTION_TIMEOUT);
        }
        if (process.env.THEME_EXTRACTION_MAX_RETRIES) {
            envOverrides.maxRetries = parseInt(process.env.THEME_EXTRACTION_MAX_RETRIES);
        }
        config = (0, llm_config_1.mergeConfig)(config, envOverrides);
        this.logger.log(`加载主题提取配置 - 模式: ${mode}, 模型: ${config.model}`);
        return config;
    }
    getTranslationConfig() {
        return Object.assign({}, this.translationConfig);
    }
    getThemeExtractionConfig() {
        return Object.assign({}, this.themeExtractionConfig);
    }
    updateTranslationConfig(newConfig) {
        this.translationConfig = (0, llm_config_1.mergeConfig)(this.translationConfig, newConfig);
        (0, llm_config_1.validateLLMConfig)(this.translationConfig);
        if (newConfig.apiKey || newConfig.baseURL) {
            this.openai = new openai_1.default({
                apiKey: this.translationConfig.apiKey,
                baseURL: this.translationConfig.baseURL
            });
        }
        this.logger.log(`翻译配置已更新 - 模型: ${this.translationConfig.model}`);
    }
    updateThemeExtractionConfig(newConfig) {
        this.themeExtractionConfig = (0, llm_config_1.mergeConfig)(this.themeExtractionConfig, newConfig);
        (0, llm_config_1.validateLLMConfig)(this.themeExtractionConfig);
        this.logger.log(`主题提取配置已更新 - 模型: ${this.themeExtractionConfig.model}`);
    }
    sanitizeSensitiveWords(text) {
        let sanitized = text;
        for (const { pattern, replacement } of this.sensitiveReplacements) {
            sanitized = sanitized.replace(pattern, replacement);
        }
        return sanitized;
    }
    async translateText(text, retryCount = 0, isSegment = false, forceRetranslate = false) {
        if (!text || text.trim() === '') {
            return '';
        }
        const ULTRA_LONG_THRESHOLD = 100000;
        if (!isSegment && text.length > ULTRA_LONG_THRESHOLD) {
            this.logger.warn(`单个文本过长 (${text.length}字符，超过${ULTRA_LONG_THRESHOLD}字符阈值)，跳过翻译`);
            return '';
        }
        this.failureStats.totalAttempts++;
        let inputText = text;
        if (!isSegment) {
            inputText = this.sanitizeSensitiveWords(text);
        }
        const cacheKey = this.generateCacheKey(inputText);
        if (!forceRetranslate && this.translationCache.has(cacheKey)) {
            this.apiCallCount++;
            this.logger.debug(`命中翻译缓存 - 长度: ${inputText.length}`);
            return this.translationCache.get(cacheKey) || '';
        }
        if (!isSegment && inputText.length > this.translationConfig.maxTextLength) {
            return this.translateLongText(inputText, forceRetranslate);
        }
        let originalConfig = null;
        if (!isSegment && inputText.length > 20000) {
            originalConfig = this.switchToOptimalConfig(inputText.length);
        }
        return this.executeWithConcurrencyControl(async () => {
            var _a, _b;
            try {
                this.apiCallCount++;
                const startTime = Date.now();
                const processedText = this.preprocessContent(inputText);
                const completion = await this.openai.chat.completions.create({
                    model: this.translationConfig.model,
                    messages: [
                        { role: 'system', content: this.translationConfig.systemPrompt },
                        { role: 'user', content: processedText }
                    ],
                    max_tokens: this.translationConfig.maxTokens,
                    temperature: this.translationConfig.temperature
                }, {
                    timeout: this.translationConfig.timeout
                });
                const translatedText = ((_b = (_a = completion.choices[0]) === null || _a === void 0 ? void 0 : _a.message) === null || _b === void 0 ? void 0 : _b.content) || '';
                const elapsed = Date.now() - startTime;
                this.apiTotalTime += elapsed;
                this.logger.debug(`翻译API调用完成，耗时: ${elapsed}ms，总调用次数: ${this.apiCallCount}`);
                this.translationCache.set(cacheKey, translatedText);
                this.consecutiveFailures = 0;
                this.failureStats.successfulProcessing++;
                if (originalConfig) {
                    this.restoreOriginalConfig(originalConfig);
                }
                return translatedText;
            }
            catch (error) {
                if (originalConfig) {
                    this.restoreOriginalConfig(originalConfig);
                }
                this.consecutiveFailures++;
                this.logger.error(`翻译失败 - 模型: ${this.translationConfig.model}, 重试次数: ${retryCount}, 错误: ${error.message}`);
                if (error.message.includes('inappropriate content') ||
                    error.message.includes('content_filter') ||
                    error.message.includes('content policy') ||
                    error.message.includes('safety') ||
                    error.message.includes('harmful')) {
                    this.failureStats.contentFilter++;
                    this.logger.warn(`检测到内容过滤错误，错误信息: ${error.message}, 当前重试次数=${retryCount}, 最大重试次数=${this.translationConfig.maxRetries}`);
                    if (retryCount >= (this.translationConfig.maxRetries || 2)) {
                        this.logger.error(`内容过滤错误超过最大重试次数(${this.translationConfig.maxRetries})，当前重试次数=${retryCount}，跳过该文档`);
                        this.logger.error(`原始文本前100字符: ${inputText.substring(0, 100)}...`);
                        throw new Error(`SKIP_DOCUMENT:内容过滤错误超过最大重试次数 - ${error.message}`);
                    }
                    this.logger.warn(`使用智能预处理重试，重试次数: ${retryCount + 1}`);
                    const preprocessingResult = this.intelligentPreprocessing(inputText);
                    this.logger.debug(`智能预处理结果: 策略=${preprocessingResult.strategy}, 敏感词=${preprocessingResult.analysis.original.sensitiveWords.length}个, 风险等级=${preprocessingResult.analysis.original.riskLevel}, 敏感类别=[${preprocessingResult.analysis.original.categories.join(', ')}]`);
                    const originalLength = inputText.length;
                    const processedLength = preprocessingResult.processedText.length;
                    this.logger.debug(`文本预处理: 原长度=${originalLength}, 处理后长度=${processedLength}, 压缩率=${Math.round((1 - processedLength / originalLength) * 100)}%`);
                    if (preprocessingResult.needsManualReview) {
                        this.logger.warn(`文档需要人工审核: 敏感类别=${preprocessingResult.analysis.original.categories.join(', ')}, 敏感词数量=${preprocessingResult.analysis.original.sensitiveWords.length}`);
                        this.logger.debug(`检测到的敏感词: ${preprocessingResult.analysis.original.sensitiveWords.slice(0, 10).join(', ')}${preprocessingResult.analysis.original.sensitiveWords.length > 10 ? '...' : ''}`);
                    }
                    return this.translateText(preprocessingResult.processedText, retryCount + 1, isSegment, forceRetranslate);
                }
                if (error.message.includes('timed out') || error.message.includes('timeout')) {
                    this.failureStats.timeout++;
                    if (retryCount < (this.translationConfig.maxRetries || 2)) {
                        const waitTime = Math.pow(2, retryCount) * (this.translationConfig.retryDelay || 1000);
                        this.logger.warn(`翻译请求超时，${waitTime}ms后重试，重试次数: ${retryCount + 1} - 模型: ${this.translationConfig.model}`);
                        await new Promise(resolve => setTimeout(resolve, waitTime));
                        return this.translateText(inputText, retryCount + 1, isSegment, forceRetranslate);
                    }
                    else {
                        this.logger.error(`翻译超时错误超过最大重试次数(${this.translationConfig.maxRetries})，跳过该文档`);
                        throw new Error(`SKIP_DOCUMENT:翻译超时错误超过最大重试次数`);
                    }
                }
                if (error.message.includes('rate limit') || error.message.includes('429')) {
                    this.failureStats.rateLimit++;
                    if (retryCount < (this.translationConfig.maxRetries || 2)) {
                        const waitTime = (retryCount + 1) * 5000;
                        this.logger.warn(`API限制错误，${waitTime}ms后重试，重试次数: ${retryCount + 1} - 模型: ${this.translationConfig.model}`);
                        await new Promise(resolve => setTimeout(resolve, waitTime));
                        return this.translateText(inputText, retryCount + 1, isSegment, forceRetranslate);
                    }
                    else {
                        this.logger.error(`API限制错误超过最大重试次数(${this.translationConfig.maxRetries})，跳过该文档`);
                        throw new Error(`SKIP_DOCUMENT:API限制错误超过最大重试次数`);
                    }
                }
                if (error.message.includes('network') ||
                    error.message.includes('connection') ||
                    error.message.includes('ENOTFOUND') ||
                    error.message.includes('ECONNRESET') ||
                    error.message.includes('socket')) {
                    this.failureStats.networkError++;
                    this.logger.error(`网络连接错误 - 模型: ${this.translationConfig.model}: ${error.message}`);
                }
                else {
                    this.failureStats.other++;
                }
                this.logger.error(`翻译失败 - 模型: ${this.translationConfig.model}: ${error.message}`, error.stack);
                throw new Error(`翻译失败: ${error.message}`);
            }
        });
    }
    async translateBatch(texts) {
        const results = [];
        for (const text of texts) {
            try {
                const translated = await this.translateText(text);
                results.push(translated);
            }
            catch (error) {
                this.logger.error(`批量翻译出错: ${error.message}`);
                results.push('');
            }
        }
        return results;
    }
    generateCacheKey(text) {
        return text.trim().toLowerCase();
    }
    getAPIStats() {
        return {
            callCount: this.apiCallCount,
            totalTime: this.apiTotalTime,
            averageTime: this.apiCallCount > 0 ? this.apiTotalTime / this.apiCallCount : 0,
            cacheSize: this.translationCache.size
        };
    }
    getFailureStats() {
        const totalFailures = this.failureStats.contentFilter +
            this.failureStats.timeout +
            this.failureStats.rateLimit +
            this.failureStats.networkError +
            this.failureStats.other;
        const successRate = this.failureStats.totalAttempts > 0 ?
            (this.failureStats.successfulProcessing / this.failureStats.totalAttempts * 100).toFixed(2) :
            '0.00';
        return {
            totalAttempts: this.failureStats.totalAttempts,
            successfulProcessing: this.failureStats.successfulProcessing,
            totalFailures: totalFailures,
            successRate: parseFloat(successRate),
            failures: {
                contentFilter: {
                    count: this.failureStats.contentFilter,
                    percentage: totalFailures > 0 ? (this.failureStats.contentFilter / totalFailures * 100).toFixed(2) : '0.00'
                },
                timeout: {
                    count: this.failureStats.timeout,
                    percentage: totalFailures > 0 ? (this.failureStats.timeout / totalFailures * 100).toFixed(2) : '0.00'
                },
                rateLimit: {
                    count: this.failureStats.rateLimit,
                    percentage: totalFailures > 0 ? (this.failureStats.rateLimit / totalFailures * 100).toFixed(2) : '0.00'
                },
                networkError: {
                    count: this.failureStats.networkError,
                    percentage: totalFailures > 0 ? (this.failureStats.networkError / totalFailures * 100).toFixed(2) : '0.00'
                },
                other: {
                    count: this.failureStats.other,
                    percentage: totalFailures > 0 ? (this.failureStats.other / totalFailures * 100).toFixed(2) : '0.00'
                }
            },
            consecutiveFailures: this.consecutiveFailures,
            recommendations: this.generateRecommendations()
        };
    }
    generateRecommendations() {
        const recommendations = [];
        const stats = this.failureStats;
        const totalFailures = stats.contentFilter + stats.timeout + stats.rateLimit + stats.networkError + stats.other;
        if (totalFailures === 0) {
            recommendations.push('系统运行正常，无需特殊优化');
            return recommendations;
        }
        if (stats.contentFilter > totalFailures * 0.3) {
            recommendations.push('内容过滤失败率较高，建议：');
            recommendations.push('  - 加强敏感内容预处理');
            recommendations.push('  - 检查新闻来源的内容质量');
            recommendations.push('  - 考虑调整内容处理策略');
        }
        if (stats.timeout > totalFailures * 0.2) {
            recommendations.push('超时问题频发，建议：');
            recommendations.push('  - 增加请求超时时间');
            recommendations.push('  - 减少批次大小');
            recommendations.push('  - 检查网络连接稳定性');
        }
        if (stats.rateLimit > totalFailures * 0.2) {
            recommendations.push('API限制频繁触发，建议：');
            recommendations.push('  - 减少并发请求数');
            recommendations.push('  - 增加请求间隔时间');
            recommendations.push('  - 考虑升级API套餐');
        }
        if (stats.networkError > totalFailures * 0.1) {
            recommendations.push('网络连接不稳定，建议：');
            recommendations.push('  - 检查网络环境');
            recommendations.push('  - 增加重试机制');
            recommendations.push('  - 考虑使用备用网络');
        }
        return recommendations;
    }
    resetFailureStats() {
        this.failureStats = {
            contentFilter: 0,
            timeout: 0,
            rateLimit: 0,
            networkError: 0,
            other: 0,
            totalAttempts: 0,
            successfulProcessing: 0
        };
        this.consecutiveFailures = 0;
        this.logger.log('失败统计已重置');
    }
    clearCache() {
        const cacheSize = this.translationCache.size;
        this.translationCache.clear();
        this.logger.log(`清除了${cacheSize}条翻译缓存`);
    }
    async executeWithConcurrencyControl(task) {
        if (this.activeRequests < (this.translationConfig.maxConcurrentRequests || 3)) {
            this.activeRequests++;
            try {
                return await task();
            }
            finally {
                this.activeRequests--;
                this.processNextQueuedTask();
            }
        }
        return new Promise((resolve, reject) => {
            this.requestQueue.push(async () => {
                try {
                    const result = await task();
                    resolve(result);
                }
                catch (error) {
                    reject(error);
                }
            });
            this.logger.debug(`当前并发请求数: ${this.activeRequests}, 队列中的请求数: ${this.requestQueue.length}`);
        });
    }
    processNextQueuedTask() {
        if (this.requestQueue.length > 0 && this.activeRequests < (this.translationConfig.maxConcurrentRequests || 3)) {
            const nextTask = this.requestQueue.shift();
            if (nextTask) {
                this.activeRequests++;
                nextTask().finally(() => {
                    this.activeRequests--;
                    this.processNextQueuedTask();
                });
            }
        }
    }
    async translateLongText(text, forceRetranslate = false) {
        const ULTRA_LONG_THRESHOLD = 100000;
        if (text.length > ULTRA_LONG_THRESHOLD) {
            this.logger.warn(`长文本过长 (${text.length}字符，超过${ULTRA_LONG_THRESHOLD}字符阈值)，跳过翻译`);
            return '';
        }
        const originalConfig = this.switchToOptimalConfig(text.length);
        try {
            const isUltraLong = text.length > 50000;
            const segmentInfos = this.splitTextIntoSegments(text, isUltraLong);
            this.logger.debug(`长文本分段处理: 原文长度=${text.length}, 共${segmentInfos.length}段, 超长模式=${isUltraLong}`);
            const translatedSegments = [];
            let successCount = 0;
            let failureCount = 0;
            for (let i = 0; i < segmentInfos.length; i++) {
                const segmentInfo = segmentInfos[i];
                const segment = segmentInfo.segment;
                if (segment.trim()) {
                    try {
                        this.logger.debug(`翻译第${i + 1}/${segmentInfos.length}段，长度: ${segment.length}, 分割类型: ${segmentInfo.separatorType}`);
                        const translated = await this.translateText(segment, 0, true, forceRetranslate);
                        if (this.isValidTranslation(translated, segment)) {
                            translatedSegments.push(translated + segmentInfo.separator);
                            successCount++;
                            this.logger.debug(`段落${i + 1}翻译成功，原长度: ${segment.length}, 译文长度: ${translated.length}`);
                        }
                        else {
                            this.logger.warn(`段落${i + 1}翻译质量不佳，尝试重新翻译`);
                            try {
                                const retranslated = await this.translateText(segment, 0, true, true);
                                if (this.isValidTranslation(retranslated, segment)) {
                                    translatedSegments.push(retranslated + segmentInfo.separator);
                                    successCount++;
                                    this.logger.debug(`段落${i + 1}重新翻译成功`);
                                }
                                else {
                                    this.logger.warn(`段落${i + 1}重新翻译仍然失败，尝试细分翻译`);
                                    const finegrainedResult = await this.attemptFinegrainedTranslation(segment, segmentInfo.separator);
                                    translatedSegments.push(finegrainedResult);
                                    failureCount++;
                                }
                            }
                            catch (retryError) {
                                this.logger.warn(`段落${i + 1}重新翻译出错: ${retryError.message}，尝试细分翻译`);
                                const finegrainedResult = await this.attemptFinegrainedTranslation(segment, segmentInfo.separator);
                                translatedSegments.push(finegrainedResult);
                                failureCount++;
                            }
                        }
                        if (i < segmentInfos.length - 1) {
                            await new Promise(resolve => setTimeout(resolve, 200));
                        }
                    }
                    catch (error) {
                        this.logger.error(`段落${i + 1}翻译失败: ${error.message}`);
                        if (error.message.includes('SKIP_DOCUMENT')) {
                            this.logger.warn(`段落${i + 1}内容敏感，尝试细分翻译`);
                            const finegrainedResult = await this.attemptFinegrainedTranslation(segment, segmentInfo.separator);
                            translatedSegments.push(finegrainedResult);
                        }
                        else {
                            this.logger.warn(`段落${i + 1}翻译失败，尝试细分翻译`);
                            const finegrainedResult = await this.attemptFinegrainedTranslation(segment, segmentInfo.separator);
                            translatedSegments.push(finegrainedResult);
                        }
                        failureCount++;
                    }
                }
                else {
                    translatedSegments.push(segmentInfo.separator);
                }
            }
            this.logger.log(`长文本翻译完成: 成功${successCount}段, 失败${failureCount}段, 总计${segmentInfos.length}段`);
            const failureRate = failureCount / segmentInfos.length;
            if (failureRate > 0.3) {
                this.logger.warn(`长文本翻译失败率过高: ${(failureRate * 100).toFixed(1)}%, 建议检查内容质量`);
            }
            const result = translatedSegments.join('');
            const originalLength = text.length;
            const translatedLength = result.length;
            const lengthRatio = translatedLength / originalLength;
            this.logger.debug(`长文本翻译质量检查: 原文${originalLength}字符 -> 译文${translatedLength}字符, 比例: ${(lengthRatio * 100).toFixed(1)}%`);
            if (lengthRatio < 0.3 && originalLength > 500) {
                this.logger.warn(`翻译结果可能不完整: 原文${originalLength}字符, 译文${translatedLength}字符, 比例过低`);
            }
            this.restoreOriginalConfig(originalConfig);
            return result;
        }
        catch (error) {
            this.restoreOriginalConfig(originalConfig);
            throw error;
        }
    }
    async attemptFinegrainedTranslation(segment, originalSeparator) {
        this.logger.debug(`开始细粒度翻译，段落长度: ${segment.length}`);
        if (segment.length < 100) {
            this.logger.debug(`段落太短，直接保留原文`);
            return segment + originalSeparator;
        }
        const sentences = this.splitIntoSentences(segment);
        const translatedParts = [];
        let successCount = 0;
        let failureCount = 0;
        for (let j = 0; j < sentences.length; j++) {
            const sentence = sentences[j].trim();
            if (sentence.length < 10) {
                translatedParts.push(sentence);
                continue;
            }
            try {
                const translated = await this.translateText(sentence, 0, true, false);
                if (translated && translated.trim() !== '' && translated !== sentence) {
                    translatedParts.push(translated);
                    successCount++;
                }
                else {
                    translatedParts.push(sentence);
                    failureCount++;
                }
            }
            catch (sentenceError) {
                this.logger.debug(`句子翻译失败，保留原文: ${sentence.substring(0, 50)}...`);
                translatedParts.push(sentence);
                failureCount++;
            }
            if (j < sentences.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }
        const result = translatedParts.join(' ') + originalSeparator;
        this.logger.debug(`细粒度翻译完成: 成功${successCount}句, 失败${failureCount}句, 总计${sentences.length}句`);
        return result;
    }
    splitIntoSentences(text) {
        const sentenceEnders = [
            '. ', '! ', '? ', '。', '！', '？',
            '.\n', '!\n', '?\n', '。\n', '！\n', '？\n'
        ];
        let sentences = [text];
        for (const ender of sentenceEnders) {
            const newSentences = [];
            for (const sentence of sentences) {
                if (sentence.includes(ender)) {
                    const parts = sentence.split(ender);
                    for (let i = 0; i < parts.length; i++) {
                        if (i < parts.length - 1) {
                            newSentences.push(parts[i] + ender.trim());
                        }
                        else {
                            if (parts[i].trim()) {
                                newSentences.push(parts[i]);
                            }
                        }
                    }
                }
                else {
                    newSentences.push(sentence);
                }
            }
            sentences = newSentences.filter(s => s.trim().length > 0);
        }
        if (sentences.length > 20) {
            const mergedSentences = [];
            let currentGroup = '';
            for (const sentence of sentences) {
                if (currentGroup.length + sentence.length < 200) {
                    currentGroup += (currentGroup ? ' ' : '') + sentence;
                }
                else {
                    if (currentGroup) {
                        mergedSentences.push(currentGroup);
                    }
                    currentGroup = sentence;
                }
            }
            if (currentGroup) {
                mergedSentences.push(currentGroup);
            }
            sentences = mergedSentences;
        }
        return sentences;
    }
    async translateTitleOnly(title, forceRetranslate = false) {
        var _a, _b, _c;
        if (!title || title.trim() === '') {
            return '';
        }
        const cacheKey = 'title_' + this.generateCacheKey(title);
        if (!forceRetranslate && this.translationCache.has(cacheKey)) {
            this.logger.debug(`命中标题翻译缓存`);
            return this.translationCache.get(cacheKey) || '';
        }
        const titleSystemPrompt = `你是一位专业的翻译专家，专门负责翻译新闻标题。

翻译要求：
1. 只翻译提供的标题文本，不要添加任何额外内容
2. 保持标题的简洁性，通常不超过50个中文字符
3. 如果原标题是问句，翻译后也应该是问句
4. 不要在翻译中包含时间、日期、具体数字等详细信息，除非它们是标题的核心部分
5. 直接输出翻译结果，不要包含任何解释或格式化

示例：
原文：Rocket launch today: Is there a rocket launch and what time?
译文：今日火箭发射：是否有火箭发射以及发射时间？

现在请翻译以下标题：`;
        try {
            const completion = await this.openai.chat.completions.create({
                model: this.translationConfig.model,
                messages: [
                    { role: 'system', content: titleSystemPrompt },
                    { role: 'user', content: title }
                ],
                max_tokens: 200,
                temperature: 0.1
            }, {
                timeout: this.translationConfig.timeout
            });
            const translatedTitle = ((_c = (_b = (_a = completion.choices[0]) === null || _a === void 0 ? void 0 : _a.message) === null || _b === void 0 ? void 0 : _b.content) === null || _c === void 0 ? void 0 : _c.trim()) || '';
            if (this.validateTitleTranslation(translatedTitle, title)) {
                this.translationCache.set(cacheKey, translatedTitle);
                this.logger.debug(`标题翻译成功: "${title}" -> "${translatedTitle}"`);
                return translatedTitle;
            }
            else {
                this.logger.warn(`标题翻译质量不佳，返回原标题: "${translatedTitle}"`);
                return title;
            }
        }
        catch (error) {
            this.logger.error(`标题翻译失败: ${error.message}`);
            return title;
        }
    }
    validateTitleTranslation(translatedTitle, originalTitle) {
        if (!translatedTitle || translatedTitle.trim() === '') {
            return false;
        }
        if (translatedTitle.length > 100) {
            this.logger.debug(`标题翻译过长: ${translatedTitle.length}字符`);
            return false;
        }
        if (translatedTitle.includes('\n') || translatedTitle.includes('：') && translatedTitle.split('：').length > 2) {
            this.logger.debug(`标题翻译包含格式化内容`);
            return false;
        }
        const detailPatterns = [
            /\d{1,2}:\d{2}/,
            /\d+月\d+日/,
            /发射时间表|具体时间|详细信息/,
            /以下|如下|包括|分别|列表/,
            /第一|第二|第三|首先|其次|最后/,
        ];
        for (const pattern of detailPatterns) {
            if (pattern.test(translatedTitle)) {
                this.logger.debug(`标题翻译包含详细信息: ${pattern}`);
                return false;
            }
        }
        const chineseCharCount = (translatedTitle.match(/[\u4e00-\u9fff]/g) || []).length;
        const totalCharCount = translatedTitle.length;
        const chineseRatio = chineseCharCount / totalCharCount;
        if (chineseRatio < 0.5) {
            this.logger.debug(`标题翻译中文比例过低: ${chineseRatio.toFixed(2)}`);
            return false;
        }
        if (translatedTitle === originalTitle) {
            this.logger.debug(`标题翻译与原文相同，可能翻译失败`);
            return false;
        }
        return true;
    }
    isValidTranslation(translated, original) {
        if (!translated || translated.trim() === '') {
            return false;
        }
        const chineseCharCount = (translated.match(/[\u4e00-\u9fff]/g) || []).length;
        const totalCharCount = translated.length;
        const chineseRatio = chineseCharCount / totalCharCount;
        if (chineseRatio < 0.3) {
            this.logger.debug(`翻译质量检查失败：中文字符比例过低 ${chineseRatio.toFixed(2)}`);
            return false;
        }
        const longEnglishPattern = /[a-zA-Z\s]{100,}/g;
        const longEnglishMatches = translated.match(longEnglishPattern);
        if (longEnglishMatches && longEnglishMatches.length > 0) {
            this.logger.debug(`翻译质量检查失败：包含大段未翻译英文，共${longEnglishMatches.length}处`);
            return false;
        }
        const lengthRatio = translated.length / original.length;
        if (lengthRatio < 0.3 || lengthRatio > 3.0) {
            this.logger.debug(`翻译质量检查失败：长度比例异常 ${lengthRatio.toFixed(2)}`);
            return false;
        }
        return true;
    }
    validateCombinedTranslationResult(result, originalTitle, originalSummary, originalContent) {
        if (result.title_cn) {
            if (result.title_cn.length > 100) {
                return { isValid: false, reason: '标题翻译过长，可能包含额外内容' };
            }
            if (result.title_cn.includes('\n') || result.title_cn.includes('：') && result.title_cn.split('：').length > 2) {
                return { isValid: false, reason: '标题翻译包含格式化内容' };
            }
            const detailPatterns = [
                /\d{1,2}:\d{2}/,
                /\d+月\d+日/,
                /发射时间|发射窗口|具体时间/,
                /以下|如下|包括|分别/,
            ];
            for (const pattern of detailPatterns) {
                if (pattern.test(result.title_cn)) {
                    return { isValid: false, reason: '标题翻译包含详细信息或时间表' };
                }
            }
        }
        if (result.content_cn && originalContent) {
            if (!this.isValidTranslation(result.content_cn, originalContent)) {
                return { isValid: false, reason: '内容翻译质量不佳' };
            }
        }
        return { isValid: true };
    }
    splitTextIntoSegments(text, isUltraLong = false) {
        const segments = [];
        let remainingText = text;
        const maxLength = isUltraLong ?
            Math.min(this.translationConfig.maxTextLength * 0.7, 30000) :
            this.translationConfig.maxTextLength;
        while (remainingText.length > 0) {
            if (remainingText.length <= maxLength) {
                segments.push({
                    segment: remainingText,
                    separator: '',
                    separatorType: 'end'
                });
                break;
            }
            let splitIndex = -1;
            let bestSeparator = '';
            let separatorType = '';
            let actualSeparator = '';
            const paragraphIndex = remainingText.lastIndexOf('\n\n', maxLength);
            if (paragraphIndex > maxLength * (isUltraLong ? 0.3 : 0.5)) {
                splitIndex = paragraphIndex;
                bestSeparator = '段落';
                separatorType = 'paragraph';
                actualSeparator = '\n\n';
            }
            if (splitIndex <= 0) {
                for (const separator of ['. ', '! ', '? ', '。', '！', '？']) {
                    const sentenceIndex = remainingText.lastIndexOf(separator, maxLength);
                    if (sentenceIndex > splitIndex && sentenceIndex > maxLength * (isUltraLong ? 0.2 : 0.3)) {
                        splitIndex = sentenceIndex + separator.length;
                        bestSeparator = '句子';
                        separatorType = 'sentence';
                        actualSeparator = '';
                    }
                }
            }
            if (splitIndex <= 0) {
                const lineIndex = remainingText.lastIndexOf('\n', maxLength);
                if (lineIndex > maxLength * (isUltraLong ? 0.2 : 0.3)) {
                    splitIndex = lineIndex;
                    bestSeparator = '行';
                    separatorType = 'line';
                    actualSeparator = '\n';
                }
            }
            if (splitIndex <= 0) {
                const wordBoundaryIndex = remainingText.lastIndexOf(' ', maxLength);
                if (wordBoundaryIndex > maxLength * (isUltraLong ? 0.6 : 0.8)) {
                    splitIndex = wordBoundaryIndex;
                    bestSeparator = '词汇';
                    separatorType = 'word';
                    actualSeparator = ' ';
                }
            }
            if (splitIndex <= 0) {
                splitIndex = maxLength;
                bestSeparator = '强制';
                separatorType = 'force';
                actualSeparator = '';
            }
            this.logger.debug(`文本分段: 长度=${splitIndex}, 分割方式=${bestSeparator}, 剩余=${remainingText.length - splitIndex}`);
            const segmentContent = remainingText.substring(0, splitIndex);
            segments.push({
                segment: segmentContent,
                separator: actualSeparator,
                separatorType: separatorType
            });
            if (separatorType === 'paragraph') {
                remainingText = remainingText.substring(splitIndex + 2);
            }
            else if (separatorType === 'line') {
                remainingText = remainingText.substring(splitIndex + 1);
            }
            else if (separatorType === 'word') {
                remainingText = remainingText.substring(splitIndex + 1);
            }
            else {
                remainingText = remainingText.substring(splitIndex);
            }
        }
        return segments;
    }
    removeAdvertisements(text) {
        const adPatterns = [
            /^\s*广告[:：]/i,
            /^\s*Sponsored[:：]?/i,
            /^\s*Advertisement[:：]?/i,
            /点击(这里|链接)查看全文/i,
            /如果您无法正常浏览此邮件/i,
            /免责声明[:：]/i,
            /本文(来源|来自)/i,
            /更多精彩内容请关注/i,
            /All rights reserved/i,
            /版权所有/i
        ];
        const cleanedLines = text.split(/\n+/).filter(line => {
            return !adPatterns.some(pattern => pattern.test(line.trim()));
        });
        if (cleanedLines.length > 0) {
            const lastLine = cleanedLines[cleanedLines.length - 1].trim();
            if (lastLine.length < 30 && /\b(http|www\.|\.com|\.cn)\b/i.test(lastLine)) {
                cleanedLines.pop();
            }
        }
        return cleanedLines.join('\n').trim();
    }
    preprocessContent(text) {
        let processed = this.removeAdvertisements(text);
        processed = processed
            .replace(/<[^>]+>/g, ' ')
            .replace(/https?:\/\/\S+/g, '[URL]')
            .replace(/\S+@\S+\.\S+/g, '[EMAIL]')
            .replace(/\s{2,}/g, ' ')
            .trim();
        return processed;
    }
    applyStricterPreprocessing(text) {
        let processedText = this.preprocessContent(text);
        processedText = processedText
            .replace(/(\d{4,})/g, '[NUMBER]')
            .replace(/[^\w\s.,;:\-()\[\]{}"']/g, ' ')
            .replace(/\b(missile|nuclear|classified|confidential|secret|intelligence|spy|surveillance|hack|breach|leak|sensitive|restricted|clearance|covert|operation|agent|agency|infiltrate|sabotage|conspiracy|whistleblower)\b/gi, '[SENSITIVE]')
            .replace(/\s+/g, ' ')
            .split(/[.!?]\s+/)
            .map(sentence => sentence.trim().substring(0, 100))
            .join('. ')
            .trim();
        if (processedText.length > 1000) {
            processedText = processedText.substring(0, 1000) + '...';
        }
        return processedText;
    }
    async extractThemes(title, content, retryCount = 0) {
        if ((!title || title.trim() === '') && (!content || content.trim() === '')) {
            return '';
        }
        let textForThemeExtraction = '';
        if (title && title.trim() !== '') {
            textForThemeExtraction += `标题：${title}\n\n`;
        }
        if (content && content.trim() !== '') {
            const maxContentLength = 2000;
            const truncatedContent = content.length > maxContentLength
                ? content.substring(0, maxContentLength) + '...'
                : content;
            textForThemeExtraction += `内容：${truncatedContent}`;
        }
        const cacheKey = 'theme_' + this.generateCacheKey(textForThemeExtraction);
        if (this.translationCache.has(cacheKey)) {
            this.logger.debug(`使用缓存的主题提取结果: ${cacheKey.substring(0, 30)}...`);
            return this.translationCache.get(cacheKey) || '';
        }
        return this.executeWithConcurrencyControl(async () => {
            var _a, _b;
            try {
                this.apiCallCount++;
                const startTime = Date.now();
                const completion = await this.openai.chat.completions.create({
                    model: this.themeExtractionConfig.model,
                    messages: [
                        { role: 'system', content: this.themeExtractionConfig.systemPrompt },
                        { role: 'user', content: textForThemeExtraction }
                    ],
                    max_tokens: this.themeExtractionConfig.maxTokens,
                    temperature: this.themeExtractionConfig.temperature
                }, {
                    timeout: this.themeExtractionConfig.timeout
                });
                const themes = ((_b = (_a = completion.choices[0]) === null || _a === void 0 ? void 0 : _a.message) === null || _b === void 0 ? void 0 : _b.content) || '';
                const elapsed = Date.now() - startTime;
                this.apiTotalTime += elapsed;
                this.logger.debug(`主题提取API调用完成，耗时: ${elapsed}ms，总调用次数: ${this.apiCallCount}`);
                this.translationCache.set(cacheKey, themes);
                this.consecutiveFailures = 0;
                return themes;
            }
            catch (error) {
                this.consecutiveFailures++;
                this.logger.warn(`主题提取服务连续失败${this.consecutiveFailures}次 - 模型: ${this.themeExtractionConfig.model}`);
                if (error.message.includes('inappropriate content')) {
                    if (retryCount < (this.themeExtractionConfig.maxRetries || 2)) {
                        this.logger.warn(`检测到不适当内容，尝试进一步处理后重试，重试次数: ${retryCount + 1}`);
                        const furtherProcessedTitle = this.applyStricterPreprocessing(title);
                        const furtherProcessedContent = this.applyStricterPreprocessing(content);
                        return this.extractThemes(furtherProcessedTitle, furtherProcessedContent, retryCount + 1);
                    }
                    else {
                        this.logger.error(`主题提取内容不适当错误超过最大重试次数(${this.themeExtractionConfig.maxRetries})，跳过该文档`);
                        throw new Error(`SKIP_DOCUMENT:主题提取内容不适当错误超过最大重试次数`);
                    }
                }
                if (error.message.includes('timed out')) {
                    if (retryCount < (this.themeExtractionConfig.maxRetries || 2)) {
                        const waitTime = Math.pow(2, retryCount) * (this.themeExtractionConfig.retryDelay || 1000);
                        this.logger.warn(`主题提取请求超时，${waitTime}ms后重试，重试次数: ${retryCount + 1} - 模型: ${this.themeExtractionConfig.model}`);
                        await new Promise(resolve => setTimeout(resolve, waitTime));
                        return this.extractThemes(title, content, retryCount + 1);
                    }
                    else {
                        this.logger.error(`主题提取超时错误超过最大重试次数(${this.themeExtractionConfig.maxRetries})，跳过该文档`);
                        throw new Error(`SKIP_DOCUMENT:主题提取超时错误超过最大重试次数`);
                    }
                }
                this.logger.error(`主题提取失败 - 模型: ${this.themeExtractionConfig.model}: ${error.message}`, error.stack);
                throw new Error(`主题提取失败: ${error.message}`);
            }
        });
    }
    async translateAndExtractInOneCall(title, summary, content, retryCount = 0, forceRetranslate = false) {
        if ((!title || title.trim() === '') &&
            (!summary || summary.trim() === '') &&
            (!content || content.trim() === '')) {
            return {
                title_cn: '',
                summary_cn: '',
                content_cn: '',
                themes_cn: ''
            };
        }
        const contentLength = (content === null || content === void 0 ? void 0 : content.length) || 0;
        const ULTRA_LONG_THRESHOLD = 100000;
        if (contentLength > ULTRA_LONG_THRESHOLD) {
            this.logger.warn(`文档content过长 (${contentLength}字符，超过${ULTRA_LONG_THRESHOLD}字符阈值)，跳过翻译并清空已有翻译`);
            return {
                title_cn: '',
                summary_cn: '',
                content_cn: '',
                themes_cn: ''
            };
        }
        const totalLength = ((title === null || title === void 0 ? void 0 : title.length) || 0) + ((summary === null || summary === void 0 ? void 0 : summary.length) || 0) + ((content === null || content === void 0 ? void 0 : content.length) || 0);
        const originalConfig = this.switchToOptimalConfig(totalLength);
        const shouldUseSeparateTranslation = content && (content.length > this.translationConfig.maxTextLength * 0.6 ||
            totalLength > this.translationConfig.maxTextLength * 0.8);
        if (shouldUseSeparateTranslation) {
            this.logger.debug(`内容过长(content: ${content === null || content === void 0 ? void 0 : content.length}, total: ${totalLength}字符)，使用分段翻译处理`);
            try {
                const titleCn = title ? await this.translateTitleOnly(title, forceRetranslate) : '';
                const summaryCn = summary ? await this.translateText(summary, 0, true, forceRetranslate) : '';
                const contentCn = await this.translateLongText(content, forceRetranslate);
                const themes = await this.extractThemes(titleCn, contentCn);
                const result = {
                    title_cn: titleCn,
                    summary_cn: summaryCn,
                    content_cn: contentCn,
                    themes_cn: themes
                };
                this.restoreOriginalConfig(originalConfig);
                return result;
            }
            catch (error) {
                this.restoreOriginalConfig(originalConfig);
                throw error;
            }
        }
        let inputText = '';
        if (title && title.trim() !== '') {
            inputText += `标题：${title}\n\n`;
        }
        if (summary && summary.trim() !== '') {
            inputText += `摘要：${summary}\n\n`;
        }
        if (content && content.trim() !== '') {
            inputText += `内容：${content}`;
        }
        const processedInput = this.sanitizeSensitiveWords(inputText);
        const cacheKey = 'combined_' + this.generateCacheKey(processedInput);
        if (!forceRetranslate && this.translationCache.has(cacheKey)) {
            this.logger.debug(`命中组合翻译缓存: ${cacheKey.substring(0, 30)}...`);
            const cachedResult = this.translationCache.get(cacheKey);
            if (cachedResult) {
                try {
                    return JSON.parse(cachedResult);
                }
                catch (parseError) {
                    this.logger.warn(`缓存结果JSON解析失败，重新调用API`);
                }
            }
        }
        const combinedSystemPrompt = `你是一位专业的翻译和文本分析专家，特别擅长航空航天领域。

输入格式说明：
- 输入文本会按照"标题：xxx"、"摘要：xxx"、"内容：xxx"的格式提供
- 你需要分别翻译每个部分，绝对不能混淆各部分的内容

翻译要求：
1. **标题翻译(title_cn)**：
   - 只翻译"标题："后面的部分，保持简洁准确
   - 标题翻译必须简短，通常不超过50个中文字符
   - 如果原标题是问句，翻译后也应该是问句
   - 绝对不要在标题中包含任何列表、时间表、详细信息或内容摘要
   - 标题翻译应该只反映文章的主题，不包含具体细节
   
2. **摘要翻译(summary_cn)**：
   - 只翻译"摘要："后面的部分
   - 保持摘要的特点：简明扼要
   
3. **内容翻译(content_cn)**：
   - 只翻译"内容："后面的部分
   - 保留原文的所有换行符、段落格式和缩进空格
   - 保留数字、时间、专有名词的格式
   
4. **主题提取(themes_cn)**：
   - 基于整篇文章提取不超过5个中文主题词
   - 如果是科普类型添加"科普"，如果是军事类型添加"军事"

**重要警告**：绝对不要将一个字段的内容混入到另一个字段中！

严格按照以下JSON格式返回，不要有其他内容：
{"title_cn":"仅标题翻译","summary_cn":"仅摘要翻译","content_cn":"仅内容翻译","themes_cn":"主题词1,主题词2,主题词3"}`;
        return this.executeWithConcurrencyControl(async () => {
            var _a, _b, _c, _d, _e;
            try {
                this.apiCallCount++;
                const startTime = Date.now();
                const finalProcessedText = this.preprocessContent(processedInput);
                const completion = await this.openai.chat.completions.create({
                    model: this.translationConfig.model,
                    messages: [
                        { role: 'system', content: combinedSystemPrompt },
                        { role: 'user', content: finalProcessedText }
                    ],
                    max_tokens: this.translationConfig.maxTokens || 7000,
                    temperature: this.translationConfig.temperature
                }, {
                    timeout: this.translationConfig.timeout
                });
                const responseContent = ((_b = (_a = completion.choices[0]) === null || _a === void 0 ? void 0 : _a.message) === null || _b === void 0 ? void 0 : _b.content) || '';
                const elapsed = Date.now() - startTime;
                this.apiTotalTime += elapsed;
                this.logger.debug(`组合翻译API调用完成，耗时: ${elapsed}ms，总调用次数: ${this.apiCallCount}`);
                let result;
                try {
                    let cleanedResponse = responseContent.trim();
                    cleanedResponse = cleanedResponse.replace(/^```(?:json|javascript|js)?\s*/i, '');
                    cleanedResponse = cleanedResponse.replace(/\s*```$/i, '');
                    const jsonStartPattern = /\{[\s\S]*?"title_cn"[\s\S]*?\}/;
                    const jsonMatch = cleanedResponse.match(jsonStartPattern);
                    if (jsonMatch) {
                        cleanedResponse = jsonMatch[0];
                    }
                    else {
                        const startIndex = cleanedResponse.indexOf('{');
                        const lastIndex = cleanedResponse.lastIndexOf('}');
                        if (startIndex !== -1 && lastIndex !== -1 && lastIndex > startIndex) {
                            cleanedResponse = cleanedResponse.substring(startIndex, lastIndex + 1);
                        }
                    }
                    result = JSON.parse(cleanedResponse);
                    if (!result.title_cn && !result.summary_cn && !result.content_cn) {
                        throw new Error('响应中缺少翻译字段');
                    }
                    result = {
                        title_cn: this.validateAndCleanTitle(result.title_cn || '', title),
                        summary_cn: this.validateAndCleanField(result.summary_cn || '', summary, 'summary'),
                        content_cn: this.validateAndCleanField(result.content_cn || '', content, 'content'),
                        themes_cn: result.themes_cn || ''
                    };
                    this.logger.debug(`JSON解析成功，提取字段: title_cn=${!!result.title_cn}, summary_cn=${!!result.summary_cn}, content_cn=${!!result.content_cn}, themes_cn=${!!result.themes_cn}`);
                }
                catch (parseError) {
                    this.logger.error(`JSON解析失败，原始响应: ${responseContent}`);
                    this.logger.debug(`解析错误详情: ${parseError.message}`);
                    const lines = responseContent.split('\n');
                    const extractedTitle = this.extractFieldFromResponse(lines, 'title_cn') || title;
                    const extractedSummary = this.extractFieldFromResponse(lines, 'summary_cn') || summary;
                    const extractedContent = this.extractFieldFromResponse(lines, 'content_cn') || content;
                    const extractedThemes = this.extractFieldFromResponse(lines, 'themes_cn') || '';
                    result = {
                        title_cn: this.validateAndCleanTitle(extractedTitle, title),
                        summary_cn: this.validateAndCleanField(extractedSummary, summary, 'summary'),
                        content_cn: this.validateAndCleanField(extractedContent, content, 'content'),
                        themes_cn: extractedThemes
                    };
                    this.logger.warn(`使用备用解析方法，提取结果: ${JSON.stringify(result)}`);
                }
                const validationResult = this.validateCombinedTranslationResult(result, title, summary, content);
                if (!validationResult.isValid) {
                    this.logger.warn(`组合翻译结果验证失败: ${validationResult.reason}，尝试分段翻译`);
                    const titleCn = title ? await this.translateTitleOnly(title, forceRetranslate) : '';
                    const summaryCn = summary ? await this.translateText(summary, 0, true, forceRetranslate) : '';
                    const contentCn = await this.translateLongText(content, forceRetranslate);
                    const themes = await this.extractThemes(titleCn, contentCn);
                    const fallbackResult = {
                        title_cn: titleCn,
                        summary_cn: summaryCn,
                        content_cn: contentCn,
                        themes_cn: themes
                    };
                    this.restoreOriginalConfig(originalConfig);
                    this.logger.debug(`分段翻译完成 - 标题长度: ${(titleCn === null || titleCn === void 0 ? void 0 : titleCn.length) || 0}, 内容长度: ${(contentCn === null || contentCn === void 0 ? void 0 : contentCn.length) || 0}`);
                    return fallbackResult;
                }
                this.translationCache.set(cacheKey, JSON.stringify(result));
                this.consecutiveFailures = 0;
                this.failureStats.successfulProcessing++;
                this.restoreOriginalConfig(originalConfig);
                this.logger.debug(`组合翻译成功 - 模型: ${this.translationConfig.model}, 标题长度: ${((_c = result.title_cn) === null || _c === void 0 ? void 0 : _c.length) || 0}, 内容长度: ${((_d = result.content_cn) === null || _d === void 0 ? void 0 : _d.length) || 0}, 主题数: ${((_e = result.themes_cn) === null || _e === void 0 ? void 0 : _e.split(',').length) || 0}`);
                return result;
            }
            catch (error) {
                this.restoreOriginalConfig(originalConfig);
                this.consecutiveFailures++;
                this.logger.error(`组合翻译失败 - 模型: ${this.translationConfig.model}, 重试次数: ${retryCount}, 错误: ${error.message}`);
                if (error.message.includes('inappropriate content') ||
                    error.message.includes('content_filter') ||
                    error.message.includes('content policy') ||
                    error.message.includes('safety') ||
                    error.message.includes('harmful')) {
                    this.failureStats.contentFilter++;
                    if (retryCount >= (this.translationConfig.maxRetries || 2)) {
                        this.logger.error(`组合翻译内容过滤错误超过最大重试次数(${this.translationConfig.maxRetries})，跳过该文档`);
                        throw new Error(`SKIP_DOCUMENT:组合翻译内容过滤错误超过最大重试次数 - ${error.message}`);
                    }
                    this.logger.warn(`使用智能预处理重试组合翻译，重试次数: ${retryCount + 1}`);
                    const preprocessingResult = this.intelligentPreprocessing(inputText);
                    const retryTitle = this.extractOriginalField(preprocessingResult.processedText, '标题');
                    const retrySummary = this.extractOriginalField(preprocessingResult.processedText, '摘要');
                    const retryContent = this.extractOriginalField(preprocessingResult.processedText, '内容');
                    return this.translateAndExtractInOneCall(retryTitle, retrySummary, retryContent, retryCount + 1);
                }
                if (error.message.includes('timed out') || error.message.includes('timeout')) {
                    this.failureStats.timeout++;
                    if (retryCount < (this.translationConfig.maxRetries || 2)) {
                        const waitTime = Math.pow(2, retryCount) * (this.translationConfig.retryDelay || 1000);
                        this.logger.warn(`组合翻译请求超时，${waitTime}ms后重试，重试次数: ${retryCount + 1}`);
                        await new Promise(resolve => setTimeout(resolve, waitTime));
                        return this.translateAndExtractInOneCall(title, summary, content, retryCount + 1);
                    }
                    else {
                        this.logger.error(`组合翻译超时错误超过最大重试次数，跳过该文档`);
                        throw new Error(`SKIP_DOCUMENT:组合翻译超时错误超过最大重试次数`);
                    }
                }
                if (error.message.includes('rate limit') || error.message.includes('429')) {
                    this.failureStats.rateLimit++;
                    if (retryCount < (this.translationConfig.maxRetries || 2)) {
                        const waitTime = (retryCount + 1) * 5000;
                        this.logger.warn(`组合翻译API限制错误，${waitTime}ms后重试，重试次数: ${retryCount + 1}`);
                        await new Promise(resolve => setTimeout(resolve, waitTime));
                        return this.translateAndExtractInOneCall(title, summary, content, retryCount + 1);
                    }
                    else {
                        this.logger.error(`组合翻译API限制错误超过最大重试次数，跳过该文档`);
                        throw new Error(`SKIP_DOCUMENT:组合翻译API限制错误超过最大重试次数`);
                    }
                }
                if (error.message.includes('network') ||
                    error.message.includes('connection') ||
                    error.message.includes('ENOTFOUND') ||
                    error.message.includes('ECONNRESET') ||
                    error.message.includes('socket')) {
                    this.failureStats.networkError++;
                }
                else {
                    this.failureStats.other++;
                }
                this.logger.error(`组合翻译失败: ${error.message}`, error.stack);
                throw new Error(`组合翻译失败: ${error.message}`);
            }
        });
    }
    extractFieldFromResponse(lines, fieldName) {
        const patterns = [
            new RegExp(`"${fieldName}"\\s*:\\s*"([^"]*)"`, 'i'),
            new RegExp(`"${fieldName}"\\s*:\\s*"([^"\\\\]*(?:\\\\.[^"\\\\]*)*)"`, 'i'),
            new RegExp(`${fieldName}\\s*:\\s*([^,\\n}]+)`, 'i'),
            new RegExp(`${this.getChineseLabel(fieldName)}：([^\\n]+)`, 'i')
        ];
        for (const line of lines) {
            if (line.includes(fieldName) || line.includes(this.getChineseLabel(fieldName))) {
                for (const pattern of patterns) {
                    const match = line.match(pattern);
                    if (match && match[1]) {
                        let value = match[1].trim();
                        value = value.replace(/[,}]$/, '');
                        value = value.replace(/\\"/g, '"').replace(/\\n/g, '\n');
                        return value;
                    }
                }
            }
        }
        return '';
    }
    getChineseLabel(fieldName) {
        const labelMap = {
            'title_cn': '标题',
            'summary_cn': '摘要',
            'content_cn': '内容',
            'themes_cn': '主题词'
        };
        return labelMap[fieldName] || fieldName;
    }
    validateAndCleanTitle(translatedTitle, originalTitle) {
        if (!translatedTitle || translatedTitle.trim() === '') {
            this.logger.warn('标题翻译为空，返回原标题');
            return originalTitle;
        }
        let cleaned = translatedTitle.trim();
        const hasListContent = /(?:\n.*){3,}|(?:\d+\.\s+)|(?:-\s+.*\n)|(?:Launch Time:|Mission:|Status:|Location:)/i.test(cleaned);
        if (hasListContent) {
            this.logger.warn('标题翻译包含列表或大段内容，截取第一行作为标题');
            const firstLine = cleaned.split('\n')[0].trim();
            if (firstLine && firstLine.length > 5) {
                cleaned = firstLine;
            }
            else {
                return originalTitle;
            }
        }
        if (cleaned.includes(originalTitle)) {
            this.logger.warn('标题翻译包含原文，尝试提取纯翻译部分');
            cleaned = cleaned.replace(originalTitle, '').trim();
            cleaned = cleaned.replace(/^[：:：\-\s]+/, '').replace(/[：:：\-\s]+$/, '');
        }
        const maxTitleLength = Math.max(originalTitle.length * 3, 150);
        if (cleaned.length > maxTitleLength) {
            this.logger.warn(`标题翻译过长(${cleaned.length}字符)，可能包含其他内容，强制截取`);
            const sentenceEnd = cleaned.search(/[。！？.!?]/);
            if (sentenceEnd > 0 && sentenceEnd < maxTitleLength) {
                cleaned = cleaned.substring(0, sentenceEnd).trim();
            }
            else {
                cleaned = cleaned.substring(0, maxTitleLength).trim();
            }
        }
        const nonTitlePatterns = [
            /摘要[:：]/i,
            /内容[:：]/i,
            /主题词[:：]/i,
            /\n\n/,
            /^['"「『]/,
            /['"」』]$/,
            /\d+\.\s+/,
            /-\s+.*:/,
            /Launch Time:|Mission:|Status:|Location:/i,
            /UTC|AM|PM/i,
            /Pad\s+\d+/i,
            /Scheduled|Under Review|Postponed/i,
            /Please note/i,
            /today.*schedule/i
        ];
        for (const pattern of nonTitlePatterns) {
            if (pattern.test(cleaned)) {
                this.logger.warn(`标题翻译包含非标题内容(${pattern})，截取到分隔符前或使用原标题`);
                const match = cleaned.match(pattern);
                if (match && match.index !== undefined && match.index > 10) {
                    cleaned = cleaned.substring(0, match.index).trim();
                }
                else {
                    return originalTitle;
                }
                break;
            }
        }
        const isQuestion = /^[^.!。！]*[?？]$/.test(originalTitle.trim());
        if (isQuestion && !cleaned.includes('?') && !cleaned.includes('？')) {
            this.logger.warn('原标题是问句但翻译后不包含问号，可能混入其他内容');
            if (cleaned.length > originalTitle.length * 2) {
                return originalTitle;
            }
        }
        if (!cleaned || cleaned.length < 3) {
            this.logger.warn('标题翻译清理后过短，返回原标题');
            return originalTitle;
        }
        const englishRatio = (cleaned.match(/[a-zA-Z]/g) || []).length / cleaned.length;
        if (englishRatio > 0.8 && cleaned !== originalTitle) {
            this.logger.warn('标题翻译包含过多英文，可能翻译失败，返回原标题');
            return originalTitle;
        }
        this.logger.debug(`标题验证通过: "${cleaned}"`);
        return cleaned;
    }
    validateAndCleanField(translatedField, originalField, fieldType) {
        if (!translatedField || translatedField.trim() === '') {
            this.logger.warn(`${fieldType}翻译为空，返回原文`);
            return originalField;
        }
        let cleaned = translatedField.trim();
        const lengthRatio = cleaned.length / originalField.length;
        const englishChars = (cleaned.match(/[a-zA-Z]/g) || []).length;
        const totalChars = cleaned.length;
        const englishRatio = englishChars / totalChars;
        if (englishRatio > 0.6 && cleaned !== originalField && originalField.length > 100) {
            this.logger.warn(`${fieldType}翻译包含过多英文(${(englishRatio * 100).toFixed(1)}%)，可能翻译失败`);
        }
        if (lengthRatio < 0.1 && originalField.length > 200) {
            this.logger.warn(`${fieldType}翻译过短(比例${(lengthRatio * 100).toFixed(1)}%)，可能不完整`);
        }
        else if (lengthRatio > 5 && originalField.length > 100) {
            this.logger.warn(`${fieldType}翻译过长(比例${(lengthRatio * 100).toFixed(1)}%)，可能包含额外内容`);
        }
        this.logger.debug(`${fieldType}验证通过，长度比例: ${(lengthRatio * 100).toFixed(1)}%, 英文比例: ${(englishRatio * 100).toFixed(1)}%`);
        return cleaned;
    }
    extractOriginalField(processedText, fieldLabel) {
        const regex = new RegExp(`${fieldLabel}：([\\s\\S]*?)(?=\\n\\n|$)`, 'i');
        const match = processedText.match(regex);
        return match ? match[1].trim() : '';
    }
    analyzeSensitiveContent(text) {
        const sensitivePatterns = {
            security: /\b(terrorism|terrorist|bomb|explosive|attack|kill|weapon|suicide|violent|war|conflict|military|missile|nuclear|assassination|threat|hostage|extremist|radical|jihad|massacre|combat|battlefield|warfare|fighter|bomber|destroyer|battleship|tank|artillery|grenade|rifle|pistol|ammunition|soldier|troops|combat|invasion|occupation|siege|raid|assault|warfare|defense|offensive|strike|launch|target|enemy|hostile|aggression|counterattack)\b/gi,
            drugs: /\b(drug|cocaine|heroin|marijuana|cannabis|meth|amphetamine|opium|narcotic|substance abuse|overdose|smuggling|trafficking|dealer|addict|rehabilitation|withdrawal)\b/gi,
            adult: /\b(sex|porn|nude|naked|adult|explicit|xxx|erotic|obscene|prostitute|brothel|strip|sexual|intimacy|seduction)\b/gi,
            political: /\b(genocide|torture|dictatorship|oppression|censorship|propaganda|corruption|riot|protest|revolution|rebellion|uprising|coup|regime|authoritarian|totalitarian|persecution|discrimination|violation|human rights|freedom|democracy|election fraud|suppression)\b/gi,
            intelligence: /\b(missile|nuclear|classified|confidential|secret|intelligence|spy|surveillance|hack|breach|leak|sensitive|restricted|clearance|covert|operation|agent|agency|infiltrate|sabotage|conspiracy|whistleblower|espionage|reconnaissance|undercover|mole|double agent|black ops|cyber attack|data breach|national security|top secret|state secrets)\b/gi,
            violence: /\b(death|murder|torture|abuse|violence|blood|injury|harm|damage|destroy|destruction|killing|slaughter|execution|brutality|cruelty|savage|vicious|ruthless|merciless)\b/gi,
            cybersecurity: /\b(hacker|hacking|malware|virus|trojan|phishing|ransomware|ddos|cyber|breach|vulnerability|exploit|penetration|backdoor|rootkit|keylogger|botnet|dark web|anonymous|zero day)\b/gi,
            terrorism_extended: /\b(isis|al.?qaeda|taliban|suicide bomber|car bomb|ied|improvised explosive|terror attack|mass shooting|school shooting|public attack|lone wolf|radicalization|indoctrination)\b/gi,
            military_tech: /\b(drone|uav|guided missile|ballistic|intercontinental|submarine|aircraft carrier|stealth|radar|sonar|satellite surveillance|gps jamming|electronic warfare|signal intelligence)\b/gi
        };
        const sensitiveWords = [];
        const categories = [];
        let totalMatches = 0;
        Object.entries(sensitivePatterns).forEach(([category, pattern]) => {
            const matches = text.match(pattern);
            if (matches) {
                sensitiveWords.push(...matches);
                categories.push(category);
                totalMatches += matches.length;
            }
        });
        let riskLevel = 'low';
        if (totalMatches > 15 ||
            categories.includes('terrorism_extended') ||
            categories.includes('cybersecurity') ||
            (categories.includes('security') && categories.includes('intelligence')) ||
            (categories.includes('violence') && totalMatches > 8)) {
            riskLevel = 'high';
        }
        else if (totalMatches > 8 ||
            categories.length > 3 ||
            categories.includes('security') ||
            categories.includes('intelligence') ||
            categories.includes('military_tech')) {
            riskLevel = 'medium';
        }
        return {
            hasSensitiveContent: sensitiveWords.length > 0,
            sensitiveWords: [...new Set(sensitiveWords)],
            categories: [...new Set(categories)],
            riskLevel
        };
    }
    intelligentPreprocessing(text, title) {
        const analysis = this.analyzeSensitiveContent(text);
        const titleAnalysis = title ? this.analyzeSensitiveContent(title) : null;
        let processedText = text;
        let strategy = 'none';
        let needsManualReview = false;
        const isNewsContent = /\b(报道|新闻|发布|宣布|消息|据.*报告|according to|reported|announced|news|press|media)\b/i.test(text + (title || ''));
        const isTechnicalContent = /\b(技术|科学|研究|实验|数据|algorithm|technology|science|research|satellite|space|orbit)\b/i.test(text + (title || ''));
        if (analysis.riskLevel === 'high') {
            if (isNewsContent || isTechnicalContent) {
                processedText = this.applyContextualPreprocessing(text, 'news_technical');
                strategy = 'contextual_news_technical';
            }
            else {
                processedText = this.applyStricterPreprocessing(text);
                strategy = 'strict';
                needsManualReview = true;
            }
        }
        else if (analysis.riskLevel === 'medium') {
            processedText = this.preprocessContent(text);
            strategy = 'basic';
        }
        else if (analysis.hasSensitiveContent) {
            processedText = this.applyLightPreprocessing(text);
            strategy = 'light';
        }
        return {
            processedText,
            strategy,
            analysis: {
                original: analysis,
                title: titleAnalysis,
                isNewsContent,
                isTechnicalContent
            },
            needsManualReview
        };
    }
    applyContextualPreprocessing(text, contentType) {
        let processedText = text;
        if (contentType === 'news_technical') {
            processedText = processedText
                .replace(/\b(kill|murder|destroy|attack|violence)\b/gi, '[ACTION]')
                .replace(/\b(threat|danger|risk|hazard)\b/gi, '[CONCERN]')
                .replace(/\b(classified|secret|confidential)(?!\s+(information|data|technology))/gi, '[RESTRICTED]')
                .replace(/\b(terrorism|terrorist|extremist|radical)\b/gi, '[GROUP]');
        }
        else {
            processedText = this.preprocessContent(text);
        }
        return processedText.trim();
    }
    applyLightPreprocessing(text) {
        return text
            .replace(/\b(terrorism|terrorist|bomb|explosive|kill|murder|suicide)\b/gi, '[REDACTED]')
            .replace(/\b(porn|nude|explicit|xxx|obscene)\b/gi, '[FILTERED]')
            .replace(/https?:\/\/\S+/g, '[URL]')
            .replace(/\S+@\S+\.\S+/g, '[EMAIL]')
            .trim();
    }
};
TranslationService = TranslationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], TranslationService);
exports.TranslationService = TranslationService;
//# sourceMappingURL=translation.service.js.map