import { ElasticsearchService } from '@nestjs/elasticsearch';
import { ElasticsearchBaseService } from './elasticsearch.base.service';
import { DebrisToEventQueryDto, EventToDebrisQueryDto } from '../dto/debris-event-correlation.dto';
import { EventCorrelationResultDto } from '../dto/correlation-response.dto';
import { CorrelationResponseDto } from '../dto/correlation-response.dto';
export declare class ElasticsearchCorrelationService {
    private readonly elasticsearchService;
    private readonly esBaseService;
    private readonly logger;
    private readonly config;
    constructor(elasticsearchService: ElasticsearchService, esBaseService: ElasticsearchBaseService);
    private calculateCosparMatch;
    private calculateTimeScore;
    private calculateNameScore;
    private calculateConfidence;
    findRelatedEvents(query: DebrisToEventQueryDto): Promise<CorrelationResponseDto>;
    findRelatedDebris(query: EventToDebrisQueryDto): Promise<EventCorrelationResultDto>;
}
