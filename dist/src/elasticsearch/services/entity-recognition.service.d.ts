import { HttpService } from '@nestjs/axios';
import { EntityRecognitionResult } from '../types/news.types';
export declare class EntityRecognitionService {
    private readonly httpService;
    private readonly logger;
    private entityCollections;
    private isInitialized;
    private initializationPromise;
    constructor(httpService: HttpService);
    initialize(): Promise<void>;
    private loadEntityCollections;
    private loadSatelliteNames;
    private loadConstellationNames;
    private loadLaunchSiteNamesFromApi;
    private loadRocketNames;
    private loadProviders;
    recognizeEntities(content: string, contentCn?: string): Promise<EntityRecognitionResult>;
    private extractEntities;
    private escapeRegex;
    private findChineseTranslation;
    reloadEntityCollections(): Promise<void>;
    getEntityStatistics(): any;
}
