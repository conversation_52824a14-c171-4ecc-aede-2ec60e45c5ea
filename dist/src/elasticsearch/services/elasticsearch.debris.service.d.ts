import { ElasticsearchBaseService } from './elasticsearch.base.service';
import { DebrisQueryDto } from '../dto/debris-query.dto';
import { DebrisSearchResponse } from '../interfaces/debris-search-response.interface';
import { DebrisEventSearchResponse } from '../interfaces/debris-event-search-response.interface';
export declare class ElasticsearchDebrisService {
    private readonly esBaseService;
    private readonly logger;
    private readonly debrisDiscosIndex;
    private readonly debrisSpacetrackIndex;
    private readonly debrisEventIndex;
    constructor(esBaseService: ElasticsearchBaseService);
    private readonly countryNameMap;
    private readonly reverseCountryNameMap;
    private getCountryCode;
    private getAllCountryNameVariants;
    searchDebris(query: DebrisQueryDto): Promise<DebrisSearchResponse>;
    private searchDiscosIndex;
    private searchSpacetrackIndex;
    private searchSpacetrackByCosparIds;
    private aggregateResults;
    private applyRangeFilters;
    private calculateMatchScores;
    private buildDebrisQueryConditions;
    private buildDebrisEventQueryConditions;
    private addFieldMatchCondition;
    private normalizeDate;
    private checkRangeFieldsMatch;
    private calculateMatchedFields;
    private calculateStringSimilarity;
    private createMatchFieldsDescription;
    searchDebrisEvents(query: DebrisQueryDto & {
        event_id?: string;
        event_type?: string;
        event_time_start?: string;
        event_time_end?: string;
    }): Promise<DebrisEventSearchResponse>;
    getDebrisNames(): Promise<string[]>;
    getDebrisObjectClasses(): Promise<string[]>;
    getDebrisMissions(): Promise<string[]>;
    getDebrisCountries(): Promise<string[]>;
    private searchDebrisByOrbitParams;
    private searchDiscosByCosparIds;
}
