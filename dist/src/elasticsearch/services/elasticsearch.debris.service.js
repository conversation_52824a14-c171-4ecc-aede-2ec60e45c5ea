"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var ElasticsearchDebrisService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElasticsearchDebrisService = void 0;
const common_1 = require("@nestjs/common");
const elasticsearch_base_service_1 = require("./elasticsearch.base.service");
const debris_query_dto_1 = require("../dto/debris-query.dto");
let ElasticsearchDebrisService = ElasticsearchDebrisService_1 = class ElasticsearchDebrisService {
    constructor(esBaseService) {
        this.esBaseService = esBaseService;
        this.logger = new common_1.Logger(ElasticsearchDebrisService_1.name);
        this.debrisDiscosIndex = 'debris_discos';
        this.debrisSpacetrackIndex = 'debris_spacetrack';
        this.debrisEventIndex = 'event_discos';
        this.countryNameMap = {
            'USA': '美国',
            'US': '美国',
            'CIS': '独联体',
            'PRC': '中国',
            'JPN': '日本',
            'UK': '英国',
            'FR': '法国',
            'IN': '印度',
            'IND': '印度',
            'ESA': '欧洲航天局',
            'CA': '加拿大',
            'IT': '意大利',
            'KR': '韩国',
            'ASRA': '澳大利亚',
            'ISR': '以色列',
            'ISRA': '以色列',
            'BR': '巴西',
            'DE': '德国',
            'GER': '德国',
            'ES': '西班牙',
            'UA': '乌克兰',
            'NL': '荷兰',
            'TH': '泰国',
            'IR': '伊朗',
            'IRAN': '伊朗',
            'PK': '巴基斯坦',
            'AE': '阿联酋',
            'SG': '新加坡',
            'MY': '马来西亚',
            'ID': '印度尼西亚',
            'AR': '阿根廷',
            'ARGN': '阿根廷',
            'MX': '墨西哥',
            'CL': '智利',
            'VE': '委内瑞拉',
            'ZA': '南非',
            'NG': '尼日利亚',
            'EG': '埃及',
            'DZ': '阿尔及利亚',
            'MA': '摩洛哥',
            'TN': '突尼斯',
            'KE': '肯尼亚',
            'ET': '埃塞俄比亚',
            'GH': '加纳',
            'SA': '沙特阿拉伯',
            'QA': '卡塔尔',
            'KW': '科威特',
            'BH': '巴林',
            'OM': '阿曼',
            'JO': '约旦',
            'LB': '黎巴嫩',
            'SY': '叙利亚',
            'IQ': '伊拉克',
            'TR': '土耳其',
            'GR': '希腊',
            'PT': '葡萄牙',
            'BE': '比利时',
            'LU': '卢森堡',
            'CH': '瑞士',
            'AT': '奥地利',
            'SE': '瑞典',
            'NO': '挪威',
            'DK': '丹麦',
            'FI': '芬兰',
            'IE': '爱尔兰',
            'IS': '冰岛',
            'PL': '波兰',
            'CZ': '捷克',
            'SK': '斯洛伐克',
            'HU': '匈牙利',
            'RO': '罗马尼亚',
            'BG': '保加利亚',
            'RS': '塞尔维亚',
            'HR': '克罗地亚',
            'SI': '斯洛文尼亚',
            'AL': '阿尔巴尼亚',
            'MK': '北马其顿',
            'ME': '黑山',
            'BA': '波斯尼亚和黑塞哥维那',
            'MD': '摩尔多瓦',
            'BY': '白俄罗斯',
            'LT': '立陶宛',
            'LV': '拉脱维亚',
            'EE': '爱沙尼亚',
            'AM': '亚美尼亚',
            'AZ': '阿塞拜疆',
            'GE': '格鲁吉亚',
            'KZ': '哈萨克斯坦',
            'UZ': '乌兹别克斯坦',
            'TM': '土库曼斯坦',
            'KG': '吉尔吉斯斯坦',
            'TJ': '塔吉克斯坦',
            'MN': '蒙古',
            'KP': '朝鲜',
            'NKOR': '朝鲜',
            'VN': '越南',
            'LA': '老挝',
            'KH': '柬埔寨',
            'MM': '缅甸',
            'BD': '孟加拉国',
            'NP': '尼泊尔',
            'LK': '斯里兰卡',
            'BT': '不丹',
            'MV': '马尔代夫',
            'PH': '菲律宾',
            'BN': '文莱',
            'TL': '东帝汶',
            'PG': '巴布亚新几内亚',
            'FJ': '斐济',
            'SB': '所罗门群岛',
            'VU': '瓦努阿图',
            'NZ': '新西兰',
            'CU': '古巴',
            'DO': '多米尼加共和国',
            'HT': '海地',
            'JM': '牙买加',
            'BS': '巴哈马',
            'BB': '巴巴多斯',
            'TT': '特立尼达和多巴哥',
            'PA': '巴拿马',
            'CR': '哥斯达黎加',
            'NI': '尼加拉瓜',
            'HN': '洪都拉斯',
            'SV': '萨尔瓦多',
            'GT': '危地马拉',
            'BZ': '伯利兹',
            'CO': '哥伦比亚',
            'PE': '秘鲁',
            'EC': '厄瓜多尔',
            'BO': '玻利维亚',
            'PY': '巴拉圭',
            'UY': '乌拉圭',
            'GY': '圭亚那',
            'SR': '苏里南',
            'GF': '法属圭亚那',
            'CSSI': '国际空间站',
            'ISS': '国际空间站',
            'O3B': 'O3b网络',
            'GLOB': '全球星',
            'IRID': '铱星',
            'ONEW': '万维网',
            'MESH': '星链',
            'UNKN': '未知',
            'TBD': '待定',
            'TWN': '台湾',
            'CHBZ': '中巴联合',
            'EUME': '欧洲-中东联合',
            'ITSO': '国际通信卫星组织',
            'ORB': '轨道科学公司',
            'SEAL': '海洋发射平台'
        };
        this.reverseCountryNameMap = Object.entries(this.countryNameMap).reduce((acc, [code, name]) => {
            acc[name] = code;
            return acc;
        }, {});
    }
    getCountryCode(countryName) {
        if (this.countryNameMap[countryName]) {
            return countryName;
        }
        if (this.reverseCountryNameMap[countryName]) {
            return this.reverseCountryNameMap[countryName];
        }
        return countryName;
    }
    getAllCountryNameVariants(countryName) {
        const variants = new Set();
        variants.add(countryName);
        if (this.countryNameMap[countryName]) {
            variants.add(this.countryNameMap[countryName]);
        }
        if (this.reverseCountryNameMap[countryName]) {
            variants.add(this.reverseCountryNameMap[countryName]);
        }
        return Array.from(variants);
    }
    async searchDebris(query) {
        try {
            this.logger.debug(`搜索碎片信息，参数: ${JSON.stringify(query)}`);
            const { page = 1, limit = 10, sort, period_minutes_range, incl_degrees_range, apogee_km_range, perigee_km_range } = query, filters = __rest(query, ["page", "limit", "sort", "period_minutes_range", "incl_degrees_range", "apogee_km_range", "perigee_km_range"]);
            const sortField = sort === null || sort === void 0 ? void 0 : sort.field;
            const sortOrder = (sort === null || sort === void 0 ? void 0 : sort.order) || 'desc';
            const hasOrbitParamsOnly = ((period_minutes_range !== undefined ||
                incl_degrees_range !== undefined ||
                apogee_km_range !== undefined ||
                perigee_km_range !== undefined) &&
                Object.keys(filters).length === 0);
            this.logger.debug(`是否只有轨道参数范围条件: ${hasOrbitParamsOnly}`);
            let aggregatedResults = [];
            if (hasOrbitParamsOnly) {
                this.logger.debug('只有轨道参数范围条件，直接从debris_spacetrack索引中搜索');
                aggregatedResults = await this.searchDebrisByOrbitParams({ period_minutes_range, incl_degrees_range, apogee_km_range, perigee_km_range }, page, limit, sortField, sortOrder);
            }
            else {
                const must = this.buildDebrisQueryConditions(filters);
                let discosResults = await this.searchDiscosIndex(must, page, limit, sortField, sortOrder);
                if (discosResults.length > 0) {
                    const cosparIds = discosResults.map(item => item.cospar_id).filter((id) => !!id);
                    this.logger.debug(`从debris_discos结果中提取的cospar_id: ${JSON.stringify(cosparIds)}`);
                    if (cosparIds.length > 0) {
                        const spacetrackResults = await this.searchSpacetrackByCosparIds(cosparIds);
                        this.logger.debug(`在debris_spacetrack中找到匹配的文档数量: ${spacetrackResults.length}`);
                        aggregatedResults = this.aggregateResults(discosResults, spacetrackResults);
                        this.logger.debug(`聚合后的结果包含field_values的项目数: ${aggregatedResults.filter(item => item.field_values && Object.keys(item.field_values).length > 0).length}`);
                    }
                    else {
                        this.logger.debug(`没有找到有效的cospar_id，使用原始discos结果`);
                        aggregatedResults = discosResults;
                    }
                }
                else {
                    this.logger.debug(`debris_discos索引中没有匹配的文档，直接查询debris_spacetrack索引`);
                    aggregatedResults = await this.searchSpacetrackIndex(must, page, limit, sortField, sortOrder);
                }
            }
            const filteredResults = this.applyRangeFilters(aggregatedResults, {
                period_minutes_range,
                incl_degrees_range,
                apogee_km_range,
                perigee_km_range
            });
            const scoredResults = this.calculateMatchScores(filteredResults, filters, {
                period_minutes_range,
                incl_degrees_range,
                apogee_km_range,
                perigee_km_range
            });
            const finalResults = scoredResults.map(item => {
                return {
                    _id: item._id,
                    cospar_id: item.cospar_id,
                    match_score: item.match_score,
                    field_values: item.field_values || {},
                    matched_fields: item.matched_fields || {},
                    matched_fields_description: item.matched_fields_description || [],
                    matched_fields_count: item.matched_fields_count || 0
                };
            });
            return {
                total: finalResults.length,
                page,
                size: limit,
                items: finalResults.slice((page - 1) * limit, page * limit)
            };
        }
        catch (error) {
            this.logger.error(`搜索碎片信息失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async searchDiscosIndex(must, page, limit, sortField, sortOrder = 'desc') {
        const searchBody = {
            query: {
                bool: {
                    must,
                },
            },
            from: 0,
            size: 1000,
        };
        if (sortField) {
            searchBody.sort = [
                {
                    [sortField]: {
                        order: sortOrder,
                    },
                },
            ];
        }
        this.logger.debug(`查询debris_discos索引，查询体: ${JSON.stringify(searchBody)}`);
        const searchResult = await this.esBaseService.search({
            index: this.debrisDiscosIndex,
            body: searchBody,
        });
        const totalHits = searchResult.hits.total.value || 0;
        this.logger.debug(`debris_discos索引查询结果总数: ${totalHits}`);
        if (totalHits === 0) {
            this.logger.debug(`在debris_discos索引中未找到匹配的文档`);
            return [];
        }
        return searchResult.hits.hits.map((hit) => (Object.assign(Object.assign({}, hit._source), { _id: hit._id, _index: this.debrisDiscosIndex, match_score: hit._score })));
    }
    async searchSpacetrackIndex(must, page, limit, sortField, sortOrder = 'desc') {
        const searchBody = {
            query: {
                bool: {
                    must,
                },
            },
            from: 0,
            size: 1000,
        };
        if (sortField) {
            searchBody.sort = [
                {
                    [sortField]: {
                        order: sortOrder,
                    },
                },
            ];
        }
        const searchResult = await this.esBaseService.search({
            index: this.debrisSpacetrackIndex,
            body: searchBody,
        });
        return searchResult.hits.hits.map((hit) => {
            const source = hit._source;
            const result = Object.assign(Object.assign({}, source), { _id: hit._id, _index: this.debrisSpacetrackIndex, match_score: hit._score, source_index: this.debrisSpacetrackIndex, source_fields: {}, field_values: {} });
            for (const key of Object.keys(source)) {
                if (key === '_id' || key === '_index' || key === 'match_score' ||
                    key === 'source_fields' || key === 'source_index' || key === 'field_values')
                    continue;
                const value = source[key];
                if (value !== undefined && result.source_fields && result.field_values) {
                    result.source_fields[key] = this.debrisSpacetrackIndex;
                    result.field_values[key] = [
                        { value, source: this.debrisSpacetrackIndex }
                    ];
                }
            }
            return result;
        });
    }
    async searchSpacetrackByCosparIds(cosparIds) {
        this.logger.debug(`根据cospar_id搜索debris_spacetrack索引，cospar_ids: ${JSON.stringify(cosparIds)}`);
        const should = cosparIds.map(cosparId => ({
            term: {
                'cospar_id.keyword': {
                    value: cosparId
                }
            }
        }));
        this.logger.debug(`构建的should查询条件: ${JSON.stringify(should)}`);
        const searchBody = {
            query: {
                bool: {
                    should: should,
                    minimum_should_match: 1
                }
            },
            size: 1000
        };
        searchBody.sort = [
            {
                _id: {
                    order: 'desc'
                }
            }
        ];
        try {
            const searchResult = await this.esBaseService.search({
                index: this.debrisSpacetrackIndex,
                body: searchBody
            });
            const results = searchResult.hits.hits.map((hit) => {
                this.logger.debug(`找到匹配文档: _id=${hit._id}, cospar_id=${hit._source.cospar_id}, source=${JSON.stringify(hit._source)}`);
                const source = hit._source;
                const result = Object.assign(Object.assign({}, source), { _id: hit._id, _index: this.debrisSpacetrackIndex, match_score: hit._score, source_index: this.debrisSpacetrackIndex, source_fields: {}, field_values: {} });
                for (const key of Object.keys(source)) {
                    if (key === '_id' || key === '_index' || key === 'match_score' ||
                        key === 'source_fields' || key === 'source_index' || key === 'field_values')
                        continue;
                    const value = source[key];
                    if (value !== undefined && result.source_fields && result.field_values) {
                        result.source_fields[key] = this.debrisSpacetrackIndex;
                        result.field_values[key] = [
                            { value, source: this.debrisSpacetrackIndex }
                        ];
                    }
                }
                return result;
            });
            const foundCosparIds = results.map(item => item.cospar_id);
            const notFoundCosparIds = cosparIds.filter(id => !foundCosparIds.includes(id));
            this.logger.debug(`从debris_spacetrack索引中找到${results.length}个匹配文档`);
            this.logger.debug(`找到的cospar_ids: ${JSON.stringify(foundCosparIds)}`);
            this.logger.debug(`未找到的cospar_ids: ${JSON.stringify(notFoundCosparIds)}`);
            return results;
        }
        catch (error) {
            this.logger.error(`搜索debris_spacetrack索引失败: ${error.message}`, error.stack);
            return [];
        }
    }
    aggregateResults(discosResults, spacetrackResults) {
        const aggregated = [];
        this.logger.debug(`开始聚合结果: discos结果数量=${discosResults.length}, spacetrack结果数量=${spacetrackResults.length}`);
        const spacetrackByCosparId = new Map();
        this.logger.debug(`处理前的spacetrack文档: ${JSON.stringify(spacetrackResults.map(item => ({
            _id: item._id,
            cospar_id: item.cospar_id,
            name: item.name
        })))}`);
        for (const item of spacetrackResults) {
            if (!item.cospar_id) {
                this.logger.debug(`跳过没有cospar_id的spacetrack文档: _id=${item._id}`);
                continue;
            }
            const existing = spacetrackByCosparId.get(item.cospar_id);
            if (!existing || item._id > existing._id) {
                spacetrackByCosparId.set(item.cospar_id, item);
                this.logger.debug(`为cospar_id=${item.cospar_id}设置spacetrack文档: _id=${item._id}`);
            }
            else {
                this.logger.debug(`忽略cospar_id=${item.cospar_id}的旧spacetrack文档: _id=${item._id}, 已有_id=${existing._id}`);
            }
        }
        this.logger.debug(`整理后的spacetrack文档数量: ${spacetrackByCosparId.size}, cospar_ids: ${JSON.stringify([...spacetrackByCosparId.keys()])}`);
        this.logger.debug(`所有discos文档的cospar_id: ${JSON.stringify(discosResults.map(item => item.cospar_id))}`);
        let aggregatedWithFieldValues = 0;
        for (const discosItem of discosResults) {
            if (!discosItem.cospar_id) {
                this.logger.debug(`跳过没有cospar_id的discos文档: _id=${discosItem._id}`);
                aggregated.push(Object.assign(Object.assign({}, discosItem), { source_index: this.debrisDiscosIndex }));
                continue;
            }
            const spacetrackItem = spacetrackByCosparId.get(discosItem.cospar_id);
            if (!spacetrackItem) {
                this.logger.debug(`没有找到cospar_id=${discosItem.cospar_id}的spacetrack文档`);
                const modifiedDiscosItem = Object.assign(Object.assign({}, discosItem), { source_index: this.debrisDiscosIndex, source_fields: {}, field_values: {} });
                for (const key of Object.keys(discosItem)) {
                    if (key === '_id' || key === '_index' || key === 'match_score' ||
                        key === 'source_fields' || key === 'source_index' || key === 'field_values')
                        continue;
                    const value = discosItem[key];
                    if (value !== undefined && modifiedDiscosItem.source_fields && modifiedDiscosItem.field_values) {
                        modifiedDiscosItem.source_fields[key] = this.debrisDiscosIndex;
                        modifiedDiscosItem.field_values[key] = [
                            { value, source: this.debrisDiscosIndex }
                        ];
                    }
                }
                aggregated.push(modifiedDiscosItem);
            }
            else {
                this.logger.debug(`为cospar_id=${discosItem.cospar_id}聚合文档，discos文档_id=${discosItem._id}，spacetrack文档_id=${spacetrackItem._id}`);
                this.logger.debug(`discos文档: ${JSON.stringify(discosItem)}`);
                this.logger.debug(`spacetrack文档: ${JSON.stringify(spacetrackItem)}`);
                const aggregatedItem = {
                    _id: discosItem._id,
                    cospar_id: discosItem.cospar_id,
                    match_score: discosItem.match_score,
                    source_fields: {},
                    field_values: {}
                };
                const allKeys = new Set([
                    ...Object.keys(discosItem),
                    ...Object.keys(spacetrackItem)
                ]);
                this.logger.debug(`合并字段列表: ${JSON.stringify([...allKeys])}`);
                for (const key of allKeys) {
                    if (key === '_id' || key === '_index' || key === 'match_score' ||
                        key === 'source_fields' || key === 'source_index' || key === 'field_values')
                        continue;
                    const discosValue = discosItem[key];
                    const spacetrackValue = spacetrackItem[key];
                    this.logger.debug(`处理字段 ${key}: discos值=${JSON.stringify(discosValue)}, spacetrack值=${JSON.stringify(spacetrackValue)}`);
                    if (discosValue !== undefined && spacetrackValue !== undefined) {
                        const isValueEqual = JSON.stringify(discosValue) === JSON.stringify(spacetrackValue);
                        aggregatedItem[key] = discosValue;
                        if (aggregatedItem.source_fields) {
                            aggregatedItem.source_fields[key] = this.debrisDiscosIndex;
                        }
                        if (isValueEqual) {
                            if (aggregatedItem.field_values) {
                                aggregatedItem.field_values[key] = [
                                    { value: discosValue, source: this.debrisDiscosIndex }
                                ];
                            }
                        }
                        else {
                            this.logger.debug(`字段${key}在两个索引中值不同: discos=${JSON.stringify(discosValue)}, spacetrack=${JSON.stringify(spacetrackValue)}`);
                            if (aggregatedItem.field_values) {
                                aggregatedItem.field_values[key] = [
                                    { value: discosValue, source: this.debrisDiscosIndex },
                                    { value: spacetrackValue, source: this.debrisSpacetrackIndex }
                                ];
                            }
                            aggregatedWithFieldValues++;
                        }
                    }
                    else if (discosValue !== undefined) {
                        aggregatedItem[key] = discosValue;
                        if (aggregatedItem.source_fields) {
                            aggregatedItem.source_fields[key] = this.debrisDiscosIndex;
                        }
                        if (aggregatedItem.field_values) {
                            aggregatedItem.field_values[key] = [
                                { value: discosValue, source: this.debrisDiscosIndex }
                            ];
                        }
                    }
                    else if (spacetrackValue !== undefined) {
                        aggregatedItem[key] = spacetrackValue;
                        if (aggregatedItem.source_fields) {
                            aggregatedItem.source_fields[key] = this.debrisSpacetrackIndex;
                        }
                        if (aggregatedItem.field_values) {
                            aggregatedItem.field_values[key] = [
                                { value: spacetrackValue, source: this.debrisSpacetrackIndex }
                            ];
                        }
                    }
                }
                this.logger.debug(`聚合后的文档: ${JSON.stringify({
                    _id: aggregatedItem._id,
                    cospar_id: aggregatedItem.cospar_id,
                    name: aggregatedItem.name,
                    source_fields: aggregatedItem.source_fields,
                    field_values_keys: aggregatedItem.field_values ? Object.keys(aggregatedItem.field_values) : []
                })}`);
                aggregated.push(aggregatedItem);
            }
        }
        this.logger.debug(`聚合完成: 总结果数量=${aggregated.length}, 包含field_values的项目数=${aggregatedWithFieldValues}`);
        for (const [cosparId, spacetrackItem] of spacetrackByCosparId.entries()) {
            if (!discosResults.some(item => item.cospar_id === cosparId)) {
                this.logger.debug(`添加仅在spacetrack中的文档: cospar_id=${cosparId}, _id=${spacetrackItem._id}`);
                const modifiedSpacetrackItem = Object.assign(Object.assign({}, spacetrackItem), { source_index: this.debrisSpacetrackIndex, source_fields: {}, field_values: {} });
                for (const key of Object.keys(spacetrackItem)) {
                    if (key === '_id' || key === '_index' || key === 'match_score' ||
                        key === 'source_fields' || key === 'source_index' || key === 'field_values')
                        continue;
                    const value = spacetrackItem[key];
                    if (value !== undefined && modifiedSpacetrackItem.source_fields && modifiedSpacetrackItem.field_values) {
                        modifiedSpacetrackItem.source_fields[key] = this.debrisSpacetrackIndex;
                        modifiedSpacetrackItem.field_values[key] = [
                            { value, source: this.debrisSpacetrackIndex }
                        ];
                    }
                }
                aggregated.push(modifiedSpacetrackItem);
            }
        }
        return aggregated;
    }
    applyRangeFilters(items, ranges) {
        if (!ranges.period_minutes_range && !ranges.incl_degrees_range &&
            !ranges.apogee_km_range && !ranges.perigee_km_range) {
            return items;
        }
        return items.filter(item => {
            const rangeMatches = this.checkRangeFieldsMatch(item, ranges);
            return rangeMatches.allMatched;
        });
    }
    calculateMatchScores(items, filters, ranges) {
        return items.map(item => {
            const rangeFieldsMatches = this.checkRangeFieldsMatch(item, ranges);
            const matchedFields = this.calculateMatchedFields(item, filters, rangeFieldsMatches.matches);
            const matchedFieldsArr = Object.entries(matchedFields).filter(([_, matched]) => matched).map(([field]) => field);
            const matchedFieldsDescription = this.createMatchFieldsDescription(matchedFields);
            return Object.assign(Object.assign({}, item), { matched_fields: matchedFields, matched_fields_description: matchedFieldsDescription, matched_fields_count: matchedFieldsArr.length, match_score: item.match_score ||
                    (matchedFieldsArr.length +
                        matchedFieldsDescription.reduce((sum, desc) => sum + desc.score, 0)) });
        }).sort((a, b) => {
            if (a.match_score !== b.match_score) {
                return b.match_score - a.match_score;
            }
            if (a.matched_fields_count !== b.matched_fields_count) {
                return b.matched_fields_count - a.matched_fields_count;
            }
            return (b._id || '').localeCompare(a._id || '');
        });
    }
    buildDebrisQueryConditions(filters) {
        const must = [];
        if (filters.keyword) {
            must.push({
                multi_match: {
                    query: filters.keyword,
                    fields: ['name^2', 'cospar_id^2', 'country', 'mission^1.5', 'object_class', 'launch_site', 'rcs_size'],
                    type: 'best_fields',
                    fuzziness: 'AUTO',
                    operator: 'OR',
                    minimum_should_match: '70%'
                },
            });
        }
        if (filters.cospar_id) {
            this.logger.debug(`添加cospar_id查询条件: ${JSON.stringify(filters.cospar_id)}`);
            if (filters.cospar_id.matchType === debris_query_dto_1.MatchType.EXACT) {
                this.logger.debug(`使用精确匹配查询cospar_id: ${filters.cospar_id.value.toString()}`);
                must.push({
                    bool: {
                        should: [
                            { term: { "cospar_id": filters.cospar_id.value.toString() } },
                            { term: { "cospar_id.keyword": filters.cospar_id.value.toString() } }
                        ],
                        minimum_should_match: 1
                    }
                });
            }
            else {
                this.logger.debug(`使用模糊匹配查询cospar_id: ${filters.cospar_id.value.toString()}`);
                must.push({
                    match: {
                        cospar_id: {
                            query: filters.cospar_id.value.toString(),
                            fuzziness: "AUTO"
                        }
                    }
                });
            }
        }
        if (filters.norad_id) {
            must.push({
                term: {
                    norad_id: filters.norad_id.value.toString()
                }
            });
        }
        if (filters.name) {
            const nameQuery = {
                match: {
                    name: {
                        query: filters.name,
                        fuzziness: 'AUTO',
                        operator: 'OR'
                    }
                }
            };
            must.push(nameQuery);
        }
        if (filters.mission) {
            const missionQuery = {
                match: {
                    mission: {
                        query: filters.mission,
                        fuzziness: 'AUTO',
                        operator: 'OR'
                    }
                }
            };
            must.push(missionQuery);
        }
        if (filters.country) {
            const countryValue = filters.country.value.toString();
            this.logger.debug(`处理国家查询条件: ${countryValue}`);
            const countryVariants = this.getAllCountryNameVariants(countryValue);
            this.logger.debug(`国家名称变体: ${JSON.stringify(countryVariants)}`);
            const shouldQueries = countryVariants.map(variant => ({
                match: {
                    country: {
                        query: variant,
                        operator: 'OR'
                    }
                }
            }));
            must.push({
                bool: {
                    should: shouldQueries,
                    minimum_should_match: 1
                }
            });
        }
        if (filters.object_class) {
            must.push({
                match: {
                    object_class: {
                        query: filters.object_class.value.toString(),
                        operator: 'OR'
                    }
                }
            });
        }
        this.addFieldMatchCondition(must, 'launch_date', filters.launch_date);
        this.addFieldMatchCondition(must, 'launch_site', filters.launch_site);
        this.addFieldMatchCondition(must, 'rcs_size', filters.rcs_size);
        this.addFieldMatchCondition(must, 'decay', filters.decay);
        this.addFieldMatchCondition(must, 'first_epoch', filters.first_epoch);
        return must.length ? must : [{ match_all: {} }];
    }
    buildDebrisEventQueryConditions(filters) {
        const must = [];
        if (filters.keyword) {
            must.push({
                multi_match: {
                    query: filters.keyword,
                    fields: ['event_type', 'description'],
                    type: 'best_fields',
                    fuzziness: 'AUTO',
                },
            });
        }
        if (filters.event_id) {
            must.push({
                term: {
                    event_id: filters.event_id,
                },
            });
        }
        if (filters.event_type) {
            must.push({
                match: {
                    event_type: filters.event_type,
                },
            });
        }
        const timeRangeQuery = {};
        if (filters.event_time_start) {
            timeRangeQuery.gte = this.normalizeDate(filters.event_time_start);
        }
        if (filters.event_time_end) {
            timeRangeQuery.lte = this.normalizeDate(filters.event_time_end);
        }
        if (Object.keys(timeRangeQuery).length > 0) {
            must.push({
                range: {
                    event_time: timeRangeQuery,
                },
            });
        }
        return must.length ? must : [{ match_all: {} }];
    }
    addFieldMatchCondition(must, fieldName, matchConfig) {
        if (!matchConfig)
            return;
        const { matchType, value } = matchConfig;
        if (matchType === 'exact') {
            must.push({
                term: {
                    [fieldName]: value,
                },
            });
        }
        else {
            must.push({
                match: {
                    [fieldName]: {
                        query: value,
                        fuzziness: 'AUTO',
                    },
                },
            });
        }
    }
    normalizeDate(date) {
        if (!date)
            return null;
        try {
            const dateObj = new Date(date);
            return dateObj.toISOString();
        }
        catch (error) {
            this.logger.warn(`日期格式化失败: ${date}`);
            return date;
        }
    }
    checkRangeFieldsMatch(source, ranges) {
        const matches = {};
        let allMatched = true;
        const orbitInfo = source.orbit_info || source;
        if (ranges.period_minutes_range) {
            const { min, max } = ranges.period_minutes_range;
            const value = parseFloat(orbitInfo.period_minutes);
            if (!isNaN(value)) {
                const minMatch = min === undefined || value >= min;
                const maxMatch = max === undefined || value <= max;
                matches.period_minutes_range = minMatch && maxMatch;
                if (!matches.period_minutes_range) {
                    allMatched = false;
                }
            }
            else {
                matches.period_minutes_range = false;
                allMatched = false;
            }
        }
        if (ranges.incl_degrees_range) {
            const { min, max } = ranges.incl_degrees_range;
            const value = parseFloat(orbitInfo.incl_degrees);
            if (!isNaN(value)) {
                const minMatch = min === undefined || value >= min;
                const maxMatch = max === undefined || value <= max;
                matches.incl_degrees_range = minMatch && maxMatch;
                if (!matches.incl_degrees_range) {
                    allMatched = false;
                }
            }
            else {
                matches.incl_degrees_range = false;
                allMatched = false;
            }
        }
        if (ranges.apogee_km_range) {
            const { min, max } = ranges.apogee_km_range;
            const value = parseFloat(orbitInfo.apogee_km);
            if (!isNaN(value)) {
                const minMatch = min === undefined || value >= min;
                const maxMatch = max === undefined || value <= max;
                matches.apogee_km_range = minMatch && maxMatch;
                if (!matches.apogee_km_range) {
                    allMatched = false;
                }
            }
            else {
                matches.apogee_km_range = false;
                allMatched = false;
            }
        }
        if (ranges.perigee_km_range) {
            const { min, max } = ranges.perigee_km_range;
            const value = parseFloat(orbitInfo.perigee_km);
            if (!isNaN(value)) {
                const minMatch = min === undefined || value >= min;
                const maxMatch = max === undefined || value <= max;
                matches.perigee_km_range = minMatch && maxMatch;
                if (!matches.perigee_km_range) {
                    allMatched = false;
                }
            }
            else {
                matches.perigee_km_range = false;
                allMatched = false;
            }
        }
        return { allMatched, matches };
    }
    calculateMatchedFields(hit, filters, rangeMatches) {
        const source = hit;
        const matchedFields = {};
        Object.entries(rangeMatches).forEach(([key, value]) => {
            matchedFields[key] = value;
        });
        if (filters.keyword) {
            matchedFields.keyword = true;
        }
        if (filters.norad_id) {
            matchedFields.norad_id = source.norad_id && source.norad_id.toString() === filters.norad_id.value.toString();
        }
        if (filters.cospar_id) {
            matchedFields.cospar_id = source.cospar_id === filters.cospar_id.value.toString();
        }
        if (filters.name) {
            const nameValue = filters.name.toLowerCase();
            const sourceName = source.name ? source.name.toLowerCase() : '';
            const similarity = this.calculateStringSimilarity(sourceName, nameValue);
            matchedFields.name = similarity > 0.6;
            if (!matchedFields.name && source.field_values && source.field_values.name) {
                matchedFields.name = source.field_values.name.some((item) => {
                    const itemName = item.value ? item.value.toString().toLowerCase() : '';
                    const itemSimilarity = this.calculateStringSimilarity(itemName, nameValue);
                    return itemSimilarity > 0.6;
                });
            }
        }
        if (filters.mission) {
            const missionValue = filters.mission.toLowerCase();
            const sourceMission = source.mission ? source.mission.toLowerCase() : '';
            const similarity = this.calculateStringSimilarity(sourceMission, missionValue);
            matchedFields.mission = similarity > 0.6;
            if (!matchedFields.mission && source.field_values && source.field_values.mission) {
                matchedFields.mission = source.field_values.mission.some((item) => {
                    const itemMission = item.value ? item.value.toString().toLowerCase() : '';
                    const itemSimilarity = this.calculateStringSimilarity(itemMission, missionValue);
                    return itemSimilarity > 0.6;
                });
            }
        }
        if (filters.country) {
            const countryValue = filters.country.value.toString().toLowerCase();
            const sourceCountry = source.country ? source.country.toLowerCase() : '';
            const countryWords = countryValue.split(/\s+/);
            const sourceCountryWords = sourceCountry.split(/\s+/);
            matchedFields.country = countryWords.some(word => sourceCountryWords.some((sourceWord) => sourceWord.includes(word) || word.includes(sourceWord)));
            if (!matchedFields.country && source.field_values && source.field_values.country) {
                matchedFields.country = source.field_values.country.some((item) => {
                    const itemCountry = item.value ? item.value.toString().toLowerCase() : '';
                    const itemCountryWords = itemCountry.split(/\s+/);
                    return countryWords.some(word => itemCountryWords.some((itemWord) => itemWord.includes(word) || word.includes(itemWord)));
                });
            }
        }
        if (filters.object_class) {
            const objectClassValue = filters.object_class.value.toString().toLowerCase();
            const sourceObjectClass = source.object_class ? source.object_class.toLowerCase() : '';
            const objectClassWords = objectClassValue.split(/\s+/);
            const sourceObjectClassWords = sourceObjectClass.split(/\s+/);
            matchedFields.object_class = objectClassWords.some(word => sourceObjectClassWords.some((sourceWord) => sourceWord.includes(word) || word.includes(sourceWord)));
            if (!matchedFields.object_class && source.field_values && source.field_values.object_class) {
                matchedFields.object_class = source.field_values.object_class.some((item) => {
                    const itemObjectClass = item.value ? item.value.toString().toLowerCase() : '';
                    const itemObjectClassWords = itemObjectClass.split(/\s+/);
                    return objectClassWords.some(word => itemObjectClassWords.some((itemWord) => itemWord.includes(word) || word.includes(itemWord)));
                });
            }
        }
        return matchedFields;
    }
    calculateStringSimilarity(a, b) {
        if (!a || !b)
            return 0;
        if (a === b)
            return 1;
        if (a.includes(b) || b.includes(a)) {
            return 0.8;
        }
        const matrix = [];
        for (let i = 0; i <= a.length; i++) {
            matrix[i] = [i];
        }
        for (let j = 0; j <= b.length; j++) {
            matrix[0][j] = j;
        }
        for (let i = 1; i <= a.length; i++) {
            for (let j = 1; j <= b.length; j++) {
                const cost = a[i - 1] === b[j - 1] ? 0 : 1;
                matrix[i][j] = Math.min(matrix[i - 1][j] + 1, matrix[i][j - 1] + 1, matrix[i - 1][j - 1] + cost);
            }
        }
        const maxDistance = Math.max(a.length, b.length);
        return 1 - matrix[a.length][b.length] / maxDistance;
    }
    createMatchFieldsDescription(matchedFields) {
        const descriptions = [];
        Object.entries(matchedFields).forEach(([field, matched]) => {
            if (matched) {
                let matchLevel;
                let score;
                if (field === 'norad_id' || field === 'cospar_id') {
                    matchLevel = '精确匹配';
                    score = 1.0;
                }
                else if (field.endsWith('_range')) {
                    matchLevel = '范围匹配';
                    score = 0.8;
                }
                else if (field === 'name' || field === 'mission') {
                    matchLevel = '相似度匹配';
                    score = 0.7;
                }
                else if (field === 'country' || field === 'object_class') {
                    matchLevel = '含义匹配';
                    score = 0.6;
                }
                else {
                    matchLevel = '部分匹配';
                    score = 0.5;
                }
                descriptions.push({
                    field,
                    matchLevel,
                    score
                });
            }
        });
        return descriptions;
    }
    async searchDebrisEvents(query) {
        try {
            const { page = 1, limit = 10, sort } = query, filters = __rest(query, ["page", "limit", "sort"]);
            const sortField = (sort === null || sort === void 0 ? void 0 : sort.field) || 'event_time';
            const sortOrder = (sort === null || sort === void 0 ? void 0 : sort.order) || 'desc';
            const must = this.buildDebrisEventQueryConditions(filters);
            const searchResult = await this.esBaseService.search({
                index: this.debrisEventIndex,
                body: {
                    query: {
                        bool: {
                            must,
                        },
                    },
                    sort: [
                        {
                            [sortField]: {
                                order: sortOrder,
                            },
                        },
                    ],
                    from: (page - 1) * limit,
                    size: limit,
                },
            });
            const total = typeof searchResult.hits.total === 'number'
                ? searchResult.hits.total
                : searchResult.hits.total.value;
            const items = searchResult.hits.hits.map((hit) => {
                const source = hit._source;
                return {
                    _id: hit._id,
                    event_id: source.event_id,
                    event_type: source.event_type,
                    event_time: source.event_time,
                    description: source.description,
                    debris_ids: source.debris_ids,
                    satellite_ids: source.satellite_ids,
                    location: source.location
                };
            });
            return {
                total,
                page,
                size: limit,
                items,
            };
        }
        catch (error) {
            this.logger.error(`搜索碎片事件失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getDebrisNames() {
        try {
            this.logger.debug('获取所有碎片名称集合');
            const discosResult = await this.esBaseService.search({
                index: this.debrisDiscosIndex,
                body: {
                    size: 0,
                    aggs: {
                        unique_names: {
                            terms: {
                                field: 'name.keyword',
                                size: 10000
                            }
                        }
                    }
                }
            });
            const spacetrackResult = await this.esBaseService.search({
                index: this.debrisSpacetrackIndex,
                body: {
                    size: 0,
                    aggs: {
                        unique_names: {
                            terms: {
                                field: 'name.keyword',
                                size: 10000
                            }
                        }
                    }
                }
            });
            const discosNames = discosResult.aggregations && 'unique_names' in discosResult.aggregations
                ? discosResult.aggregations.unique_names.buckets.map((bucket) => bucket.key) || []
                : [];
            const spacetrackNames = spacetrackResult.aggregations && 'unique_names' in spacetrackResult.aggregations
                ? spacetrackResult.aggregations.unique_names.buckets.map((bucket) => bucket.key) || []
                : [];
            const allNames = [...new Set([...discosNames, ...spacetrackNames])];
            const filteredNames = allNames.filter(name => name && name.trim() !== '');
            const sortedNames = filteredNames.sort((a, b) => a.localeCompare(b));
            this.logger.debug(`获取到${sortedNames.length}个唯一碎片名称`);
            return sortedNames;
        }
        catch (error) {
            this.logger.error(`获取碎片名称集合失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getDebrisObjectClasses() {
        try {
            this.logger.debug('获取所有碎片类别集合');
            const discosResult = await this.esBaseService.search({
                index: this.debrisDiscosIndex,
                body: {
                    size: 0,
                    aggs: {
                        unique_classes: {
                            terms: {
                                field: 'object_class.keyword',
                                size: 1000
                            }
                        }
                    }
                }
            });
            const spacetrackResult = await this.esBaseService.search({
                index: this.debrisSpacetrackIndex,
                body: {
                    size: 0,
                    aggs: {
                        unique_classes: {
                            terms: {
                                field: 'object_class.keyword',
                                size: 1000
                            }
                        }
                    }
                }
            });
            const discosClasses = discosResult.aggregations && 'unique_classes' in discosResult.aggregations
                ? discosResult.aggregations.unique_classes.buckets.map((bucket) => bucket.key) || []
                : [];
            const spacetrackClasses = spacetrackResult.aggregations && 'unique_classes' in spacetrackResult.aggregations
                ? spacetrackResult.aggregations.unique_classes.buckets.map((bucket) => bucket.key) || []
                : [];
            const allClasses = [...new Set([...discosClasses, ...spacetrackClasses])];
            const filteredClasses = allClasses.filter(objectClass => objectClass && objectClass.trim() !== '');
            const sortedClasses = filteredClasses.sort((a, b) => a.localeCompare(b));
            this.logger.debug(`获取到${sortedClasses.length}个唯一碎片类别`);
            return sortedClasses;
        }
        catch (error) {
            this.logger.error(`获取碎片类别集合失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getDebrisMissions() {
        try {
            this.logger.debug('获取所有碎片任务名称集合');
            const result = await this.esBaseService.search({
                index: this.debrisDiscosIndex,
                body: {
                    size: 0,
                    aggs: {
                        unique_missions: {
                            terms: {
                                field: 'mission.keyword',
                                size: 10000,
                                exclude: ["", "null"]
                            }
                        }
                    }
                }
            });
            const missions = result.aggregations && 'unique_missions' in result.aggregations
                ? result.aggregations.unique_missions.buckets.map((bucket) => bucket.key) || []
                : [];
            const filteredMissions = missions.filter((mission) => mission && mission.trim() !== '');
            const sortedMissions = filteredMissions.sort((a, b) => a.localeCompare(b));
            this.logger.debug(`获取到${sortedMissions.length}个唯一碎片任务名称`);
            return sortedMissions;
        }
        catch (error) {
            this.logger.error(`获取碎片任务名称集合失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getDebrisCountries() {
        try {
            this.logger.debug('获取所有碎片所属国家集合');
            const spacetrackResult = await this.esBaseService.search({
                index: this.debrisSpacetrackIndex,
                body: {
                    size: 0,
                    aggs: {
                        unique_countries: {
                            terms: {
                                field: 'country.keyword',
                                size: 1000
                            }
                        }
                    }
                }
            });
            const countries = spacetrackResult.aggregations && 'unique_countries' in spacetrackResult.aggregations
                ? spacetrackResult.aggregations.unique_countries.buckets.map((bucket) => bucket.key) || []
                : [];
            const filteredCountries = countries.filter((country) => country && country.trim() !== '');
            const formattedCountries = filteredCountries.map((englishName) => {
                const chineseName = this.countryNameMap[englishName] || englishName;
                return `${chineseName}(${englishName})`;
            });
            const sortedCountries = formattedCountries.sort((a, b) => {
                const chineseNameA = a.split('(')[0];
                const chineseNameB = b.split('(')[0];
                return chineseNameA.localeCompare(chineseNameB, 'zh-CN');
            });
            this.logger.debug(`获取到${sortedCountries.length}个唯一碎片所属国家`);
            return sortedCountries;
        }
        catch (error) {
            this.logger.error(`获取碎片所属国家集合失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async searchDebrisByOrbitParams(ranges, page, limit, sortField, sortOrder = 'desc') {
        this.logger.debug(`根据轨道参数范围搜索碎片信息: ${JSON.stringify(ranges)}`);
        const must = [];
        if (ranges.period_minutes_range) {
            const rangeQuery = {};
            if (ranges.period_minutes_range.min !== undefined) {
                rangeQuery.gte = ranges.period_minutes_range.min;
            }
            if (ranges.period_minutes_range.max !== undefined) {
                rangeQuery.lte = ranges.period_minutes_range.max;
            }
            if (Object.keys(rangeQuery).length > 0) {
                must.push({
                    range: {
                        "orbit_info.period_minutes": rangeQuery
                    }
                });
            }
        }
        if (ranges.incl_degrees_range) {
            const rangeQuery = {};
            if (ranges.incl_degrees_range.min !== undefined) {
                rangeQuery.gte = ranges.incl_degrees_range.min;
            }
            if (ranges.incl_degrees_range.max !== undefined) {
                rangeQuery.lte = ranges.incl_degrees_range.max;
            }
            if (Object.keys(rangeQuery).length > 0) {
                must.push({
                    range: {
                        "orbit_info.incl_degrees": rangeQuery
                    }
                });
            }
        }
        if (ranges.apogee_km_range) {
            const rangeQuery = {};
            if (ranges.apogee_km_range.min !== undefined) {
                rangeQuery.gte = ranges.apogee_km_range.min;
            }
            if (ranges.apogee_km_range.max !== undefined) {
                rangeQuery.lte = ranges.apogee_km_range.max;
            }
            if (Object.keys(rangeQuery).length > 0) {
                must.push({
                    range: {
                        "orbit_info.apogee_km": rangeQuery
                    }
                });
            }
        }
        if (ranges.perigee_km_range) {
            const rangeQuery = {};
            if (ranges.perigee_km_range.min !== undefined) {
                rangeQuery.gte = ranges.perigee_km_range.min;
            }
            if (ranges.perigee_km_range.max !== undefined) {
                rangeQuery.lte = ranges.perigee_km_range.max;
            }
            if (Object.keys(rangeQuery).length > 0) {
                must.push({
                    range: {
                        "orbit_info.perigee_km": rangeQuery
                    }
                });
            }
        }
        if (must.length === 0) {
            this.logger.debug('没有提供任何轨道参数范围条件');
            return [];
        }
        const searchBody = {
            query: {
                bool: {
                    must
                }
            },
            from: 0,
            size: 1000,
            sort: [
                {
                    _id: {
                        order: 'desc'
                    }
                }
            ]
        };
        if (sortField) {
            searchBody.sort.unshift({
                [sortField]: {
                    order: sortOrder
                }
            });
        }
        this.logger.debug(`轨道参数查询体: ${JSON.stringify(searchBody)}`);
        const searchResult = await this.esBaseService.search({
            index: this.debrisSpacetrackIndex,
            body: searchBody
        });
        this.logger.debug(`轨道参数查询结果数量: ${searchResult.hits.hits.length}`);
        const spacetrackByCosparId = new Map();
        for (const hit of searchResult.hits.hits) {
            if (!hit._id || !hit._source) {
                this.logger.warn(`跳过无效的文档: ${JSON.stringify(hit)}`);
                continue;
            }
            const source = hit._source;
            const cosparId = source.cospar_id;
            if (!cosparId) {
                this.logger.debug(`跳过没有cospar_id的文档: _id=${hit._id}`);
                continue;
            }
            const existing = spacetrackByCosparId.get(cosparId);
            if (!existing || hit._id > existing._id) {
                spacetrackByCosparId.set(cosparId, hit);
                this.logger.debug(`为cospar_id=${cosparId}设置文档: _id=${hit._id}`);
            }
            else {
                this.logger.debug(`忽略cospar_id=${cosparId}的旧文档: _id=${hit._id}, 已有_id=${existing._id}`);
            }
        }
        this.logger.debug(`按cospar_id分组后的文档数量: ${spacetrackByCosparId.size}`);
        const cosparIds = Array.from(spacetrackByCosparId.keys());
        if (cosparIds.length === 0) {
            this.logger.debug('没有找到任何匹配的文档');
            return [];
        }
        const discosResults = await this.searchDiscosByCosparIds(cosparIds);
        this.logger.debug(`在debris_discos中找到匹配的文档数量: ${discosResults.length}`);
        const spacetrackResults = Array.from(spacetrackByCosparId.values()).map(hit => {
            const source = hit._source;
            const result = Object.assign(Object.assign({}, source), { _id: hit._id, _index: this.debrisSpacetrackIndex, match_score: hit._score, source_index: this.debrisSpacetrackIndex, source_fields: {}, field_values: {} });
            for (const key of Object.keys(source)) {
                if (key === '_id' || key === '_index' || key === 'match_score' ||
                    key === 'source_fields' || key === 'source_index' || key === 'field_values')
                    continue;
                const value = source[key];
                if (value !== undefined && result.source_fields && result.field_values) {
                    result.source_fields[key] = this.debrisSpacetrackIndex;
                    result.field_values[key] = [
                        { value, source: this.debrisSpacetrackIndex }
                    ];
                }
            }
            return result;
        });
        if (discosResults.length > 0) {
            this.logger.debug('将debris_discos和debris_spacetrack的结果进行聚合');
            return this.aggregateResults(discosResults, spacetrackResults);
        }
        return spacetrackResults;
    }
    async searchDiscosByCosparIds(cosparIds) {
        this.logger.debug(`根据cospar_id搜索debris_discos索引，cospar_ids: ${JSON.stringify(cosparIds)}`);
        const should = cosparIds.map(cosparId => ({
            term: {
                'cospar_id.keyword': {
                    value: cosparId
                }
            }
        }));
        const searchBody = {
            query: {
                bool: {
                    should: should,
                    minimum_should_match: 1
                }
            },
            size: 1000
        };
        try {
            const searchResult = await this.esBaseService.search({
                index: this.debrisDiscosIndex,
                body: searchBody
            });
            const results = [];
            for (const hit of searchResult.hits.hits) {
                if (!hit._id || !hit._source) {
                    this.logger.warn(`跳过无效的文档: ${JSON.stringify(hit)}`);
                    continue;
                }
                results.push(Object.assign(Object.assign({}, hit._source), { _id: hit._id, _index: this.debrisDiscosIndex, match_score: hit._score || 0 }));
            }
            return results;
        }
        catch (error) {
            this.logger.error(`搜索debris_discos索引失败: ${error.message}`, error.stack);
            return [];
        }
    }
};
ElasticsearchDebrisService = ElasticsearchDebrisService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [elasticsearch_base_service_1.ElasticsearchBaseService])
], ElasticsearchDebrisService);
exports.ElasticsearchDebrisService = ElasticsearchDebrisService;
//# sourceMappingURL=elasticsearch.debris.service.js.map