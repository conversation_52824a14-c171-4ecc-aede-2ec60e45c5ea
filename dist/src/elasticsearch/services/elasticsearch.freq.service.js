"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var ElasticsearchFreqService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElasticsearchFreqService = void 0;
const common_1 = require("@nestjs/common");
const elasticsearch_base_service_1 = require("./elasticsearch.base.service");
let ElasticsearchFreqService = ElasticsearchFreqService_1 = class ElasticsearchFreqService {
    constructor(esBaseService) {
        this.esBaseService = esBaseService;
        this.logger = new common_1.Logger(ElasticsearchFreqService_1.name);
        this.freqIndex = 'freq_info';
    }
    async searchFrequencies(query) {
        try {
            const { page = 1, limit = 10 } = query, filters = __rest(query, ["page", "limit"]);
            const must = this.buildFreqQueryConditions(filters);
            const searchResult = await this.esBaseService.search({
                index: this.freqIndex,
                body: {
                    query: {
                        bool: {
                            must,
                        },
                    },
                    sort: [
                        {
                            freq: {
                                order: 'asc',
                            },
                        },
                    ],
                    from: (page - 1) * limit,
                    size: limit,
                },
            });
            const total = typeof searchResult.hits.total === 'number'
                ? searchResult.hits.total
                : searchResult.hits.total.value;
            const items = searchResult.hits.hits.map((hit) => (Object.assign(Object.assign({}, hit._source), { _id: hit._id })));
            return {
                total,
                page,
                size: limit,
                items,
            };
        }
        catch (error) {
            this.logger.error(`搜索频率信息失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getServiceTypes() {
        try {
            const result = await this.esBaseService.search({
                index: this.freqIndex,
                body: {
                    size: 0,
                    aggs: {
                        service_types: {
                            terms: {
                                field: 'service_type.keyword',
                                size: 1000,
                            },
                        },
                    },
                },
            });
            const serviceTypes = result.aggregations && 'service_types' in result.aggregations
                ? result.aggregations.service_types.buckets || []
                : [];
            return serviceTypes.map((bucket) => bucket.key);
        }
        catch (error) {
            this.logger.error(`获取服务类型列表失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getCountries() {
        try {
            const result = await this.esBaseService.search({
                index: this.freqIndex,
                body: {
                    size: 0,
                    aggs: {
                        countries: {
                            terms: {
                                field: 'country.keyword',
                                size: 1000,
                            },
                        },
                    },
                },
            });
            const countries = result.aggregations && 'countries' in result.aggregations
                ? result.aggregations.countries.buckets || []
                : [];
            return countries.map((bucket) => bucket.key);
        }
        catch (error) {
            this.logger.error(`获取国家列表失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getSatellites() {
        try {
            const result = await this.esBaseService.search({
                index: this.freqIndex,
                body: {
                    size: 0,
                    aggs: {
                        satellites: {
                            terms: {
                                field: 'sat_name.keyword',
                                size: 1000,
                            },
                        },
                    },
                },
            });
            const satellites = result.aggregations && 'satellites' in result.aggregations
                ? result.aggregations.satellites.buckets || []
                : [];
            return satellites.map((bucket) => bucket.key);
        }
        catch (error) {
            this.logger.error(`获取卫星列表失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    buildFreqQueryConditions(filters) {
        const must = [];
        if (filters.sat_name) {
            must.push({
                match: {
                    sat_name: {
                        query: filters.sat_name,
                        fuzziness: 'AUTO',
                    },
                },
            });
        }
        if (filters.freq_min || filters.freq_max) {
            const rangeQuery = {};
            if (filters.freq_min) {
                rangeQuery.gte = filters.freq_min;
            }
            if (filters.freq_max) {
                rangeQuery.lte = filters.freq_max;
            }
            must.push({
                range: {
                    freq: rangeQuery,
                },
            });
        }
        if (filters.service_type) {
            must.push({
                match: {
                    service_type: filters.service_type,
                },
            });
        }
        if (filters.country) {
            must.push({
                match: {
                    country: filters.country,
                },
            });
        }
        return must.length ? must : [{ match_all: {} }];
    }
};
ElasticsearchFreqService = ElasticsearchFreqService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [elasticsearch_base_service_1.ElasticsearchBaseService])
], ElasticsearchFreqService);
exports.ElasticsearchFreqService = ElasticsearchFreqService;
//# sourceMappingURL=elasticsearch.freq.service.js.map