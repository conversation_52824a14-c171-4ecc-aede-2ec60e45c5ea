"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var ElasticsearchLaunchService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElasticsearchLaunchService = void 0;
const common_1 = require("@nestjs/common");
const elasticsearch_1 = require("@nestjs/elasticsearch");
const elasticsearch_base_service_1 = require("./elasticsearch.base.service");
const launch_query_dto_1 = require("../dto/launch-query.dto");
const launchSites_1 = require("../../../data/launchSites");
const launchSites_2 = require("../../../data/launchSites");
let ElasticsearchLaunchService = ElasticsearchLaunchService_1 = class ElasticsearchLaunchService extends elasticsearch_base_service_1.ElasticsearchBaseService {
    constructor(elasticsearchService) {
        super(elasticsearchService);
        this.elasticsearchService = elasticsearchService;
        this.logger = new common_1.Logger(ElasticsearchLaunchService_1.name);
        this.launchIndexPatterns = ['launch_jonathan', 'launch_spacenow*'];
        this.gunterIndex = 'launch_gunter';
        this.wikiLaunchSitesIndex = 'launchsites_wiki';
        this.serviceProviderIndex = 'orgs_jonathan';
    }
    async getLaunchIndices(status) {
        try {
            if (status === launch_query_dto_1.LaunchStatus.HISTORY || status === launch_query_dto_1.LaunchStatus.SUCCESS || status === launch_query_dto_1.LaunchStatus.FAILURE) {
                return ['launch_jonathan'];
            }
            const indices = await this.elasticsearchService.cat.indices({
                format: 'json'
            });
            const matchedIndices = indices
                .map((index) => index.index)
                .filter((indexName) => {
                return this.launchIndexPatterns.some(pattern => {
                    if (pattern.endsWith('*')) {
                        const prefix = pattern.slice(0, -1);
                        return indexName.startsWith(prefix);
                    }
                    return indexName === pattern;
                });
            });
            this.logger.debug(`找到 ${matchedIndices.length} 个匹配的发射信息索引: ${matchedIndices.join(', ')}`);
            return matchedIndices;
        }
        catch (error) {
            this.logger.error(`获取发射信息索引失败: ${error.message}`, error.stack);
            return this.launchIndexPatterns;
        }
    }
    convertToChineseName(siteName) {
        if (!siteName || typeof siteName !== 'string') {
            return '';
        }
        const normalizedInput = siteName.toLowerCase().trim();
        const site = launchSites_1.launchSites.find(site => {
            var _a;
            return site.code.toLowerCase() === normalizedInput ||
                site.englishName.toLowerCase() === normalizedInput ||
                site.chineseName === siteName.trim() ||
                ((_a = site.aliases) === null || _a === void 0 ? void 0 : _a.some(alias => alias.toLowerCase() === normalizedInput));
        });
        return site ? site.chineseName : siteName;
    }
    async searchLaunchInfo(query) {
        var _a, _b, _c;
        try {
            this.logger.debug(`搜索发射信息，参数: ${JSON.stringify(query)}`);
            const { page = 1, limit = 10 } = query;
            const from = (page - 1) * limit;
            const must = [];
            if (query.launchDateStart || query.launchDateEnd) {
                const rangeQuery = {
                    range: {
                        launch_date: {}
                    }
                };
                if (query.launchDateStart) {
                    const utcStartDate = this.convertBeijingToUTC(query.launchDateStart);
                    this.logger.debug(`时间转换: 北京时间起始日期 ${query.launchDateStart} -> UTC时间 ${utcStartDate}`);
                    rangeQuery.range.launch_date.gte = utcStartDate;
                }
                if (query.launchDateEnd) {
                    const utcEndDate = this.convertBeijingToUTC(query.launchDateEnd, true);
                    this.logger.debug(`时间转换: 北京时间结束日期 ${query.launchDateEnd} -> UTC时间 ${utcEndDate}`);
                    rangeQuery.range.launch_date.lte = utcEndDate;
                }
                must.push(rangeQuery);
            }
            if (query.rocketName) {
                must.push({
                    term: {
                        "rocket_name.keyword": query.rocketName
                    }
                });
            }
            if (query.siteName) {
                const siteIdentifiers = this.getAllLaunchSiteIdentifiers(query.siteName);
                must.push({
                    bool: {
                        should: siteIdentifiers.map(name => ({
                            term: {
                                "site_name.keyword": name
                            }
                        })),
                        minimum_should_match: 1
                    }
                });
                this.logger.debug(`发射场查询将匹配以下任意值: ${siteIdentifiers.join(', ')}`);
            }
            if (query.provider) {
                must.push({
                    term: {
                        "provider.keyword": query.provider
                    }
                });
            }
            if (query.status) {
                const indices = await this.getLaunchIndices(query.status);
                this.logger.debug(`使用索引: ${indices.join(', ')}`);
                if (query.status === launch_query_dto_1.LaunchStatus.HISTORY ||
                    query.status === launch_query_dto_1.LaunchStatus.SUCCESS ||
                    query.status === launch_query_dto_1.LaunchStatus.FAILURE) {
                    if (query.status !== launch_query_dto_1.LaunchStatus.HISTORY) {
                        const statusChar = query.status === launch_query_dto_1.LaunchStatus.SUCCESS ? 'S' : 'F';
                        must.push({
                            script: {
                                script: {
                                    source: "doc['launch_code.keyword'].value != null && doc['launch_code.keyword'].value.length() >= 2 && doc['launch_code.keyword'].value.substring(1, 2) == params.status",
                                    params: {
                                        status: statusChar
                                    }
                                }
                            }
                        });
                    }
                    const response = await this.elasticsearchService.search({
                        index: indices,
                        track_total_hits: true,
                        body: {
                            query: {
                                bool: {
                                    must
                                }
                            },
                            sort: [
                                { launch_date: { order: launch_query_dto_1.SortDirection.DESC } }
                            ],
                            from,
                            size: limit
                        }
                    });
                    const hits = ((_a = response.hits) === null || _a === void 0 ? void 0 : _a.hits) || [];
                    const total = typeof response.hits.total === 'number'
                        ? response.hits.total
                        : response.hits.total.value || 0;
                    const documents = hits.map(hit => {
                        const source = hit._source;
                        const { _id, _index } = source, restSource = __rest(source, ["_id", "_index"]);
                        return Object.assign(Object.assign({}, restSource), { site_name: this.convertToChineseName(source.site_name) });
                    });
                    return {
                        total,
                        page,
                        limit,
                        results: documents
                    };
                }
                const statusValues = this.mapStatusToDbValues(query.status);
                must.push({
                    bool: {
                        should: statusValues.map(value => ({
                            term: {
                                "status.keyword": value
                            }
                        })),
                        minimum_should_match: 1
                    }
                });
                if (must.length === 0) {
                    must.push({ match_all: {} });
                }
                let sortOrder = query.sortDirection || launch_query_dto_1.SortDirection.DESC;
                if (query.status === launch_query_dto_1.LaunchStatus.UPCOMING && !query.sortDirection) {
                    sortOrder = launch_query_dto_1.SortDirection.ASC;
                }
                this.logger.debug(`排序方向: ${sortOrder}, 状态: ${query.status}, 用户指定排序: ${query.sortDirection || '未指定'}`);
                const response = await this.elasticsearchService.search({
                    index: indices,
                    track_total_hits: true,
                    body: {
                        query: {
                            bool: {
                                must
                            }
                        },
                        sort: [
                            { launch_date: { order: sortOrder } }
                        ],
                        from,
                        size: limit
                    }
                });
                const hits = ((_b = response.hits) === null || _b === void 0 ? void 0 : _b.hits) || [];
                const total = typeof response.hits.total === 'number'
                    ? response.hits.total
                    : response.hits.total.value || 0;
                const documents = hits.map(hit => {
                    const source = hit._source;
                    const { _id, _index } = source, restSource = __rest(source, ["_id", "_index"]);
                    return Object.assign(Object.assign({}, restSource), { site_name: this.convertToChineseName(source.site_name) });
                });
                return {
                    total,
                    page,
                    limit,
                    results: documents
                };
            }
            const indices = await this.getLaunchIndices();
            this.logger.debug(`使用索引: ${indices.join(', ')}`);
            if (must.length === 0) {
                must.push({ match_all: {} });
            }
            const response = await this.elasticsearchService.search({
                index: indices,
                track_total_hits: true,
                body: {
                    query: {
                        bool: {
                            must
                        }
                    },
                    sort: [
                        { launch_date: { order: query.sortDirection || launch_query_dto_1.SortDirection.DESC } }
                    ],
                    from,
                    size: limit
                }
            });
            const hits = ((_c = response.hits) === null || _c === void 0 ? void 0 : _c.hits) || [];
            const total = typeof response.hits.total === 'number'
                ? response.hits.total
                : response.hits.total.value || 0;
            const documents = hits.map(hit => {
                const source = hit._source;
                const { _id, _index } = source, restSource = __rest(source, ["_id", "_index"]);
                return Object.assign(Object.assign({}, restSource), { site_name: this.convertToChineseName(source.site_name) });
            });
            return {
                total,
                page,
                limit,
                results: documents
            };
        }
        catch (error) {
            this.logger.error(`搜索发射信息失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getRocketNames() {
        var _a, _b;
        try {
            this.logger.debug('获取火箭名称集合');
            const indices = await this.getLaunchIndices();
            this.logger.debug(`使用索引: ${indices.join(', ')}`);
            const response = await this.elasticsearchService.search({
                index: indices,
                size: 0,
                body: {
                    aggs: {
                        rocket_names: {
                            terms: {
                                field: 'rocket_name.keyword',
                                size: 1000,
                                order: {
                                    _key: 'asc'
                                }
                            }
                        }
                    }
                }
            });
            const buckets = ((_b = (_a = response.aggregations) === null || _a === void 0 ? void 0 : _a.rocket_names) === null || _b === void 0 ? void 0 : _b.buckets) || [];
            const rocketNames = buckets
                .map((bucket) => bucket.key)
                .filter((name) => name && name.trim() !== '' &&
                name.toLowerCase() !== 'null' &&
                name.toLowerCase() !== 'undefined' &&
                name.toLowerCase() !== 'none');
            this.logger.debug(`获取到 ${rocketNames.length} 个火箭名称`);
            return rocketNames;
        }
        catch (error) {
            this.logger.error(`获取火箭名称集合失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getSiteNames() {
        var _a, _b;
        try {
            this.logger.debug('获取发射场名称集合');
            const indices = await this.getLaunchIndices();
            this.logger.debug(`使用索引: ${indices.join(', ')}`);
            const response = await this.elasticsearchService.search({
                index: indices,
                size: 0,
                body: {
                    aggs: {
                        site_names: {
                            terms: {
                                field: 'site_name.keyword',
                                size: 1000,
                                order: {
                                    _key: 'asc'
                                }
                            }
                        }
                    }
                }
            });
            const buckets = ((_b = (_a = response.aggregations) === null || _a === void 0 ? void 0 : _a.site_names) === null || _b === void 0 ? void 0 : _b.buckets) || [];
            const siteNames = buckets
                .map((bucket) => bucket.key)
                .filter((name) => name && name.trim() !== '' &&
                name.toLowerCase() !== 'null' &&
                name.toLowerCase() !== 'undefined' &&
                name.toLowerCase() !== 'none');
            this.logger.debug(`获取到 ${siteNames.length} 个发射场名称`);
            return siteNames;
        }
        catch (error) {
            this.logger.error(`获取发射场名称集合失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getProviders() {
        var _a, _b;
        try {
            this.logger.debug('获取服务商名称集合');
            const indices = await this.getLaunchIndices();
            this.logger.debug(`使用索引: ${indices.join(', ')}`);
            const response = await this.elasticsearchService.search({
                index: indices,
                size: 0,
                body: {
                    aggs: {
                        providers: {
                            terms: {
                                field: 'provider.keyword',
                                size: 1000,
                                order: {
                                    _key: 'asc'
                                }
                            }
                        }
                    }
                }
            });
            const buckets = ((_b = (_a = response.aggregations) === null || _a === void 0 ? void 0 : _a.providers) === null || _b === void 0 ? void 0 : _b.buckets) || [];
            const providers = buckets
                .map((bucket) => bucket.key)
                .filter((name) => name && name.trim() !== '' &&
                name.toLowerCase() !== 'null' &&
                name.toLowerCase() !== 'undefined' &&
                name.toLowerCase() !== 'none');
            this.logger.debug(`获取到 ${providers.length} 个服务商名称`);
            return providers;
        }
        catch (error) {
            this.logger.error(`获取服务商名称集合失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    convertBeijingToUTC(beijingDateStr, isEndOfDay = false) {
        try {
            const [year, month, day] = beijingDateStr.split('-').map(Number);
            const beijingDate = new Date(year, month - 1, day, isEndOfDay ? 23 : 0, isEndOfDay ? 59 : 0, isEndOfDay ? 59 : 0);
            const utcTimestamp = beijingDate.getTime() - 8 * 60 * 60 * 1000;
            const utcDate = new Date(utcTimestamp);
            const utcDateStr = utcDate.toISOString();
            return utcDateStr;
        }
        catch (error) {
            this.logger.error(`时间转换失败: ${error.message}`, error.stack);
            return beijingDateStr;
        }
    }
    mapStatusToDbValues(status) {
        switch (status) {
            case launch_query_dto_1.LaunchStatus.UPCOMING:
                return [
                    'To Be Confirmed',
                    'Go for Launch',
                    'To Be Determined',
                    'On Hold'
                ];
            case launch_query_dto_1.LaunchStatus.IN_FLIGHT:
                return [
                    'Launch in Flight'
                ];
            case launch_query_dto_1.LaunchStatus.SUCCESS:
                return [
                    'Launch Successful',
                    'Payload Deployed',
                    '1',
                ];
            case launch_query_dto_1.LaunchStatus.FAILURE:
                return [
                    'Launch Failure',
                    'Launch was a Partial Failure',
                    '0',
                    '2',
                    '3',
                    '-1'
                ];
            case launch_query_dto_1.LaunchStatus.HISTORY:
                return [
                    'Launch Successful',
                    'Payload Deployed',
                    '1',
                    'Launch Failure',
                    'Launch was a Partial Failure',
                    '0',
                    '2',
                    '3',
                    '-1'
                ];
            default:
                return [];
        }
    }
    async searchLaunchByCosparId(query) {
        var _a;
        try {
            this.logger.debug(`通过COSPAR ID查询发射信息，参数: ${JSON.stringify(query)}`);
            const response = await this.elasticsearchService.search({
                index: this.gunterIndex,
                body: {
                    query: {
                        term: {
                            "cospar_launch": query.cospar_launch
                        }
                    }
                }
            });
            const hits = ((_a = response.hits) === null || _a === void 0 ? void 0 : _a.hits) || [];
            const total = typeof response.hits.total === 'number'
                ? response.hits.total
                : response.hits.total.value || 0;
            const documents = hits.map(hit => {
                const source = hit._source || {};
                return Object.assign({ _id: hit._id, _index: hit._index }, source);
            });
            return {
                total,
                results: documents
            };
        }
        catch (error) {
            this.logger.error(`通过COSPAR ID查询发射信息失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getWikiSiteNames() {
        var _a, _b;
        try {
            this.logger.debug('获取wiki发射场名称集合');
            const response = await this.elasticsearchService.search({
                index: this.wikiLaunchSitesIndex,
                size: 0,
                body: {
                    aggs: {
                        site_names: {
                            terms: {
                                field: 'site_name.keyword',
                                size: 1000,
                                order: {
                                    _key: 'asc'
                                }
                            }
                        }
                    }
                }
            });
            const buckets = ((_b = (_a = response.aggregations) === null || _a === void 0 ? void 0 : _a.site_names) === null || _b === void 0 ? void 0 : _b.buckets) || [];
            const siteNames = buckets
                .map((bucket) => bucket.key)
                .filter((name) => name && name.trim() !== '' &&
                name.toLowerCase() !== 'null' &&
                name.toLowerCase() !== 'undefined' &&
                name.toLowerCase() !== 'none');
            this.logger.debug(`获取到 ${siteNames.length} 个wiki发射场名称`);
            return siteNames;
        }
        catch (error) {
            this.logger.error(`获取wiki发射场名称集合失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getAllLaunchSitesInfo() {
        try {
            this.logger.debug('获取所有发射场信息');
            const response = await this.elasticsearchService.search({
                index: this.wikiLaunchSitesIndex,
                size: 10000,
                body: {
                    query: {
                        match_all: {}
                    },
                    _source: ['site_name', 'location']
                }
            });
            const hits = response.hits.hits;
            this.logger.debug(`从ES获取到 ${hits.length} 条发射场信息`);
            const launchSitesInfo = [];
            hits.forEach((hit) => {
                const source = hit._source;
                const documentId = hit._id;
                if (!source || !source.site_name) {
                    return;
                }
                const siteNames = Array.isArray(source.site_name) ? source.site_name : [source.site_name];
                siteNames.forEach((siteName) => {
                    if (siteName && typeof siteName === 'string' && siteName.trim() !== '') {
                        const englishName = siteName.trim();
                        const chineseName = (0, launchSites_2.getLaunchSiteChineseName)(englishName);
                        const existingEntry = launchSitesInfo.find(item => item.english_name === englishName);
                        if (!existingEntry) {
                            launchSitesInfo.push({
                                english_name: englishName,
                                chinese_name: chineseName,
                                location: source.location || '',
                                _id: documentId
                            });
                        }
                    }
                });
            });
            this.logger.debug(`处理后得到 ${launchSitesInfo.length} 条有效发射场信息`);
            return launchSitesInfo;
        }
        catch (error) {
            this.logger.error(`获取所有发射场信息失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    getAllLaunchSiteIdentifiers(nameOrCode) {
        var _a;
        if (!nameOrCode || typeof nameOrCode !== 'string') {
            return [nameOrCode];
        }
        const normalizedInput = nameOrCode.toLowerCase().trim();
        for (const site of launchSites_1.launchSites) {
            if (site.code.toLowerCase() === normalizedInput ||
                site.englishName.toLowerCase() === normalizedInput ||
                site.chineseName === nameOrCode.trim() ||
                ((_a = site.aliases) === null || _a === void 0 ? void 0 : _a.some(alias => alias.toLowerCase() === normalizedInput))) {
                const identifiers = [
                    site.code,
                    site.englishName,
                    site.chineseName
                ];
                if (site.aliases && site.aliases.length > 0) {
                    identifiers.push(...site.aliases);
                }
                this.logger.debug(`发射场 "${nameOrCode}" 解析为以下可能的标识符: ${identifiers.join(', ')}`);
                return identifiers.filter(id => id && id.trim() !== '');
            }
        }
        this.logger.warn(`无法解析发射场名称或代码: "${nameOrCode}". 将使用原始输入进行查询.`);
        return [nameOrCode];
    }
    async searchLaunchSiteWiki(query) {
        try {
            this.logger.debug(`查询发射场wiki信息: ${JSON.stringify(query)}`);
            if (!query.siteName && !query._id) {
                this.logger.warn('查询参数无效：siteName和_id至少需要提供一个');
                return [];
            }
            let esQuery;
            if (query._id) {
                this.logger.debug(`使用ES文档ID进行精确查询: ${query._id}`);
                try {
                    const response = await this.elasticsearchService.get({
                        index: this.wikiLaunchSitesIndex,
                        id: query._id
                    });
                    if (response.found) {
                        this.logger.debug(`通过_id查询到发射场wiki信息`);
                        return [Object.assign(Object.assign({}, response._source), { _id: response._id })];
                    }
                    else {
                        this.logger.debug(`未找到_id为 ${query._id} 的发射场信息`);
                        return [];
                    }
                }
                catch (error) {
                    if (error.statusCode === 404) {
                        this.logger.debug(`_id ${query._id} 对应的文档不存在`);
                        return [];
                    }
                    throw error;
                }
            }
            if (query.siteName) {
                const launchSite = (0, launchSites_2.findLaunchSiteByName)(query.siteName);
                if (!launchSite) {
                    this.logger.debug(`未找到匹配的发射场信息: ${query.siteName}`);
                    return [];
                }
                const siteIdentifiers = [
                    launchSite.code,
                    launchSite.englishName,
                    launchSite.chineseName,
                    ...(launchSite.aliases || [])
                ];
                this.logger.debug(`发射场匹配标识符: ${siteIdentifiers.join(', ')}`);
                const response = await this.elasticsearchService.search({
                    index: this.wikiLaunchSitesIndex,
                    body: {
                        query: {
                            bool: {
                                should: siteIdentifiers.map(name => ({
                                    term: {
                                        "site_name.keyword": name
                                    }
                                })),
                                minimum_should_match: 1
                            }
                        }
                    }
                });
                const hits = response.hits.hits.map((hit) => (Object.assign(Object.assign({}, hit._source), { _id: hit._id })));
                this.logger.debug(`查询到 ${hits.length} 条发射场wiki信息`);
                return hits;
            }
            return [];
        }
        catch (error) {
            this.logger.error(`查询发射场wiki信息失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async searchServiceProviderByKeyword(keyword) {
        var _a;
        try {
            this.logger.debug(`通过关键词查询发射服务商信息: ${keyword}`);
            const query = {
                bool: {
                    should: [
                        { term: { "code.keyword": keyword } },
                        { term: { "ucode.keyword": keyword } },
                        { term: { "shortname.keyword": keyword } },
                        { term: { "name.keyword": keyword } },
                        { term: { "short_ename.keyword": keyword } },
                        { term: { "ename.keyword": keyword } },
                        { term: { "uname.keyword": keyword } }
                    ],
                    minimum_should_match: 1
                }
            };
            const response = await this.elasticsearchService.search({
                index: this.serviceProviderIndex,
                body: {
                    query,
                    size: 10
                }
            });
            const hits = ((_a = response.hits) === null || _a === void 0 ? void 0 : _a.hits) || [];
            const total = typeof response.hits.total === 'number'
                ? response.hits.total
                : response.hits.total.value || 0;
            const providers = hits.map(hit => (Object.assign({ _id: hit._id }, hit._source)));
            this.logger.debug(`查询到 ${providers.length} 个匹配的发射服务商`);
            return {
                total,
                results: providers
            };
        }
        catch (error) {
            this.logger.error(`查询发射服务商信息失败: ${error.message}`, error.stack);
            throw error;
        }
    }
};
ElasticsearchLaunchService = ElasticsearchLaunchService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [elasticsearch_1.ElasticsearchService])
], ElasticsearchLaunchService);
exports.ElasticsearchLaunchService = ElasticsearchLaunchService;
//# sourceMappingURL=elasticsearch.launch.service.js.map