"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var ElasticsearchConstellationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElasticsearchConstellationService = void 0;
const common_1 = require("@nestjs/common");
const elasticsearch_1 = require("@nestjs/elasticsearch");
const elasticsearch_base_service_1 = require("./elasticsearch.base.service");
let ElasticsearchConstellationService = ElasticsearchConstellationService_1 = class ElasticsearchConstellationService extends elasticsearch_base_service_1.ElasticsearchBaseService {
    constructor(elasticsearchService) {
        super(elasticsearchService);
        this.logger = new common_1.Logger(ElasticsearchConstellationService_1.name);
        this.constellationIndex = 'constellation_info';
    }
    async searchConstellationInfo(queryDto) {
        try {
            this.logger.debug(`搜索星座信息，参数: ${JSON.stringify(queryDto)}`);
            const { page = 1, limit = 10, keyword, targetName, targetId, country, organization, purpose } = queryDto;
            const must = [];
            const should = [];
            if (targetName) {
                this.logger.debug(`使用目标名称查询: "${targetName}"`);
                must.push({
                    bool: {
                        should: [
                            {
                                match_phrase: {
                                    constellation_name: targetName
                                }
                            },
                            {
                                match_phrase: {
                                    company_constellation: targetName
                                }
                            }
                        ],
                        minimum_should_match: 1
                    }
                });
            }
            if (keyword) {
                should.push({
                    multi_match: {
                        query: keyword,
                        fields: ['constellation_name^3', 'company_constellation^2', 'constellation_info'],
                        type: 'best_fields',
                        fuzziness: 'AUTO'
                    }
                });
            }
            if (targetId) {
                should.push({
                    match: {
                        id: {
                            query: targetId
                        }
                    }
                });
            }
            if (organization) {
                should.push({
                    bool: {
                        should: [
                            {
                                wildcard: {
                                    'company_constellation': {
                                        value: `*${organization.toLowerCase()}*`,
                                        case_insensitive: true
                                    }
                                }
                            },
                            {
                                wildcard: {
                                    'company': {
                                        value: `*${organization.toLowerCase()}*`,
                                        case_insensitive: true
                                    }
                                }
                            },
                            {
                                wildcard: {
                                    'owner_info.owner': {
                                        value: `*${organization.toLowerCase()}*`,
                                        case_insensitive: true
                                    }
                                }
                            }
                        ],
                        minimum_should_match: 1
                    }
                });
            }
            if (country) {
                const countryVariants = this.getCountryVariants(country);
                const countryQueries = countryVariants.map(variant => ({
                    match: {
                        'owner_info.country': {
                            query: variant,
                            fuzziness: 'AUTO'
                        }
                    }
                }));
                should.push({
                    bool: {
                        should: countryQueries,
                        minimum_should_match: 1
                    }
                });
            }
            if (purpose) {
                const purposeVariants = this.getPurposeVariants(purpose);
                const purposeQueries = purposeVariants.map(variant => ({
                    multi_match: {
                        query: variant,
                        fields: ['purpose^2', 'applications'],
                        type: 'best_fields',
                        fuzziness: 'AUTO'
                    }
                }));
                should.push({
                    bool: {
                        should: purposeQueries,
                        minimum_should_match: 1
                    }
                });
            }
            const query = {
                bool: {
                    must,
                    should,
                    minimum_should_match: should.length > 0 ? 1 : 0
                }
            };
            const response = await this.elasticsearchService.search({
                index: ['constell_newspace'],
                query,
                size: limit,
                from: (page - 1) * limit,
                sort: [
                    { _score: { order: 'desc' } }
                ],
                _source: true
            });
            const hits = response.hits.hits;
            const total = typeof response.hits.total === 'number'
                ? response.hits.total
                : response.hits.total.value;
            const processedHits = hits.map(hit => {
                const source = hit._source;
                const matchedFields = this.calculateMatchedFields([{
                        index: hit._index,
                        id: hit._id,
                        score: hit._score,
                        source: source
                    }], queryDto);
                const matchedFieldsDescription = this.createMatchFieldsDescription(matchedFields);
                const matchedFieldsCount = Object.values(matchedFields).filter(Boolean).length;
                return Object.assign(Object.assign({}, source), { id: source.id || hit._id, match_score: hit._score, matched_fields: matchedFields, matched_fields_description: matchedFieldsDescription, matched_fields_count: matchedFieldsCount });
            });
            const sortedHits = processedHits.sort((a, b) => {
                if (b.matched_fields_count !== a.matched_fields_count) {
                    return b.matched_fields_count - a.matched_fields_count;
                }
                return b.match_score - a.match_score;
            });
            const result = {
                total,
                page,
                limit,
                hits: sortedHits
            };
            return result;
        }
        catch (error) {
            this.logger.error(`搜索星座信息时出错: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`无法搜索星座信息: ${error.message}`);
        }
    }
    createMatchFieldsDescription(matchedFields) {
        const descriptions = [];
        if (matchedFields.keyword) {
            descriptions.push({
                field: 'keyword',
                matchLevel: '关键词匹配',
                score: 0.9
            });
        }
        if (matchedFields.targetName) {
            descriptions.push({
                field: 'targetName',
                matchLevel: '完全匹配',
                score: 1.0
            });
        }
        if (matchedFields.targetId) {
            descriptions.push({
                field: 'targetId',
                matchLevel: '完全匹配',
                score: 1.0
            });
        }
        if (matchedFields.organization) {
            descriptions.push({
                field: 'organization',
                matchLevel: '组织匹配',
                score: 0.8
            });
        }
        if (matchedFields.country) {
            descriptions.push({
                field: 'country',
                matchLevel: '国家匹配',
                score: 0.7
            });
        }
        if (matchedFields.purpose) {
            descriptions.push({
                field: 'purpose',
                matchLevel: '用途匹配',
                score: 0.6
            });
        }
        return descriptions;
    }
    async getConstellationNames() {
        try {
            this.logger.debug('获取星座名称集合');
            const [newspaceResponse, n2yoResponse] = await Promise.all([
                this.elasticsearchService.search({
                    index: 'constell_newspace',
                    size: 1000,
                    _source: ['constellation_name'],
                    query: {
                        exists: { field: 'constellation_name' }
                    }
                }),
                this.elasticsearchService.search({
                    index: 'constell_n2yo',
                    size: 1000,
                    _source: ['constellation_name'],
                    query: {
                        exists: { field: 'constellation_name' }
                    }
                })
            ]);
            const newspaceNames = this.extractNames(newspaceResponse.hits.hits);
            const n2yoNames = this.extractNames(n2yoResponse.hits.hits);
            const allNames = [...new Set([...newspaceNames, ...n2yoNames])].sort();
            return {
                constellationNames: allNames,
                count: allNames.length
            };
        }
        catch (error) {
            this.logger.error(`获取星座名称集合时出错: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`无法获取星座名称集合: ${error.message}`);
        }
    }
    async getConstellationOrganizations() {
        try {
            this.logger.debug('获取星座所属机构集合');
            const [newspaceResponse, n2yoResponse] = await Promise.all([
                this.elasticsearchService.search({
                    index: 'constell_newspace',
                    size: 1000,
                    _source: ['company_constellation', 'owner_info.owner'],
                    query: {
                        bool: {
                            should: [
                                { exists: { field: 'company_constellation' } },
                                { exists: { field: 'owner_info.owner' } }
                            ],
                            minimum_should_match: 1
                        }
                    }
                }),
                this.elasticsearchService.search({
                    index: 'constell_n2yo',
                    size: 1000,
                    _source: ['company'],
                    query: {
                        exists: { field: 'company' }
                    }
                })
            ]);
            this.logger.debug(`从newspace索引找到 ${newspaceResponse.hits.hits.length} 条记录`);
            this.logger.debug(`从n2yo索引找到 ${n2yoResponse.hits.hits.length} 条记录`);
            const extractOrgName = (fullName) => {
                if (!fullName)
                    return '';
                const match = fullName.match(/^(.*?)(\s*\(|$)/);
                return match ? match[1].trim() : fullName.trim();
            };
            const newspaceOrgs = [];
            newspaceResponse.hits.hits.forEach(hit => {
                const source = hit._source;
                if (typeof source.company_constellation === 'string') {
                    const orgName = extractOrgName(source.company_constellation);
                    if (orgName && orgName.length > 0) {
                        newspaceOrgs.push(orgName);
                    }
                }
                else if (source.company_constellation && typeof source.company_constellation.company === 'string') {
                    const orgName = extractOrgName(source.company_constellation.company);
                    if (orgName && orgName.length > 0) {
                        newspaceOrgs.push(orgName);
                    }
                }
                if (source.owner_info && typeof source.owner_info.owner === 'string') {
                    const orgName = extractOrgName(source.owner_info.owner);
                    if (orgName && orgName.length > 0) {
                        newspaceOrgs.push(orgName);
                    }
                }
            });
            this.logger.debug(`从newspace索引提取了 ${newspaceOrgs.length} 个组织名称`);
            const n2yoOrgs = n2yoResponse.hits.hits
                .map(hit => {
                const source = hit._source;
                if (typeof source.company === 'string') {
                    return extractOrgName(source.company);
                }
                return '';
            })
                .filter(org => org && org.length > 0);
            this.logger.debug(`从n2yo索引提取了 ${n2yoOrgs.length} 个组织名称`);
            const allOrgs = [...new Set([...newspaceOrgs, ...n2yoOrgs])]
                .filter(org => org.length > 0)
                .sort();
            this.logger.debug(`找到 ${allOrgs.length} 个独特组织：${JSON.stringify(allOrgs.slice(0, 10))}...`);
            return {
                organizations: allOrgs,
                count: allOrgs.length
            };
        }
        catch (error) {
            this.logger.error(`获取星座所属机构集合时出错: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`无法获取星座所属机构集合: ${error.message}`);
        }
    }
    async getConstellationPurposes() {
        try {
            this.logger.debug('获取星座用途集合');
            const [newspaceResponse, n2yoResponse] = await Promise.all([
                this.elasticsearchService.search({
                    index: 'constell_newspace',
                    size: 1000,
                    _source: ['purpose', 'applications'],
                    query: {
                        bool: {
                            should: [
                                { exists: { field: 'purpose' } },
                                { exists: { field: 'applications' } }
                            ],
                            minimum_should_match: 1
                        }
                    }
                }),
                this.elasticsearchService.search({
                    index: 'constell_n2yo',
                    size: 1000,
                    _source: ['purpose'],
                    query: {
                        exists: { field: 'purpose' }
                    }
                })
            ]);
            this.logger.debug(`从newspace索引找到 ${newspaceResponse.hits.hits.length} 条记录`);
            this.logger.debug(`从n2yo索引找到 ${n2yoResponse.hits.hits.length} 条记录`);
            const newspacePurposes = [];
            newspaceResponse.hits.hits.forEach(hit => {
                const source = hit._source;
                if (source.purpose) {
                    if (typeof source.purpose === 'string') {
                        const purposeValues = source.purpose.split(',').map((p) => p.trim());
                        purposeValues.forEach((p) => {
                            if (p && p.length > 0) {
                                newspacePurposes.push(p);
                            }
                        });
                    }
                    else if (Array.isArray(source.purpose)) {
                        source.purpose.forEach((p) => {
                            if (p && p.length > 0) {
                                newspacePurposes.push(p);
                            }
                        });
                    }
                }
                if (source.applications) {
                    if (typeof source.applications === 'string') {
                        const appValues = source.applications.split(',').map((p) => p.trim());
                        appValues.forEach((p) => {
                            if (p && p.length > 0) {
                                newspacePurposes.push(p);
                            }
                        });
                    }
                    else if (Array.isArray(source.applications)) {
                        source.applications.forEach((p) => {
                            if (p && p.length > 0) {
                                newspacePurposes.push(p);
                            }
                        });
                    }
                }
            });
            this.logger.debug(`从newspace索引提取了 ${newspacePurposes.length} 个用途`);
            const n2yoPurposes = [];
            n2yoResponse.hits.hits.forEach(hit => {
                const source = hit._source;
                if (source.purpose) {
                    if (typeof source.purpose === 'string') {
                        const purposeValues = source.purpose.split(',').map((p) => p.trim());
                        purposeValues.forEach((p) => {
                            if (p && p.length > 0) {
                                n2yoPurposes.push(p);
                            }
                        });
                    }
                    else if (Array.isArray(source.purpose)) {
                        source.purpose.forEach((p) => {
                            if (p && p.length > 0) {
                                n2yoPurposes.push(p);
                            }
                        });
                    }
                }
            });
            this.logger.debug(`从n2yo索引提取了 ${n2yoPurposes.length} 个用途`);
            const commonPurposes = [
                '通信', 'Communication', 'Telecommunications', 'Comms', '电信',
                '导航', 'Navigation', 'Positioning', 'GPS', 'GNSS', '定位',
                '地球观测', 'Earth Observation', 'Remote Sensing', 'EO', '遥感',
                '科学研究', 'Scientific Research', 'Science', 'Research', '科研',
                '技术验证', 'Technology Demonstration', 'Tech Demo', '技术演示',
                '互联网', 'Internet', '网络', 'Web', 'Net'
            ];
            const allPurposes = [...new Set([...newspacePurposes, ...n2yoPurposes, ...commonPurposes])]
                .filter(purpose => purpose && purpose.length > 0)
                .sort();
            this.logger.debug(`找到 ${allPurposes.length} 个独特用途：${JSON.stringify(allPurposes.slice(0, 10))}...`);
            return {
                purposes: allPurposes,
                count: allPurposes.length
            };
        }
        catch (error) {
            this.logger.error(`获取星座用途集合时出错: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`无法获取星座用途集合: ${error.message}`);
        }
    }
    extractNames(hits) {
        return hits
            .map(hit => {
            const source = hit._source;
            return source === null || source === void 0 ? void 0 : source.constellation_name;
        })
            .filter(name => name && typeof name === 'string');
    }
    calculateMatchedFields(hits, queryDto) {
        const { keyword, targetName, targetId, country, organization, purpose } = queryDto;
        const matchedFields = {};
        if (keyword) {
            matchedFields.keyword = hits.some(hit => {
                const source = hit.source;
                return (this.checkFieldMatch(source, 'constellation_name', keyword) ||
                    this.checkFieldMatch(source, 'company_constellation', keyword) ||
                    this.checkFieldMatch(source, 'company', keyword) ||
                    this.checkFieldMatch(source, 'owner_info.owner', keyword) ||
                    this.checkFieldMatch(source, 'purpose', keyword));
            });
        }
        if (targetName) {
            matchedFields.targetName = hits.some(hit => this.checkFieldMatch(hit.source, 'constellation_name', targetName));
        }
        if (targetId) {
            matchedFields.targetId = hits.some(hit => Array.isArray(hit.source.satellites) &&
                hit.source.satellites.some((sat) => sat.norad_id === targetId));
        }
        if (organization) {
            matchedFields.organization = hits.some(hit => this.checkFieldMatch(hit.source, 'company_constellation', organization) ||
                this.checkFieldMatch(hit.source, 'company', organization) ||
                this.checkFieldMatch(hit.source, 'owner_info.owner', organization));
        }
        if (country) {
            matchedFields.country = hits.some(hit => this.checkFieldMatch(hit.source, 'owner_info.country', country) ||
                this.checkFieldMatch(hit.source, 'country', country));
        }
        if (purpose) {
            matchedFields.purpose = hits.some(hit => this.checkFieldMatch(hit.source, 'purpose', purpose) ||
                this.checkFieldMatch(hit.source, 'applications', purpose));
        }
        return matchedFields;
    }
    checkFieldMatch(source, fieldPath, value) {
        const fieldValue = this.getNestedValue(source, fieldPath);
        if (fieldValue === undefined || fieldValue === null) {
            return false;
        }
        const fieldValueStr = String(fieldValue).toLowerCase();
        const valueStr = value.toLowerCase();
        if (fieldValueStr === valueStr) {
            return true;
        }
        if (fieldValueStr.includes(valueStr) || valueStr.includes(fieldValueStr)) {
            return true;
        }
        if (this.checkSynonyms(fieldValueStr, valueStr)) {
            return true;
        }
        if (fieldValueStr.includes(',')) {
            const values = fieldValueStr.split(',').map(v => v.trim());
            return values.some(v => {
                if (v === valueStr || v.includes(valueStr) || valueStr.includes(v)) {
                    return true;
                }
                return this.checkSynonyms(v, valueStr);
            });
        }
        return false;
    }
    checkSynonyms(word1, word2) {
        const purposeSynonyms = {
            'internet': ['互联网', '网络', 'web', 'net', 'www'],
            'communication': ['通信', '通讯', 'comms', '电信', 'telecommunications'],
            'navigation': ['导航', '定位', 'positioning', 'gps', 'gnss'],
            'observation': ['观测', '观察', '监测', 'monitoring', 'earth observation', '地球观测', '遥感', 'remote sensing'],
            'research': ['研究', '科研', 'scientific', 'science', '科学'],
            'direct-to-cell': ['直接到手机', '卫星直连手机', '卫星到手机', 'satellite-to-cellphone', 'direct-to-device'],
            'relay': ['中继', '数据中继', 'data relay', 'orbital relay'],
        };
        const countrySynonyms = {
            'us': ['usa', 'united states', 'america', '美国', '美利坚'],
            'china': ['prc', 'people\'s republic of china', '中国', '中华人民共和国'],
            'russia': ['russian federation', 'rf', '俄罗斯', '俄罗斯联邦'],
            'uk': ['united kingdom', 'great britain', 'england', '英国', '大不列颠'],
            'japan': ['jp', '日本'],
        };
        if (purposeSynonyms[word1] && purposeSynonyms[word1].includes(word2)) {
            return true;
        }
        if (countrySynonyms[word1] && countrySynonyms[word1].includes(word2)) {
            return true;
        }
        return false;
    }
    getCountryVariants(country) {
        const countryMap = {
            'USA': ['United States', 'United States of America', 'US', 'America', '美国'],
            'China': ['PRC', 'People\'s Republic of China', '中国', '中华人民共和国'],
            'Russia': ['Russian Federation', 'RF', '俄罗斯', '俄罗斯联邦'],
            'UK': ['United Kingdom', 'Great Britain', 'England', '英国', '大不列颠'],
            'Japan': ['JP', '日本'],
        };
        for (const [key, variants] of Object.entries(countryMap)) {
            if (key.toLowerCase() === country.toLowerCase() ||
                variants.some(v => v.toLowerCase() === country.toLowerCase())) {
                return [key, ...variants];
            }
        }
        return [country];
    }
    getPurposeVariants(purpose) {
        const purposeMap = {
            '通信': ['Communication', 'Telecommunications', 'Comms', '电信'],
            '导航': ['Navigation', 'Positioning', 'GPS', 'GNSS', '定位'],
            '地球观测': ['Earth Observation', 'Remote Sensing', 'EO', '遥感'],
            '科学研究': ['Scientific Research', 'Science', 'Research', '科研'],
            '技术验证': ['Technology Demonstration', 'Tech Demo', '技术演示'],
        };
        for (const [key, variants] of Object.entries(purposeMap)) {
            if (key.toLowerCase() === purpose.toLowerCase() ||
                variants.some(v => v.toLowerCase() === purpose.toLowerCase())) {
                return [key, ...variants];
            }
        }
        return [purpose];
    }
    mergeFieldValues(hits, fieldPath, alternateFieldPath) {
        const values = new Map();
        hits.forEach(hit => {
            let value = this.getNestedValue(hit.source, fieldPath);
            if (value === undefined && alternateFieldPath) {
                value = this.getNestedValue(hit.source, alternateFieldPath);
            }
            if (value !== undefined) {
                const valueStr = typeof value === 'object' ? JSON.stringify(value) : String(value);
                if (!values.has(valueStr)) {
                    values.set(valueStr, { value, sources: [hit.index] });
                }
                else {
                    const existing = values.get(valueStr);
                    if (existing && !existing.sources.includes(hit.index)) {
                        existing.sources.push(hit.index);
                    }
                }
            }
        });
        if (values.size === 1) {
            return Array.from(values.values())[0].value;
        }
        if (values.size > 1) {
            return Array.from(values.entries()).map(([_, { value, sources }]) => ({
                value,
                sources
            }));
        }
        return undefined;
    }
    getNestedValue(obj, path) {
        if (!obj)
            return undefined;
        const parts = path.split('.');
        let current = obj;
        for (const part of parts) {
            if (current === undefined || current === null) {
                return undefined;
            }
            current = current[part];
        }
        return current;
    }
    async searchConstellations(query) {
        try {
            const { page = 1, limit = 10, sort } = query, filters = __rest(query, ["page", "limit", "sort"]);
            const sortField = (sort === null || sort === void 0 ? void 0 : sort.field) || 'name';
            const sortOrder = (sort === null || sort === void 0 ? void 0 : sort.order) || 'asc';
            const must = this.buildConstellationQueryConditions(filters);
            const searchResult = await this.elasticsearchService.search({
                index: this.constellationIndex,
                body: {
                    query: {
                        bool: {
                            must,
                        },
                    },
                    sort: [
                        {
                            [sortField]: {
                                order: sortOrder,
                            },
                        },
                    ],
                    from: (page - 1) * limit,
                    size: limit,
                },
            });
            const total = typeof searchResult.hits.total === 'number'
                ? searchResult.hits.total
                : searchResult.hits.total.value;
            const items = searchResult.hits.hits.map((hit) => (Object.assign(Object.assign({}, hit._source), { _id: hit._id })));
            return {
                total,
                page,
                size: limit,
                items,
            };
        }
        catch (error) {
            this.logger.error(`搜索星座信息失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getConstellationById(id) {
        try {
            const result = await this.elasticsearchService.search({
                index: this.constellationIndex,
                body: {
                    query: {
                        term: {
                            _id: id,
                        },
                    },
                },
            });
            if (result.hits.hits.length === 0) {
                return null;
            }
            const hit = result.hits.hits[0];
            const source = hit._source;
            return Object.assign(Object.assign({}, source), { _id: hit._id });
        }
        catch (error) {
            this.logger.error(`获取星座详情失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getConstellationMembers(constellationId) {
        try {
            const result = await this.elasticsearchService.search({
                index: 'satellite_info',
                body: {
                    query: {
                        term: {
                            constellation_id: constellationId,
                        },
                    },
                    size: 1000,
                },
            });
            return result.hits.hits.map((hit) => {
                const source = hit._source;
                return Object.assign(Object.assign({}, source), { _id: hit._id });
            });
        }
        catch (error) {
            this.logger.error(`获取星座成员失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    buildConstellationQueryConditions(filters) {
        const must = [];
        if (filters.name) {
            must.push({
                match: {
                    name: {
                        query: filters.name,
                        fuzziness: 'AUTO',
                    },
                },
            });
        }
        if (filters.operator) {
            must.push({
                match: {
                    operator: {
                        query: filters.operator,
                        fuzziness: 'AUTO',
                    },
                },
            });
        }
        if (filters.country) {
            const countryVariants = this.getCountryVariants(filters.country);
            const countryQueries = countryVariants.map(variant => ({
                match: {
                    'owner_info.country': {
                        query: variant,
                        fuzziness: 'AUTO'
                    }
                }
            }));
            must.push({
                bool: {
                    should: countryQueries,
                    minimum_should_match: 1
                }
            });
        }
        if (filters.type) {
            must.push({
                match: {
                    type: filters.type,
                },
            });
        }
        return must.length ? must : [{ match_all: {} }];
    }
    filterLatestN2YOHits(hits) {
        const hitsByIndex = {};
        hits.forEach(hit => {
            const index = hit.index;
            if (!hitsByIndex[index]) {
                hitsByIndex[index] = [];
            }
            hitsByIndex[index].push(hit);
        });
        if (hitsByIndex['constell_n2yo']) {
            const n2yoHits = hitsByIndex['constell_n2yo'];
            n2yoHits.sort((a, b) => {
                const timestampA = this.extractTimestampFromId(a.id);
                const timestampB = this.extractTimestampFromId(b.id);
                return timestampB - timestampA;
            });
            hitsByIndex['constell_n2yo'] = [n2yoHits[0]];
        }
        return Object.values(hitsByIndex).flat();
    }
    extractTimestampFromId(id) {
        const timestampPart = id.split('_')[0];
        return parseInt(timestampPart, 10) || 0;
    }
};
ElasticsearchConstellationService = ElasticsearchConstellationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [elasticsearch_1.ElasticsearchService])
], ElasticsearchConstellationService);
exports.ElasticsearchConstellationService = ElasticsearchConstellationService;
//# sourceMappingURL=elasticsearch.constellation.service.js.map