"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ElasticsearchRocketService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElasticsearchRocketService = void 0;
const common_1 = require("@nestjs/common");
const elasticsearch_1 = require("@nestjs/elasticsearch");
const elasticsearch_base_service_1 = require("./elasticsearch.base.service");
let ElasticsearchRocketService = ElasticsearchRocketService_1 = class ElasticsearchRocketService extends elasticsearch_base_service_1.ElasticsearchBaseService {
    constructor(elasticsearchService) {
        super(elasticsearchService);
        this.elasticsearchService = elasticsearchService;
        this.logger = new common_1.Logger(ElasticsearchRocketService_1.name);
        this.rocketIndices = ['veh_discos', 'veh_jonathan'];
    }
    async getRocketIndices() {
        try {
            const response = await this.elasticsearchService.cat.indices({
                format: 'json'
            });
            const matchedIndices = response
                .filter(index => index.index && this.rocketIndices.includes(index.index))
                .map(index => index.index)
                .filter(index => index !== undefined);
            this.logger.debug(`找到 ${matchedIndices.length} 个匹配的火箭信息索引: ${matchedIndices.join(', ')}`);
            return matchedIndices;
        }
        catch (error) {
            this.logger.error(`获取火箭信息索引失败: ${error.message}`, error.stack);
            return this.rocketIndices;
        }
    }
    async searchRocketInfo(query) {
        var _a;
        try {
            this.logger.debug(`搜索火箭信息，参数: ${JSON.stringify(query)}`);
            const { page = 1, limit = 10, rocketName } = query;
            const from = (page - 1) * limit;
            const must = [];
            if (rocketName) {
                must.push({
                    bool: {
                        should: [
                            {
                                term: {
                                    "rocket_name.keyword": {
                                        value: rocketName,
                                        case_insensitive: true
                                    }
                                }
                            },
                            {
                                term: {
                                    "lv_variant.keyword": {
                                        value: rocketName,
                                        case_insensitive: true
                                    }
                                }
                            }
                        ],
                        minimum_should_match: 1
                    }
                });
                this.logger.debug(`火箭型号查询条件: ${JSON.stringify(must)}`);
            }
            const indices = await this.getRocketIndices();
            this.logger.debug(`使用索引: ${indices.join(', ')}`);
            const finalQuery = must.length > 0
                ? { bool: { must } }
                : { match_all: {} };
            const searchRequest = {
                index: indices,
                track_total_hits: true,
                body: {
                    query: finalQuery,
                    from,
                    size: limit
                }
            };
            this.logger.debug(`完整的查询请求: ${JSON.stringify(searchRequest)}`);
            const response = await this.elasticsearchService.search(searchRequest);
            const hits = ((_a = response.hits) === null || _a === void 0 ? void 0 : _a.hits) || [];
            const total = typeof response.hits.total === 'number'
                ? response.hits.total
                : response.hits.total.value || 0;
            this.logger.debug(`查询结果总数: ${total}`);
            if (hits.length > 0) {
                this.logger.debug(`第一个结果: ${JSON.stringify(hits[0])}`);
            }
            else {
                this.logger.debug('没有找到匹配的结果');
            }
            const documents = hits.map(hit => {
                const source = hit._source || {};
                const nonEmptyFields = {};
                Object.entries(source).forEach(([key, value]) => {
                    if (value !== null && value !== undefined && value !== '') {
                        nonEmptyFields[key] = value;
                    }
                });
                return Object.assign({ _id: hit._id, _index: hit._index }, nonEmptyFields);
            });
            return {
                total,
                page,
                limit,
                hits: documents
            };
        }
        catch (error) {
            this.logger.error(`搜索火箭信息失败: ${error.message}`, error.stack);
            throw error;
        }
    }
};
ElasticsearchRocketService = ElasticsearchRocketService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [elasticsearch_1.ElasticsearchService])
], ElasticsearchRocketService);
exports.ElasticsearchRocketService = ElasticsearchRocketService;
//# sourceMappingURL=elasticsearch.rocket.service.js.map