import { Logger } from '@nestjs/common';
import { ElasticsearchService } from '@nestjs/elasticsearch';
import { ElasticsearchBaseService } from './elasticsearch.base.service';
import { LaunchQueryDto } from '../dto/launch-query.dto';
import { LaunchCosparQueryDto } from '../dto/launch-cospar-query.dto';
import { LaunchSiteWikiQueryDto } from '../dto/launch-site-wiki-query.dto';
export declare class ElasticsearchLaunchService extends ElasticsearchBaseService {
    protected readonly elasticsearchService: ElasticsearchService;
    protected readonly logger: Logger;
    private readonly launchIndexPatterns;
    private readonly gunterIndex;
    private readonly wikiLaunchSitesIndex;
    private readonly serviceProviderIndex;
    constructor(elasticsearchService: ElasticsearchService);
    private getLaunchIndices;
    private convertToChineseName;
    searchLaunchInfo(query: LaunchQueryDto): Promise<any>;
    getRocketNames(): Promise<string[]>;
    getSiteNames(): Promise<string[]>;
    getProviders(): Promise<string[]>;
    private convertBeijingToUTC;
    private mapStatusToDbValues;
    searchLaunchByCosparId(query: LaunchCosparQueryDto): Promise<any>;
    getWikiSiteNames(): Promise<string[]>;
    getAllLaunchSitesInfo(): Promise<Array<{
        english_name: string;
        chinese_name: string;
        location: string;
        _id: string;
    }>>;
    private getAllLaunchSiteIdentifiers;
    searchLaunchSiteWiki(query: LaunchSiteWikiQueryDto): Promise<any>;
    searchServiceProviderByKeyword(keyword: string): Promise<any>;
}
