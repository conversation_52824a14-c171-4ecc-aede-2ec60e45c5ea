"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EntityRecognitionService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EntityRecognitionService = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("@nestjs/axios");
const rxjs_1 = require("rxjs");
const launchSites_1 = require("../../../data/launchSites");
const FALLBACK_ENTITIES = {
    satellites: [
        'Starlink', 'Hubble Space Telescope', 'International Space Station', 'ISS',
        'James Webb Space Telescope', 'JWST', 'Galileo', 'GPS', 'Iridium'
    ],
    constellations: [
        'Starlink', 'OneWeb', 'Kuiper', 'Galileo', 'GPS', 'GLONASS', 'BeiDou'
    ],
    rockets: [
        'Falcon 9', 'Falcon Heavy', 'Atlas V', 'Delta IV', 'Ariane 5', 'Soyuz',
        'Long March', 'New Shepard', 'Electron'
    ],
    providers: [
        'SpaceX', 'Blue Origin', 'NASA', 'ESA', 'Roscosmos', 'CNSA', 'ISRO',
        'ULA', 'Rocket Lab', 'Virgin Galactic'
    ]
};
let EntityRecognitionService = EntityRecognitionService_1 = class EntityRecognitionService {
    constructor(httpService) {
        this.httpService = httpService;
        this.logger = new common_1.Logger(EntityRecognitionService_1.name);
        this.entityCollections = null;
        this.isInitialized = false;
        this.initializationPromise = null;
        this.httpService.axiosRef.defaults.baseURL = 'http://localhost:3000';
    }
    async initialize() {
        if (this.isInitialized) {
            return;
        }
        if (this.initializationPromise) {
            return this.initializationPromise;
        }
        this.initializationPromise = this.loadEntityCollections();
        await this.initializationPromise;
        this.isInitialized = true;
    }
    async loadEntityCollections() {
        this.logger.log('开始加载实体名称集合...');
        try {
            const [satellites, constellations, launchSitesFromApi, rockets, providers] = await Promise.allSettled([
                this.loadSatelliteNames(),
                this.loadConstellationNames(),
                this.loadLaunchSiteNamesFromApi(),
                this.loadRocketNames(),
                this.loadProviders()
            ]);
            const satelliteNames = satellites.status === 'fulfilled' ? satellites.value : FALLBACK_ENTITIES.satellites;
            if (satellites.status === 'rejected') {
                this.logger.warn(`加载卫星名称失败: ${satellites.reason}，使用预定义列表`);
            }
            const constellationNames = constellations.status === 'fulfilled' ? constellations.value : FALLBACK_ENTITIES.constellations;
            if (constellations.status === 'rejected') {
                this.logger.warn(`加载星座名称失败: ${constellations.reason}，使用预定义列表`);
            }
            const launchSiteNamesFromLocal = launchSites_1.launchSites.map(site => site.englishName);
            const launchSiteNamesFromApiResult = launchSitesFromApi.status === 'fulfilled' ? launchSitesFromApi.value : [];
            if (launchSitesFromApi.status === 'rejected') {
                this.logger.warn(`从API加载发射场名称失败: ${launchSitesFromApi.reason}`);
            }
            const allLaunchSiteNames = [...new Set([...launchSiteNamesFromLocal, ...launchSiteNamesFromApiResult])];
            const rocketNames = rockets.status === 'fulfilled' ? rockets.value : FALLBACK_ENTITIES.rockets;
            if (rockets.status === 'rejected') {
                this.logger.warn(`加载火箭名称失败: ${rockets.reason}，使用预定义列表`);
            }
            const providerNames = providers.status === 'fulfilled' ? providers.value : FALLBACK_ENTITIES.providers;
            if (providers.status === 'rejected') {
                this.logger.warn(`加载发射服务商名称失败: ${providers.reason}，使用预定义列表`);
            }
            this.entityCollections = {
                satellites: satelliteNames,
                constellations: constellationNames,
                launchSites: allLaunchSiteNames,
                rockets: rocketNames,
                providers: providerNames
            };
            this.logger.log(`实体名称集合加载完成: 卫星${satelliteNames.length}个, 星座${constellationNames.length}个, 发射场${allLaunchSiteNames.length}个, 火箭${rocketNames.length}个, 服务商${providerNames.length}个`);
        }
        catch (error) {
            this.logger.error(`加载实体名称集合失败: ${error.message}`, error.stack);
            this.entityCollections = {
                satellites: [],
                constellations: [],
                launchSites: [],
                rockets: [],
                providers: []
            };
        }
    }
    async loadSatelliteNames() {
        try {
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get('/satellite/names', {
                headers: {
                    'Authorization': `Bearer ${process.env.JWT_TOKEN || ''}`
                },
                timeout: 10000
            }));
            return response.data || [];
        }
        catch (error) {
            this.logger.warn(`加载卫星名称失败: ${error.message || '未知错误'}，将使用空列表`);
            return [];
        }
    }
    async loadConstellationNames() {
        var _a;
        try {
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get('/constellation/names', {
                headers: {
                    'Authorization': `Bearer ${process.env.JWT_TOKEN || ''}`
                },
                timeout: 10000
            }));
            return ((_a = response.data) === null || _a === void 0 ? void 0 : _a.constellationNames) || [];
        }
        catch (error) {
            this.logger.warn(`加载星座名称失败: ${error.message || '未知错误'}，将使用空列表`);
            return [];
        }
    }
    async loadLaunchSiteNamesFromApi() {
        var _a;
        try {
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get('/api/es/launch/site-names', {
                headers: {
                    'Authorization': `Bearer ${process.env.JWT_TOKEN || ''}`
                },
                timeout: 10000
            }));
            return ((_a = response.data) === null || _a === void 0 ? void 0 : _a.data) || [];
        }
        catch (error) {
            this.logger.warn(`从API加载发射场名称失败: ${error.message || '未知错误'}，将使用本地数据`);
            return [];
        }
    }
    async loadRocketNames() {
        var _a;
        try {
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get('/api/es/launch/rocket-names', {
                headers: {
                    'Authorization': `Bearer ${process.env.JWT_TOKEN || ''}`
                },
                timeout: 10000
            }));
            return ((_a = response.data) === null || _a === void 0 ? void 0 : _a.data) || [];
        }
        catch (error) {
            this.logger.warn(`加载火箭名称失败: ${error.message || '未知错误'}，将使用空列表`);
            return [];
        }
    }
    async loadProviders() {
        var _a;
        try {
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get('/api/es/launch/providers', {
                headers: {
                    'Authorization': `Bearer ${process.env.JWT_TOKEN || ''}`
                },
                timeout: 10000
            }));
            return ((_a = response.data) === null || _a === void 0 ? void 0 : _a.data) || [];
        }
        catch (error) {
            this.logger.warn(`加载发射服务商名称失败: ${error.message || '未知错误'}，将使用空列表`);
            return [];
        }
    }
    async recognizeEntities(content, contentCn) {
        await this.initialize();
        if (!content || !this.entityCollections) {
            return {
                satellites: [],
                constellations: [],
                launch_sites: [],
                rockets: [],
                providers: []
            };
        }
        const result = {
            satellites: this.extractEntities(content, this.entityCollections.satellites, contentCn),
            constellations: this.extractEntities(content, this.entityCollections.constellations, contentCn),
            launch_sites: this.extractEntities(content, this.entityCollections.launchSites, contentCn),
            rockets: this.extractEntities(content, this.entityCollections.rockets, contentCn),
            providers: this.extractEntities(content, this.entityCollections.providers, contentCn)
        };
        const totalEntities = Object.values(result).reduce((sum, entities) => sum + entities.length, 0);
        this.logger.debug(`从文本中识别到 ${totalEntities} 个实体`);
        return result;
    }
    extractEntities(content, entityNames, contentCn) {
        const foundEntities = [];
        const contentLower = content.toLowerCase();
        for (const entityName of entityNames) {
            if (!entityName || entityName.length < 2)
                continue;
            const regex = new RegExp(`\\b${this.escapeRegex(entityName)}\\b`, 'gi');
            const matches = content.match(regex);
            if (matches && matches.length > 0) {
                const chineseTranslation = this.findChineseTranslation(entityName, contentCn);
                const entityWithTranslation = chineseTranslation
                    ? `${entityName}（${chineseTranslation}）`
                    : entityName;
                if (!foundEntities.includes(entityWithTranslation)) {
                    foundEntities.push(entityWithTranslation);
                }
            }
        }
        return foundEntities;
    }
    escapeRegex(text) {
        return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
    findChineseTranslation(englishName, contentCn) {
        if (!contentCn)
            return null;
        return null;
    }
    async reloadEntityCollections() {
        this.logger.log('重新加载实体名称集合...');
        this.isInitialized = false;
        this.initializationPromise = null;
        await this.initialize();
    }
    getEntityStatistics() {
        if (!this.entityCollections) {
            return null;
        }
        return {
            satellites: this.entityCollections.satellites.length,
            constellations: this.entityCollections.constellations.length,
            launchSites: this.entityCollections.launchSites.length,
            rockets: this.entityCollections.rockets.length,
            providers: this.entityCollections.providers.length,
            total: Object.values(this.entityCollections).reduce((sum, entities) => sum + entities.length, 0)
        };
    }
};
EntityRecognitionService = EntityRecognitionService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [axios_1.HttpService])
], EntityRecognitionService);
exports.EntityRecognitionService = EntityRecognitionService;
//# sourceMappingURL=entity-recognition.service.js.map