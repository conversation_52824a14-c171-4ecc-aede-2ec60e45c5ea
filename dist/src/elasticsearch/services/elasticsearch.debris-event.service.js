"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ElasticsearchDebrisEventService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElasticsearchDebrisEventService = void 0;
const common_1 = require("@nestjs/common");
const elasticsearch_1 = require("@nestjs/elasticsearch");
const elasticsearch_base_service_1 = require("./elasticsearch.base.service");
let ElasticsearchDebrisEventService = ElasticsearchDebrisEventService_1 = class ElasticsearchDebrisEventService extends elasticsearch_base_service_1.ElasticsearchBaseService {
    constructor(elasticsearchService) {
        super(elasticsearchService);
        this.elasticsearchService = elasticsearchService;
        this.logger = new common_1.Logger(ElasticsearchDebrisEventService_1.name);
        this.debrisEventIndex = 'event_discos';
    }
    async searchDebrisEvents(queryDto) {
        var _a, _b, _c, _d;
        try {
            this.logger.debug(`搜索碎片事件，参数: ${JSON.stringify(queryDto)}`);
            const { _id, piece, start_date, end_date, keyword } = queryDto;
            const must = [];
            if (_id) {
                must.push({ term: { _id } });
            }
            if (piece) {
                must.push({
                    term: {
                        "piece.keyword": piece
                    }
                });
            }
            if (start_date || end_date) {
                const range = { sdate: {} };
                if (start_date) {
                    range.sdate.gte = start_date;
                }
                if (end_date) {
                    range.sdate.lte = end_date;
                }
                must.push({ range });
            }
            if (keyword) {
                must.push({
                    multi_match: {
                        query: keyword,
                        fields: ['*'],
                        type: 'phrase_prefix'
                    }
                });
            }
            const query = {
                bool: {
                    must: must.length > 0 ? must : [{ match_all: {} }]
                }
            };
            this.logger.debug(`碎片事件查询条件: ${JSON.stringify(query)}`);
            const [discosResults, jonathanResults] = await Promise.all([
                this.elasticsearchService.search({
                    index: 'event_discos',
                    query
                }),
                this.elasticsearchService.search({
                    index: 'event_jonathan',
                    query
                })
            ]);
            const calculateConfidence = (hit, queryDto) => {
                let confidence = 0;
                const source = hit._source;
                if (queryDto.piece && source.piece === queryDto.piece) {
                    confidence += 0.4;
                }
                if (queryDto.start_date || queryDto.end_date) {
                    const eventDate = new Date(source.sdate).getTime();
                    const startDate = queryDto.start_date ? new Date(queryDto.start_date).getTime() : 0;
                    const endDate = queryDto.end_date ? new Date(queryDto.end_date).getTime() : Date.now();
                    if (eventDate >= startDate && eventDate <= endDate) {
                        confidence += 0.3;
                    }
                }
                else {
                    confidence += 0.15;
                }
                if (queryDto.keyword) {
                    const keywordLower = queryDto.keyword.toLowerCase();
                    const fieldsToCheck = ['name', 'event_type', 'description', 'object_class'];
                    const availableFields = fieldsToCheck.filter(field => field in source && typeof source[field] === 'string');
                    let keywordMatchCount = 0;
                    for (const field of availableFields) {
                        const value = source[field];
                        if (typeof value === 'string' && value.toLowerCase().includes(keywordLower)) {
                            keywordMatchCount++;
                        }
                    }
                    if (keywordMatchCount > 0) {
                        confidence += (0.3 * keywordMatchCount / availableFields.length);
                    }
                }
                else {
                    confidence += 0.15;
                }
                confidence += 0.1;
                return Math.min(confidence, 1);
            };
            const hits = [
                ...(((_a = discosResults.hits) === null || _a === void 0 ? void 0 : _a.hits) || []).map(hit => ({
                    _id: hit._id,
                    _source: hit._source,
                    source: 'discos',
                    confidence: calculateConfidence(hit, queryDto)
                })),
                ...(((_b = jonathanResults.hits) === null || _b === void 0 ? void 0 : _b.hits) || []).map(hit => ({
                    _id: hit._id,
                    _source: hit._source,
                    source: 'jonathan',
                    confidence: calculateConfidence(hit, queryDto)
                }))
            ];
            const total = (((_c = discosResults.hits) === null || _c === void 0 ? void 0 : _c.total)
                ? (typeof discosResults.hits.total === 'number'
                    ? discosResults.hits.total
                    : discosResults.hits.total.value || 0)
                : 0) +
                (((_d = jonathanResults.hits) === null || _d === void 0 ? void 0 : _d.total)
                    ? (typeof jonathanResults.hits.total === 'number'
                        ? jonathanResults.hits.total
                        : jonathanResults.hits.total.value || 0)
                    : 0);
            hits.sort((a, b) => b.confidence - a.confidence);
            return {
                total,
                hits: hits.map(hit => {
                    const source = hit._source;
                    return {
                        _id: hit._id || '',
                        source: hit.source,
                        confidence: hit.confidence,
                        piece: source.piece,
                        sdate: source.sdate,
                        event_type: source.event_type,
                        name: source.name,
                        cataloguedFragments: source.cataloguedFragments,
                        object_class: source.object_class
                    };
                })
            };
        }
        catch (error) {
            this.logger.error(`搜索碎片事件失败: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`搜索碎片事件失败: ${error.message}`);
        }
    }
    async getDebrisEventById(id) {
        try {
            const result = await this.elasticsearchService.search({
                index: this.debrisEventIndex,
                body: {
                    query: {
                        term: {
                            _id: id,
                        },
                    },
                },
            });
            if (result.hits.hits.length === 0) {
                return null;
            }
            const hit = result.hits.hits[0];
            const source = hit._source;
            return Object.assign(Object.assign({}, source), { _id: hit._id });
        }
        catch (error) {
            this.logger.error(`获取碎片事件详情失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getDebrisEventStats() {
        try {
            const result = await this.elasticsearchService.search({
                index: this.debrisEventIndex,
                body: {
                    size: 0,
                    aggs: {
                        event_types: {
                            terms: {
                                field: 'event_type.keyword',
                                size: 10,
                            },
                        },
                        event_by_year: {
                            date_histogram: {
                                field: 'event_time',
                                calendar_interval: 'year',
                                format: 'yyyy',
                            },
                        },
                    },
                },
            });
            const eventTypes = result.aggregations && 'event_types' in result.aggregations
                ? result.aggregations.event_types.buckets || []
                : [];
            const eventByYear = result.aggregations && 'event_by_year' in result.aggregations
                ? result.aggregations.event_by_year.buckets || []
                : [];
            return {
                event_types: eventTypes.map((bucket) => ({
                    type: bucket.key,
                    count: bucket.doc_count,
                })),
                event_by_year: eventByYear.map((bucket) => ({
                    year: bucket.key_as_string,
                    count: bucket.doc_count,
                })),
            };
        }
        catch (error) {
            this.logger.error(`获取碎片事件统计信息失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    buildDebrisEventQueryConditions(filters) {
        const must = [];
        if (filters.event_type) {
            must.push({
                match: {
                    event_type: filters.event_type,
                },
            });
        }
        if (filters.start_date || filters.end_date) {
            const rangeQuery = {};
            if (filters.start_date) {
                rangeQuery.gte = this.normalizeDate(filters.start_date);
            }
            if (filters.end_date) {
                rangeQuery.lte = this.normalizeDate(filters.end_date);
            }
            must.push({
                range: {
                    event_time: rangeQuery,
                },
            });
        }
        if (filters.keyword) {
            must.push({
                multi_match: {
                    query: filters.keyword,
                    fields: ['description', 'event_type'],
                    type: 'best_fields',
                    fuzziness: 'AUTO',
                },
            });
        }
        if (filters.debris_id) {
            must.push({
                term: {
                    debris_ids: filters.debris_id,
                },
            });
        }
        return must.length ? must : [{ match_all: {} }];
    }
    normalizeDate(date) {
        if (!date)
            return null;
        try {
            const dateObj = new Date(date);
            return dateObj.toISOString();
        }
        catch (error) {
            this.logger.warn(`日期格式化失败: ${date}`);
            return date;
        }
    }
};
ElasticsearchDebrisEventService = ElasticsearchDebrisEventService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [elasticsearch_1.ElasticsearchService])
], ElasticsearchDebrisEventService);
exports.ElasticsearchDebrisEventService = ElasticsearchDebrisEventService;
//# sourceMappingURL=elasticsearch.debris-event.service.js.map