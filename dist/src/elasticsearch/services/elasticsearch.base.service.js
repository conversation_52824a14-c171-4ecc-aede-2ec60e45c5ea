"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElasticsearchBaseService = void 0;
const common_1 = require("@nestjs/common");
const elasticsearch_1 = require("@nestjs/elasticsearch");
let ElasticsearchBaseService = class ElasticsearchBaseService {
    constructor(elasticsearchService) {
        this.elasticsearchService = elasticsearchService;
        this.logger = new common_1.Logger(this.constructor.name);
        this.testConnection();
    }
    async testConnection() {
        try {
            const info = await this.elasticsearchService.info();
            this.logger.log(`成功连接到Elasticsearch: ${info.name}`);
        }
        catch (error) {
            this.logger.error(`无法连接到Elasticsearch: ${error.message}`, error.stack);
        }
    }
    async search(params) {
        try {
            return await this.elasticsearchService.search(params);
        }
        catch (error) {
            this.logger.error(`搜索失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    normalizeDate(dateStr) {
        if (!dateStr)
            return null;
        try {
            const date = new Date(dateStr);
            if (isNaN(date.getTime()))
                return null;
            return date.toISOString().split('T')[0];
        }
        catch (error) {
            this.logger.warn(`无法解析日期: ${dateStr}`);
            return null;
        }
    }
    calculateStringSimilarity(str1, str2) {
        if (!str1 || !str2)
            return 0;
        const s1 = this.normalizeString(str1);
        const s2 = this.normalizeString(str2);
        if (s1 === s2)
            return 1;
        const m = s1.length;
        const n = s2.length;
        const dp = Array(m + 1).fill(0).map(() => Array(n + 1).fill(0));
        for (let i = 0; i <= m; i++)
            dp[i][0] = i;
        for (let j = 0; j <= n; j++)
            dp[0][j] = j;
        for (let i = 1; i <= m; i++) {
            for (let j = 1; j <= n; j++) {
                if (s1[i - 1] === s2[j - 1]) {
                    dp[i][j] = dp[i - 1][j - 1];
                }
                else {
                    dp[i][j] = Math.min(dp[i - 1][j] + 1, dp[i][j - 1] + 1, dp[i - 1][j - 1] + 1);
                }
            }
        }
        return 1 - dp[m][n] / Math.max(m, n);
    }
    normalizeString(str) {
        return str
            .toLowerCase()
            .replace(/[^a-z0-9]/g, '')
            .trim();
    }
};
ElasticsearchBaseService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [elasticsearch_1.ElasticsearchService])
], ElasticsearchBaseService);
exports.ElasticsearchBaseService = ElasticsearchBaseService;
//# sourceMappingURL=elasticsearch.base.service.js.map