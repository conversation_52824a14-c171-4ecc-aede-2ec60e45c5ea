"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ElasticsearchLoopholeService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElasticsearchLoopholeService = void 0;
const common_1 = require("@nestjs/common");
const elasticsearch_base_service_1 = require("./elasticsearch.base.service");
let ElasticsearchLoopholeService = ElasticsearchLoopholeService_1 = class ElasticsearchLoopholeService {
    constructor(esBaseService) {
        this.esBaseService = esBaseService;
        this.logger = new common_1.Logger(ElasticsearchLoopholeService_1.name);
        this.loopholeIndex = 'cve';
    }
    async searchLoopholeInfo(query) {
        try {
            this.logger.debug(`搜索漏洞信息，参数: ${JSON.stringify(query)}`);
            const must = [];
            if (query._id) {
                must.push({
                    term: {
                        "cveMetadata.cveId.keyword": query._id
                    }
                });
            }
            if (query.keywords) {
                must.push({
                    multi_match: {
                        query: query.keywords,
                        fields: [
                            "cveMetadata.cveId^3",
                            "containers.cna.descriptions.value^2",
                            "containers.cna.affected.vendor",
                            "containers.cna.affected.product",
                            "containers.cna.problemTypes.descriptions.description"
                        ],
                        type: "best_fields",
                        fuzziness: "AUTO"
                    }
                });
            }
            if (must.length === 0) {
                must.push({ match_all: {} });
            }
            const searchResult = await this.esBaseService.search({
                index: this.loopholeIndex,
                body: {
                    query: {
                        bool: {
                            must
                        }
                    },
                    sort: [
                        { "cveMetadata.datePublished": { order: "desc" } }
                    ],
                    size: 100
                }
            });
            const total = typeof searchResult.hits.total === 'number'
                ? searchResult.hits.total
                : searchResult.hits.total.value;
            const hits = searchResult.hits.hits.map(hit => {
                var _a, _b, _c, _d, _e;
                const source = hit._source;
                const hitId = hit._id || '';
                const loopholeDoc = {
                    descriptions: ((_b = (_a = source.containers.cna.descriptions) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.value) || '',
                    affected: this.formatAffected(source.containers.cna.affected),
                    severity: this.getSeverity(source),
                    cve_id: source.cveMetadata.cveId,
                    published_date: source.cveMetadata.datePublished,
                    patch_status: this.getPatchStatus(source),
                    patch_version: this.getPatchVersion(source),
                    references: ((_c = source.containers.cna.references) === null || _c === void 0 ? void 0 : _c.map(ref => ref.url)) || [],
                    mitigation: ((_e = (_d = source.containers.cna.solutions) === null || _d === void 0 ? void 0 : _d[0]) === null || _e === void 0 ? void 0 : _e.value) || ''
                };
                return Object.assign(Object.assign({}, loopholeDoc), { _id: hitId });
            });
            return {
                total,
                hits: hits.filter(hit => hit._id !== '')
            };
        }
        catch (error) {
            this.logger.error(`搜索漏洞信息失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    formatAffected(affected) {
        if (!affected || affected.length === 0) {
            return '';
        }
        return affected.map(item => {
            var _a;
            const versions = ((_a = item.versions) === null || _a === void 0 ? void 0 : _a.map(v => v.version).join(', ')) || '';
            return `${item.vendor} ${item.product} ${versions}`;
        }).join('; ');
    }
    getSeverity(source) {
        const metrics = source.containers.cna.metrics;
        if (metrics && metrics.length > 0) {
            return metrics[0].severity || '未知';
        }
        const problemTypes = source.containers.cna.problemTypes;
        if (problemTypes && problemTypes.length > 0) {
            const descriptions = problemTypes[0].descriptions;
            if (descriptions && descriptions.length > 0) {
                const desc = descriptions[0].description.toLowerCase();
                if (desc.includes('critical'))
                    return '严重';
                if (desc.includes('high'))
                    return '高';
                if (desc.includes('medium'))
                    return '中';
                if (desc.includes('low'))
                    return '低';
            }
        }
        return '未知';
    }
    getPatchStatus(source) {
        const solutions = source.containers.cna.solutions;
        if (solutions && solutions.length > 0 && solutions[0].value) {
            const solutionText = solutions[0].value.toLowerCase();
            if (solutionText.includes('patch') || solutionText.includes('update') || solutionText.includes('upgrade')) {
                return '已修复';
            }
        }
        return '未修复';
    }
    getPatchVersion(source) {
        const solutions = source.containers.cna.solutions;
        if (solutions && solutions.length > 0 && solutions[0].value) {
            const solutionText = solutions[0].value;
            const versionMatch = solutionText.match(/version\s+(\d+\.\d+(\.\d+)*)/i);
            if (versionMatch) {
                return versionMatch[1];
            }
            const otherVersionMatch = solutionText.match(/(\d+\.\d+(\.\d+)*)/);
            if (otherVersionMatch) {
                return otherVersionMatch[1];
            }
        }
        return null;
    }
};
ElasticsearchLoopholeService = ElasticsearchLoopholeService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [elasticsearch_base_service_1.ElasticsearchBaseService])
], ElasticsearchLoopholeService);
exports.ElasticsearchLoopholeService = ElasticsearchLoopholeService;
//# sourceMappingURL=elasticsearch.loophole.service.js.map