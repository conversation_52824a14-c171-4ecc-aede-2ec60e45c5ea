"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ElasticsearchCorrelationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElasticsearchCorrelationService = void 0;
const common_1 = require("@nestjs/common");
const elasticsearch_1 = require("@nestjs/elasticsearch");
const elasticsearch_base_service_1 = require("./elasticsearch.base.service");
let ElasticsearchCorrelationService = ElasticsearchCorrelationService_1 = class ElasticsearchCorrelationService {
    constructor(elasticsearchService, esBaseService) {
        this.elasticsearchService = elasticsearchService;
        this.esBaseService = esBaseService;
        this.logger = new common_1.Logger(ElasticsearchCorrelationService_1.name);
        this.config = {
            timeWindowYears: 5,
            minNameSimilarity: 3,
            weights: {
                cosparWeight: 0.7,
                timeWeight: 0.15,
                nameWeight: 0.15,
            },
        };
    }
    calculateCosparMatch(cosparId1, cosparId2) {
        this.logger.debug(`计算COSPAR匹配度: ${cosparId1} 和 ${cosparId2}`);
        if (!cosparId1 || !cosparId2) {
            this.logger.debug('一个或两个COSPAR ID为空');
            return 0;
        }
        if (cosparId1 === cosparId2) {
            this.logger.debug('COSPAR ID完全匹配');
            return 1;
        }
        const pattern = /^(\d{4})-(\d{3})([A-Z]*)$/;
        try {
            const match1 = cosparId1.match(pattern);
            const match2 = cosparId2.match(pattern);
            if (!match1 || !match2) {
                this.logger.debug(`无效的COSPAR ID格式: ${!match1 ? cosparId1 : cosparId2}`);
                return 0;
            }
            if (match1[1] !== match2[1]) {
                this.logger.debug(`年份不匹配: ${match1[1]} vs ${match2[1]}`);
                return 0;
            }
            if (match1[2] !== match2[2]) {
                this.logger.debug(`序号不匹配: ${match1[2]} vs ${match2[2]}`);
                return 0;
            }
            const yearMatch = 0.5;
            this.logger.debug(`年份匹配分数: ${yearMatch}`);
            const numberMatch = 0.4;
            this.logger.debug(`序号匹配分数: ${numberMatch}`);
            let letterMatch = 0;
            if (match1[3] && match2[3]) {
                letterMatch = match1[3].charAt(0) === match2[3].charAt(0) ? 0.1 : 0;
                this.logger.debug(`字母匹配分数: ${letterMatch}`);
            }
            const totalScore = yearMatch + numberMatch + letterMatch;
            this.logger.debug(`COSPAR总匹配分数: ${totalScore}`);
            return totalScore;
        }
        catch (error) {
            this.logger.error(`COSPAR ID匹配出错: ${error}`);
            return 0;
        }
    }
    calculateTimeScore(eventDate, firstEpoch) {
        const eventTime = new Date(eventDate).getTime();
        const epochTime = new Date(firstEpoch).getTime();
        if (epochTime >= eventTime) {
            const yearDiff = (epochTime - eventTime) / (365 * 24 * 60 * 60 * 1000);
            if (yearDiff <= 5) {
                return 1;
            }
            if (yearDiff <= 10) {
                return 1 - (yearDiff - 5) / 5;
            }
        }
        return 0;
    }
    calculateNameScore(objectClass) {
        if (!objectClass) {
            return 0.2;
        }
        if (objectClass === 'Payload Fragmentation Debris') {
            return 0.8;
        }
        else if (objectClass.includes('Debris')) {
            return 0.5;
        }
        return 0.2;
    }
    calculateConfidence(scores) {
        const { cosparMatch, timeScore, nameScore } = scores;
        const { cosparWeight, timeWeight, nameWeight } = this.config.weights;
        const result = (cosparMatch * cosparWeight +
            timeScore * timeWeight +
            nameScore * nameWeight);
        this.logger.debug(`置信度计算:
      cosparMatch: ${cosparMatch} * ${cosparWeight} = ${cosparMatch * cosparWeight}
      timeScore: ${timeScore} * ${timeWeight} = ${timeScore * timeWeight}
      nameScore: ${nameScore} * ${nameWeight} = ${nameScore * nameWeight}
      总计: ${result}
    `);
        return result;
    }
    async findRelatedEvents(query) {
        var _a, _b, _c, _d, _e, _f;
        const { cospar_id, first_epoch, name, min_confidence } = query;
        this.logger.debug(`搜索与碎片相关的事件:
      cospar_id: ${cospar_id}
      first_epoch: ${first_epoch || '未提供'}
      name: ${name || '未提供'}
      min_confidence: ${min_confidence}
    `);
        try {
            const discosQuery = {
                index: 'event_discos',
                body: {
                    size: 1000,
                    query: {
                        match_all: {}
                    }
                }
            };
            const jonathanQuery = {
                index: 'event_jonathan',
                body: {
                    size: 1000,
                    query: {
                        match_all: {}
                    }
                }
            };
            this.logger.debug('Elasticsearch查询:', {
                discos: JSON.stringify(discosQuery, null, 2),
                jonathan: JSON.stringify(jonathanQuery, null, 2)
            });
            const [discosResult, jonathanResult] = await Promise.all([
                this.elasticsearchService.search(discosQuery),
                this.elasticsearchService.search(jonathanQuery)
            ]);
            this.logger.debug(`在discos中找到 ${((_b = (_a = discosResult.hits) === null || _a === void 0 ? void 0 : _a.hits) === null || _b === void 0 ? void 0 : _b.length) || 0} 个潜在匹配`);
            this.logger.debug(`在jonathan中找到 ${((_d = (_c = jonathanResult.hits) === null || _c === void 0 ? void 0 : _c.hits) === null || _d === void 0 ? void 0 : _d.length) || 0} 个潜在匹配`);
            const correlations = [];
            const discosHits = ((_e = discosResult.hits) === null || _e === void 0 ? void 0 : _e.hits) || [];
            for (const hit of discosHits) {
                const source = hit._source;
                if (!source || !hit._id) {
                    this.logger.debug(`跳过discos结果 ${hit._id || 'unknown'} - 无源数据或ID`);
                    continue;
                }
                this.logger.debug(`处理discos结果 ${hit._id}:`, JSON.stringify(source, null, 2));
                const cosparMatch = this.calculateCosparMatch(cospar_id, source.piece);
                if (cosparMatch === 0) {
                    this.logger.debug(`跳过discos结果 ${hit._id} - COSPAR ID不匹配`);
                    continue;
                }
                const scores = {
                    cosparMatch,
                    timeScore: first_epoch ? this.calculateTimeScore(source.sdate, first_epoch) : 0.5,
                    nameScore: this.calculateNameScore(source.object_class),
                };
                this.logger.debug(`discos ${hit._id} 的分数:`, scores);
                const confidence = this.calculateConfidence(scores);
                this.logger.debug(`discos ${hit._id} 的置信度: ${confidence}`);
                if (confidence >= min_confidence) {
                    correlations.push({
                        event_id: hit._id,
                        confidence,
                        scores: {
                            cosparMatch: scores.cosparMatch,
                            timeScore: scores.timeScore,
                            nameScore: scores.nameScore,
                        },
                        event_details: Object.assign(Object.assign({}, source), { source: 'discos' }),
                    });
                }
            }
            const jonathanHits = ((_f = jonathanResult.hits) === null || _f === void 0 ? void 0 : _f.hits) || [];
            for (const hit of jonathanHits) {
                const source = hit._source;
                if (!source || !hit._id) {
                    this.logger.debug(`跳过jonathan结果 ${hit._id || 'unknown'} - 无源数据或ID`);
                    continue;
                }
                this.logger.debug(`处理jonathan结果 ${hit._id}:`, JSON.stringify(source, null, 2));
                const cosparMatch = this.calculateCosparMatch(cospar_id, source.piece);
                if (cosparMatch === 0) {
                    this.logger.debug(`跳过jonathan结果 ${hit._id} - COSPAR ID不匹配`);
                    continue;
                }
                const scores = {
                    cosparMatch,
                    timeScore: first_epoch ? this.calculateTimeScore(source.sdate, first_epoch) : 0.5,
                    nameScore: this.calculateNameScore(source.object_class),
                };
                this.logger.debug(`jonathan ${hit._id} 的分数:`, scores);
                const confidence = this.calculateConfidence(scores);
                this.logger.debug(`jonathan ${hit._id} 的置信度: ${confidence}`);
                if (confidence >= min_confidence) {
                    correlations.push({
                        event_id: hit._id,
                        confidence,
                        scores: {
                            cosparMatch: scores.cosparMatch,
                            timeScore: scores.timeScore,
                            nameScore: scores.nameScore,
                        },
                        event_details: Object.assign(Object.assign({}, source), { source: 'jonathan' }),
                    });
                }
            }
            correlations.sort((a, b) => b.confidence - a.confidence);
            this.logger.debug(`找到 ${correlations.length} 个相关事件`);
            return {
                total: correlations.length,
                hits: correlations
            };
        }
        catch (error) {
            this.logger.error(`搜索相关事件出错: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findRelatedDebris(query) {
        var _a, _b, _c;
        const { piece, sdate, event_type, min_confidence } = query;
        this.logger.debug(`搜索与事件相关的碎片:
      piece: ${piece}
      sdate: ${sdate}
      event_type: ${event_type}
      min_confidence: ${min_confidence}
    `);
        try {
            const searchQuery = {
                index: 'debris_info',
                body: {
                    size: 1000,
                    query: {
                        bool: {
                            should: [
                                {
                                    match: {
                                        cospar_id: {
                                            query: piece,
                                            boost: 3.0
                                        }
                                    }
                                },
                                {
                                    match: {
                                        name: {
                                            query: piece,
                                            fuzziness: "AUTO",
                                            boost: 1.5
                                        }
                                    }
                                }
                            ],
                            minimum_should_match: 1
                        }
                    }
                }
            };
            this.logger.debug(`碎片搜索查询: ${JSON.stringify(searchQuery, null, 2)}`);
            const searchResult = await this.elasticsearchService.search(searchQuery);
            this.logger.debug(`找到 ${((_b = (_a = searchResult.hits) === null || _a === void 0 ? void 0 : _a.hits) === null || _b === void 0 ? void 0 : _b.length) || 0} 个潜在匹配的碎片`);
            const correlations = [];
            const hits = ((_c = searchResult.hits) === null || _c === void 0 ? void 0 : _c.hits) || [];
            for (const hit of hits) {
                const source = hit._source;
                if (!source || !hit._id) {
                    this.logger.debug(`跳过碎片 ${hit._id || 'unknown'} - 无源数据或ID`);
                    continue;
                }
                this.logger.debug(`处理碎片 ${hit._id}:`, JSON.stringify(source, null, 2));
                const scores = {
                    cosparMatch: this.calculateCosparMatch(source.cospar_id, piece),
                    timeScore: this.calculateTimeScore(sdate, source.first_epoch),
                    nameScore: this.calculateNameScore(source.object_class),
                };
                this.logger.debug(`碎片 ${hit._id} 的分数:`, scores);
                const confidence = this.calculateConfidence(scores);
                this.logger.debug(`碎片 ${hit._id} 的置信度: ${confidence}`);
                if (confidence >= min_confidence) {
                    correlations.push({
                        debris_id: hit._id,
                        confidence,
                        scores: {
                            cosparMatch: scores.cosparMatch,
                            timeScore: scores.timeScore,
                            nameScore: scores.nameScore,
                        },
                        debris_details: source
                    });
                }
            }
            correlations.sort((a, b) => b.confidence - a.confidence);
            this.logger.debug(`找到 ${correlations.length} 个相关碎片`);
            return {
                debris_list: correlations
            };
        }
        catch (error) {
            this.logger.error(`搜索相关碎片出错: ${error.message}`, error.stack);
            throw error;
        }
    }
};
ElasticsearchCorrelationService = ElasticsearchCorrelationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [elasticsearch_1.ElasticsearchService,
        elasticsearch_base_service_1.ElasticsearchBaseService])
], ElasticsearchCorrelationService);
exports.ElasticsearchCorrelationService = ElasticsearchCorrelationService;
//# sourceMappingURL=elasticsearch.correlation.service.js.map