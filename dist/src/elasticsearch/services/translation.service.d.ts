import { TranslationConfig, ThemeExtractionConfig } from '../../../config/llm.config';
import { EntityRecognitionResult } from '../types/news.types';
import { EntityRecognitionService } from './entity-recognition.service';
export declare class TranslationService {
    private readonly entityRecognitionService;
    private readonly logger;
    private openai;
    private translationCache;
    private apiCallCount;
    private apiTotalTime;
    private translationConfig;
    private themeExtractionConfig;
    private activeRequests;
    private readonly requestQueue;
    private readonly paragraphSeparators;
    private consecutiveFailures;
    private failureStats;
    private readonly sensitiveReplacements;
    constructor(entityRecognitionService: EntityRecognitionService);
    private selectOptimalConfig;
    private switchToOptimalConfig;
    private restoreOriginalConfig;
    private loadTranslationConfig;
    private loadThemeExtractionConfig;
    getTranslationConfig(): TranslationConfig;
    getThemeExtractionConfig(): ThemeExtractionConfig;
    updateTranslationConfig(newConfig: Partial<TranslationConfig>): void;
    updateThemeExtractionConfig(newConfig: Partial<ThemeExtractionConfig>): void;
    private sanitizeSensitiveWords;
    translateText(text: string, retryCount?: number, isSegment?: boolean, forceRetranslate?: boolean): Promise<string>;
    translateBatch(texts: string[]): Promise<string[]>;
    private generateCacheKey;
    getAPIStats(): {
        callCount: number;
        totalTime: number;
        averageTime: number;
        cacheSize: number;
    };
    getFailureStats(): {
        totalAttempts: number;
        successfulProcessing: number;
        totalFailures: number;
        successRate: number;
        failures: {
            contentFilter: {
                count: number;
                percentage: string;
            };
            timeout: {
                count: number;
                percentage: string;
            };
            rateLimit: {
                count: number;
                percentage: string;
            };
            networkError: {
                count: number;
                percentage: string;
            };
            other: {
                count: number;
                percentage: string;
            };
        };
        consecutiveFailures: number;
        recommendations: string[];
    };
    private generateRecommendations;
    resetFailureStats(): void;
    clearCache(): void;
    private executeWithConcurrencyControl;
    private processNextQueuedTask;
    private translateLongText;
    private attemptFinegrainedTranslation;
    private splitIntoSentences;
    private translateTitleOnly;
    private validateTitleTranslation;
    private isValidTranslation;
    private validateCombinedTranslationResult;
    private splitTextIntoSegments;
    private removeAdvertisements;
    private preprocessContent;
    private applyStricterPreprocessing;
    extractThemes(title: string, content: string, retryCount?: number): Promise<string>;
    translateAndExtractInOneCall(title: string, summary: string, content: string, retryCount?: number, forceRetranslate?: boolean): Promise<{
        title_cn: string;
        summary_cn: string;
        content_cn: string;
        themes_cn: string;
        entities?: EntityRecognitionResult;
    }>;
    private extractFieldFromResponse;
    private getChineseLabel;
    private validateAndCleanTitle;
    private validateAndCleanField;
    private extractOriginalField;
    private analyzeSensitiveContent;
    private intelligentPreprocessing;
    private applyContextualPreprocessing;
    private applyLightPreprocessing;
}
