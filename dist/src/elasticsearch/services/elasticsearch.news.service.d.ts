import { ElasticsearchBaseService } from './elasticsearch.base.service';
import { ElasticsearchService } from '@nestjs/elasticsearch';
import { ExtractThemesParams, FailedDocument, HotTheme, HotThemesParams, NewsDocument, NewsListResult, TranslateNewsParams, TranslationStatistics } from '../types/news.types';
import { TranslationService } from './translation.service';
import { NewsListQueryDto } from '../dto/news-list-query.dto';
export declare class ElasticsearchNewsService extends ElasticsearchBaseService {
    protected readonly elasticsearchService: ElasticsearchService;
    private readonly translationService;
    private lastProcessedTimes;
    private failedDocuments;
    private readonly maxFailedDocuments;
    constructor(elasticsearchService: ElasticsearchService, translationService: TranslationService);
    getNewsIndices(): Promise<string[]>;
    getUntranslatedNews(index: string, batchSize?: number, forceRetranslate?: boolean): Promise<NewsDocument[]>;
    updateNewsWithTranslation(index: string, id: string, translations: {
        title_cn?: string;
        summary_cn?: string;
        content_cn?: string;
    }): Promise<boolean>;
    updateNewsWithThemes(index: string, id: string, themes: string): Promise<boolean>;
    private recordTranslationFailure;
    getNewsForThemeExtraction(index: string, batchSize?: number, forceReextract?: boolean): Promise<NewsDocument[]>;
    translateNews(params: TranslateNewsParams): Promise<TranslationStatistics>;
    getTranslationStatus(): Promise<any>;
    getAPIStats(): {
        callCount: number;
        totalTime: number;
        averageTime: number;
        cacheSize: number;
    };
    getFailureStats(): {
        failedDocuments: FailedDocument[];
        totalAttempts: number;
        successfulProcessing: number;
        totalFailures: number;
        successRate: number;
        failures: {
            contentFilter: {
                count: number;
                percentage: string;
            };
            timeout: {
                count: number;
                percentage: string;
            };
            rateLimit: {
                count: number;
                percentage: string;
            };
            networkError: {
                count: number;
                percentage: string;
            };
            other: {
                count: number;
                percentage: string;
            };
        };
        consecutiveFailures: number;
        recommendations: string[];
    };
    resetFailureStats(): void;
    getHotThemes(params: HotThemesParams): Promise<{
        themes: HotTheme[];
        total: number;
        indexCount: number;
        indexes: string[];
        processedDocs: number;
    }>;
    extractNewsThemes(params: ExtractThemesParams): Promise<TranslationStatistics>;
    private addFailedDocument;
    getFailedTranslations(): FailedDocument[];
    retryFailedTranslations(failedDocs?: FailedDocument[], maxRetries?: number): Promise<TranslationStatistics>;
    private translateSpecificDocument;
    private getDocumentById;
    private removeFailedDocument;
    private fetchContentFromUrl;
    private extractContentFromHtml;
    private cleanAdLines;
    searchNewsList(query: NewsListQueryDto): Promise<NewsListResult>;
}
