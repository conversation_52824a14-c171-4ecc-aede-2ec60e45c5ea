import { ElasticsearchBaseService } from './elasticsearch.base.service';
import { LoopholeQueryDto } from '../dto/loophole-query.dto';
import { LoopholeSearchResponse } from '../types/loophole.types';
export declare class ElasticsearchLoopholeService {
    private readonly esBaseService;
    private readonly logger;
    private readonly loopholeIndex;
    constructor(esBaseService: ElasticsearchBaseService);
    searchLoopholeInfo(query: LoopholeQueryDto): Promise<LoopholeSearchResponse>;
    private formatAffected;
    private getSeverity;
    private getPatchStatus;
    private getPatchVersion;
}
