import { Logger } from '@nestjs/common';
import { ElasticsearchService as NestElasticsearchService } from '@nestjs/elasticsearch';
import { ElasticsearchBaseService } from './elasticsearch.base.service';
import { ConstellationQueryDto, ConstellationSearchResponse, ConstellationNamesResponseDto, ConstellationOrganizationsResponseDto, ConstellationPurposesResponseDto } from '../types/constellation.types';
export declare class ElasticsearchConstellationService extends ElasticsearchBaseService {
    protected readonly logger: Logger;
    private readonly constellationIndex;
    constructor(elasticsearchService: NestElasticsearchService);
    searchConstellationInfo(queryDto: ConstellationQueryDto): Promise<ConstellationSearchResponse>;
    private createMatchFieldsDescription;
    getConstellationNames(): Promise<ConstellationNamesResponseDto>;
    getConstellationOrganizations(): Promise<ConstellationOrganizationsResponseDto>;
    getConstellationPurposes(): Promise<ConstellationPurposesResponseDto>;
    private extractNames;
    private calculateMatchedFields;
    private checkFieldMatch;
    private checkSynonyms;
    private getCountryVariants;
    private getPurposeVariants;
    private mergeFieldValues;
    private getNestedValue;
    searchConstellations(query: any): Promise<any>;
    getConstellationById(id: string): Promise<any>;
    getConstellationMembers(constellationId: string): Promise<any[]>;
    private buildConstellationQueryConditions;
    private filterLatestN2YOHits;
    private extractTimestampFromId;
}
