import { Logger } from '@nestjs/common';
import { ElasticsearchService } from '@nestjs/elasticsearch';
import { ElasticsearchBaseService } from './elasticsearch.base.service';
import { RocketQueryDto } from '../dto/rocket-query.dto';
export declare class ElasticsearchRocketService extends ElasticsearchBaseService {
    protected readonly elasticsearchService: ElasticsearchService;
    protected readonly logger: Logger;
    private readonly rocketIndices;
    constructor(elasticsearchService: ElasticsearchService);
    private getRocketIndices;
    searchRocketInfo(query: RocketQueryDto): Promise<any>;
}
