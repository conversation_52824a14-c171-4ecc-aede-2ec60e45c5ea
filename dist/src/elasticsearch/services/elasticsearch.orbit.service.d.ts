import { Logger } from '@nestjs/common';
import { ElasticsearchService as NestElasticsearchService } from '@nestjs/elasticsearch';
import { ElasticsearchBaseService } from './elasticsearch.base.service';
import { OrbitQueryDto } from '../dto/orbit-query.dto';
import { TleQueryConfig } from '../../../config/tle-query.config';
export declare class ElasticsearchOrbitService extends ElasticsearchBaseService {
    protected readonly logger: Logger;
    private readonly orbitIndex;
    private tleQueryConfig;
    constructor(elasticsearchService: NestElasticsearchService);
    private getOrbitType;
    searchOrbitInfo(queryDto: OrbitQueryDto): Promise<{
        total: any;
        hits: any;
    }>;
    private processSearchResults;
    searchOrbits(query: any): Promise<any>;
    getOrbitHistory(noradId: number, startDate?: string, endDate?: string): Promise<any[]>;
    private buildOrbitQueryConditions;
    protected normalizeDate(date: string): string | null;
    getBulkSatelliteTleByNoradIds(noradIds: number[], page?: number, limit?: number, useOneTimeQuery?: boolean): Promise<any>;
    getAllSatelliteTleData(sampleMode?: boolean): Promise<any>;
    getTleQueryConfig(): TleQueryConfig;
    updateTleQueryConfig(newConfig: Partial<TleQueryConfig>): void;
    private getQueryStrategyDescription;
}
