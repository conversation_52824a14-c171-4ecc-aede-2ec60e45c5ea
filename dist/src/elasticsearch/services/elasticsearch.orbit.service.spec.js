"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const elasticsearch_orbit_service_1 = require("./elasticsearch.orbit.service");
const elasticsearch_1 = require("@nestjs/elasticsearch");
describe('ElasticsearchOrbitService', () => {
    let service;
    let elasticsearchService;
    const mockElasticsearchService = {
        search: jest.fn().mockResolvedValue({
            hits: {
                total: { value: 3, relation: 'eq' },
                hits: []
            }
        })
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                elasticsearch_orbit_service_1.ElasticsearchOrbitService,
                {
                    provide: elasticsearch_1.ElasticsearchService,
                    useValue: mockElasticsearchService,
                },
            ],
        }).compile();
        service = module.get(elasticsearch_orbit_service_1.ElasticsearchOrbitService);
        elasticsearchService = module.get(elasticsearch_1.ElasticsearchService);
    });
    it('应当定义服务', () => {
        expect(service).toBeDefined();
    });
    describe('getBulkSatelliteTleByNoradIds', () => {
        it('应当限制请求的卫星数量不超过最大限制', async () => {
            const noradIds = Array(150).fill(0).map((_, index) => 25544 + index);
            await service.getBulkSatelliteTleByNoradIds(noradIds);
            expect(elasticsearchService.search).toHaveBeenCalled();
        });
    });
});
//# sourceMappingURL=elasticsearch.orbit.service.spec.js.map