import { Logger } from '@nestjs/common';
import { ElasticsearchService as NestElasticsearchService } from '@nestjs/elasticsearch';
import { SearchResponse } from '@elastic/elasticsearch/lib/api/types';
export declare class ElasticsearchBaseService {
    protected readonly elasticsearchService: NestElasticsearchService;
    protected readonly logger: Logger;
    constructor(elasticsearchService: NestElasticsearchService);
    private testConnection;
    search<T = any>(params: {
        index: string;
        body: any;
    }): Promise<SearchResponse<T>>;
    protected normalizeDate(dateStr: string | null | undefined): string | null;
    protected calculateStringSimilarity(str1: string, str2: string): number;
    protected normalizeString(str: string): string;
}
