import { Logger } from '@nestjs/common';
import { ElasticsearchService as NestElasticsearchService } from '@nestjs/elasticsearch';
import { ElasticsearchBaseService } from './elasticsearch.base.service';
import { DebrisEventQueryDto } from '../dto/debris-event-query.dto';
import { DebrisEventSearchResponse } from '../types/debris-event.types';
export declare class ElasticsearchDebrisEventService extends ElasticsearchBaseService {
    protected readonly elasticsearchService: NestElasticsearchService;
    protected readonly logger: Logger;
    private readonly debrisEventIndex;
    constructor(elasticsearchService: NestElasticsearchService);
    searchDebrisEvents(queryDto: DebrisEventQueryDto): Promise<DebrisEventSearchResponse>;
    getDebrisEventById(id: string): Promise<any>;
    getDebrisEventStats(): Promise<any>;
    private buildDebrisEventQueryConditions;
    protected normalizeDate(date: string): string | null;
}
