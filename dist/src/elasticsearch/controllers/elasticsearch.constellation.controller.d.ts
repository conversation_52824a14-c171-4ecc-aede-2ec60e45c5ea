import { ElasticsearchConstellationService } from '../services/elasticsearch.constellation.service';
import { ConstellationQueryDto, ConstellationSearchResponse, ConstellationNamesResponseDto, ConstellationOrganizationsResponseDto, ConstellationPurposesResponseDto } from '../types/constellation.types';
export declare class ElasticsearchConstellationController {
    private readonly constellationService;
    private readonly logger;
    constructor(constellationService: ElasticsearchConstellationService);
    searchConstellationInfo(query: ConstellationQueryDto): Promise<ConstellationSearchResponse>;
    getConstellationNames(): Promise<ConstellationNamesResponseDto>;
    getConstellationOrganizations(): Promise<ConstellationOrganizationsResponseDto>;
    getConstellationPurposes(): Promise<ConstellationPurposesResponseDto>;
}
