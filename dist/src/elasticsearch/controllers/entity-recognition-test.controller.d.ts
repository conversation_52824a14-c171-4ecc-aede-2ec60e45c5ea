import { EntityRecognitionService } from '../services/entity-recognition.service';
export declare class EntityRecognitionTestController {
    private readonly entityRecognitionService;
    private readonly logger;
    constructor(entityRecognitionService: EntityRecognitionService);
    getEntityStatistics(): Promise<any>;
    testEntityRecognition(content: string): Promise<any>;
    runSampleTest(): Promise<any>;
}
