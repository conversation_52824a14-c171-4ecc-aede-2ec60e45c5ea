import { ElasticsearchFreqService } from '../services/elasticsearch.freq.service';
import { FreqQueryDto } from '../dto/freq-query.dto';
import { FreqSearchResponse } from '../types/freq.types';
export declare class ElasticsearchFreqController {
    private readonly freqService;
    private readonly logger;
    constructor(freqService: ElasticsearchFreqService);
    searchFrequencies(query: FreqQueryDto): Promise<FreqSearchResponse>;
}
