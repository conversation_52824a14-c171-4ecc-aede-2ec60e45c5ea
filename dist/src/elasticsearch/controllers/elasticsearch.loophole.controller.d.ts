import { ElasticsearchLoopholeService } from '../services/elasticsearch.loophole.service';
import { LoopholeQueryDto } from '../dto/loophole-query.dto';
import { LoopholeSearchResponse } from '../types/loophole.types';
export declare class ElasticsearchLoopholeController {
    private readonly loopholeService;
    private readonly logger;
    constructor(loopholeService: ElasticsearchLoopholeService);
    searchLoopholeInfo(query: LoopholeQueryDto): Promise<LoopholeSearchResponse>;
}
