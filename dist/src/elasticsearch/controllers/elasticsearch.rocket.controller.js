"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ElasticsearchRocketController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElasticsearchRocketController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const elasticsearch_rocket_service_1 = require("../services/elasticsearch.rocket.service");
const rocket_query_dto_1 = require("../dto/rocket-query.dto");
let ElasticsearchRocketController = ElasticsearchRocketController_1 = class ElasticsearchRocketController {
    constructor(rocketService) {
        this.rocketService = rocketService;
        this.logger = new common_1.Logger(ElasticsearchRocketController_1.name);
    }
    async searchRocketInfo(query) {
        try {
            this.logger.debug(`接收到火箭信息查询请求: ${JSON.stringify(query)}`);
            const result = await this.rocketService.searchRocketInfo(query);
            this.logger.debug(`查询结果: 找到${result.total}条记录`);
            return {
                success: true,
                data: result,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`查询火箭信息失败: ${error.message}`, error.stack);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
};
__decorate([
    (0, common_1.Post)('search'),
    (0, swagger_1.ApiOperation)({
        summary: '查询火箭信息',
        description: `
    根据提供的查询条件搜索火箭信息。

    ## 数据来源
    从Elasticsearch数据库中的veh_discos和veh_jonathan索引中查询火箭信息。

    ## 查询条件
    - 火箭型号：匹配rocket_name或lv_variant字段，不区分大小写

    ## 返回数据
    - 返回所有非空字段及其值
    - 结果区分精确匹配(match_type: 'exact')和模糊匹配(match_type: 'fuzzy')
    - 精确匹配的结果排在前面，模糊匹配的结果按相似度从大到小排序
    - 每个结果包含similarity_score字段，表示与查询条件的相似度(0-1)
    `
    }),
    (0, swagger_1.ApiBody)({
        type: rocket_query_dto_1.RocketQueryDto,
        examples: {
            '基本查询': {
                summary: '基本查询示例',
                description: '使用分页参数进行基本查询',
                value: {
                    page: 1,
                    limit: 10
                }
            },
            '火箭型号查询': {
                summary: '火箭型号查询示例',
                description: '使用火箭型号进行查询',
                value: {
                    page: 1,
                    limit: 10,
                    rocketName: 'Falcon'
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '成功返回火箭信息',
        schema: {
            properties: {
                success: { type: 'boolean', example: true },
                data: {
                    type: 'object',
                    properties: {
                        total: { type: 'number', example: 42 },
                        page: { type: 'number', example: 1 },
                        limit: { type: 'number', example: 10 },
                        hits: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    _id: { type: 'string', example: 'falcon9' },
                                    _index: { type: 'string', example: 'veh_discos' },
                                    _score: { type: 'number', example: 2.5 },
                                    match_type: { type: 'string', example: 'exact', enum: ['exact', 'fuzzy'] },
                                    similarity_score: { type: 'number', example: 0.85 },
                                    rocket_name: { type: 'string', example: 'Falcon 9' },
                                    lv_variant: { type: 'string', example: 'FT4' },
                                    lv_family: { type: 'string', example: 'Falcon9' },
                                    lv_manufacturer: { type: 'string', example: 'SPX' },
                                    class: { type: 'string', example: 'O' },
                                    length: { type: 'number', example: 71 },
                                    diameter: { type: 'number', example: 3.65 },
                                    launch_mass: { type: 'number', example: 480 },
                                    leo_capacity: { type: 'number', example: 22800 },
                                    gto_capacity: { type: 'number', example: 8300 }
                                }
                            }
                        }
                    }
                },
                timestamp: { type: 'string', example: '2023-06-15T12:34:56.789Z' }
            }
        }
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [rocket_query_dto_1.RocketQueryDto]),
    __metadata("design:returntype", Promise)
], ElasticsearchRocketController.prototype, "searchRocketInfo", null);
ElasticsearchRocketController = ElasticsearchRocketController_1 = __decorate([
    (0, common_1.Controller)('api/es/rocket'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiTags)('发射信息'),
    __metadata("design:paramtypes", [elasticsearch_rocket_service_1.ElasticsearchRocketService])
], ElasticsearchRocketController);
exports.ElasticsearchRocketController = ElasticsearchRocketController;
//# sourceMappingURL=elasticsearch.rocket.controller.js.map