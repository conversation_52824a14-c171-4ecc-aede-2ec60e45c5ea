import { ElasticsearchLaunchService } from '../services/elasticsearch.launch.service';
import { LaunchQueryDto } from '../dto/launch-query.dto';
import { LaunchCosparQueryDto } from '../dto/launch-cospar-query.dto';
import { LaunchSiteWikiQueryDto } from '../dto/launch-site-wiki-query.dto';
import { LaunchServiceProviderQueryDto } from '../dto/launch-service-provider-query.dto';
export declare class ElasticsearchLaunchController {
    private readonly launchService;
    private readonly logger;
    constructor(launchService: ElasticsearchLaunchService);
    searchLaunchInfo(query: LaunchQueryDto): Promise<any>;
    getRocketNames(): Promise<any>;
    getSiteNames(): Promise<any>;
    getProviders(): Promise<any>;
    searchLaunchByCosparId(query: LaunchCosparQueryDto): Promise<{
        success: boolean;
        data: any;
        timestamp: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        timestamp: string;
        data?: undefined;
    }>;
    getWikiSiteNames(): Promise<any>;
    getAllLaunchSites(): Promise<any>;
    searchLaunchSiteWiki(query: LaunchSiteWikiQueryDto): Promise<any>;
    searchServiceProviders(query: LaunchServiceProviderQueryDto): Promise<any>;
}
