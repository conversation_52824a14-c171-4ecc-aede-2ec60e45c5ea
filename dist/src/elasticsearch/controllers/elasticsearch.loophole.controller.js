"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ElasticsearchLoopholeController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElasticsearchLoopholeController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const elasticsearch_loophole_service_1 = require("../services/elasticsearch.loophole.service");
const loophole_query_dto_1 = require("../dto/loophole-query.dto");
let ElasticsearchLoopholeController = ElasticsearchLoopholeController_1 = class ElasticsearchLoopholeController {
    constructor(loopholeService) {
        this.loopholeService = loopholeService;
        this.logger = new common_1.Logger(ElasticsearchLoopholeController_1.name);
    }
    async searchLoopholeInfo(query) {
        this.logger.debug(`接收到漏洞信息查询请求: ${JSON.stringify(query)}`);
        const result = await this.loopholeService.searchLoopholeInfo(query);
        this.logger.debug(`查询结果: ${JSON.stringify(result)}`);
        return result;
    }
};
__decorate([
    (0, common_1.Post)('search'),
    (0, swagger_1.ApiOperation)({
        summary: '查询漏洞信息',
        description: '根据提供的查询条件搜索漏洞信息，支持CVE编号精确匹配和关键词模糊搜索'
    }),
    (0, swagger_1.ApiBody)({
        type: loophole_query_dto_1.LoopholeQueryDto,
        examples: {
            byCveId: {
                summary: '使用CVE编号查询',
                value: {
                    _id: "CVE-2024-1234"
                }
            },
            byKeywords: {
                summary: '使用关键词查询',
                value: {
                    keywords: "Cobham Sailor"
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        schema: {
            properties: {
                total: { type: 'number', example: 100 },
                hits: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            _id: { type: 'string', example: 'CVE-2024-1234' },
                            cve_id: { type: 'string', example: 'CVE-2024-1234' },
                            descriptions: { type: 'string', example: 'A vulnerability in Cobham Sailor 900 VSAT systems allows an attacker to gain unauthorized access.' },
                            affected: { type: 'string', example: 'Cobham Sailor 900 VSAT 1.0, 1.1' },
                            severity: { type: 'string', example: '高' },
                            published_date: { type: 'string', format: 'date-time', example: '2024-01-15T10:30:00Z' },
                            patch_status: { type: 'string', example: '已修复' },
                            patch_version: { type: 'string', example: '1.2.0' },
                            references: { type: 'array', items: { type: 'string' }, example: ['https://nvd.nist.gov/vuln/detail/CVE-2024-1234'] },
                            mitigation: { type: 'string', example: '升级到1.2.0版本以修复此漏洞' }
                        }
                    }
                }
            }
        }
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [loophole_query_dto_1.LoopholeQueryDto]),
    __metadata("design:returntype", Promise)
], ElasticsearchLoopholeController.prototype, "searchLoopholeInfo", null);
ElasticsearchLoopholeController = ElasticsearchLoopholeController_1 = __decorate([
    (0, common_1.Controller)('loophole'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiTags)('漏洞信息'),
    __metadata("design:paramtypes", [elasticsearch_loophole_service_1.ElasticsearchLoopholeService])
], ElasticsearchLoopholeController);
exports.ElasticsearchLoopholeController = ElasticsearchLoopholeController;
//# sourceMappingURL=elasticsearch.loophole.controller.js.map