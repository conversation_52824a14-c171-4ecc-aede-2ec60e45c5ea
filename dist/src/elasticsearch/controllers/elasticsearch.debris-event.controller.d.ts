import { ElasticsearchDebrisEventService } from '../services/elasticsearch.debris-event.service';
import { DebrisEventQueryDto } from '../dto/debris-event-query.dto';
export declare class ElasticsearchDebrisEventController {
    private readonly debrisEventService;
    private readonly logger;
    constructor(debrisEventService: ElasticsearchDebrisEventService);
    searchDebrisEvents(query: DebrisEventQueryDto): Promise<any>;
}
