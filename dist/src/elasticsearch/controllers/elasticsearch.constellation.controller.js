"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ElasticsearchConstellationController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElasticsearchConstellationController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const elasticsearch_constellation_service_1 = require("../services/elasticsearch.constellation.service");
const constellation_types_1 = require("../types/constellation.types");
let ElasticsearchConstellationController = ElasticsearchConstellationController_1 = class ElasticsearchConstellationController {
    constructor(constellationService) {
        this.constellationService = constellationService;
        this.logger = new common_1.Logger(ElasticsearchConstellationController_1.name);
    }
    async searchConstellationInfo(query) {
        this.logger.debug(`接收到星座信息查询请求: ${JSON.stringify(query)}`);
        const result = await this.constellationService.searchConstellationInfo(query);
        this.logger.debug(`查询结果: ${JSON.stringify(result)}`);
        return result;
    }
    async getConstellationNames() {
        this.logger.debug('接收到获取星座名称列表请求');
        const result = await this.constellationService.getConstellationNames();
        this.logger.debug(`获取到 ${result.constellationNames.length} 个星座名称`);
        return result;
    }
    async getConstellationOrganizations() {
        this.logger.debug('接收到获取星座组织列表请求');
        const result = await this.constellationService.getConstellationOrganizations();
        this.logger.debug(`获取到 ${result.organizations.length} 个组织`);
        return result;
    }
    async getConstellationPurposes() {
        this.logger.debug('接收到获取星座用途列表请求');
        const result = await this.constellationService.getConstellationPurposes();
        this.logger.debug(`获取到 ${result.purposes.length} 个用途`);
        return result;
    }
};
__decorate([
    (0, common_1.Post)('search'),
    (0, swagger_1.ApiOperation)({
        summary: '查询星座信息',
        description: `根据提供的查询条件搜索星座信息，支持关键词、目标名称、目标ID、国家、组织和用途等条件。
    
CURL示例:
\`\`\`bash
# 完整查询示例
curl -X POST http://localhost:3001/constellation/search \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  -d '{
    "page": 1,
    "limit": 10,
    "keyword": "Starlink",
    "targetName": "Starlink",
    "targetId": "STARLINK-1234",
    "country": "USA",
    "organization": "SpaceX",
    "purpose": "互联网"
  }'

# 使用关键词查询
curl -X POST http://localhost:3001/constellation/search \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  -d '{
    "page": 1,
    "limit": 10,
    "keyword": "Starlink"
  }'
\`\`\`
    `
    }),
    (0, swagger_1.ApiBody)({
        type: constellation_types_1.ConstellationQueryDto,
        examples: {
            complete: {
                summary: '完整的查询示例',
                value: {
                    page: 1,
                    limit: 10,
                    keyword: "Starlink",
                    targetName: "Starlink",
                    targetId: "STARLINK-1234",
                    country: "USA",
                    organization: "SpaceX",
                    purpose: "互联网"
                }
            },
            byKeyword: {
                summary: '使用关键词查询',
                value: {
                    page: 1,
                    limit: 10,
                    keyword: "Starlink"
                }
            },
            byTargetName: {
                summary: '使用目标名称查询',
                value: {
                    page: 1,
                    limit: 10,
                    targetName: "OneWeb"
                }
            },
            byOrganization: {
                summary: '使用组织名称查询',
                value: {
                    page: 1,
                    limit: 10,
                    organization: "SpaceX"
                }
            },
            byPurpose: {
                summary: '使用用途查询',
                value: {
                    page: 1,
                    limit: 10,
                    purpose: "通信"
                }
            },
            byCountry: {
                summary: '使用国家查询',
                value: {
                    page: 1,
                    limit: 10,
                    country: "中国"
                }
            },
            paginationExample: {
                summary: '分页查询示例',
                value: {
                    page: 2,
                    limit: 20
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        type: constellation_types_1.ConstellationSearchResponseDto
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [constellation_types_1.ConstellationQueryDto]),
    __metadata("design:returntype", Promise)
], ElasticsearchConstellationController.prototype, "searchConstellationInfo", null);
__decorate([
    (0, common_1.Get)('names'),
    (0, swagger_1.ApiTags)('星座信息'),
    (0, swagger_1.ApiOperation)({
        summary: '获取星座名称列表',
        description: '获取系统中所有星座的名称列表，用于下拉选择等场景'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        type: constellation_types_1.ConstellationNamesResponseDto
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ElasticsearchConstellationController.prototype, "getConstellationNames", null);
__decorate([
    (0, common_1.Get)('organizations'),
    (0, swagger_1.ApiTags)('星座信息'),
    (0, swagger_1.ApiOperation)({
        summary: '获取星座组织列表',
        description: '获取系统中所有星座相关的组织列表，用于下拉选择等场景'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        type: constellation_types_1.ConstellationOrganizationsResponseDto
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ElasticsearchConstellationController.prototype, "getConstellationOrganizations", null);
__decorate([
    (0, common_1.Get)('purposes'),
    (0, swagger_1.ApiTags)('星座信息'),
    (0, swagger_1.ApiOperation)({
        summary: '获取星座用途列表',
        description: '获取系统中所有星座的用途列表，用于下拉选择等场景'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        type: constellation_types_1.ConstellationPurposesResponseDto
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ElasticsearchConstellationController.prototype, "getConstellationPurposes", null);
ElasticsearchConstellationController = ElasticsearchConstellationController_1 = __decorate([
    (0, common_1.Controller)('constellation'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiTags)('星座信息'),
    __metadata("design:paramtypes", [elasticsearch_constellation_service_1.ElasticsearchConstellationService])
], ElasticsearchConstellationController);
exports.ElasticsearchConstellationController = ElasticsearchConstellationController;
//# sourceMappingURL=elasticsearch.constellation.controller.js.map