"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ElasticsearchCorrelationController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElasticsearchCorrelationController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const elasticsearch_correlation_service_1 = require("../services/elasticsearch.correlation.service");
const debris_event_correlation_dto_1 = require("../dto/debris-event-correlation.dto");
const correlation_response_dto_1 = require("../dto/correlation-response.dto");
let ElasticsearchCorrelationController = ElasticsearchCorrelationController_1 = class ElasticsearchCorrelationController {
    constructor(correlationService) {
        this.correlationService = correlationService;
        this.logger = new common_1.Logger(ElasticsearchCorrelationController_1.name);
    }
    async findRelatedEvents(query) {
        this.logger.debug(`接收到碎片关联事件查询请求: ${JSON.stringify(query)}`);
        const result = await this.correlationService.findRelatedEvents(query);
        this.logger.debug(`查询结果: ${JSON.stringify(result)}`);
        return result;
    }
    async findRelatedDebris(query) {
        this.logger.debug(`接收到事件关联碎片查询请求: ${JSON.stringify(query)}`);
        const result = await this.correlationService.findRelatedDebris(query);
        this.logger.debug(`查询结果: ${JSON.stringify(result)}`);
        return result;
    }
};
__decorate([
    (0, common_1.Post)('debris-to-event'),
    (0, swagger_1.ApiTags)('碎片信息'),
    (0, swagger_1.ApiOperation)({
        summary: '查询碎片关联的事件',
        description: `根据碎片信息查询可能关联的碎片事件，并计算置信度。

关联逻辑说明：
1. COSPAR ID匹配（权重70%）：
   - **COSPAR ID匹配是关联的必要条件，如果COSPAR ID不匹配，则不会出现在结果列表中**
   - 完全匹配得分1.0
   - 年份匹配得分0.5
   - 序号部分（三位数字）匹配得分0.4
   - 字母部分首字母匹配得分0.1
   - COSPAR ID是关联的主要依据，匹配度越高，关联置信度越高

2. 时间匹配（权重15%）：
   - 如果首次纪元时间在事件时间之后且时间差在5年内，得分1.0
   - 如果时间差在5-10年内，按比例递减得分
   - 如果未提供首次纪元时间，默认得分0.5

3. 名称/类型匹配（权重15%）：
   - 碎片类型为"Payload Fragmentation Debris"得分0.8
   - 碎片类型包含"Debris"得分0.5
   - 其他类型得分0.2
   - 如果未提供名称，使用默认得分

4. 综合置信度计算：
   - 各维度得分按权重加权求和
   - 只返回置信度大于等于min_confidence的结果
   - 结果按置信度降序排序
   
注意：即使综合置信度高于min_confidence，如果COSPAR ID不匹配（得分为0），也不会出现在结果列表中。`
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        type: correlation_response_dto_1.CorrelationResponseDto
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [debris_event_correlation_dto_1.DebrisToEventQueryDto]),
    __metadata("design:returntype", Promise)
], ElasticsearchCorrelationController.prototype, "findRelatedEvents", null);
__decorate([
    (0, common_1.Post)('event-to-debris'),
    (0, swagger_1.ApiTags)('碎片信息'),
    (0, swagger_1.ApiOperation)({
        summary: '查询事件关联的碎片',
        description: '根据事件信息查询可能关联的碎片，并计算置信度'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        type: correlation_response_dto_1.EventCorrelationResultDto
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [debris_event_correlation_dto_1.EventToDebrisQueryDto]),
    __metadata("design:returntype", Promise)
], ElasticsearchCorrelationController.prototype, "findRelatedDebris", null);
ElasticsearchCorrelationController = ElasticsearchCorrelationController_1 = __decorate([
    (0, common_1.Controller)('correlation'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiTags)('碎片信息'),
    __metadata("design:paramtypes", [elasticsearch_correlation_service_1.ElasticsearchCorrelationService])
], ElasticsearchCorrelationController);
exports.ElasticsearchCorrelationController = ElasticsearchCorrelationController;
//# sourceMappingURL=elasticsearch.correlation.controller.js.map