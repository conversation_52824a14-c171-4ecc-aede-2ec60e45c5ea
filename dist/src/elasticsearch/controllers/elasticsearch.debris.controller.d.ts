import { ElasticsearchDebrisService } from '../services/elasticsearch.debris.service';
import { DebrisQueryDto } from '../dto/debris-query.dto';
import { DebrisSearchResponse } from '../interfaces/debris-search-response.interface';
export declare class ElasticsearchDebrisController {
    private readonly debrisService;
    private readonly logger;
    constructor(debrisService: ElasticsearchDebrisService);
    searchDebris(query: DebrisQueryDto): Promise<DebrisSearchResponse>;
    getDebrisNames(): Promise<string[]>;
    getDebrisObjectClasses(): Promise<string[]>;
    getDebrisMissions(): Promise<string[]>;
    getDebrisCountries(): Promise<string[]>;
    debugQueryDto(): {
        example: {
            page: number;
            limit: number;
            cospar_id: {
                matchType: string;
                value: string;
            };
            keyword: string;
            country: {
                matchType: string;
                value: string;
            };
            object_class: {
                matchType: string;
                value: string;
            };
            apogee_km_range: {
                min: number;
                max: number;
            };
            perigee_km_range: {
                min: number;
                max: number;
            };
            sort: {
                field: string;
                order: string;
            };
        };
    };
}
