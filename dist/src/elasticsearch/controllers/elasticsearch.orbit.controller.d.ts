import { ElasticsearchOrbitService } from '../services/elasticsearch.orbit.service';
import { OrbitQueryDto } from '../dto/orbit-query.dto';
import { BulkNoradIdsQueryDto } from '../dto/bulk-norad-ids-query.dto';
import { TleQueryConfig } from '../../../config/tle-query.config';
export declare class ElasticsearchOrbitController {
    private readonly orbitService;
    private readonly logger;
    constructor(orbitService: ElasticsearchOrbitService);
    searchOrbitInfo(query: OrbitQueryDto): Promise<any>;
    getBulkSatelliteTle(query: BulkNoradIdsQueryDto): Promise<any>;
    getAllSatelliteTle(sampleMode?: boolean): Promise<any>;
    getTleQueryConfig(): Promise<TleQueryConfig>;
    updateTleQueryConfig(newConfig: Partial<TleQueryConfig>): Promise<any>;
}
