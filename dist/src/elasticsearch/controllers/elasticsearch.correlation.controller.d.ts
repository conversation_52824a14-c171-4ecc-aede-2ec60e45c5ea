import { ElasticsearchCorrelationService } from '../services/elasticsearch.correlation.service';
import { DebrisToEventQueryDto, EventToDebrisQueryDto } from '../dto/debris-event-correlation.dto';
import { CorrelationResponseDto, EventCorrelationResultDto } from '../dto/correlation-response.dto';
export declare class ElasticsearchCorrelationController {
    private readonly correlationService;
    private readonly logger;
    constructor(correlationService: ElasticsearchCorrelationService);
    findRelatedEvents(query: DebrisToEventQueryDto): Promise<CorrelationResponseDto>;
    findRelatedDebris(query: EventToDebrisQueryDto): Promise<EventCorrelationResultDto>;
}
