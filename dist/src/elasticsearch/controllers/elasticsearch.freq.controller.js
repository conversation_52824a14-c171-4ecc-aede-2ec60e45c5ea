"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ElasticsearchFreqController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElasticsearchFreqController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const elasticsearch_freq_service_1 = require("../services/elasticsearch.freq.service");
const freq_query_dto_1 = require("../dto/freq-query.dto");
let ElasticsearchFreqController = ElasticsearchFreqController_1 = class ElasticsearchFreqController {
    constructor(freqService) {
        this.freqService = freqService;
        this.logger = new common_1.Logger(ElasticsearchFreqController_1.name);
    }
    async searchFrequencies(query) {
        this.logger.debug(`接收到频率信息查询请求: ${JSON.stringify(query)}`);
        const result = await this.freqService.searchFrequencies(query);
        this.logger.debug(`查询结果: ${JSON.stringify(result)}`);
        return result;
    }
};
__decorate([
    (0, common_1.Post)('search'),
    (0, swagger_1.ApiOperation)({
        summary: '查询频率信息',
        description: '根据提供的查询条件搜索频率信息，支持多种过滤条件和排序选项'
    }),
    (0, swagger_1.ApiBody)({
        type: freq_query_dto_1.FreqQueryDto,
        examples: {
            byFrequency: {
                summary: '使用频率范围查询',
                value: {
                    page: 1,
                    limit: 10,
                    min_frequency: 100,
                    max_frequency: 200
                }
            },
            byPurpose: {
                summary: '使用用途查询',
                value: {
                    page: 1,
                    limit: 10,
                    purpose: "通信"
                }
            },
            byCountry: {
                summary: '使用国家查询',
                value: {
                    page: 1,
                    limit: 10,
                    country: "中国"
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功'
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [freq_query_dto_1.FreqQueryDto]),
    __metadata("design:returntype", Promise)
], ElasticsearchFreqController.prototype, "searchFrequencies", null);
ElasticsearchFreqController = ElasticsearchFreqController_1 = __decorate([
    (0, common_1.Controller)('freq'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiTags)('频率数据'),
    __metadata("design:paramtypes", [elasticsearch_freq_service_1.ElasticsearchFreqService])
], ElasticsearchFreqController);
exports.ElasticsearchFreqController = ElasticsearchFreqController;
//# sourceMappingURL=elasticsearch.freq.controller.js.map