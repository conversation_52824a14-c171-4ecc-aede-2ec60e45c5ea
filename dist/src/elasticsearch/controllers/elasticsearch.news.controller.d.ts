import { ElasticsearchNewsService } from '../services/elasticsearch.news.service';
import { HotThemesResponse, NewsListResult, ThemeExtractionResponse, TranslationResponse } from '../types/news.types';
import { ExtractThemesDto, HotThemesDto, TranslateNewsDto } from '../dto/news.dto';
import { NewsListQueryDto } from '../dto/news-list-query.dto';
export declare class ElasticsearchNewsController {
    private readonly newsService;
    private readonly logger;
    constructor(newsService: ElasticsearchNewsService);
    getNewsIndices(): Promise<{
        success: boolean;
        data: string[];
        timestamp: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        timestamp: string;
        data?: undefined;
    }>;
    getTranslationStatus(): Promise<{
        success: boolean;
        data: any;
        timestamp: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        timestamp: string;
        data?: undefined;
    }>;
    translateNews(params: TranslateNewsDto): Promise<TranslationResponse>;
    getAPIStats(): Promise<{
        success: boolean;
        data: {
            callCount: number;
            totalTime: number;
            averageTime: number;
            cacheSize: number;
        };
        timestamp: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        timestamp: string;
        data?: undefined;
    }>;
    getFailureStats(): Promise<{
        success: boolean;
        data: {
            failedDocuments: import("../types/news.types").FailedDocument[];
            totalAttempts: number;
            successfulProcessing: number;
            totalFailures: number;
            successRate: number;
            failures: {
                contentFilter: {
                    count: number;
                    percentage: string;
                };
                timeout: {
                    count: number;
                    percentage: string;
                };
                rateLimit: {
                    count: number;
                    percentage: string;
                };
                networkError: {
                    count: number;
                    percentage: string;
                };
                other: {
                    count: number;
                    percentage: string;
                };
            };
            consecutiveFailures: number;
            recommendations: string[];
        };
        timestamp: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        timestamp: string;
        data?: undefined;
    }>;
    resetFailureStats(): Promise<{
        success: boolean;
        message: string;
        timestamp: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        timestamp: string;
        message?: undefined;
    }>;
    getFailedTranslations(): Promise<{
        success: boolean;
        data: {
            count: number;
            documents: import("../types/news.types").FailedDocument[];
        };
        timestamp: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        timestamp: string;
        data?: undefined;
    }>;
    retryFailedTranslations(params: {
        maxRetries?: number;
        specificDocuments?: {
            index: string;
            id: string;
        }[];
    }): Promise<{
        success: boolean;
        message: string;
        count: number;
        timestamp: string;
        statistics?: undefined;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        statistics: import("../types/news.types").TranslationStatistics;
        timestamp: string;
        count?: undefined;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        timestamp: string;
        count?: undefined;
        statistics?: undefined;
    }>;
    extractThemes(params: ExtractThemesDto): Promise<ThemeExtractionResponse>;
    getNewsList(query: NewsListQueryDto): Promise<{
        success: boolean;
        data?: NewsListResult;
        error?: string;
        timestamp: string;
    }>;
    getHotThemes(params: HotThemesDto): Promise<HotThemesResponse>;
}
