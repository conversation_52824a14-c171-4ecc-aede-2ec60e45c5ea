"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ElasticsearchDebrisEventController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElasticsearchDebrisEventController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const elasticsearch_debris_event_service_1 = require("../services/elasticsearch.debris-event.service");
const debris_event_query_dto_1 = require("../dto/debris-event-query.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let ElasticsearchDebrisEventController = ElasticsearchDebrisEventController_1 = class ElasticsearchDebrisEventController {
    constructor(debrisEventService) {
        this.debrisEventService = debrisEventService;
        this.logger = new common_1.Logger(ElasticsearchDebrisEventController_1.name);
    }
    async searchDebrisEvents(query) {
        this.logger.debug(`接收到碎片事件信息查询请求: ${JSON.stringify(query)}`);
        const result = await this.debrisEventService.searchDebrisEvents(query);
        this.logger.debug(`查询结果: ${JSON.stringify(result)}`);
        return result;
    }
};
__decorate([
    (0, common_1.Post)('search'),
    (0, swagger_1.ApiOperation)({
        summary: '查询碎片事件信息',
        description: '根据提供的查询条件搜索碎片事件信息，支持多种过滤条件和排序选项'
    }),
    (0, swagger_1.ApiBody)({
        type: debris_event_query_dto_1.DebrisEventQueryDto,
        examples: {
            byEventId: {
                summary: '使用事件ID查询',
                value: {
                    page: 1,
                    limit: 10,
                    event_id: "EVENT-001"
                }
            },
            byDate: {
                summary: '使用日期范围查询',
                value: {
                    page: 1,
                    limit: 10,
                    start_date: "2023-01-01",
                    end_date: "2023-12-31"
                }
            },
            byType: {
                summary: '使用事件类型查询',
                value: {
                    page: 1,
                    limit: 10,
                    event_type: "碰撞"
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        schema: {
            properties: {
                total: { type: 'number', example: 100 },
                page: { type: 'number', example: 1 },
                size: { type: 'number', example: 10 },
                items: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            event_id: { type: 'string', example: 'EVENT-001' },
                            event_type: { type: 'string', example: '碰撞' },
                            event_date: { type: 'string', format: 'date-time', example: '2023-05-15T10:30:00Z' },
                            description: { type: 'string', example: '两颗碎片发生近距离接触' },
                            objects_involved: { type: 'array', items: { type: 'string' }, example: ['DEBRIS-001', 'DEBRIS-002'] },
                            severity: { type: 'string', example: '高' }
                        }
                    }
                }
            }
        }
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [debris_event_query_dto_1.DebrisEventQueryDto]),
    __metadata("design:returntype", Promise)
], ElasticsearchDebrisEventController.prototype, "searchDebrisEvents", null);
ElasticsearchDebrisEventController = ElasticsearchDebrisEventController_1 = __decorate([
    (0, swagger_1.ApiTags)('碎片信息'),
    (0, common_1.Controller)('api/debris-events'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [elasticsearch_debris_event_service_1.ElasticsearchDebrisEventService])
], ElasticsearchDebrisEventController);
exports.ElasticsearchDebrisEventController = ElasticsearchDebrisEventController;
//# sourceMappingURL=elasticsearch.debris-event.controller.js.map