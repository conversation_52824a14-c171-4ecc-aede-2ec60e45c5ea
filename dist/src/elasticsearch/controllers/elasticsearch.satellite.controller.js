"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ElasticsearchSatelliteController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElasticsearchSatelliteController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const elasticsearch_satellite_service_1 = require("../services/elasticsearch.satellite.service");
const satellite_query_dto_1 = require("../dto/satellite-query.dto");
let ElasticsearchSatelliteController = ElasticsearchSatelliteController_1 = class ElasticsearchSatelliteController {
    constructor(satelliteService) {
        this.satelliteService = satelliteService;
        this.logger = new common_1.Logger(ElasticsearchSatelliteController_1.name);
    }
    async searchSatelliteInfo(query) {
        this.logger.debug(`接收到卫星信息查询请求: ${JSON.stringify(query)}`);
        const result = await this.satelliteService.searchSatelliteInfo(query);
        if (!result) {
            this.logger.warn(`查询结果为空，返回默认空结果`);
            return {
                total: 0,
                page: query.page || 1,
                limit: query.limit || 10,
                hits: []
            };
        }
        if (result.hits && result.hits.length > 0) {
            this.logger.debug(`查询结果包含相似度信息: ${result.hits.some(hit => hit._similarity_info)}`);
            if (result.hits[0]._similarity_info) {
                this.logger.debug(`第一个结果的相似度信息: ${JSON.stringify(result.hits[0]._similarity_info)}`);
            }
        }
        this.logger.debug(`查询结果: 共${result.total}条记录，当前第${result.page}页，每页${result.limit}条`);
        return result;
    }
    async getSatelliteNames() {
        this.logger.debug('接收到获取卫星名称集合请求');
        const result = await this.satelliteService.getSatelliteNames();
        this.logger.debug(`获取到 ${result.length} 个卫星名称`);
        return result;
    }
    async getSatelliteStatuses() {
        this.logger.debug('接收到获取卫星运行状态集合请求');
        const result = await this.satelliteService.getSatelliteStatuses();
        this.logger.debug(`获取到 ${result.length} 个卫星运行状态`);
        return result;
    }
    async testDirectQuery(name) {
        this.logger.debug(`接收到直接查询测试请求: ${name}`);
        const result = await this.satelliteService.testDirectQuery(name);
        this.logger.debug(`测试结果: ${JSON.stringify(result)}`);
        return result;
    }
};
__decorate([
    (0, common_1.Post)('search'),
    (0, swagger_1.ApiOperation)({
        summary: '搜索卫星信息',
        description: `
    根据提供的查询条件搜索卫星信息。支持多种查询条件组合，包括关键词搜索、精确匹配和模糊匹配。
    
    ## 查询参数说明
    - page: 页码，默认为1
    - limit: 每页数量，默认为10
    - keyword: 关键词搜索，会匹配卫星名称、别名等字段
    - similarity_threshold: 相似度阈值（0-1之间），只返回相似度高于该阈值的结果，默认为0.6
    - 其他字段支持精确匹配和模糊匹配，可以通过FieldMatchConfig配置
    
    ## 相似度计算
    系统会为每个查询条件计算相似度分数，并在结果中返回。相似度分数范围为0-1，1表示完全匹配。
    - 对于关键词查询，会计算关键词与卫星名称、别名等字段的相似度
    - 对于字段匹配，会计算查询值与字段值的相似度
    - 结果中的_similarity_info字段包含了详细的相似度信息
    
    ## 结果排序
    默认按相似度分数降序排序，也可以通过sort参数指定排序字段和方向。
    
    ## 结果聚合
    来自不同数据源的相同卫星信息会被聚合为一条记录，并在_sources字段中标明数据来源。
    `
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        schema: {
            type: 'object',
            properties: {
                total: { type: 'number', description: '总结果数' },
                page: { type: 'number', description: '当前页码' },
                limit: { type: 'number', description: '每页数量' },
                hits: {
                    type: 'array',
                    description: '查询结果',
                    items: {
                        type: 'object',
                        properties: {
                            satellite_name: { type: 'array', description: '卫星名称' },
                            alternative_name: { type: 'array', description: '卫星别名' },
                            cospar_id: { type: 'array', description: 'COSPAR ID' },
                            country_of_registry: { type: 'array', description: '注册国家' },
                            owner: { type: 'array', description: '所有者' },
                            status: { type: 'array', description: '状态' },
                            norad_id: { type: 'array', description: 'NORAD ID' },
                            launch_info: { type: 'array', description: '发射信息' },
                            orbit_info: { type: 'array', description: '轨道信息' },
                            update_time: { type: 'array', description: '更新时间' },
                            _sources: { type: 'array', description: '数据来源' },
                            _score: { type: 'number', description: '匹配分数' },
                            _similarity_info: {
                                type: 'object',
                                description: '相似度信息',
                                properties: {
                                    total_score: { type: 'number', description: '总分数' },
                                    normalized_score: { type: 'number', description: '标准化分数' },
                                    index: { type: 'string', description: '索引名称' },
                                    fields: {
                                        type: 'object',
                                        description: '字段相似度',
                                        additionalProperties: { type: 'number' }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [satellite_query_dto_1.SatelliteQueryDto]),
    __metadata("design:returntype", Promise)
], ElasticsearchSatelliteController.prototype, "searchSatelliteInfo", null);
__decorate([
    (0, common_1.Get)('names'),
    (0, swagger_1.ApiOperation)({
        summary: '获取卫星名称集合',
        description: `
    从所有卫星信息索引（satsinfo_gunter、satsinfo_n2yo、satsinfo_nanosats、satsinfo_satnogs、satsinfo_ucs）中
    提取卫星名称（satellite_name）和别名（alternative_name）字段的值，合并为一个集合并返回。
    
    ## 数据处理
    1. 从所有卫星信息索引中提取卫星名称和别名
    2. 过滤掉空值、"None"值、"null"值和"undefined"值
    3. 去重并排序
    
    ## 用途
    该API主要用于：
    1. 提供卫星名称的自动补全功能
    2. 展示系统中所有已知的卫星名称
    3. 辅助用户进行卫星信息查询
    `
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        schema: {
            type: 'array',
            items: {
                type: 'string'
            },
            example: [
                "AEHF-1",
                "AEHF-2",
                "AEHF-3",
                "AEHF-4",
                "AEHF-5",
                "AEHF-6",
                "AGILE",
                "AIM",
                "ALOS-2",
                "ALPHASAT"
            ]
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ElasticsearchSatelliteController.prototype, "getSatelliteNames", null);
__decorate([
    (0, common_1.Get)('statuses'),
    (0, swagger_1.ApiOperation)({
        summary: '获取卫星运行状态集合',
        description: `
    从所有卫星信息索引（satsinfo_gunter、satsinfo_n2yo、satsinfo_nanosats、satsinfo_satnogs、satsinfo_ucs）中
    提取status字段的值，合并为一个集合并返回。
    
    ## 数据处理
    1. 从所有卫星信息索引中提取卫星运行状态
    2. 过滤掉空值、"None"值、"null"值和"undefined"值
    3. 使用多级匹配策略处理状态字符串：
       - 先尝试完整匹配
       - 再尝试匹配第一个标点符号前的部分
       - 再尝试匹配前几个单词
       - 如果都没有匹配，使用第一个标点符号前的部分
    4. 添加中文翻译
    5. 去重并排序
    
    ## 用途
    该API主要用于：
    1. 提供卫星运行状态的筛选条件
    2. 展示系统中所有已知的卫星运行状态类型
    3. 辅助用户进行卫星信息查询
    `
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    en: {
                        type: 'string',
                        description: '英文状态'
                    },
                    zh: {
                        type: 'string',
                        description: '中文状态'
                    }
                }
            },
            example: [
                { "en": "ACTIVE", "zh": "活跃" },
                { "en": "ALIVE", "zh": "活跃" },
                { "en": "CANCELLED", "zh": "已取消" },
                { "en": "DEAD", "zh": "失效" },
                { "en": "DECAYED", "zh": "已衰减" },
                { "en": "DEPLOYMENT", "zh": "部署" },
                { "en": "DEPLOYMENT FAILURE", "zh": "部署失败" },
                { "en": "DEPLOYMENT PROHIBITED", "zh": "禁止部署" },
                { "en": "FUTURE", "zh": "计划中" },
                { "en": "IN ORBIT", "zh": "在轨道上" },
                { "en": "LAUNCH FAILURE", "zh": "发射失败" },
                { "en": "NO SIGNAL", "zh": "无信号" },
                { "en": "NOT LAUNCHED", "zh": "未发射" },
                { "en": "ON SPACECRAFT", "zh": "在航天器上" },
                { "en": "OPERATIONAL", "zh": "运行中" },
                { "en": "REENTRY", "zh": "再入大气层" }
            ]
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ElasticsearchSatelliteController.prototype, "getSatelliteStatuses", null);
__decorate([
    (0, common_1.Post)('test-direct-query'),
    (0, swagger_1.ApiOperation)({
        summary: '测试直接查询特定卫星名称',
        description: '用于测试直接查询特定卫星名称，返回查询结果'
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                name: {
                    type: 'string',
                    description: '卫星名称',
                    example: 'BGS ARPIT'
                }
            }
        }
    }),
    __param(0, (0, common_1.Body)('name')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ElasticsearchSatelliteController.prototype, "testDirectQuery", null);
ElasticsearchSatelliteController = ElasticsearchSatelliteController_1 = __decorate([
    (0, common_1.Controller)('satellite'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiTags)('卫星信息'),
    __metadata("design:paramtypes", [elasticsearch_satellite_service_1.ElasticsearchSatelliteService])
], ElasticsearchSatelliteController);
exports.ElasticsearchSatelliteController = ElasticsearchSatelliteController;
//# sourceMappingURL=elasticsearch.satellite.controller.js.map