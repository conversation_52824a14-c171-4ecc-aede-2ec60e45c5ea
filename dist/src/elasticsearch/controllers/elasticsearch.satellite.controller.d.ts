import { ElasticsearchSatelliteService } from '../services/elasticsearch.satellite.service';
import { SatelliteQueryDto } from '../dto/satellite-query.dto';
import { SatelliteSearchResponse } from '../types/satellite.types';
export declare class ElasticsearchSatelliteController {
    private readonly satelliteService;
    private readonly logger;
    constructor(satelliteService: ElasticsearchSatelliteService);
    searchSatelliteInfo(query: SatelliteQueryDto): Promise<SatelliteSearchResponse>;
    getSatelliteNames(): Promise<string[]>;
    getSatelliteStatuses(): Promise<Array<{
        en: string;
        zh: string;
    }>>;
    testDirectQuery(name: string): Promise<any>;
}
