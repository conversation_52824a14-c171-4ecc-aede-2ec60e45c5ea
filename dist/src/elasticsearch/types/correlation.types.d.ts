export interface Score {
    cosparMatch: number;
    timeScore: number;
    nameScore: number;
}
export interface CorrelationResult {
    event_id: string;
    confidence: number;
}
export interface EventCorrelationResult {
    debris_list: string[];
}
export interface ScoringWeights {
    cosparWeight: number;
    timeWeight: number;
    nameWeight: number;
}
export interface CorrelationConfig {
    timeWindowYears: number;
    minNameSimilarity: number;
    weights: ScoringWeights;
}
