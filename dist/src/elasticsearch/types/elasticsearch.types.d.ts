export interface EventDocument {
    piece: string;
    sdate: string;
    event_type: string;
    cataloguedFragments: number;
    name?: string;
    object_class?: string;
    source?: 'discos' | 'jonathan';
}
export interface DebrisDocument {
    cospar_id: string;
    launch_date: string;
    first_epoch: string;
    name: string;
    object_class: string;
}
export interface SearchHit<T> {
    _index: string;
    _id: string;
    _score: number;
    _source?: T;
}
export interface SearchResponse<T> {
    hits?: {
        total?: {
            value: number;
            relation: string;
        };
        max_score?: number;
        hits: Array<{
            _index: string;
            _id: string;
            _score?: number;
            _source?: T;
        }>;
    };
}
