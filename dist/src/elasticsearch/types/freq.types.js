"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FreqSearchResponse = exports.FreqDocument = void 0;
const swagger_1 = require("@nestjs/swagger");
class FreqDocument {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '卫星名称' }),
    __metadata("design:type", String)
], FreqDocument.prototype, "sat_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '卫星编号', required: false }),
    __metadata("design:type", Number)
], FreqDocument.prototype, "norad_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '国际卫星标识符', required: false }),
    __metadata("design:type", String)
], FreqDocument.prototype, "cospar_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '频率（MHz）' }),
    __metadata("design:type", Number)
], FreqDocument.prototype, "frequency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '带宽（MHz）' }),
    __metadata("design:type", Number)
], FreqDocument.prototype, "bandwidth", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '极化方式', required: false }),
    __metadata("design:type", String)
], FreqDocument.prototype, "polarization", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '业务类型', required: false }),
    __metadata("design:type", String)
], FreqDocument.prototype, "service", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '传输方向', required: false }),
    __metadata("design:type", String)
], FreqDocument.prototype, "direction", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '发射特性', required: false }),
    __metadata("design:type", String)
], FreqDocument.prototype, "emission", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '功率（dBW）', required: false }),
    __metadata("design:type", Number)
], FreqDocument.prototype, "power", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '波束名称', required: false }),
    __metadata("design:type", String)
], FreqDocument.prototype, "beam_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态', required: false }),
    __metadata("design:type", String)
], FreqDocument.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '发布日期', required: false }),
    __metadata("design:type", String)
], FreqDocument.prototype, "date_of_publication", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '备注', required: false }),
    __metadata("design:type", String)
], FreqDocument.prototype, "remarks", void 0);
exports.FreqDocument = FreqDocument;
class FreqSearchResponse {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总记录数' }),
    __metadata("design:type", Number)
], FreqSearchResponse.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '频率信息列表', type: [FreqDocument] }),
    __metadata("design:type", Array)
], FreqSearchResponse.prototype, "data", void 0);
exports.FreqSearchResponse = FreqSearchResponse;
//# sourceMappingURL=freq.types.js.map