"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConstellationPurposesResponseDto = exports.ConstellationOrganizationsResponseDto = exports.ConstellationNamesResponseDto = exports.ConstellationSourceDto = exports.ConstellationSearchResponseDto = exports.ConstellationHitDto = exports.ConstellationMatchDescriptionDto = exports.ConstellationMatchedFieldsDto = exports.ConstellationSatelliteDto = exports.ConstellationDataSource = exports.ConstellationQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class ConstellationQueryDto {
    constructor() {
        this.page = 1;
        this.limit = 10;
    }
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '查询关键词（在星座名称、公司名称等字段中进行模糊匹配）',
        required: false,
        example: 'Starlink'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ConstellationQueryDto.prototype, "keyword", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '目标名称（星座名称）',
        required: false,
        example: 'Starlink'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ConstellationQueryDto.prototype, "targetName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '目标编号',
        required: false,
        example: 'STARLINK-1234'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ConstellationQueryDto.prototype, "targetId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '所属国家（支持中英文，如：中国、USA、Russia等）',
        required: false,
        example: 'USA'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ConstellationQueryDto.prototype, "country", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '所属机构（如：SpaceX、OneWeb、中国航天科技集团等）',
        required: false,
        example: 'SpaceX'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ConstellationQueryDto.prototype, "organization", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '星座用途（支持中英文，如：通信/Communication、导航/Navigation、地球观测/Earth Observation、互联网/Internet等）',
        required: false,
        example: '互联网'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ConstellationQueryDto.prototype, "purpose", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '页码',
        required: false,
        default: 1,
        minimum: 1,
        example: 1
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], ConstellationQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '每页数量',
        required: false,
        default: 10,
        minimum: 1,
        example: 10
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], ConstellationQueryDto.prototype, "limit", void 0);
exports.ConstellationQueryDto = ConstellationQueryDto;
var ConstellationDataSource;
(function (ConstellationDataSource) {
    ConstellationDataSource["NEWSPACE"] = "constell_newspace";
    ConstellationDataSource["N2YO"] = "constell_n2yo";
})(ConstellationDataSource = exports.ConstellationDataSource || (exports.ConstellationDataSource = {}));
class ConstellationSatelliteDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'NORAD ID',
        example: '45123'
    }),
    __metadata("design:type", String)
], ConstellationSatelliteDto.prototype, "norad_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星名称',
        example: 'STARLINK-1234'
    }),
    __metadata("design:type", String)
], ConstellationSatelliteDto.prototype, "name", void 0);
exports.ConstellationSatelliteDto = ConstellationSatelliteDto;
class ConstellationMatchedFieldsDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '目标名称是否匹配',
        example: true
    }),
    __metadata("design:type", Boolean)
], ConstellationMatchedFieldsDto.prototype, "targetName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '目标ID是否匹配',
        example: false
    }),
    __metadata("design:type", Boolean)
], ConstellationMatchedFieldsDto.prototype, "targetId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '组织是否匹配',
        example: true
    }),
    __metadata("design:type", Boolean)
], ConstellationMatchedFieldsDto.prototype, "organization", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '国家是否匹配',
        example: false
    }),
    __metadata("design:type", Boolean)
], ConstellationMatchedFieldsDto.prototype, "country", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '用途是否匹配',
        example: false
    }),
    __metadata("design:type", Boolean)
], ConstellationMatchedFieldsDto.prototype, "purpose", void 0);
exports.ConstellationMatchedFieldsDto = ConstellationMatchedFieldsDto;
class ConstellationMatchDescriptionDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '字段名称',
        example: 'targetName'
    }),
    __metadata("design:type", String)
], ConstellationMatchDescriptionDto.prototype, "field", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '匹配级别',
        example: '完全匹配'
    }),
    __metadata("design:type", String)
], ConstellationMatchDescriptionDto.prototype, "matchLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '匹配分数',
        example: 1.0
    }),
    __metadata("design:type", Number)
], ConstellationMatchDescriptionDto.prototype, "score", void 0);
exports.ConstellationMatchDescriptionDto = ConstellationMatchDescriptionDto;
class ConstellationHitDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '星座ID',
        example: '123456'
    }),
    __metadata("design:type", String)
], ConstellationHitDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '星座名称',
        example: 'Starlink'
    }),
    __metadata("design:type", String)
], ConstellationHitDto.prototype, "constellation_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '所属公司',
        example: 'SpaceX'
    }),
    __metadata("design:type", String)
], ConstellationHitDto.prototype, "company", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星列表',
        type: 'array',
        isArray: true
    }),
    __metadata("design:type", Array)
], ConstellationHitDto.prototype, "satellites", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '匹配分数',
        example: 0.95
    }),
    __metadata("design:type", Number)
], ConstellationHitDto.prototype, "match_score", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '匹配字段',
        type: 'object',
        additionalProperties: false
    }),
    __metadata("design:type", ConstellationMatchedFieldsDto)
], ConstellationHitDto.prototype, "matched_fields", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '匹配字段描述',
        type: 'array',
        isArray: true
    }),
    __metadata("design:type", Array)
], ConstellationHitDto.prototype, "matched_fields_description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '数据源信息',
        type: 'object',
        additionalProperties: true
    }),
    __metadata("design:type", Object)
], ConstellationHitDto.prototype, "sources", void 0);
exports.ConstellationHitDto = ConstellationHitDto;
class ConstellationSearchResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '总记录数',
        example: 10
    }),
    __metadata("design:type", Number)
], ConstellationSearchResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '当前页码',
        example: 1
    }),
    __metadata("design:type", Number)
], ConstellationSearchResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '每页记录数',
        example: 10
    }),
    __metadata("design:type", Number)
], ConstellationSearchResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '查询结果列表',
        type: 'array',
        isArray: true
    }),
    __metadata("design:type", Array)
], ConstellationSearchResponseDto.prototype, "hits", void 0);
exports.ConstellationSearchResponseDto = ConstellationSearchResponseDto;
class ConstellationSourceDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '数据源类型',
        enum: ConstellationDataSource,
        example: 'constell_newspace'
    }),
    __metadata("design:type", String)
], ConstellationSourceDto.prototype, "source", void 0);
exports.ConstellationSourceDto = ConstellationSourceDto;
class ConstellationNamesResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '星座名称集合',
        type: [String],
        example: ['Starlink', 'OneWeb', 'Kuiper']
    }),
    __metadata("design:type", Array)
], ConstellationNamesResponseDto.prototype, "constellationNames", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '星座数量',
        type: Number,
        example: 3
    }),
    __metadata("design:type", Number)
], ConstellationNamesResponseDto.prototype, "count", void 0);
exports.ConstellationNamesResponseDto = ConstellationNamesResponseDto;
class ConstellationOrganizationsResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '星座所属机构集合',
        type: [String],
        example: ['SpaceX', 'OneWeb', 'Amazon', 'Boeing', 'Lockheed Martin']
    }),
    __metadata("design:type", Array)
], ConstellationOrganizationsResponseDto.prototype, "organizations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '机构数量',
        type: Number,
        example: 5
    }),
    __metadata("design:type", Number)
], ConstellationOrganizationsResponseDto.prototype, "count", void 0);
exports.ConstellationOrganizationsResponseDto = ConstellationOrganizationsResponseDto;
class ConstellationPurposesResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '星座用途集合',
        type: [String],
        example: ['通信', 'Communication', '导航', 'Navigation', '地球观测', 'Earth Observation']
    }),
    __metadata("design:type", Array)
], ConstellationPurposesResponseDto.prototype, "purposes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '用途数量',
        type: Number,
        example: 6
    }),
    __metadata("design:type", Number)
], ConstellationPurposesResponseDto.prototype, "count", void 0);
exports.ConstellationPurposesResponseDto = ConstellationPurposesResponseDto;
//# sourceMappingURL=constellation.types.js.map