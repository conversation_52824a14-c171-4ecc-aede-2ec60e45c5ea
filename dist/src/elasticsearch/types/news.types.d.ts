export declare enum EntityType {
    SATELLITES = "satellites",
    CONSTELLATIONS = "constellations",
    LAUNCH_SITES = "launch_sites",
    ROCKETS = "rockets",
    PROVIDERS = "providers"
}
export interface EntityRecognitionResult {
    satellites: string[];
    constellations: string[];
    launch_sites: string[];
    rockets: string[];
    providers: string[];
}
export interface NewsDocument {
    title: string;
    summary: string;
    content: string;
    title_cn?: string;
    summary_cn?: string;
    content_cn?: string;
    themes_cn?: string;
    entities?: EntityRecognitionResult;
    published_at?: string;
    source?: string;
    author?: string;
    url?: string;
    info_source?: string;
    content_fetched?: boolean;
    content_fetch_time?: string;
    content_fetch_success?: boolean;
    _id?: string;
    _index?: string;
    [key: string]: any;
}
export interface FailedDocument {
    index: string;
    id: string;
    reason: string;
    timestamp: Date;
    retryCount?: number;
    skipped?: boolean;
}
export interface TranslationStatistics {
    total: number;
    success: number;
    failed: number;
    skipped: number;
    indexCount: number;
    indexes: string[];
    startTime: Date;
    endTime?: Date;
    elapsedTime?: number;
    failedDocuments?: FailedDocument[];
    skippedDocuments?: FailedDocument[];
    themeExtraction?: {
        total: number;
        success: number;
        failed: number;
        skipped: number;
    };
}
export interface PaginationParams {
    page?: number;
    size?: number;
}
export interface SearchParams extends PaginationParams {
    keywords?: string;
    dateFrom?: string;
    dateTo?: string;
    source?: string;
    sortBy?: 'published_at' | 'score';
    sortOrder?: 'asc' | 'desc';
}
export interface NewsListResult {
    data: NewsDocument[];
    total: number;
    page: number;
    size: number;
    totalPages: number;
}
export declare enum LLMMode {
    DEFAULT = "default",
    HIGH_QUALITY = "high_quality",
    FAST = "fast"
}
export interface TranslateNewsParams {
    batchSize?: number;
    maxDocs?: number;
    forceRetranslate?: boolean;
    forceRefetchContent?: boolean;
    specificIndexes?: string[];
    llmMode?: LLMMode;
    customModel?: string;
    autoExtractThemes?: boolean;
    documentId?: string;
}
export interface ExtractThemesParams {
    batchSize?: number;
    maxDocs?: number;
    forceReextract?: boolean;
    specificIndexes?: string[];
    llmMode?: LLMMode;
    customModel?: string;
}
export interface TranslationResponse {
    success: boolean;
    message: string;
    statistics: TranslationStatistics;
    timestamp?: string;
}
export interface ThemeExtractionResponse {
    success: boolean;
    message: string;
    statistics: TranslationStatistics;
    timestamp?: string;
}
export interface HotThemesParams {
    topN?: number;
    minCount?: number;
    specificIndexes?: string[];
}
export interface HotTheme {
    theme: string;
    count: number;
}
export interface HotThemesResponse {
    success: boolean;
    message: string;
    data: {
        themes: HotTheme[];
        total: number;
        indexCount: number;
        indexes: string[];
        processedDocs: number;
    };
    timestamp?: string;
}
