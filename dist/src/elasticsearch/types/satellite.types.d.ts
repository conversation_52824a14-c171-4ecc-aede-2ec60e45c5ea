export interface SatelliteDocument {
    info_source: string;
    update_time: string;
    satellite_name: string;
    alternative_name: string;
    country_of_registry: string;
    owner: string;
    status: string;
    norad_id: number;
    source: string;
    orbit_info: {
        decay_date: string | null;
        deployed_date: string | null;
        period_minutes?: number;
        inclination_degrees?: number;
        apogee_km?: number;
        perigee_km?: number;
        eccentricity?: number;
        arg_of_pericenter?: number;
        mean_anomaly?: number;
        mean_motion?: number;
    };
    launch_info: {
        launch_date: string | null;
        launch_site?: string;
        launch_vehicle?: string;
    };
}
export interface SourcedValue {
    value: any;
    sources: string[];
}
export interface AggregatedSatelliteInfo {
    satellite_name: SourcedValue[];
    alternative_name: SourcedValue[];
    cospar_id: SourcedValue[];
    country_of_registry: SourcedValue[];
    owner: SourcedValue[];
    status: SourcedValue[];
    norad_id: SourcedValue[];
    launch_info: SourcedValue[];
    orbit_info: SourcedValue[];
    mass_kg?: SourcedValue[];
    power_watts?: SourcedValue[];
    lifetime_years?: SourcedValue[];
    contractor?: SourcedValue[];
    purpose?: SourcedValue[];
    detailed_purpose?: SourcedValue[];
    payload?: SourcedValue[];
    payload_description?: SourcedValue[];
    update_time: SourcedValue[];
    _sources: string[];
}
export interface SatelliteSearchResponse {
    total: number;
    page: number;
    limit: number;
    hits: (AggregatedSatelliteInfo & {
        _score?: number;
        _similarity_info?: {
            total_score: number;
            normalized_score: number;
            index?: string;
            indices?: string[];
            fields: Record<string, number>;
        };
    })[];
}
