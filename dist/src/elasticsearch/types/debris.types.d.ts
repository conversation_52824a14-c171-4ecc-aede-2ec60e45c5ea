export interface BaseDebrisDocument {
    debris_num: number;
    cospar_id: string;
    name: string;
    launch_date: string;
    launch_site: string | null;
    decay: string | null;
    first_epoch: string;
    object_class: string;
}
export interface SourcedValue {
    value: any;
    sources: string[];
}
export interface OrbitInfo {
    period_minutes: number;
    incl_degrees: number;
    apogee_km: number;
    perigee_km: number;
    'latest elset': string[];
}
export interface AttributesDetail {
    shape: string | null;
    depth: number | null;
    mass: number | null;
    xSectMax: number | null;
    xSectAvg: number | null;
    width: number | null;
    height: number | null;
}
export interface SpaceTrackDebrisDocument {
    info_source: string;
    debris_num: number;
    cospar_id: string;
    norad_id: number;
    name: string;
    country: string;
    launch_date: string;
    launch_site: string | null;
    rcs_size: string;
    decay: string | null;
    first_epoch: string;
    object_class: string;
    mission: string | null;
    on_orbit_cat_debris: number | null;
    cat_debris: number | null;
    orbit_info: OrbitInfo;
    attributes_detail: AttributesDetail | null;
}
export interface DiscosDebrisDocument extends SpaceTrackDebrisDocument {
    parent_object: string | null;
    fragmentation_source: string | null;
    fragmentation_date: string | null;
}
export interface AggregatedDebrisInfo {
    debris_num: SourcedValue[];
    cospar_id: SourcedValue[];
    norad_id: SourcedValue[];
    name: SourcedValue[];
    country: SourcedValue[];
    launch_date: SourcedValue[];
    launch_site: SourcedValue[];
    rcs_size: SourcedValue[];
    decay: SourcedValue[];
    first_epoch: SourcedValue[];
    object_class: SourcedValue[];
    mission: SourcedValue[];
    on_orbit_cat_debris: SourcedValue[];
    cat_debris: SourcedValue[];
    orbit_info?: OrbitInfo;
    attributes_detail?: AttributesDetail;
    parent_object: SourcedValue[];
    fragmentation_source: SourcedValue[];
    fragmentation_date: SourcedValue[];
}
export interface DebrisSearchResponse {
    total: number;
    page: number;
    limit: number;
    hits: AggregatedDebrisInfo[];
}
