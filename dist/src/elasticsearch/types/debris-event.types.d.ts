export interface EventDocument {
    piece: string;
    sdate: string;
    event_type: string;
    name: string;
    cataloguedFragments?: number;
    object_class?: string;
    description?: string;
}
export interface DebrisEventSearchResponse {
    total: number;
    hits: {
        _id: string;
        source: string;
        confidence: number;
        piece: string;
        sdate: string;
        event_type: string;
        name: string;
        cataloguedFragments?: number;
        object_class?: string;
    }[];
}
