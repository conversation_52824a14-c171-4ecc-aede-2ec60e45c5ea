export declare class ConstellationQueryDto {
    keyword?: string;
    targetName?: string;
    targetId?: string;
    country?: string;
    organization?: string;
    purpose?: string;
    page?: number;
    limit?: number;
}
export declare enum ConstellationDataSource {
    NEWSPACE = "constell_newspace",
    N2YO = "constell_n2yo"
}
export interface ConstellationSearchResponse {
    total: number;
    page: number;
    limit: number;
    hits: Array<{
        id: string;
        constellation_name: string;
        company: string;
        satellites: Array<{
            norad_id: string;
            name: string;
        }>;
        match_score: number;
        matched_fields?: {
            targetName: boolean;
            targetId?: boolean;
            organization: boolean;
            country: boolean;
            purpose: boolean;
        };
        matched_fields_description?: Array<{
            field: string;
            matchLevel: string;
            score: number;
        }>;
        sources: {
            [key: string]: {
                source: ConstellationDataSource;
                original_data?: any;
            };
        };
    }>;
}
export declare class ConstellationSatelliteDto {
    norad_id: string;
    name: string;
}
export declare class ConstellationMatchedFieldsDto {
    targetName: boolean;
    targetId?: boolean;
    organization: boolean;
    country: boolean;
    purpose: boolean;
}
export declare class ConstellationMatchDescriptionDto {
    field: string;
    matchLevel: string;
    score: number;
}
export declare class ConstellationHitDto {
    id: string;
    constellation_name: string;
    company: string;
    satellites: ConstellationSatelliteDto[];
    match_score: number;
    matched_fields?: ConstellationMatchedFieldsDto;
    matched_fields_description?: ConstellationMatchDescriptionDto[];
    sources: Record<string, ConstellationSourceDto>;
}
export declare class ConstellationSearchResponseDto {
    total: number;
    page: number;
    limit: number;
    hits: ConstellationHitDto[];
}
export declare class ConstellationSourceDto {
    source: ConstellationDataSource;
}
export declare class ConstellationNamesResponseDto {
    constellationNames: string[];
    count: number;
}
export declare class ConstellationOrganizationsResponseDto {
    organizations: string[];
    count: number;
}
export declare class ConstellationPurposesResponseDto {
    purposes: string[];
    count: number;
}
