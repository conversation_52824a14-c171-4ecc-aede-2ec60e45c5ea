export interface DebrisEventSearchResponse {
    total: number;
    page: number;
    size: number;
    items: DebrisEventItem[];
}
export interface DebrisEventItem {
    _id: string;
    event_id?: string;
    event_type?: string;
    event_time?: string;
    description?: string;
    debris_ids?: string[];
    satellite_ids?: string[];
    location?: {
        lat?: number;
        lon?: number;
        alt?: number;
    };
    [key: string]: any;
}
