export interface DebrisSearchResponse {
    total: number;
    page: number;
    size: number;
    items: DebrisItemWithMatch[];
}
export interface DebrisItemWithMatch extends DebrisItem {
    match_score: number;
    source_index?: string;
    source_fields?: Record<string, string>;
    field_values?: Record<string, Array<{
        value: any;
        source: string;
    }>>;
    matched_fields?: {
        keyword?: boolean;
        norad_id?: boolean;
        cospar_id?: boolean;
        name?: boolean;
        country?: boolean;
        object_class?: boolean;
        mission?: boolean;
        period_minutes_range?: boolean;
        incl_degrees_range?: boolean;
        apogee_km_range?: boolean;
        perigee_km_range?: boolean;
    };
    matched_fields_description?: Array<{
        field: string;
        matchLevel: string;
        score: number;
    }>;
    matched_fields_count?: number;
}
export interface DebrisItem {
    _id: string;
    norad_id?: number;
    cospar_id?: string;
    name?: string;
    country?: string;
    launch_date?: string;
    launch_site?: string;
    rcs_size?: string;
    decay?: string;
    first_epoch?: string;
    object_class?: string;
    mission?: string;
    [key: string]: any;
}
