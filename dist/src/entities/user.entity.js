"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = void 0;
const typeorm_1 = require("typeorm");
const user_role_enum_1 = require("../auth/enums/user-role.enum");
const user_approval_status_enum_1 = require("../auth/enums/user-approval-status.enum");
const class_transformer_1 = require("class-transformer");
let User = class User {
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], User.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100 }),
    __metadata("design:type", String)
], User.prototype, "username", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100 }),
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", String)
], User.prototype, "password", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100 }),
    __metadata("design:type", String)
], User.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 20,
        default: user_role_enum_1.UserRole.FREE
    }),
    __metadata("design:type", String)
], User.prototype, "role", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'approval_status',
        type: 'varchar',
        length: 20,
        default: user_approval_status_enum_1.UserApprovalStatus.PENDING
    }),
    __metadata("design:type", String)
], User.prototype, "approvalStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'approved_at',
        type: 'timestamp with time zone',
        nullable: true,
        comment: '审批操作时间（包括批准和拒绝）'
    }),
    __metadata("design:type", Date)
], User.prototype, "approvedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'approved_by',
        type: 'integer',
        nullable: true,
        comment: '执行审批操作的管理员用户ID（包括批准和拒绝）'
    }),
    __metadata("design:type", Number)
], User.prototype, "approvedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'rejection_reason', type: 'varchar', length: 500, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "rejectionReason", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'api_calls_today', type: 'integer', default: 0 }),
    __metadata("design:type", Number)
], User.prototype, "apiCallsToday", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'downloads_today', type: 'integer', default: 0 }),
    __metadata("design:type", Number)
], User.prototype, "downloadsToday", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_api_reset', type: 'timestamp with time zone', default: () => 'CURRENT_TIMESTAMP' }),
    __metadata("design:type", Date)
], User.prototype, "lastApiReset", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'avatar_url', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "avatarUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: true }),
    __metadata("design:type", Boolean)
], User.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at', type: 'timestamp with time zone' }),
    __metadata("design:type", Date)
], User.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at', type: 'timestamp with time zone' }),
    __metadata("design:type", Date)
], User.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)({ name: 'deleted_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", Date)
], User.prototype, "deletedAt", void 0);
User = __decorate([
    (0, typeorm_1.Entity)('users'),
    (0, typeorm_1.Unique)(['username']),
    (0, typeorm_1.Unique)(['email'])
], User);
exports.User = User;
//# sourceMappingURL=user.entity.js.map