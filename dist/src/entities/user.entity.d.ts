import { UserRole } from '../auth/enums/user-role.enum';
import { UserApprovalStatus } from '../auth/enums/user-approval-status.enum';
export declare class User {
    id: number;
    username: string;
    password: string;
    email: string;
    role: UserRole;
    approvalStatus: UserApprovalStatus;
    approvedAt?: Date;
    approvedBy?: number;
    rejectionReason?: string;
    apiCallsToday: number;
    downloadsToday: number;
    lastApiReset: Date;
    avatarUrl?: string;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
    deletedAt?: Date;
}
