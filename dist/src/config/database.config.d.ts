declare const _default: (() => {
    type: string;
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    schema: string;
    entities: string[];
    synchronize: boolean;
    dropSchema: boolean;
    logging: boolean;
    ssl: boolean;
}) & import("@nestjs/config").ConfigFactoryKeyHost<{
    type: string;
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    schema: string;
    entities: string[];
    synchronize: boolean;
    dropSchema: boolean;
    logging: boolean;
    ssl: boolean;
}>;
export default _default;
