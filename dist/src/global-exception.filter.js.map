{"version": 3, "file": "global-exception.filter.js", "sourceRoot": "", "sources": ["../../src/global-exception.filter.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAkG;AAI3F,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAChC,KAAK,CAAC,SAAkB,EAAE,IAAmB;QAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,EAAY,CAAC;QAC7C,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,EAAW,CAAC;QAE1C,IAAI,MAAM,GAAG,mBAAU,CAAC,qBAAqB,CAAC;QAC9C,IAAI,OAAO,GAAG,uBAAuB,CAAC;QACtC,IAAI,KAAK,GAAG,IAAI,CAAC;QAEjB,IAAI,SAAS,YAAY,sBAAa,EAAE;YACtC,MAAM,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YAC/B,MAAM,iBAAiB,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;YAElD,IAAI,OAAO,iBAAiB,KAAK,QAAQ,IAAI,iBAAiB,KAAK,IAAI,EAAE;gBACvE,OAAO,GAAI,iBAAyB,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,CAAC;gBAClE,KAAK,GAAI,iBAAyB,CAAC,KAAK,CAAC;aAC1C;iBAAM;gBACL,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;aAC7B;SACF;aAAM,IAAI,SAAS,YAAY,KAAK,EAAE;YACrC,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;YAC5B,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;SACzB;QAGD,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI,EAAE,OAAO,CAAC,GAAG;YACjB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,MAAM;YACN,OAAO;YACP,KAAK;YACL,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,OAAO,EAAE,OAAO,CAAC,OAAO;SACzB,CAAC,CAAC;QAEH,QAAQ;aACL,MAAM,CAAC,MAAM,CAAC;aACd,IAAI,CAAC;YACJ,UAAU,EAAE,MAAM;YAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI,EAAE,OAAO,CAAC,GAAG;YACjB,OAAO;YACP,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;SAClE,CAAC,CAAC;IACP,CAAC;CACF,CAAA;AA/CY,qBAAqB;IADjC,IAAA,cAAK,GAAE;GACK,qBAAqB,CA+CjC;AA/CY,sDAAqB"}