"use strict";
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("../app.module");
const user_role_enum_1 = require("../auth/enums/user-role.enum");
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../entities/user.entity");
async function createAdminUser() {
    const app = await core_1.NestFactory.createApplicationContext(app_module_1.AppModule);
    const dataSource = app.get(typeorm_1.DataSource);
    try {
        console.log('开始创建/更新admin用户...');
        const userRepository = dataSource.getRepository(user_entity_1.User);
        let adminUser = await userRepository.findOne({ where: { username: 'admin' } });
        if (adminUser) {
            console.log('找到现有admin用户，更新角色为admin...');
            adminUser.role = user_role_enum_1.UserRole.ADMIN;
            await userRepository.save(adminUser);
            console.log('admin用户角色已更新为admin');
        }
        else {
            console.log('admin用户不存在，请先注册admin用户');
            console.log('运行以下命令注册admin用户：');
            console.log('curl -X POST http://localhost:3001/auth/register -H "Content-Type: application/json" -d \'{"username":"admin","password":"admin123","email":"<EMAIL>"}\'');
        }
        adminUser = await userRepository.findOne({ where: { username: 'admin' } });
        if (adminUser) {
            const { password } = adminUser, userInfo = __rest(adminUser, ["password"]);
            console.log('当前admin用户信息：', userInfo);
        }
    }
    catch (error) {
        console.error('创建admin用户时出错：', error);
    }
    finally {
        await app.close();
    }
}
if (require.main === module) {
    createAdminUser();
}
//# sourceMappingURL=create-admin-user.js.map