"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const auth_module_1 = require("./auth/auth.module");
const elasticsearch_module_1 = require("./elasticsearch/elasticsearch.module");
const orbit_analysis_module_1 = require("./modules/orbit-analysis.module");
const satellite_module_1 = require("./modules/satellite.module");
const aggregation_task_module_1 = require("./modules/aggregation-task.module");
const database_module_1 = require("./modules/database.module");
const satellite_tiles_module_1 = require("./modules/satellite-tiles.module");
const news_scheduler_module_1 = require("./modules/news-scheduler.module");
const satellite_scheduler_module_1 = require("./modules/satellite-scheduler.module");
const satellite_aggregation_controller_1 = require("./controllers/satellite-aggregation.controller");
const core_1 = require("@nestjs/core");
const jwt_auth_guard_1 = require("./auth/guards/jwt-auth.guard");
const user_module_1 = require("./modules/user/user.module");
const llm_config_module_1 = require("./modules/llm-config.module");
const data_source_1 = require("./data-source");
let AppModule = class AppModule {
};
AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
            }),
            typeorm_1.TypeOrmModule.forRoot(data_source_1.AppDataSource.options),
            auth_module_1.AuthModule,
            elasticsearch_module_1.ESModule,
            orbit_analysis_module_1.OrbitAnalysisModule,
            satellite_module_1.SatelliteModule,
            aggregation_task_module_1.AggregationTaskModule,
            database_module_1.DatabaseModule,
            satellite_tiles_module_1.SatelliteTilesModule,
            news_scheduler_module_1.NewsSchedulerModule,
            satellite_scheduler_module_1.SatelliteSchedulerModule,
            user_module_1.UserModule,
            llm_config_module_1.LLMConfigModule,
        ],
        controllers: [
            satellite_aggregation_controller_1.SatelliteAggregationController,
        ],
        providers: [
            {
                provide: core_1.APP_GUARD,
                useClass: jwt_auth_guard_1.JwtAuthGuard,
            },
        ],
    })
], AppModule);
exports.AppModule = AppModule;
//# sourceMappingURL=app.module.js.map