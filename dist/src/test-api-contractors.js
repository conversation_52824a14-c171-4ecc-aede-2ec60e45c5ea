"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const database_controller_1 = require("./controllers/database.controller");
const fs = require("fs");
const path = require("path");
async function bootstrap() {
    try {
        const app = await core_1.NestFactory.create(app_module_1.AppModule);
        const databaseController = app.get(database_controller_1.DatabaseController);
        console.log('正在调用getLaunchContractors API方法...');
        const result = await databaseController.getLaunchContractors();
        console.log(`成功获取${result.contractors.length}个发射承包商`);
        const outputPath = path.join(process.cwd(), 'api-launch-contractors-result.json');
        fs.writeFileSync(outputPath, JSON.stringify(result, null, 2));
        console.log(`结果已写入文件: ${outputPath}`);
        await app.close();
    }
    catch (error) {
        console.error('测试失败:', error);
        process.exit(1);
    }
}
bootstrap();
//# sourceMappingURL=test-api-contractors.js.map