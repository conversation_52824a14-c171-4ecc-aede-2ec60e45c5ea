{"version": 3, "file": "test-contractors-manual.js", "sourceRoot": "", "sources": ["../../src/test-contractors-manual.ts"], "names": [], "mappings": ";;AAAA,uCAA2C;AAC3C,6CAAyC;AACzC,kEAA8D;AAC9D,yBAAyB;AACzB,6BAA6B;AAK7B,KAAK,UAAU,SAAS;IACtB,IAAI;QAEF,MAAM,GAAG,GAAG,MAAM,kBAAW,CAAC,MAAM,CAAC,sBAAS,CAAC,CAAC;QAGhD,MAAM,eAAe,GAAG,GAAG,CAAC,GAAG,CAAC,kCAAe,CAAC,CAAC;QAEjD,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;QAGlC,MAAM,YAAY,GAAG,KAAK,IAAI,EAAE;YAE9B,MAAM,eAAe,GAAG;gBACtB,qBAAqB;gBACrB,iBAAiB;gBACjB,oDAAoD;gBACpD,4CAA4C;gBAC5C,wDAAwD;gBACxD,wCAAwC;gBACxC,wCAAwC;gBACxC,6CAA6C;gBAC7C,4CAA4C;gBAC5C,gCAAgC;gBAChC,kBAAkB;gBAClB,qBAAqB;gBACrB,2BAA2B;aAC5B,CAAC;YAGF,MAAM,iBAAiB,GAAG,CAAC,UAAkB,EAAE,EAAE;gBAE/C,MAAM,iBAAiB,GAAkD;oBACvE,qBAAqB,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,qBAAqB,EAAE;oBAChE,iBAAiB,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,iBAAiB,EAAE;oBACxD,oDAAoD,EAAE,EAAE,EAAE,EAAE,iBAAiB,EAAE,EAAE,EAAE,oDAAoD,EAAE;oBACzI,4CAA4C,EAAE,EAAE,EAAE,EAAE,eAAe,EAAE,EAAE,EAAE,4CAA4C,EAAE;oBACvH,wDAAwD,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE,EAAE,EAAE,wDAAwD,EAAE;oBAC7I,wCAAwC,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,wCAAwC,EAAE;oBAC3G,wCAAwC,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE,EAAE,EAAE,wCAAwC,EAAE;oBAC9G,6CAA6C,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE,EAAE,EAAE,6CAA6C,EAAE;oBACxH,4CAA4C,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,4CAA4C,EAAE;oBACnH,gCAAgC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,gCAAgC,EAAE;oBACzF,kBAAkB,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,kBAAkB,EAAE;oBAC1D,qBAAqB,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,qBAAqB,EAAE;oBAChE,2BAA2B,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,2BAA2B,EAAE;oBAC7E,6BAA6B,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE,EAAE,EAAE,6BAA6B,EAAE;iBACzF,CAAC;gBAGF,MAAM,cAAc,GAAiE;oBACnF,EAAE,OAAO,EAAE,sBAAsB,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,qBAAqB,EAAE,EAAE;oBAC3F,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE;oBACjE,EAAE,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,eAAe,EAAE,EAAE;oBAChF,EAAE,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,kBAAkB,EAAE,EAAE;oBACrF,EAAE,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,kBAAkB,EAAE,EAAE;oBAC1F,EAAE,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,kBAAkB,EAAE,EAAE;oBACxF,EAAE,OAAO,EAAE,sBAAsB,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,qBAAqB,EAAE,EAAE;oBAC3F,EAAE,OAAO,EAAE,uCAAuC,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,6BAA6B,EAAE,EAAE;oBACrH,EAAE,OAAO,EAAE,8BAA8B,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE,EAAE,EAAE,6BAA6B,EAAE,EAAE;iBACpH,CAAC;gBAGF,IAAI,iBAAiB,CAAC,UAAU,CAAC,EAAE;oBACjC,OAAO,iBAAiB,CAAC,UAAU,CAAC,CAAC;iBACtC;gBAGD,KAAK,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,cAAc,EAAE;oBACrD,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;wBAE5B,OAAO;4BACL,EAAE,EAAE,WAAW,CAAC,EAAE;4BAClB,EAAE,EAAE,UAAU;yBACf,CAAC;qBACH;iBACF;gBAGD,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAG9C,MAAM,SAAS,GAAG,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAErD,IAAI,SAAS,IAAI,CAAC,SAAS,EAAE;oBAE3B,IAAI,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;wBACrC,OAAO;4BACL,EAAE,EAAE,KAAK,UAAU,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;4BAC1D,EAAE,EAAE,UAAU;yBACf,CAAC;qBACH;yBAEI,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;wBACtC,OAAO;4BACL,EAAE,EAAE,KAAK,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;4BACrD,EAAE,EAAE,UAAU;yBACf,CAAC;qBACH;yBACI,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;wBACtC,OAAO;4BACL,EAAE,EAAE,KAAK,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;4BACrD,EAAE,EAAE,UAAU;yBACf,CAAC;qBACH;yBACI,IAAI,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;wBACpC,OAAO;4BACL,EAAE,EAAE,KAAK,UAAU,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;4BACnD,EAAE,EAAE,UAAU;yBACf,CAAC;qBACH;yBACI,IAAI,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;wBACrC,OAAO;4BACL,EAAE,EAAE,KAAK,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;4BACpD,EAAE,EAAE,UAAU;yBACf,CAAC;qBACH;yBACI,IAAI,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;wBACnC,OAAO;4BACL,EAAE,EAAE,KAAK,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;4BAClD,EAAE,EAAE,UAAU;yBACf,CAAC;qBACH;yBACI,IAAI,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;wBACnC,OAAO;4BACL,EAAE,EAAE,KAAK,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;4BAClD,EAAE,EAAE,UAAU;yBACf,CAAC;qBACH;yBACI,IAAI,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;wBACrC,OAAO;4BACL,EAAE,EAAE,KAAK,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;4BACpD,EAAE,EAAE,UAAU;yBACf,CAAC;qBACH;yBACI,IAAI,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;wBACpC,OAAO;4BACL,EAAE,EAAE,MAAM,UAAU,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;4BACpD,EAAE,EAAE,UAAU;yBACf,CAAC;qBACH;yBACI,IAAI,qBAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;wBAC/C,OAAO;4BACL,EAAE,EAAE,KAAK,UAAU,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;4BAC9D,EAAE,EAAE,UAAU;yBACf,CAAC;qBACH;yBACI,IAAI,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;wBACrC,OAAO;4BACL,EAAE,EAAE,KAAK,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;4BACpD,EAAE,EAAE,UAAU;yBACf,CAAC;qBACH;yBACI;wBAEH,OAAO,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC;qBAC3C;iBACF;qBAAM,IAAI,SAAS,IAAI,CAAC,SAAS,EAAE;oBAElC,OAAO,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC;iBAC3C;qBAAM,IAAI,SAAS,IAAI,SAAS,EAAE;oBAEjC,OAAO,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC;iBAC3C;qBAAM;oBAEL,OAAO,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC;iBAC3C;YACH,CAAC,CAAC;YAGF,MAAM,OAAO,GAAG,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;gBAC/C,MAAM,WAAW,GAAG,iBAAiB,CAAC,UAAU,CAAC,CAAC;gBAClD,OAAO;oBACL,QAAQ,EAAE,UAAU;oBACpB,WAAW;iBACZ,CAAC;YACJ,CAAC,CAAC,CAAC;YAGH,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACvB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC9B,QAAQ,EAAE,CAAC,CAAC,QAAQ;gBACpB,EAAE,EAAE,CAAC,CAAC,WAAW,CAAC,EAAE;gBACpB,EAAE,EAAE,CAAC,CAAC,WAAW,CAAC,EAAE;aACrB,CAAC,CAAC,CAAC,CAAC;YAGL,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,8BAA8B,CAAC,CAAC;YAC5E,EAAE,CAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC;gBAC1C,OAAO,EAAE,IAAI;gBACb,OAAO;aACR,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YAEb,OAAO,CAAC,GAAG,CAAC,YAAY,UAAU,EAAE,CAAC,CAAC;QACxC,CAAC,CAAC;QAGF,MAAM,YAAY,EAAE,CAAC;QAGrB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;KACnB;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC9B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;AACH,CAAC;AAGD,SAAS,EAAE,CAAC"}