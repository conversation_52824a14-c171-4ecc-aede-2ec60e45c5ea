"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const constellation_controller_1 = require("./constellation.controller");
const satellite_service_1 = require("../services/satellite.service");
const constellation_service_1 = require("../services/constellation.service");
const constellation_with_tle_dto_1 = require("../dto/constellation-with-tle.dto");
describe('ConstellationController', () => {
    let controller;
    let constellationService;
    let satelliteService;
    const mockLogger = {
        log: jest.fn(),
        debug: jest.fn(),
        error: jest.fn(),
    };
    const mockSatelliteService = {
        getSatelliteConstellationsLocal: jest.fn().mockResolvedValue([
            { en: 'Starlink', zh: '星链' },
            { en: 'OneWeb', zh: '万维网' },
        ]),
        updateSatelliteConstellationInfo: jest.fn().mockResolvedValue({
            success: true,
            message: '成功更新100个卫星的星座信息',
            updated: 100,
        }),
    };
    const mockConstellationService = {
        getConstellationsWithTle: jest.fn().mockResolvedValue({
            total: 2,
            constellations: [
                {
                    name: 'Starlink',
                    satelliteCount: 42,
                },
                {
                    name: 'OneWeb',
                    satelliteCount: 36,
                },
            ],
        }),
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            controllers: [constellation_controller_1.ConstellationController],
            providers: [
                {
                    provide: satellite_service_1.SatelliteService,
                    useValue: mockSatelliteService,
                },
                {
                    provide: constellation_service_1.ConstellationService,
                    useValue: mockConstellationService,
                },
            ],
        }).compile();
        controller = module.get(constellation_controller_1.ConstellationController);
        constellationService = module.get(constellation_service_1.ConstellationService);
        satelliteService = module.get(satellite_service_1.SatelliteService);
        controller.logger = mockLogger;
    });
    it('should be defined', () => {
        expect(controller).toBeDefined();
    });
    describe('getConstellations', () => {
        it('should return an array of constellations', async () => {
            const result = await controller.getConstellations();
            expect(result).toEqual([
                { en: 'Starlink', zh: '星链' },
                { en: 'OneWeb', zh: '万维网' },
            ]);
            expect(satelliteService.getSatelliteConstellationsLocal).toHaveBeenCalled();
            expect(mockLogger.log).toHaveBeenCalledWith('接收到获取卫星星座集合请求（本地数据库）');
        });
    });
    describe('updateConstellationInfo', () => {
        it('should update constellation information', async () => {
            const result = await controller.updateConstellationInfo();
            expect(result).toEqual({
                success: true,
                message: '成功更新100个卫星的星座信息',
                updated: 100,
            });
            expect(satelliteService.updateSatelliteConstellationInfo).toHaveBeenCalled();
            expect(mockLogger.log).toHaveBeenCalledWith('接收到更新卫星星座信息请求');
        });
    });
    describe('getConstellationsWithTle', () => {
        it('should return constellations with TLE information', async () => {
            const result = await controller.getConstellationsWithTle();
            expect(result).toBeInstanceOf(constellation_with_tle_dto_1.ConstellationsWithTleResponseDto);
            expect(result.total).toBe(2);
            expect(result.constellations).toHaveLength(2);
            expect(result.constellations[0].name).toBe('Starlink');
            expect(result.constellations[0].satelliteCount).toBe(42);
            expect(constellationService.getConstellationsWithTle).toHaveBeenCalled();
            expect(mockLogger.log).toHaveBeenCalledWith('接收到获取具有TLE轨道信息的卫星星座列表请求');
            expect(mockLogger.debug).toHaveBeenCalledWith('查询结果: 找到2个星座');
        });
    });
});
//# sourceMappingURL=constellation.controller.spec.js.map