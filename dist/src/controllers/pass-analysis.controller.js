"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PassAnalysisController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const pass_analysis_service_1 = require("../services/pass-analysis/pass-analysis.service");
const pass_analysis_dto_1 = require("../services/pass-analysis/dto/pass-analysis.dto");
let PassAnalysisController = class PassAnalysisController {
    constructor(passAnalysisService) {
        this.passAnalysisService = passAnalysisService;
    }
    async analyzePass(request) {
        const passes = await this.passAnalysisService.analyzeMultipleSatellites({
            groundStation: request.groundStation,
            satellites: request.satellites,
            startTime: new Date(request.startTime),
            endTime: new Date(request.endTime),
            minElevation: request.minElevation,
            timeStep: request.timeStep
        });
        return { passes };
    }
};
__decorate([
    (0, common_1.Post)('pass-analysis'),
    (0, swagger_1.ApiOperation)({
        summary: '卫星过境分析',
        description: `
      分析指定时间段内卫星相对于地面站的过境情况。
      
      功能说明：
      - 支持批量分析多颗卫星的过境情况
      - 可设置最小仰角过滤条件
      - 可调整时间步长以平衡精度和性能
      - 返回每次过境的入境和离境信息
      
      注意事项：
      - 时间使用UTC时间
      - 角度单位为度
      - 距离单位为千米
      - 时间步长建议60秒，可根据需要调整
      - 分析时间段建议不超过7天
    `
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '过境分析结果',
        type: pass_analysis_dto_1.PassAnalysisResponseDto,
        content: {
            'application/json': {
                example: {
                    passes: [
                        {
                            satellite: {
                                satId: '25544',
                                name: 'ISS (ZARYA)',
                                organization: 'NASA',
                                company: 'NASA',
                                country: 'USA',
                                type: '空间站'
                            },
                            startPass: {
                                time: '2024-02-24T02:30:00Z',
                                azimuth: 123.45,
                                elevation: 10.0,
                                range: 1200.5
                            },
                            endPass: {
                                time: '2024-02-24T02:40:00Z',
                                azimuth: 67.89,
                                elevation: 10.0,
                                range: 1150.3
                            },
                            duration: 600
                        }
                    ]
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数错误',
        content: {
            'application/json': {
                example: {
                    statusCode: 400,
                    message: ['时间步长必须在1到3600秒之间'],
                    error: 'Bad Request'
                }
            }
        }
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [pass_analysis_dto_1.PassAnalysisRequestDto]),
    __metadata("design:returntype", Promise)
], PassAnalysisController.prototype, "analyzePass", null);
PassAnalysisController = __decorate([
    (0, common_1.Controller)('api/pass-analysis'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiTags)('轨道数据'),
    __metadata("design:paramtypes", [pass_analysis_service_1.PassAnalysisService])
], PassAnalysisController);
exports.PassAnalysisController = PassAnalysisController;
//# sourceMappingURL=pass-analysis.controller.js.map