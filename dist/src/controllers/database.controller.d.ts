import { DatabaseService } from '../services/database.service';
declare class NumberRangeDto {
    min?: number;
    max?: number;
}
declare class DateRangeDto {
    start?: string;
    end?: string;
}
declare class SatelliteFilterDto {
    keyword?: string;
    satelliteName?: string;
    noradId?: string;
    cosparId?: string;
    constellationName?: string;
    status?: string;
    countryOfOwner?: string;
    owner?: string;
    users?: string;
    purpose?: string;
    orbitClass?: string;
    orbitType?: string;
    perigeeKm?: NumberRangeDto;
    apogeeKm?: NumberRangeDto;
    eccentricity?: NumberRangeDto;
    inclDegrees?: NumberRangeDto;
    periodMinutes?: NumberRangeDto;
    decayDate?: DateRangeDto;
    deployedDate?: DateRangeDto;
    launchDate?: DateRangeDto;
    contractor?: string;
    countryOfContractor?: string;
    launchSite?: string;
    launchVehicle?: string;
    page?: number;
    limit?: number;
}
export declare class DatabaseController {
    private readonly databaseService;
    private readonly logger;
    constructor(databaseService: DatabaseService);
    executeQuery(body: {
        sql: string;
        parameters?: any[];
    }): Promise<any>;
    filterSatellites(filterDto: SatelliteFilterDto): Promise<any>;
    private isEmptyFilter;
    getSatelliteUsers(): Promise<any>;
    getSatellitePurposes(): Promise<any>;
    getLaunchContractors(): Promise<any>;
    getLaunchSites(): Promise<any>;
    getLaunchVehicles(): Promise<any>;
}
export {};
