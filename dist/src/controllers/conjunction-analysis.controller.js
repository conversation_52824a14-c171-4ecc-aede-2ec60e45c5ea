"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConjunctionAnalysisController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const conjunction_analysis_service_1 = require("../services/conjunction-analysis/conjunction-analysis.service");
const conjunction_analysis_dto_1 = require("../services/conjunction-analysis/dto/conjunction-analysis.dto");
let ConjunctionAnalysisController = class ConjunctionAnalysisController {
    constructor(conjunctionAnalysisService) {
        this.conjunctionAnalysisService = conjunctionAnalysisService;
    }
    async analyzeConjunctions(request) {
        const conjunctions = await this.conjunctionAnalysisService.analyzeConjunctions({
            backgroundSatellites: request.backgroundSatellites,
            targetSatellites: request.targetSatellites,
            startTime: new Date(request.startTime),
            endTime: new Date(request.endTime),
            threshold: request.threshold,
            timeStep: request.timeStep,
            batchSize: request.batchSize
        });
        return { conjunctions };
    }
};
__decorate([
    (0, common_1.Post)('conjunction-analysis'),
    (0, swagger_1.ApiOperation)({
        summary: '卫星交汇分析',
        description: `
      分析指定时间段内背景目标卫星集合与关注目标卫星集合的交汇情况。
      
      功能说明：
      - 支持批量分析多颗卫星的交汇情况
      - 可设置距离门限进行过滤
      - 可调整时间步长以平衡精度和性能
      - 返回每次交汇的详细信息，包括：
        * 交汇开始和结束时间
        * 交汇时长
        * 最近距离及其发生时间
        * RTN坐标系中的相对方向
      
      注意事项：
      - 时间使用UTC时间
      - 距离单位为千米
      - 方向角度单位为度
      - 时间步长建议60秒，可根据需要调整
      - 分析时间段建议不超过7天
      - 对于大量卫星的分析，建议适当增加时间步长和批处理大小
      - TLE数据应保证时效性，建议使用最新的数据
    `
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '交汇分析接口',
        type: conjunction_analysis_dto_1.ConjunctionAnalysisResponseDto,
        content: {
            'application/json': {
                examples: {
                    request: {
                        summary: '请求示例',
                        description: '包含多颗背景卫星和目标卫星的交汇分析请求',
                        value: {
                            backgroundSatellites: [
                                {
                                    satId: '25544',
                                    name: 'ISS (ZARYA)',
                                    line1: '1 25544U 98067A   24054.88088078  .00014558  00000+0  26729-3 0  9990',
                                    line2: '2 25544  51.6416 150.2506 0004789  89.9089  32.8809 15.49512781434577'
                                },
                                {
                                    satId: '43205',
                                    name: 'ONEWEB-0347',
                                    line1: '1 43205U 18004C   24054.85177259  .00000218  00000+0  61883-4 0  9991',
                                    line2: '2 43205  87.9049 157.3875 0002049  79.6081 280.5347 13.16373104297506'
                                }
                            ],
                            targetSatellites: [
                                {
                                    satId: '48274',
                                    name: 'STARLINK-3432',
                                    line1: '1 48274U 21041AF  24054.86885282  .00002571  00000+0  16843-3 0  9994',
                                    line2: '2 48274  53.0559  30.0121 0001038  89.7222 270.3898 15.06396635146374'
                                },
                                {
                                    satId: '51105',
                                    name: 'STARLINK-5234',
                                    line1: '1 51105U 22002BT  24054.87654321  .00001234  00000+0  12345-3 0  9990',
                                    line2: '2 51105  53.2156  75.4321 0001234  90.1234 269.8766 15.06543210123456'
                                }
                            ],
                            startTime: '2024-02-24T00:00:00Z',
                            endTime: '2024-02-25T00:00:00Z',
                            threshold: 100,
                            timeStep: 60,
                            batchSize: 100
                        }
                    },
                    response: {
                        summary: '响应示例',
                        description: '多个卫星交汇事件的分析结果',
                        value: {
                            conjunctions: [
                                {
                                    conjunctionId: 'conj-2024022401',
                                    satellite1: {
                                        satId: '25544',
                                        name: 'ISS (ZARYA)'
                                    },
                                    satellite2: {
                                        satId: '48274',
                                        name: 'STARLINK-3432'
                                    },
                                    threshold: 100,
                                    startTime: '2024-02-24T02:30:00Z',
                                    endTime: '2024-02-24T02:35:00Z',
                                    duration: 300,
                                    closestApproach: {
                                        time: '2024-02-24T02:32:30Z',
                                        distance: 85.6,
                                        rtn: {
                                            r: 45.67,
                                            t: -23.45,
                                            n: 12.34
                                        }
                                    }
                                },
                                {
                                    conjunctionId: 'conj-2024022402',
                                    satellite1: {
                                        satId: '25544',
                                        name: 'ISS (ZARYA)'
                                    },
                                    satellite2: {
                                        satId: '51105',
                                        name: 'STARLINK-5234'
                                    },
                                    threshold: 100,
                                    startTime: '2024-02-24T04:15:00Z',
                                    endTime: '2024-02-24T04:18:00Z',
                                    duration: 180,
                                    closestApproach: {
                                        time: '2024-02-24T04:16:30Z',
                                        distance: 92.3,
                                        rtn: {
                                            r: 38.45,
                                            t: 15.67,
                                            n: -8.92
                                        }
                                    }
                                },
                                {
                                    conjunctionId: 'conj-2024022403',
                                    satellite1: {
                                        satId: '43205',
                                        name: 'ONEWEB-0347'
                                    },
                                    satellite2: {
                                        satId: '48274',
                                        name: 'STARLINK-3432'
                                    },
                                    threshold: 100,
                                    startTime: '2024-02-24T06:45:00Z',
                                    endTime: '2024-02-24T06:48:30Z',
                                    duration: 210,
                                    closestApproach: {
                                        time: '2024-02-24T06:46:45Z',
                                        distance: 78.9,
                                        rtn: {
                                            r: -32.56,
                                            t: 42.18,
                                            n: 15.73
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数错误',
        content: {
            'application/json': {
                example: {
                    statusCode: 400,
                    message: ['距离门限必须大于0'],
                    error: 'Bad Request'
                }
            }
        }
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [conjunction_analysis_dto_1.ConjunctionAnalysisRequestDto]),
    __metadata("design:returntype", Promise)
], ConjunctionAnalysisController.prototype, "analyzeConjunctions", null);
ConjunctionAnalysisController = __decorate([
    (0, common_1.Controller)('api/conjunction-analysis'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiTags)('轨道数据'),
    __metadata("design:paramtypes", [conjunction_analysis_service_1.ConjunctionAnalysisService])
], ConjunctionAnalysisController);
exports.ConjunctionAnalysisController = ConjunctionAnalysisController;
//# sourceMappingURL=conjunction-analysis.controller.js.map