"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LLMConfigController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const llm_config_dto_1 = require("../dto/llm-config.dto");
const llm_config_service_1 = require("../services/llm-config.service");
let LLMConfigController = class LLMConfigController {
    constructor(llmConfigService) {
        this.llmConfigService = llmConfigService;
    }
    async getAllConfigs() {
        return await this.llmConfigService.getAllConfigs();
    }
    async getConfig(configType) {
        return await this.llmConfigService.getConfig(configType);
    }
    async updateConfig(updateDto) {
        return await this.llmConfigService.updateConfig(updateDto);
    }
    async testConnection(testDto) {
        return await this.llmConfigService.testConnection(testDto);
    }
    async resetConfig(resetDto) {
        return await this.llmConfigService.resetConfig(resetDto);
    }
    async getStats() {
        return await this.llmConfigService.getStats();
    }
    async clearStats() {
        return await this.llmConfigService.clearStats();
    }
    async exportConfigs(includeSensitive = false) {
        const configs = await this.llmConfigService.getAllConfigs();
        return {
            exportTime: new Date().toISOString(),
            configs: configs.map(config => (Object.assign(Object.assign({}, config), { apiKey: includeSensitive ? '***EXPORTED***' : config.apiKey }))),
            metadata: {
                version: '1.0.0',
                totalConfigs: configs.length
            }
        };
    }
};
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: '获取所有大模型配置',
        description: '获取翻译和主题提取的所有大模型配置信息，API密钥会被脱敏处理'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        type: [llm_config_dto_1.LLMConfigResponseDto]
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: '未授权' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: '服务器内部错误' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], LLMConfigController.prototype, "getAllConfigs", null);
__decorate([
    (0, common_1.Get)(':configType'),
    (0, swagger_1.ApiOperation)({
        summary: '获取指定类型的大模型配置',
        description: '根据配置类型获取对应的大模型配置信息'
    }),
    (0, swagger_1.ApiParam)({
        name: 'configType',
        description: '配置类型',
        enum: llm_config_dto_1.ConfigType,
        example: llm_config_dto_1.ConfigType.TRANSLATION
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        type: llm_config_dto_1.LLMConfigResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: '未授权' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '配置不存在' }),
    __param(0, (0, common_1.Param)('configType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LLMConfigController.prototype, "getConfig", null);
__decorate([
    (0, common_1.Put)(),
    (0, swagger_1.ApiOperation)({
        summary: '更新大模型配置',
        description: '更新指定类型的大模型配置，只更新提供的字段，其他字段保持不变'
    }),
    (0, swagger_1.ApiBody)({
        type: llm_config_dto_1.UpdateLLMConfigDto,
        description: '配置更新数据',
        examples: {
            updateTranslation: {
                summary: '更新翻译配置示例',
                value: {
                    configType: 'translation',
                    model: 'qwen-max-latest',
                    apiKey: 'sk-new-api-key',
                    systemPrompt: '你是一位专业的航空航天翻译专家...',
                    temperature: 0.05
                }
            },
            updateThemeExtraction: {
                summary: '更新主题提取配置示例',
                value: {
                    configType: 'theme_extraction',
                    model: 'qwen-turbo',
                    maxTokens: 2000,
                    temperature: 0.2
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '更新成功',
        type: llm_config_dto_1.LLMConfigResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: '未授权' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [llm_config_dto_1.UpdateLLMConfigDto]),
    __metadata("design:returntype", Promise)
], LLMConfigController.prototype, "updateConfig", null);
__decorate([
    (0, common_1.Post)('test'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '测试大模型配置连接',
        description: '测试指定配置类型的大模型连接是否正常，可选择提供测试文本'
    }),
    (0, swagger_1.ApiBody)({
        type: llm_config_dto_1.TestConnectionDto,
        description: '测试连接参数',
        examples: {
            testTranslation: {
                summary: '测试翻译配置',
                value: {
                    configType: 'translation',
                    testText: 'Hello, world!'
                }
            },
            testThemeExtraction: {
                summary: '测试主题提取配置',
                value: {
                    configType: 'theme_extraction',
                    testText: 'This is a news article about space technology...'
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '测试完成',
        type: llm_config_dto_1.TestConnectionResponseDto,
        examples: {
            success: {
                summary: '测试成功',
                value: {
                    success: true,
                    responseTime: 1234,
                    result: '你好，世界！',
                    timestamp: '2024-01-01T00:00:00.000Z'
                }
            },
            failure: {
                summary: '测试失败',
                value: {
                    success: false,
                    responseTime: 5000,
                    error: 'API连接超时',
                    timestamp: '2024-01-01T00:00:00.000Z'
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: '未授权' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [llm_config_dto_1.TestConnectionDto]),
    __metadata("design:returntype", Promise)
], LLMConfigController.prototype, "testConnection", null);
__decorate([
    (0, common_1.Post)('reset'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '重置大模型配置到默认值',
        description: '将指定类型的配置重置为系统默认值，需要确认操作'
    }),
    (0, swagger_1.ApiBody)({
        type: llm_config_dto_1.ResetConfigDto,
        description: '重置配置参数',
        examples: {
            resetTranslation: {
                summary: '重置翻译配置',
                value: {
                    configType: 'translation',
                    confirm: true
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '重置成功',
        type: llm_config_dto_1.LLMConfigResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误或未确认操作' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: '未授权' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [llm_config_dto_1.ResetConfigDto]),
    __metadata("design:returntype", Promise)
], LLMConfigController.prototype, "resetConfig", null);
__decorate([
    (0, common_1.Get)('stats/summary'),
    (0, swagger_1.ApiOperation)({
        summary: '获取大模型配置统计信息',
        description: '获取翻译和主题提取功能的使用统计信息，包括请求次数、成功率、平均响应时间等'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        type: llm_config_dto_1.ConfigStatsDto,
        examples: {
            success: {
                summary: '统计信息示例',
                value: {
                    translation: {
                        totalRequests: 1000,
                        successfulRequests: 950,
                        failedRequests: 50,
                        averageResponseTime: 1500,
                        lastUsed: '2024-01-01T00:00:00.000Z'
                    },
                    themeExtraction: {
                        totalRequests: 500,
                        successfulRequests: 480,
                        failedRequests: 20,
                        averageResponseTime: 2000,
                        lastUsed: '2024-01-01T00:00:00.000Z'
                    },
                    timestamp: '2024-01-01T00:00:00.000Z'
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: '未授权' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: '服务器内部错误' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], LLMConfigController.prototype, "getStats", null);
__decorate([
    (0, common_1.Delete)('stats'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({
        summary: '清除统计信息',
        description: '清除所有大模型配置的统计信息，将计数器重置为零'
    }),
    (0, swagger_1.ApiResponse)({ status: 204, description: '清除成功' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: '未授权' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], LLMConfigController.prototype, "clearStats", null);
__decorate([
    (0, common_1.Get)('export/all'),
    (0, swagger_1.ApiOperation)({
        summary: '导出所有配置',
        description: '导出所有大模型配置为JSON格式，方便备份和迁移'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'includeSensitive',
        description: '是否包含敏感信息（API密钥）',
        type: Boolean,
        required: false,
        example: false
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '导出成功',
        schema: {
            type: 'object',
            properties: {
                exportTime: { type: 'string', description: '导出时间' },
                configs: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/LLMConfigResponseDto' }
                },
                metadata: {
                    type: 'object',
                    properties: {
                        version: { type: 'string' },
                        totalConfigs: { type: 'number' }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: '未授权' }),
    __param(0, (0, common_1.Query)('includeSensitive')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Boolean]),
    __metadata("design:returntype", Promise)
], LLMConfigController.prototype, "exportConfigs", null);
LLMConfigController = __decorate([
    (0, swagger_1.ApiTags)('大模型配置'),
    (0, common_1.Controller)('llm-config'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [llm_config_service_1.LLMConfigService])
], LLMConfigController);
exports.LLMConfigController = LLMConfigController;
//# sourceMappingURL=llm-config.controller.js.map