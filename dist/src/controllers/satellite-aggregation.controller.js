"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SatelliteAggregationController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SatelliteAggregationController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const aggregation_task_service_1 = require("../services/aggregation-task.service");
let SatelliteAggregationController = SatelliteAggregationController_1 = class SatelliteAggregationController {
    constructor(aggregationTaskService) {
        this.aggregationTaskService = aggregationTaskService;
        this.logger = new common_1.Logger(SatelliteAggregationController_1.name);
    }
    async getAggregationStatus() {
        this.logger.debug('接收到查询卫星聚合任务状态请求');
        try {
            const task = await this.aggregationTaskService.getLatestTask();
            if (!task) {
                return {
                    success: false,
                    message: '未找到任何聚合任务'
                };
            }
            return {
                success: true,
                id: task.id,
                task_type: task.task_type,
                status: task.status,
                progress: task.progress,
                processed_records: task.processed_records,
                aggregated_records: task.aggregated_records,
                start_time: task.start_time,
                end_time: task.end_time,
                parameters: task.parameters,
                error_message: task.error_message,
                createdAt: task.createdAt,
                updatedAt: task.updatedAt
            };
        }
        catch (error) {
            this.logger.error(`查询聚合任务状态失败: ${error.message}`, error.stack);
            return {
                success: false,
                message: `查询失败: ${error.message}`
            };
        }
    }
    async getRunningAggregations() {
        this.logger.debug('接收到查询正在运行的聚合任务请求');
        try {
            const tasks = await this.aggregationTaskService.getRunningTasks();
            if (!tasks || tasks.length === 0) {
                return {
                    success: true,
                    data: [],
                    message: '当前没有正在运行的聚合任务'
                };
            }
            return {
                success: true,
                data: tasks.map(task => ({
                    id: task.id,
                    task_type: task.task_type,
                    status: task.status,
                    progress: task.progress,
                    processed_records: task.processed_records,
                    start_time: task.start_time,
                    parameters: task.parameters,
                    createdAt: task.createdAt
                }))
            };
        }
        catch (error) {
            this.logger.error(`查询正在运行的聚合任务失败: ${error.message}`, error.stack);
            return {
                success: false,
                message: `查询失败: ${error.message}`
            };
        }
    }
};
__decorate([
    (0, common_1.Get)('status'),
    (0, swagger_1.ApiOperation)({
        summary: '查询卫星聚合任务状态',
        description: `
    获取最新的卫星数据聚合任务状态。
    
    ## 返回数据说明
    - id: 任务ID
    - task_type: 任务类型（full: 全量聚合, partial: 部分聚合, keyword: 关键词聚合）
    - status: 任务状态（pending: 等待执行, running: 执行中, completed: 已完成, failed: 失败）
    - progress: 任务进度百分比
    - processed_records: 已处理的记录数
    - aggregated_records: 已聚合的记录数
    - start_time: 开始时间
    - end_time: 结束时间（如果已完成）
    - parameters: 任务参数
    - error_message: 错误信息（如果失败）
    `
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'number', description: '任务ID' },
                task_type: { type: 'string', description: '任务类型' },
                status: { type: 'string', description: '任务状态' },
                progress: { type: 'number', description: '进度百分比' },
                processed_records: { type: 'number', description: '已处理的记录数' },
                aggregated_records: { type: 'number', description: '已聚合的记录数' },
                start_time: { type: 'string', format: 'date-time', description: '开始时间' },
                end_time: { type: 'string', format: 'date-time', description: '结束时间' },
                parameters: { type: 'object', description: '任务参数' },
                error_message: { type: 'string', description: '错误信息' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '未找到任何任务',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: false },
                message: { type: 'string', example: '未找到任何聚合任务' }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SatelliteAggregationController.prototype, "getAggregationStatus", null);
__decorate([
    (0, common_1.Get)('running'),
    (0, swagger_1.ApiOperation)({
        summary: '查询正在运行的聚合任务',
        description: `
    查询所有正在运行（状态为running）的聚合任务列表。
    
    ## 用途
    用于监控当前系统中正在执行的聚合任务，了解系统负载情况。
    `
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'number', description: '任务ID' },
                    task_type: { type: 'string', description: '任务类型' },
                    status: { type: 'string', description: '任务状态' },
                    start_time: { type: 'string', format: 'date-time', description: '开始时间' },
                    processed_records: { type: 'number', description: '处理的记录数' },
                    progress: { type: 'number', description: '进度百分比' }
                }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SatelliteAggregationController.prototype, "getRunningAggregations", null);
SatelliteAggregationController = SatelliteAggregationController_1 = __decorate([
    (0, common_1.Controller)('local/satellite-aggregation'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiTags)('卫星聚合任务'),
    __metadata("design:paramtypes", [aggregation_task_service_1.AggregationTaskService])
], SatelliteAggregationController);
exports.SatelliteAggregationController = SatelliteAggregationController;
//# sourceMappingURL=satellite-aggregation.controller.js.map