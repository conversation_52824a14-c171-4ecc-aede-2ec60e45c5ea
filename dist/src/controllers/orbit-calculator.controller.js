"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var OrbitCalculatorController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrbitCalculatorController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const OrbitCalculator_1 = require("../services/orbit-calculator/OrbitCalculator");
const calculate_positions_dto_1 = require("../services/orbit-calculator/dto/calculate-positions.dto");
const common_2 = require("@nestjs/common");
let OrbitCalculatorController = OrbitCalculatorController_1 = class OrbitCalculatorController {
    constructor(orbitCalculator) {
        this.orbitCalculator = orbitCalculator;
        this.logger = new common_2.Logger(OrbitCalculatorController_1.name);
    }
    async calculatePositions(request) {
        var _a;
        const time = request.time ? new Date(request.time) : new Date();
        const maxBatchSize = 500;
        const batchSize = request.batchSize && request.batchSize <= maxBatchSize
            ? request.batchSize
            : 100;
        const satelliteCount = ((_a = request.satellites) === null || _a === void 0 ? void 0 : _a.length) || 0;
        this.logger.log(`正在计算${satelliteCount}颗卫星的位置，批处理大小: ${batchSize}`);
        const { positions, errors } = await this.orbitCalculator.calculatePositions(request.satellites, time, {
            continueOnError: request.continueOnError !== false,
            batchSize: batchSize
        });
        return {
            positions,
            errors: errors.map(err => ({
                satId: err.satId,
                error: err.error.message
            }))
        };
    }
};
__decorate([
    (0, common_1.Post)('calculate-positions'),
    (0, swagger_1.ApiOperation)({
        summary: '批量计算卫星位置',
        description: `
      根据提供的TLE数据计算多颗卫星在指定时间的位置。
      
      功能特点：
      - 支持LEO和深空轨道卫星
      - 自动选择SGP4或SDP4算法
      - 返回ECI坐标系和地理坐标系的位置信息
      - 支持批量计算，自动并行处理
      
      参数说明：
      - satellites: 卫星TLE数据列表
      - time: 计算时间点(UTC)，可选，默认为当前时间
      - continueOnError: 发生错误时是否继续计算，可选，默认true
      - batchSize: 每批处理的卫星数量，可选，默认100
      
      注意事项：
      - TLE数据应保证时效性，建议使用最新的数据
      - 时间格式应为ISO 8601标准
      - 所有距离单位为千米(km)
      - 所有速度单位为千米/秒(km/s)
      - 所有角度单位为度
    `
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '计算成功',
        type: calculate_positions_dto_1.CalculatePositionsResponseDto,
        content: {
            'application/json': {
                example: {
                    positions: [{
                            satId: 'NORAD_25544',
                            name: 'ISS',
                            epoch: '2021-06-05T07:19:36.000Z',
                            position: {
                                x: -2345.124,
                                y: 4567.890,
                                z: 3456.123
                            },
                            velocity: {
                                x: -5.678,
                                y: -4.321,
                                z: 2.345
                            },
                            latitude: 45.678,
                            longitude: 123.456,
                            altitude: 408.789,
                            algorithm: 'SGP4'
                        }],
                    errors: []
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'TLE数据格式错误或参数无效',
        content: {
            'application/json': {
                example: {
                    statusCode: 400,
                    message: ['TLE数据格式无效', '时间格式无效'],
                    error: 'Bad Request'
                }
            }
        }
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [calculate_positions_dto_1.CalculatePositionsRequestDto]),
    __metadata("design:returntype", Promise)
], OrbitCalculatorController.prototype, "calculatePositions", null);
OrbitCalculatorController = OrbitCalculatorController_1 = __decorate([
    (0, common_1.Controller)('api/orbit-calculator'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiTags)('轨道计算'),
    (0, swagger_1.ApiExtraModels)(calculate_positions_dto_1.TLESatelliteDto, calculate_positions_dto_1.SatellitePositionDto, calculate_positions_dto_1.CalculationErrorDto),
    __metadata("design:paramtypes", [OrbitCalculator_1.OrbitCalculator])
], OrbitCalculatorController);
exports.OrbitCalculatorController = OrbitCalculatorController;
//# sourceMappingURL=orbit-calculator.controller.js.map