import { OrbitCalculator } from '../services/orbit-calculator/OrbitCalculator';
import { CalculatePositionsRequestDto, CalculatePositionsResponseDto } from '../services/orbit-calculator/dto/calculate-positions.dto';
export declare class OrbitCalculatorController {
    private readonly orbitCalculator;
    private readonly logger;
    constructor(orbitCalculator: OrbitCalculator);
    calculatePositions(request: CalculatePositionsRequestDto): Promise<CalculatePositionsResponseDto>;
}
