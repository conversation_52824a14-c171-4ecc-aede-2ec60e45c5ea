"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AggregationTaskController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AggregationTaskController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const aggregation_task_service_1 = require("../services/aggregation-task.service");
let AggregationTaskController = AggregationTaskController_1 = class AggregationTaskController {
    constructor(taskService) {
        this.taskService = taskService;
        this.logger = new common_1.Logger(AggregationTaskController_1.name);
    }
    async getTasks(page = 1, limit = 10) {
        this.logger.debug(`获取聚合任务列表: page=${page}, limit=${limit}`);
        const result = await this.taskService.getTasks(+page, +limit);
        return {
            total: result.total,
            tasks: result.tasks
        };
    }
    async getLatestTask() {
        this.logger.debug('获取最新聚合任务');
        const task = await this.taskService.getLatestTask();
        if (!task) {
            return {
                success: false,
                message: '未找到任何聚合任务'
            };
        }
        return task;
    }
    async getRunningTasks() {
        this.logger.debug('获取正在运行的聚合任务');
        return this.taskService.getRunningTasks();
    }
    async getTaskById(id) {
        this.logger.debug(`获取聚合任务详情: id=${id}`);
        try {
            const task = await this.taskService.getTaskById(+id);
            return task;
        }
        catch (error) {
            this.logger.error(`获取聚合任务详情失败: ${error.message}`);
            return {
                success: false,
                message: error.message
            };
        }
    }
    async createTask(taskData) {
        this.logger.debug(`创建聚合任务: ${JSON.stringify(taskData)}`);
        try {
            const taskParams = {
                task_type: taskData.task_type,
                parameters: taskData.parameters || {}
            };
            const task = await this.taskService.createTask(taskParams);
            return {
                success: true,
                task
            };
        }
        catch (error) {
            this.logger.error(`创建聚合任务失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async startTask(id) {
        this.logger.debug(`开始执行聚合任务: id=${id}`);
        try {
            const task = await this.taskService.startTask(+id);
            return {
                success: true,
                task
            };
        }
        catch (error) {
            this.logger.error(`开始执行聚合任务失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async updateTaskProgress(id, data) {
        this.logger.debug(`更新聚合任务进度: id=${id}, progress=${data.progress}, processed_records=${data.processed_records}`);
        try {
            const task = await this.taskService.updateTaskProgress(+id, data.progress, data.processed_records);
            return {
                success: true,
                task
            };
        }
        catch (error) {
            this.logger.error(`更新聚合任务进度失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async completeTask(id, data) {
        this.logger.debug(`完成聚合任务: id=${id}, aggregated_records=${data.aggregated_records}`);
        try {
            const task = await this.taskService.completeTask(+id, data.aggregated_records);
            return {
                success: true,
                task
            };
        }
        catch (error) {
            this.logger.error(`完成聚合任务失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async failTask(id, data) {
        this.logger.debug(`标记聚合任务失败: id=${id}, error_message=${data.error_message}`);
        try {
            const task = await this.taskService.failTask(+id, data.error_message);
            return {
                success: true,
                task
            };
        }
        catch (error) {
            this.logger.error(`标记聚合任务失败时出错: ${error.message}`, error.stack);
            throw error;
        }
    }
};
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: '获取聚合任务列表',
        description: `
    查询卫星数据聚合任务列表，支持分页。
    
    ## 返回数据说明
    - total: 总任务数
    - tasks: 任务列表
    
    任务状态说明：
    - pending: 等待执行
    - running: 执行中
    - completed: 已完成
    - failed: 失败
    `
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        description: '页码，默认为1',
        type: Number
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        description: '每页数量，默认为10',
        type: Number
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        schema: {
            type: 'object',
            properties: {
                total: { type: 'number', description: '总任务数' },
                tasks: {
                    type: 'array',
                    description: '任务列表',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'number', description: '任务ID' },
                            task_type: { type: 'string', description: '任务类型' },
                            status: { type: 'string', description: '任务状态' },
                            start_time: { type: 'string', format: 'date-time', description: '开始时间' },
                            end_time: { type: 'string', format: 'date-time', description: '结束时间' },
                            processed_records: { type: 'number', description: '处理的记录数' },
                            aggregated_records: { type: 'number', description: '聚合的记录数' },
                            progress: { type: 'number', description: '进度百分比' },
                            createdAt: { type: 'string', format: 'date-time', description: '创建时间' },
                            updatedAt: { type: 'string', format: 'date-time', description: '更新时间' }
                        }
                    }
                }
            }
        }
    }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AggregationTaskController.prototype, "getTasks", null);
__decorate([
    (0, common_1.Get)('latest'),
    (0, swagger_1.ApiOperation)({
        summary: '获取最新的聚合任务',
        description: `
    获取最近创建的一个聚合任务信息。
    
    ## 用途
    主要用于前端查询最新聚合任务的状态，了解当前是否有聚合任务正在执行。
    `
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'number', description: '任务ID' },
                task_type: { type: 'string', description: '任务类型' },
                status: { type: 'string', description: '任务状态' },
                start_time: { type: 'string', format: 'date-time', description: '开始时间' },
                end_time: { type: 'string', format: 'date-time', description: '结束时间' },
                processed_records: { type: 'number', description: '处理的记录数' },
                aggregated_records: { type: 'number', description: '聚合的记录数' },
                progress: { type: 'number', description: '进度百分比' },
                parameters: { type: 'object', description: '任务参数' },
                error_message: { type: 'string', description: '错误信息' },
                createdAt: { type: 'string', format: 'date-time', description: '创建时间' },
                updatedAt: { type: 'string', format: 'date-time', description: '更新时间' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '未找到任何任务',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: false },
                message: { type: 'string', example: '未找到任何聚合任务' }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AggregationTaskController.prototype, "getLatestTask", null);
__decorate([
    (0, common_1.Get)('running'),
    (0, swagger_1.ApiOperation)({
        summary: '获取正在运行的聚合任务',
        description: `
    获取所有正在运行（状态为running）的聚合任务列表。
    
    ## 用途
    用于监控当前系统中正在执行的聚合任务。
    `
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'number', description: '任务ID' },
                    task_type: { type: 'string', description: '任务类型' },
                    status: { type: 'string', description: '任务状态' },
                    start_time: { type: 'string', format: 'date-time', description: '开始时间' },
                    processed_records: { type: 'number', description: '处理的记录数' },
                    progress: { type: 'number', description: '进度百分比' },
                    parameters: { type: 'object', description: '任务参数' },
                    createdAt: { type: 'string', format: 'date-time', description: '创建时间' }
                }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AggregationTaskController.prototype, "getRunningTasks", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: '获取指定聚合任务详情',
        description: `
    根据任务ID获取聚合任务的详细信息，包括执行状态、进度、结果等。
    
    ## 返回数据说明
    - id: 任务ID
    - task_type: 任务类型（full: 全量聚合, partial: 部分聚合, keyword: 关键词聚合）
    - status: 任务状态（pending: 等待执行, running: 执行中, completed: 已完成, failed: 失败）
    - start_time: 开始时间
    - end_time: 结束时间
    - processed_records: 处理的记录数
    - aggregated_records: 聚合的记录数
    - progress: 进度百分比
    - parameters: 任务参数
    - error_message: 错误信息（如果失败）
    `
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'number', description: '任务ID' },
                task_type: { type: 'string', description: '任务类型' },
                status: { type: 'string', description: '任务状态' },
                start_time: { type: 'string', format: 'date-time', description: '开始时间' },
                end_time: { type: 'string', format: 'date-time', description: '结束时间' },
                processed_records: { type: 'number', description: '处理的记录数' },
                aggregated_records: { type: 'number', description: '聚合的记录数' },
                progress: { type: 'number', description: '进度百分比' },
                parameters: { type: 'object', description: '任务参数' },
                error_message: { type: 'string', description: '错误信息' },
                createdAt: { type: 'string', format: 'date-time', description: '创建时间' },
                updatedAt: { type: 'string', format: 'date-time', description: '更新时间' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '任务不存在',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: false },
                message: { type: 'string', example: '未找到ID为123的聚合任务' }
            }
        }
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AggregationTaskController.prototype, "getTaskById", null);
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({
        summary: '创建新的聚合任务',
        description: `
    创建一个新的卫星数据聚合任务。
    
    ## 参数说明
    - task_type: 任务类型（full: 全量聚合, partial: 部分聚合, keyword: 关键词聚合）
    - parameters: 任务参数，根据任务类型不同而不同
    
    ## 返回数据说明
    返回创建的任务信息，包括任务ID、状态等。
    `
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['task_type'],
            properties: {
                task_type: {
                    type: 'string',
                    description: '任务类型',
                    enum: ['full', 'partial', 'keyword'],
                    example: 'full'
                },
                parameters: {
                    type: 'object',
                    description: '任务参数',
                    example: {
                        description: '测试聚合任务',
                        keywords: ['卫星', '遥感']
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '创建成功',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                task: {
                    type: 'object',
                    properties: {
                        id: { type: 'number', description: '任务ID' },
                        task_type: { type: 'string', description: '任务类型' },
                        status: { type: 'string', description: '任务状态' },
                        start_time: { type: 'string', format: 'date-time', description: '开始时间' },
                        parameters: { type: 'object', description: '任务参数' },
                        createdAt: { type: 'string', format: 'date-time', description: '创建时间' }
                    }
                }
            }
        }
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AggregationTaskController.prototype, "createTask", null);
__decorate([
    (0, common_1.Post)(':id/start'),
    (0, swagger_1.ApiOperation)({
        summary: '开始执行聚合任务',
        description: `
    标记指定ID的聚合任务为执行中状态。
    
    ## 用途
    在任务创建后，可通过此接口将任务状态从pending更新为running，表示任务开始执行。
    `
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '更新成功',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                task: {
                    type: 'object',
                    properties: {
                        id: { type: 'number', description: '任务ID' },
                        task_type: { type: 'string', description: '任务类型' },
                        status: { type: 'string', description: '任务状态' },
                        start_time: { type: 'string', format: 'date-time', description: '开始时间' }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '任务不存在',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: false },
                message: { type: 'string', example: '未找到ID为123的任务' }
            }
        }
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AggregationTaskController.prototype, "startTask", null);
__decorate([
    (0, common_1.Post)(':id/update-progress'),
    (0, swagger_1.ApiOperation)({
        summary: '更新聚合任务进度',
        description: `
    更新指定ID的聚合任务的进度信息。
    
    ## 参数说明
    - progress: 进度百分比 (0-100)
    - processed_records: 已处理的记录数
    `
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['progress', 'processed_records'],
            properties: {
                progress: {
                    type: 'number',
                    description: '进度百分比',
                    minimum: 0,
                    maximum: 100,
                    example: 50
                },
                processed_records: {
                    type: 'number',
                    description: '已处理的记录数',
                    example: 1000
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '更新成功',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                task: {
                    type: 'object',
                    properties: {
                        id: { type: 'number', description: '任务ID' },
                        progress: { type: 'number', description: '进度百分比' },
                        processed_records: { type: 'number', description: '已处理的记录数' }
                    }
                }
            }
        }
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], AggregationTaskController.prototype, "updateTaskProgress", null);
__decorate([
    (0, common_1.Post)(':id/complete'),
    (0, swagger_1.ApiOperation)({
        summary: '完成聚合任务',
        description: `
    标记指定ID的聚合任务为已完成状态。
    
    ## 参数说明
    - aggregated_records: 聚合的记录数
    `
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['aggregated_records'],
            properties: {
                aggregated_records: {
                    type: 'number',
                    description: '聚合的记录数',
                    example: 500
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '更新成功',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                task: {
                    type: 'object',
                    properties: {
                        id: { type: 'number', description: '任务ID' },
                        status: { type: 'string', description: '任务状态' },
                        end_time: { type: 'string', format: 'date-time', description: '结束时间' },
                        aggregated_records: { type: 'number', description: '聚合的记录数' }
                    }
                }
            }
        }
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], AggregationTaskController.prototype, "completeTask", null);
__decorate([
    (0, common_1.Post)(':id/fail'),
    (0, swagger_1.ApiOperation)({
        summary: '标记聚合任务失败',
        description: `
    标记指定ID的聚合任务为失败状态。
    
    ## 参数说明
    - error_message: 错误信息
    `
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['error_message'],
            properties: {
                error_message: {
                    type: 'string',
                    description: '错误信息',
                    example: '聚合过程中遇到数据库连接错误'
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '更新成功',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                task: {
                    type: 'object',
                    properties: {
                        id: { type: 'number', description: '任务ID' },
                        status: { type: 'string', description: '任务状态' },
                        end_time: { type: 'string', format: 'date-time', description: '结束时间' },
                        error_message: { type: 'string', description: '错误信息' }
                    }
                }
            }
        }
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], AggregationTaskController.prototype, "failTask", null);
AggregationTaskController = AggregationTaskController_1 = __decorate([
    (0, common_1.Controller)('tasks/aggregation'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiTags)('聚合任务管理'),
    __metadata("design:paramtypes", [aggregation_task_service_1.AggregationTaskService])
], AggregationTaskController);
exports.AggregationTaskController = AggregationTaskController;
//# sourceMappingURL=aggregation-task.controller.js.map