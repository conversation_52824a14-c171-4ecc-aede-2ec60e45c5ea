import { SatelliteService } from '../services/satellite.service';
import { ConstellationService } from '../services/constellation.service';
import { ConstellationsWithTleResponseDto } from '../dto/constellation-with-tle.dto';
export declare class ConstellationController {
    private readonly satelliteService;
    private readonly constellationService;
    private readonly logger;
    constructor(satelliteService: SatelliteService, constellationService: ConstellationService);
    getConstellations(): Promise<Array<{
        en: string;
        zh: string;
    }>>;
    updateConstellationInfo(): Promise<{
        success: boolean;
        message: string;
        updated: number;
    }>;
    getConstellationsWithTle(): Promise<ConstellationsWithTleResponseDto>;
}
