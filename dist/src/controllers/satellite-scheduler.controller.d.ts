import { SatelliteSchedulerService } from '../services/satellite-scheduler/satellite-scheduler.service';
import { SatelliteSchedulerConfig, SatelliteTaskStatus, SatelliteTaskExecutionResult, SATELLITE_PREDEFINED_CRON_EXPRESSIONS } from '../../config/satellite-scheduler.config';
export declare class SatelliteSchedulerController {
    private readonly schedulerService;
    private readonly logger;
    constructor(schedulerService: SatelliteSchedulerService);
    getTaskStatus(): {
        success: boolean;
        message: string;
        data: {
            currentStatus: SatelliteTaskStatus;
            isRunning: boolean;
            config: SatelliteSchedulerConfig;
            lastExecutionResult: SatelliteTaskExecutionResult | null;
        };
        timestamp: string;
    };
    triggerManualExecution(): Promise<{
        success: boolean;
        message: string;
        result?: SatelliteTaskExecutionResult;
        timestamp: string;
    }>;
    updateConfig(configUpdate: Partial<SatelliteSchedulerConfig>): {
        success: boolean;
        message: string;
        timestamp: string;
    };
    stopRunningTask(): Promise<{
        success: boolean;
        message: string;
        timestamp: string;
    }>;
    getPredefinedCronExpressions(): {
        success: boolean;
        message: string;
        data: typeof SATELLITE_PREDEFINED_CRON_EXPRESSIONS;
        timestamp: string;
    };
}
