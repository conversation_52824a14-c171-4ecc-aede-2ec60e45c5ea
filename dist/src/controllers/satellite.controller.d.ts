import { SatelliteService } from '../services/satellite.service';
import { SatelliteQueryDto } from '../elasticsearch/dto/satellite-query.dto';
import { SatelliteSearchResponse } from '../elasticsearch/types/satellite.types';
import { AggregationTaskService } from '../services/aggregation-task.service';
import { ElasticsearchSatelliteService } from '../elasticsearch/services/elasticsearch.satellite.service';
declare class ExtendedSatelliteQueryDto extends SatelliteQueryDto {
    sort_by?: string;
    sort_dir?: 'asc' | 'desc';
}
export declare class SatelliteController {
    private readonly satelliteService;
    private readonly aggregationTaskService;
    private readonly elasticsearchSatelliteService;
    private readonly logger;
    constructor(satelliteService: SatelliteService, aggregationTaskService: AggregationTaskService, elasticsearchSatelliteService: ElasticsearchSatelliteService);
    searchSatelliteInfo(query: ExtendedSatelliteQueryDto): Promise<SatelliteSearchResponse>;
    getSatelliteNames(): Promise<string[]>;
    getSatelliteStatuses(): Promise<Array<{
        en: string;
        zh: string;
    }>>;
    syncSatellite(name: string): Promise<{
        success: boolean;
        message: string;
    }>;
    syncAllSatellites(): Promise<{
        success: boolean;
        message: string;
    }>;
    aggregateSatelliteData(options: {
        keyword?: string;
        limit?: number;
        saveToDatabase?: boolean;
    }): Promise<{
        success: boolean;
        totalAggregated: number;
        message: string;
    }>;
    clearSatelliteData(): Promise<{
        success: boolean;
        message: string;
    }>;
    getOrbitClassesLocal(): Promise<Array<{
        en: string;
        zh: string;
    }>>;
    getOrbitTypesLocal(): Promise<Array<{
        en: string;
        zh: string;
    }>>;
    getSatelliteById(id: number): Promise<any>;
    getAggregatedSatellites(query: ExtendedSatelliteQueryDto): Promise<SatelliteSearchResponse>;
    testAggregationLogic(): Promise<any>;
    testCustomAggregation(body: {
        testDataFile: string;
    }): Promise<any>;
    aggregateAllSatelliteData(options: {
        limit?: number;
        saveToDatabase?: boolean;
    }): Promise<{
        success: boolean;
        message: string;
    }>;
    incrementalAggregateSatelliteData(options: {
        saveToDatabase?: boolean;
    }): Promise<{
        success: boolean;
        message: string;
    }>;
    updateOrbitInfo(): Promise<{
        success: boolean;
        message: string;
        updated: number;
    }>;
    updateUsersFromES(): Promise<{
        success: boolean;
        message: string;
        updated: number;
    }>;
}
export {};
