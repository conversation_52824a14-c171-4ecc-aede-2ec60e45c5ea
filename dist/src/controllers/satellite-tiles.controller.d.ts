import { SatelliteTilesService } from '../services/satellite-tiles/satellite-tiles.service';
import { GenerateSatelliteTilesDto, GenerateTilesResponseDto, TilesStatusResponseDto } from '../services/satellite-tiles/dto/generate-tiles.dto';
export declare class SatelliteTilesController {
    private readonly tilesService;
    private readonly logger;
    constructor(tilesService: SatelliteTilesService);
    generateSatelliteTiles(generateDto: GenerateSatelliteTilesDto): Promise<GenerateTilesResponseDto>;
    getTilesStatus(): Promise<TilesStatusResponseDto>;
    getSatellitePointCloudData(): Promise<import("../services/satellite-tiles/types").ISatellitePointCloud>;
    getMetadata(): Promise<import("../services/satellite-tiles/types").IPointCloudMetadata>;
}
