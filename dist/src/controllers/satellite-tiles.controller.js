"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SatelliteTilesController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SatelliteTilesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const satellite_tiles_service_1 = require("../services/satellite-tiles/satellite-tiles.service");
const generate_tiles_dto_1 = require("../services/satellite-tiles/dto/generate-tiles.dto");
let SatelliteTilesController = SatelliteTilesController_1 = class SatelliteTilesController {
    constructor(tilesService) {
        this.tilesService = tilesService;
        this.logger = new common_1.Logger(SatelliteTilesController_1.name);
    }
    async generateSatelliteTiles(generateDto) {
        var _a, _b, _c, _d;
        this.logger.log(`接收到手动生成卫星点云请求: ${JSON.stringify(generateDto)}`);
        try {
            const config = {
                outputPath: 'public/tiles',
                colorByOrbitClass: (_a = generateDto.colorByOrbitClass) !== null && _a !== void 0 ? _a : true,
                includeStatistics: (_b = generateDto.includeStatistics) !== null && _b !== void 0 ? _b : true,
                coordinateSystem: (_c = generateDto.coordinateSystem) !== null && _c !== void 0 ? _c : 'cartesian',
                compressionLevel: (_d = generateDto.compressionLevel) !== null && _d !== void 0 ? _d : 6
            };
            const result = await this.tilesService.generateSatellitePointCloudWithPush(generateDto);
            this.logger.log(`卫星点云生成成功: ${result.message}`);
            return {
                success: result.success,
                message: result.message,
                taskId: result.taskId,
                totalSatellites: result.totalSatellites,
                generationDuration: result.generationDuration,
                outputPath: result.outputPath,
                pushResults: result.pushResults,
                generatedFiles: result.generatedFiles,
                fileSizes: result.fileSizes
            };
        }
        catch (error) {
            this.logger.error(`卫星点云生成失败: ${error.message}`, error.stack);
            if (error.message.includes('正在运行中')) {
                throw new common_1.HttpException({
                    success: false,
                    message: error.message,
                    statusCode: common_1.HttpStatus.CONFLICT
                }, common_1.HttpStatus.CONFLICT);
            }
            throw new common_1.HttpException({
                success: false,
                message: error.message,
                statusCode: common_1.HttpStatus.INTERNAL_SERVER_ERROR
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getTilesStatus() {
        this.logger.debug('获取卫星点云生成状态');
        try {
            const currentTask = this.tilesService.getCurrentTaskStatus();
            const metadata = await this.tilesService.getMetadata();
            if (currentTask && currentTask.status === 'running') {
                return {
                    status: 'generating',
                    lastGenerated: (metadata === null || metadata === void 0 ? void 0 : metadata.lastGenerated) || '',
                    nextScheduled: (metadata === null || metadata === void 0 ? void 0 : metadata.nextScheduled) || '',
                    totalSatellites: currentTask.totalSatellites,
                    generationDuration: (metadata === null || metadata === void 0 ? void 0 : metadata.generationDuration) || 0,
                    version: (metadata === null || metadata === void 0 ? void 0 : metadata.version) || '1.0.0'
                };
            }
            else if (currentTask && currentTask.status === 'failed') {
                return {
                    status: 'error',
                    lastGenerated: (metadata === null || metadata === void 0 ? void 0 : metadata.lastGenerated) || '',
                    nextScheduled: (metadata === null || metadata === void 0 ? void 0 : metadata.nextScheduled) || '',
                    totalSatellites: (metadata === null || metadata === void 0 ? void 0 : metadata.totalSatellites) || 0,
                    generationDuration: (metadata === null || metadata === void 0 ? void 0 : metadata.generationDuration) || 0,
                    errorMessage: currentTask.errorMessage,
                    version: (metadata === null || metadata === void 0 ? void 0 : metadata.version) || '1.0.0'
                };
            }
            else if (metadata) {
                return {
                    status: 'idle',
                    lastGenerated: metadata.lastGenerated,
                    nextScheduled: metadata.nextScheduled,
                    totalSatellites: metadata.totalSatellites,
                    generationDuration: metadata.generationDuration,
                    errorMessage: metadata.errorMessage,
                    version: metadata.version
                };
            }
            else {
                return {
                    status: 'idle',
                    lastGenerated: '',
                    nextScheduled: '',
                    totalSatellites: 0,
                    generationDuration: 0,
                    version: '1.0.0'
                };
            }
        }
        catch (error) {
            this.logger.error(`获取点云状态失败: ${error.message}`, error.stack);
            throw new common_1.HttpException({
                success: false,
                message: '获取点云状态失败',
                statusCode: common_1.HttpStatus.INTERNAL_SERVER_ERROR
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getSatellitePointCloudData() {
        this.logger.debug('获取卫星点云数据');
        try {
            const pointCloudData = await this.tilesService.getSatellitePointCloudData();
            if (!pointCloudData) {
                throw new common_1.HttpException({
                    success: false,
                    message: '卫星点云数据文件不存在，请先生成数据',
                    statusCode: common_1.HttpStatus.NOT_FOUND
                }, common_1.HttpStatus.NOT_FOUND);
            }
            return pointCloudData;
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            this.logger.error(`获取卫星点云数据失败: ${error.message}`, error.stack);
            throw new common_1.HttpException({
                success: false,
                message: '获取卫星点云数据失败',
                statusCode: common_1.HttpStatus.INTERNAL_SERVER_ERROR
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getMetadata() {
        this.logger.debug('获取卫星点云元数据');
        try {
            const metadata = await this.tilesService.getMetadata();
            if (!metadata) {
                throw new common_1.HttpException({
                    success: false,
                    message: '元数据文件不存在',
                    statusCode: common_1.HttpStatus.NOT_FOUND
                }, common_1.HttpStatus.NOT_FOUND);
            }
            return metadata;
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            this.logger.error(`获取元数据失败: ${error.message}`, error.stack);
            throw new common_1.HttpException({
                success: false,
                message: '获取元数据失败',
                statusCode: common_1.HttpStatus.INTERNAL_SERVER_ERROR
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
__decorate([
    (0, common_1.Post)('generate'),
    (0, swagger_1.ApiOperation)({
        summary: '手动生成卫星点云数据',
        description: `
    手动触发卫星点云数据生成任务。该接口会：
    1. 获取所有卫星的最新TLE数据
    2. 计算当前时刻所有卫星的位置
    3. 生成3D点云数据文件
    4. 保存到public/tiles目录供前端使用
    
    ## 功能特性
    - 支持按轨道类型自动着色
    - 包含详细的统计信息
    - 支持笛卡尔和地理坐标系
    - 自动生成元数据文件
    
    ## 注意事项
    - 生成过程可能需要几分钟时间
    - 同时只能有一个生成任务运行
    - 生成的文件会覆盖之前的数据
    `
    }),
    (0, swagger_1.ApiBody)({
        type: generate_tiles_dto_1.GenerateSatelliteTilesDto,
        examples: {
            default: {
                summary: '默认配置生成',
                value: {
                    forceRegenerate: false,
                    colorByOrbitClass: true,
                    includeStatistics: true,
                    coordinateSystem: 'cartesian',
                    compressionLevel: 6
                }
            },
            force_regenerate: {
                summary: '强制重新生成',
                value: {
                    forceRegenerate: true,
                    colorByOrbitClass: true,
                    includeStatistics: true,
                    coordinateSystem: 'cartesian',
                    compressionLevel: 9
                }
            },
            geographic_coords: {
                summary: '使用地理坐标系',
                value: {
                    forceRegenerate: false,
                    colorByOrbitClass: true,
                    includeStatistics: true,
                    coordinateSystem: 'geographic',
                    compressionLevel: 6
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '生成成功',
        type: generate_tiles_dto_1.GenerateTilesResponseDto,
        schema: {
            example: {
                success: true,
                message: '成功生成 5000 颗卫星的点云数据',
                taskId: 'task_2025-01-06T14_00_00_000Z',
                totalSatellites: 5000,
                generationDuration: 15000,
                outputPath: '/tiles/satellites.json'
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: '已有任务正在运行',
        schema: {
            example: {
                success: false,
                message: '已有点云生成任务正在运行中，请稍后再试',
                statusCode: 409
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: '生成失败',
        schema: {
            example: {
                success: false,
                message: '获取卫星TLE数据失败',
                statusCode: 500
            }
        }
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [generate_tiles_dto_1.GenerateSatelliteTilesDto]),
    __metadata("design:returntype", Promise)
], SatelliteTilesController.prototype, "generateSatelliteTiles", null);
__decorate([
    (0, common_1.Get)('status'),
    (0, swagger_1.ApiOperation)({
        summary: '获取点云生成状态',
        description: `
    获取当前卫星点云数据的生成状态和元数据信息。
    
    ## 返回信息
    - 生成状态（idle/generating/error）
    - 最后生成时间
    - 下次计划生成时间
    - 卫星总数
    - 生成耗时
    - 错误信息（如果有）
    - 数据版本
    
    ## 用途
    - 监控定时任务执行状态
    - 获取数据更新时间
    - 检查系统健康状态
    `
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        type: generate_tiles_dto_1.TilesStatusResponseDto,
        schema: {
            example: {
                status: 'idle',
                lastGenerated: '2025-01-06T14:00:00Z',
                nextScheduled: '2025-01-06T18:00:00Z',
                totalSatellites: 5000,
                generationDuration: 15000,
                errorMessage: null,
                version: '1.0.0'
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SatelliteTilesController.prototype, "getTilesStatus", null);
__decorate([
    (0, common_1.Get)('satellites'),
    (0, swagger_1.ApiOperation)({
        summary: '获取卫星点云数据',
        description: `
    获取最新生成的卫星点云数据文件内容。
    
    ## 数据格式
    返回包含所有卫星位置、属性和颜色信息的JSON数据：
    - 生成时间和计算时间
    - 卫星总数
    - 卫星列表（位置、属性、颜色）
    - 统计信息（轨道类型分布、高度范围）
    
    ## 用途
    - 前端3D可视化渲染
    - 数据分析和统计
    - 第三方应用集成
    
    ## 注意事项
    - 数据量可能较大，建议使用压缩传输
    - 数据每4小时更新一次
    - 如果没有生成过数据，返回404
    `
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        schema: {
            type: 'object',
            properties: {
                generatedAt: { type: 'string', format: 'date-time', example: '2025-01-06T14:00:00Z' },
                totalSatellites: { type: 'number', example: 5000 },
                calculationTime: { type: 'string', format: 'date-time', example: '2025-01-06T14:00:00Z' },
                satellites: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'string', example: '25544' },
                            name: { type: 'string', example: 'ISS (ZARYA)' },
                            position: {
                                type: 'object',
                                properties: {
                                    x: { type: 'number', example: 1234567.89 },
                                    y: { type: 'number', example: 2345678.90 },
                                    z: { type: 'number', example: 3456789.01 }
                                }
                            },
                            geographic: {
                                type: 'object',
                                properties: {
                                    longitude: { type: 'number', example: 120.5 },
                                    latitude: { type: 'number', example: 30.2 },
                                    altitude: { type: 'number', example: 408000 }
                                }
                            },
                            properties: {
                                type: 'object',
                                properties: {
                                    noradId: { type: 'number', example: 25544 },
                                    orbitClass: { type: 'string', example: 'LEO' },
                                    inclination: { type: 'number', example: 51.64 }
                                }
                            },
                            color: {
                                type: 'object',
                                properties: {
                                    r: { type: 'number', example: 0 },
                                    g: { type: 'number', example: 255 },
                                    b: { type: 'number', example: 0 }
                                }
                            }
                        }
                    }
                },
                statistics: {
                    type: 'object',
                    properties: {
                        orbitClassDistribution: {
                            type: 'object',
                            example: { 'LEO': 3000, 'MEO': 1500, 'GEO': 500 }
                        },
                        altitudeRange: {
                            type: 'object',
                            properties: {
                                min: { type: 'number', example: 200000 },
                                max: { type: 'number', example: 36000000 },
                                average: { type: 'number', example: 800000 }
                            }
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '数据不存在',
        schema: {
            example: {
                success: false,
                message: '卫星点云数据文件不存在，请先生成数据',
                statusCode: 404
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SatelliteTilesController.prototype, "getSatellitePointCloudData", null);
__decorate([
    (0, common_1.Get)('metadata'),
    (0, swagger_1.ApiOperation)({
        summary: '获取点云元数据',
        description: `
    获取卫星点云数据的元数据信息，包括生成时间、状态等。
    
    ## 返回信息
    - 最后生成时间
    - 下次计划生成时间
    - 卫星总数
    - 生成耗时
    - 生成状态
    - 错误信息（如果有）
    - 数据版本
    
    ## 用途
    - 检查数据是否为最新
    - 监控系统运行状态
    - 获取数据统计信息
    `
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        schema: {
            type: 'object',
            properties: {
                lastGenerated: { type: 'string', format: 'date-time', example: '2025-01-06T14:00:00Z' },
                nextScheduled: { type: 'string', format: 'date-time', example: '2025-01-06T18:00:00Z' },
                totalSatellites: { type: 'number', example: 5000 },
                generationDuration: { type: 'number', example: 15000 },
                status: { type: 'string', enum: ['idle', 'generating', 'error'], example: 'idle' },
                errorMessage: { type: 'string', example: null },
                version: { type: 'string', example: '1.0.0' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '元数据不存在',
        schema: {
            example: {
                success: false,
                message: '元数据文件不存在',
                statusCode: 404
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SatelliteTilesController.prototype, "getMetadata", null);
SatelliteTilesController = SatelliteTilesController_1 = __decorate([
    (0, common_1.Controller)('tiles'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiTags)('卫星3D Tiles点云'),
    __metadata("design:paramtypes", [satellite_tiles_service_1.SatelliteTilesService])
], SatelliteTilesController);
exports.SatelliteTilesController = SatelliteTilesController;
//# sourceMappingURL=satellite-tiles.controller.js.map