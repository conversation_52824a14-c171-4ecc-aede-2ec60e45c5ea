import { AggregationTaskService } from '../services/aggregation-task.service';
import { AggregationTask } from '../entities/aggregation-task.entity';
export declare class AggregationTaskController {
    private readonly taskService;
    private readonly logger;
    constructor(taskService: AggregationTaskService);
    getTasks(page?: number, limit?: number): Promise<{
        total: number;
        tasks: AggregationTask[];
    }>;
    getLatestTask(): Promise<AggregationTask | {
        success: boolean;
        message: string;
    }>;
    getRunningTasks(): Promise<AggregationTask[]>;
    getTaskById(id: number): Promise<AggregationTask | {
        success: boolean;
        message: string;
    }>;
    createTask(taskData: {
        task_type: string;
        parameters?: any;
    }): Promise<{
        success: boolean;
        task: AggregationTask;
    }>;
    startTask(id: number): Promise<{
        success: boolean;
        task: AggregationTask;
    }>;
    updateTaskProgress(id: number, data: {
        progress: number;
        processed_records: number;
    }): Promise<{
        success: boolean;
        task: AggregationTask;
    }>;
    completeTask(id: number, data: {
        aggregated_records: number;
    }): Promise<{
        success: boolean;
        task: AggregationTask;
    }>;
    failTask(id: number, data: {
        error_message: string;
    }): Promise<{
        success: boolean;
        task: AggregationTask;
    }>;
}
