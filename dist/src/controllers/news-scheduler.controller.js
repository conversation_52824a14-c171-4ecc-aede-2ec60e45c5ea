"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var NewsSchedulerController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.NewsSchedulerController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const news_scheduler_service_1 = require("../services/news-scheduler/news-scheduler.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const news_scheduler_config_1 = require("../../config/news-scheduler.config");
let NewsSchedulerController = NewsSchedulerController_1 = class NewsSchedulerController {
    constructor(schedulerService) {
        this.schedulerService = schedulerService;
        this.logger = new common_1.Logger(NewsSchedulerController_1.name);
    }
    getTaskStatus() {
        this.logger.log('获取新闻定时任务状态');
        try {
            const status = this.schedulerService.getTaskStatus();
            return {
                success: true,
                message: '获取任务状态成功',
                data: status,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`获取任务状态失败: ${error.message}`, error.stack);
            return {
                success: false,
                message: `获取任务状态失败: ${error.message}`,
                data: {
                    currentStatus: news_scheduler_config_1.TaskStatus.FAILED,
                    isRunning: false,
                    config: {},
                    lastExecutionResults: []
                },
                timestamp: new Date().toISOString()
            };
        }
    }
    async triggerManualExecution() {
        this.logger.log('收到手动触发新闻处理任务请求');
        try {
            const result = await this.schedulerService.triggerManualExecution();
            return {
                success: result.success,
                message: result.message,
                results: result.results,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`手动触发任务失败: ${error.message}`, error.stack);
            return {
                success: false,
                message: `手动触发任务失败: ${error.message}`,
                timestamp: new Date().toISOString()
            };
        }
    }
    updateConfig(configUpdate) {
        this.logger.log(`收到配置更新请求: ${JSON.stringify(configUpdate)}`);
        try {
            this.schedulerService.updateConfig(configUpdate);
            return {
                success: true,
                message: '配置更新成功',
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`配置更新失败: ${error.message}`, error.stack);
            return {
                success: false,
                message: `配置更新失败: ${error.message}`,
                timestamp: new Date().toISOString()
            };
        }
    }
    async stopRunningTask() {
        this.logger.log('收到停止任务请求');
        try {
            await this.schedulerService.stopRunningTask();
            return {
                success: true,
                message: '任务已停止',
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`停止任务失败: ${error.message}`, error.stack);
            return {
                success: false,
                message: `停止任务失败: ${error.message}`,
                timestamp: new Date().toISOString()
            };
        }
    }
    getPredefinedCronExpressions() {
        this.logger.log('获取预定义cron表达式');
        return {
            success: true,
            message: '获取cron表达式成功',
            data: news_scheduler_config_1.PREDEFINED_CRON_EXPRESSIONS,
            timestamp: new Date().toISOString()
        };
    }
};
__decorate([
    (0, common_1.Get)('status'),
    (0, swagger_1.ApiOperation)({
        summary: '获取定时任务状态',
        description: '获取当前新闻处理定时任务的运行状态、配置信息和最近执行结果'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '成功获取任务状态',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: '获取任务状态成功' },
                data: {
                    type: 'object',
                    properties: {
                        currentStatus: { type: 'string', enum: ['idle', 'running', 'completed', 'failed'] },
                        isRunning: { type: 'boolean' },
                        config: {
                            type: 'object',
                            properties: {
                                enabled: { type: 'boolean' },
                                cronExpression: { type: 'string' },
                                timezone: { type: 'string' },
                                translationConfig: { type: 'object' },
                                themeExtractionConfig: { type: 'object' }
                            }
                        },
                        lastExecutionResults: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    taskType: { type: 'string', enum: ['translation', 'theme_extraction'] },
                                    status: { type: 'string', enum: ['idle', 'running', 'completed', 'failed'] },
                                    startTime: { type: 'string', format: 'date-time' },
                                    endTime: { type: 'string', format: 'date-time' },
                                    duration: { type: 'number' },
                                    statistics: { type: 'object' },
                                    error: { type: 'string' }
                                }
                            }
                        }
                    }
                },
                timestamp: { type: 'string', format: 'date-time' }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Object)
], NewsSchedulerController.prototype, "getTaskStatus", null);
__decorate([
    (0, common_1.Post)('trigger'),
    (0, swagger_1.ApiOperation)({
        summary: '手动触发新闻处理任务',
        description: '手动触发新闻翻译和主题提取任务的执行，任务将在后台异步执行'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '任务触发成功',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: '新闻处理任务执行成功' },
                results: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            taskType: { type: 'string', enum: ['translation', 'theme_extraction'] },
                            status: { type: 'string', enum: ['idle', 'running', 'completed', 'failed'] },
                            startTime: { type: 'string', format: 'date-time' },
                            endTime: { type: 'string', format: 'date-time' },
                            duration: { type: 'number' },
                            statistics: { type: 'object' },
                            error: { type: 'string' }
                        }
                    }
                },
                timestamp: { type: 'string', format: 'date-time' }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], NewsSchedulerController.prototype, "triggerManualExecution", null);
__decorate([
    (0, common_1.Post)('config'),
    (0, swagger_1.ApiOperation)({
        summary: '更新定时任务配置',
        description: '动态更新新闻处理定时任务的配置，包括执行时间、批次大小等参数'
    }),
    (0, swagger_1.ApiBody)({
        description: '配置更新参数',
        schema: {
            type: 'object',
            properties: {
                enabled: { type: 'boolean', description: '是否启用定时任务' },
                cronExpression: { type: 'string', description: 'cron表达式，定义执行时间' },
                timezone: { type: 'string', description: '时区' },
                translationConfig: {
                    type: 'object',
                    properties: {
                        batchSize: { type: 'number', description: '翻译批次大小' },
                        maxDocs: { type: 'number', description: '最大处理文档数' },
                        forceReprocess: { type: 'boolean', description: '是否强制重新处理' },
                        specificIndexes: { type: 'array', items: { type: 'string' }, description: '指定处理的索引' },
                        llmMode: { type: 'string', enum: ['default', 'high_quality', 'fast'], description: '大模型模式' },
                        customModel: { type: 'string', description: '自定义模型名称' }
                    }
                },
                themeExtractionConfig: {
                    type: 'object',
                    properties: {
                        batchSize: { type: 'number', description: '主题提取批次大小' },
                        maxDocs: { type: 'number', description: '最大处理文档数' },
                        forceReprocess: { type: 'boolean', description: '是否强制重新处理' },
                        specificIndexes: { type: 'array', items: { type: 'string' }, description: '指定处理的索引' },
                        llmMode: { type: 'string', enum: ['default', 'high_quality', 'fast'], description: '大模型模式' },
                        customModel: { type: 'string', description: '自定义模型名称' }
                    }
                },
                maxRetries: { type: 'number', description: '最大重试次数' },
                retryDelay: { type: 'number', description: '重试延迟时间（毫秒）' }
            }
        },
        examples: {
            '更新执行时间': {
                summary: '更新定时任务执行时间',
                description: '将执行时间改为每天6:00、14:00、22:00',
                value: {
                    cronExpression: '0 6,14,22 * * *'
                }
            },
            '更新批次配置': {
                summary: '更新批次处理配置',
                description: '调整翻译和主题提取的批次大小',
                value: {
                    translationConfig: {
                        batchSize: 30,
                        llmMode: 'fast'
                    },
                    themeExtractionConfig: {
                        batchSize: 25,
                        llmMode: 'high_quality'
                    }
                }
            },
            '禁用定时任务': {
                summary: '禁用定时任务',
                description: '临时禁用定时任务的自动执行',
                value: {
                    enabled: false
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '配置更新成功',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: '配置更新成功' },
                timestamp: { type: 'string', format: 'date-time' }
            }
        }
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Object)
], NewsSchedulerController.prototype, "updateConfig", null);
__decorate([
    (0, common_1.Post)('stop'),
    (0, swagger_1.ApiOperation)({
        summary: '停止正在运行的任务',
        description: '紧急停止当前正在运行的新闻处理任务'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '任务停止成功',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: '任务已停止' },
                timestamp: { type: 'string', format: 'date-time' }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], NewsSchedulerController.prototype, "stopRunningTask", null);
__decorate([
    (0, common_1.Get)('cron-expressions'),
    (0, swagger_1.ApiOperation)({
        summary: '获取预定义的cron表达式',
        description: '获取系统预定义的常用cron表达式，方便用户选择合适的执行时间'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '成功获取cron表达式列表',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: '获取cron表达式成功' },
                data: {
                    type: 'object',
                    additionalProperties: { type: 'string' }
                },
                timestamp: { type: 'string', format: 'date-time' }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Object)
], NewsSchedulerController.prototype, "getPredefinedCronExpressions", null);
NewsSchedulerController = NewsSchedulerController_1 = __decorate([
    (0, common_1.Controller)('api/news-scheduler'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiTags)('新闻定时任务'),
    __metadata("design:paramtypes", [news_scheduler_service_1.NewsSchedulerService])
], NewsSchedulerController);
exports.NewsSchedulerController = NewsSchedulerController;
//# sourceMappingURL=news-scheduler.controller.js.map