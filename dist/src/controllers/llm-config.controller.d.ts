import { UpdateLLMConfigDto, LLMConfigResponseDto, ConfigType, TestConnectionDto, TestConnectionResponseDto, ResetConfigDto, ConfigStatsDto } from '../dto/llm-config.dto';
import { LLMConfigService } from '../services/llm-config.service';
export declare class LLMConfigController {
    private readonly llmConfigService;
    constructor(llmConfigService: LLMConfigService);
    getAllConfigs(): Promise<LLMConfigResponseDto[]>;
    getConfig(configType: ConfigType): Promise<LLMConfigResponseDto>;
    updateConfig(updateDto: UpdateLLMConfigDto): Promise<LLMConfigResponseDto>;
    testConnection(testDto: TestConnectionDto): Promise<TestConnectionResponseDto>;
    resetConfig(resetDto: ResetConfigDto): Promise<LLMConfigResponseDto>;
    getStats(): Promise<ConfigStatsDto>;
    clearStats(): Promise<void>;
    exportConfigs(includeSensitive?: boolean): Promise<any>;
}
