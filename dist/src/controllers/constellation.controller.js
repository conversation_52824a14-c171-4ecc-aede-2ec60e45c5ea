"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ConstellationController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConstellationController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const satellite_service_1 = require("../services/satellite.service");
const constellation_service_1 = require("../services/constellation.service");
const constellation_with_tle_dto_1 = require("../dto/constellation-with-tle.dto");
let ConstellationController = ConstellationController_1 = class ConstellationController {
    constructor(satelliteService, constellationService) {
        this.satelliteService = satelliteService;
        this.constellationService = constellationService;
        this.logger = new common_1.Logger(ConstellationController_1.name);
    }
    async getConstellations() {
        this.logger.log('接收到获取卫星星座集合请求（本地数据库）');
        return this.satelliteService.getSatelliteConstellationsLocal();
    }
    async updateConstellationInfo() {
        this.logger.log('接收到更新卫星星座信息请求');
        return this.satelliteService.updateSatelliteConstellationInfo();
    }
    async getConstellationsWithTle() {
        this.logger.log('接收到获取具有TLE轨道信息的卫星星座列表请求');
        const result = await this.constellationService.getConstellationsWithTle();
        this.logger.debug(`查询结果: 找到${result.total}个星座`);
        return result;
    }
};
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: '获取卫星星座集合',
        description: `
    从本地数据库中获取卫星星座集合。
    
    ## 返回数据说明
    返回星座集合，每个星座包含英文名称和中文名称。
    
    ## 示例
    \`\`\`json
    [
      {
        "en": "Starlink",
        "zh": "星链"
      },
      {
        "en": "OneWeb",
        "zh": "万维网"
      }
    ]
    \`\`\`
    `
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    en: { type: 'string', description: '英文名称', example: 'Starlink' },
                    zh: { type: 'string', description: '中文名称', example: '星链' }
                }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ConstellationController.prototype, "getConstellations", null);
__decorate([
    (0, common_1.Post)('update'),
    (0, swagger_1.ApiOperation)({
        summary: '更新卫星星座信息',
        description: `
    从ES数据库获取星座信息并更新本地数据库中的卫星星座字段。
    
    ## 更新方式
    1. 从卫星名称中提取星座信息
    2. 从ES数据库中的constell_n2yo索引获取星座成员信息，通过norad_id、cospar_id、name进行匹配
    
    ## 返回数据说明
    - success: 是否成功
    - message: 更新结果消息
    - updated: 更新的卫星数量
    `
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '更新成功',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: '成功更新100个卫星的星座信息' },
                updated: { type: 'number', example: 100 }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ConstellationController.prototype, "updateConstellationInfo", null);
__decorate([
    (0, common_1.Get)('with-tle'),
    (0, swagger_1.ApiOperation)({
        summary: '获取具有TLE轨道信息的卫星星座列表',
        description: '从本地PostgreSQL数据库中查询所有具有TLE轨道信息的卫星星座及其包含的卫星数量'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        type: constellation_with_tle_dto_1.ConstellationsWithTleResponseDto
    }),
    (0, swagger_1.ApiTags)('数据库查询'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ConstellationController.prototype, "getConstellationsWithTle", null);
ConstellationController = ConstellationController_1 = __decorate([
    (0, common_1.Controller)('local/constellation'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiTags)('本地卫星信息'),
    __metadata("design:paramtypes", [satellite_service_1.SatelliteService,
        constellation_service_1.ConstellationService])
], ConstellationController);
exports.ConstellationController = ConstellationController;
//# sourceMappingURL=constellation.controller.js.map