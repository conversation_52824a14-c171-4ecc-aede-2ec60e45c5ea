import { NewsSchedulerService } from '../services/news-scheduler/news-scheduler.service';
import { NewsSchedulerConfig, TaskStatus, TaskExecutionResult, PREDEFINED_CRON_EXPRESSIONS } from '../../config/news-scheduler.config';
export declare class NewsSchedulerController {
    private readonly schedulerService;
    private readonly logger;
    constructor(schedulerService: NewsSchedulerService);
    getTaskStatus(): {
        success: boolean;
        message: string;
        data: {
            currentStatus: TaskStatus;
            isRunning: boolean;
            config: NewsSchedulerConfig;
            lastExecutionResults: TaskExecutionResult[];
        };
        timestamp: string;
    };
    triggerManualExecution(): Promise<{
        success: boolean;
        message: string;
        results?: TaskExecutionResult[];
        timestamp: string;
    }>;
    updateConfig(configUpdate: Partial<NewsSchedulerConfig>): {
        success: boolean;
        message: string;
        timestamp: string;
    };
    stopRunningTask(): Promise<{
        success: boolean;
        message: string;
        timestamp: string;
    }>;
    getPredefinedCronExpressions(): {
        success: boolean;
        message: string;
        data: typeof PREDEFINED_CRON_EXPRESSIONS;
        timestamp: string;
    };
}
