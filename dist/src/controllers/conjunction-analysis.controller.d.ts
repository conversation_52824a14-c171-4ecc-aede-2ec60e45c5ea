import { ConjunctionAnalysisService } from '../services/conjunction-analysis/conjunction-analysis.service';
import { ConjunctionAnalysisRequestDto, ConjunctionAnalysisResponseDto } from '../services/conjunction-analysis/dto/conjunction-analysis.dto';
export declare class ConjunctionAnalysisController {
    private readonly conjunctionAnalysisService;
    constructor(conjunctionAnalysisService: ConjunctionAnalysisService);
    analyzeConjunctions(request: ConjunctionAnalysisRequestDto): Promise<ConjunctionAnalysisResponseDto>;
}
