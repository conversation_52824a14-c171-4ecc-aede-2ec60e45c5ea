import { PassAnalysisService } from '../services/pass-analysis/pass-analysis.service';
import { PassAnalysisRequestDto, PassAnalysisResponseDto } from '../services/pass-analysis/dto/pass-analysis.dto';
export declare class PassAnalysisController {
    private readonly passAnalysisService;
    constructor(passAnalysisService: PassAnalysisService);
    analyzePass(request: PassAnalysisRequestDto): Promise<PassAnalysisResponseDto>;
}
