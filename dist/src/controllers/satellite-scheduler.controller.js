"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SatelliteSchedulerController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SatelliteSchedulerController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const satellite_scheduler_service_1 = require("../services/satellite-scheduler/satellite-scheduler.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const satellite_scheduler_config_1 = require("../../config/satellite-scheduler.config");
let SatelliteSchedulerController = SatelliteSchedulerController_1 = class SatelliteSchedulerController {
    constructor(schedulerService) {
        this.schedulerService = schedulerService;
        this.logger = new common_1.Logger(SatelliteSchedulerController_1.name);
    }
    getTaskStatus() {
        this.logger.log('获取卫星数据定时任务状态');
        const status = this.schedulerService.getTaskStatus();
        return {
            success: true,
            message: '获取任务状态成功',
            data: status,
            timestamp: new Date().toISOString()
        };
    }
    async triggerManualExecution() {
        this.logger.log('收到手动触发卫星数据增量聚合任务请求');
        try {
            const result = await this.schedulerService.triggerManualExecution();
            return {
                success: result.success,
                message: result.message,
                result: result.result,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`手动触发任务失败: ${error.message}`, error.stack);
            return {
                success: false,
                message: `手动触发任务失败: ${error.message}`,
                timestamp: new Date().toISOString()
            };
        }
    }
    updateConfig(configUpdate) {
        this.logger.log(`收到配置更新请求: ${JSON.stringify(configUpdate)}`);
        try {
            this.schedulerService.updateConfig(configUpdate);
            return {
                success: true,
                message: '配置更新成功',
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`配置更新失败: ${error.message}`, error.stack);
            return {
                success: false,
                message: `配置更新失败: ${error.message}`,
                timestamp: new Date().toISOString()
            };
        }
    }
    async stopRunningTask() {
        this.logger.log('收到停止任务请求');
        try {
            await this.schedulerService.stopRunningTask();
            return {
                success: true,
                message: '任务已停止',
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`停止任务失败: ${error.message}`, error.stack);
            return {
                success: false,
                message: `停止任务失败: ${error.message}`,
                timestamp: new Date().toISOString()
            };
        }
    }
    getPredefinedCronExpressions() {
        this.logger.log('获取预定义cron表达式');
        return {
            success: true,
            message: '获取cron表达式成功',
            data: satellite_scheduler_config_1.SATELLITE_PREDEFINED_CRON_EXPRESSIONS,
            timestamp: new Date().toISOString()
        };
    }
};
__decorate([
    (0, common_1.Get)('status'),
    (0, swagger_1.ApiOperation)({
        summary: '获取卫星数据定时任务状态',
        description: '获取当前卫星数据增量聚合定时任务的运行状态、配置信息和最近执行结果'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '成功获取任务状态',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: '获取任务状态成功' },
                data: {
                    type: 'object',
                    properties: {
                        currentStatus: { type: 'string', enum: ['idle', 'running', 'completed', 'failed'] },
                        isRunning: { type: 'boolean' },
                        config: {
                            type: 'object',
                            properties: {
                                enabled: { type: 'boolean' },
                                cronExpression: { type: 'string' },
                                timezone: { type: 'string' },
                                maxRetries: { type: 'number' },
                                retryDelay: { type: 'number' },
                                saveToDatabase: { type: 'boolean' }
                            }
                        },
                        lastExecutionResult: {
                            type: 'object',
                            properties: {
                                taskType: { type: 'string', example: 'satellite_incremental_aggregate' },
                                status: { type: 'string', enum: ['idle', 'running', 'completed', 'failed'] },
                                startTime: { type: 'string', format: 'date-time' },
                                endTime: { type: 'string', format: 'date-time' },
                                duration: { type: 'number' },
                                totalNewAggregated: { type: 'number' },
                                error: { type: 'string' }
                            }
                        }
                    }
                },
                timestamp: { type: 'string', format: 'date-time' }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Object)
], SatelliteSchedulerController.prototype, "getTaskStatus", null);
__decorate([
    (0, common_1.Post)('trigger'),
    (0, swagger_1.ApiOperation)({
        summary: '手动触发卫星数据增量聚合任务',
        description: '手动触发卫星数据增量聚合任务的执行，任务将在后台异步执行。该任务会调用 /local/satellite/incremental-aggregate 接口更新本地卫星数据。'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '任务触发成功',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: '手动触发任务执行成功，新增聚合 10 条卫星数据' },
                result: {
                    type: 'object',
                    properties: {
                        taskType: { type: 'string', example: 'satellite_incremental_aggregate' },
                        status: { type: 'string', enum: ['idle', 'running', 'completed', 'failed'] },
                        startTime: { type: 'string', format: 'date-time' },
                        endTime: { type: 'string', format: 'date-time' },
                        duration: { type: 'number' },
                        totalNewAggregated: { type: 'number' },
                        error: { type: 'string' }
                    }
                },
                timestamp: { type: 'string', format: 'date-time' }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SatelliteSchedulerController.prototype, "triggerManualExecution", null);
__decorate([
    (0, common_1.Post)('config'),
    (0, swagger_1.ApiOperation)({
        summary: '更新卫星数据定时任务配置',
        description: '动态更新卫星数据增量聚合定时任务的配置，包括执行时间、重试次数等参数'
    }),
    (0, swagger_1.ApiBody)({
        description: '配置更新参数',
        schema: {
            type: 'object',
            properties: {
                enabled: { type: 'boolean', description: '是否启用定时任务' },
                cronExpression: { type: 'string', description: 'cron表达式，定义执行时间' },
                timezone: { type: 'string', description: '时区' },
                maxRetries: { type: 'number', description: '最大重试次数' },
                retryDelay: { type: 'number', description: '重试延迟时间（毫秒）' },
                saveToDatabase: { type: 'boolean', description: '是否保存到数据库' }
            }
        },
        examples: {
            '更新执行时间': {
                summary: '更新定时任务执行时间',
                description: '将执行时间改为每天2:00',
                value: {
                    cronExpression: '0 2 * * *'
                }
            },
            '更新为每12小时执行': {
                summary: '更新为每12小时执行',
                description: '将执行时间改为每天3:00和15:00',
                value: {
                    cronExpression: '0 3,15 * * *'
                }
            },
            '禁用定时任务': {
                summary: '禁用定时任务',
                description: '临时禁用定时任务的自动执行',
                value: {
                    enabled: false
                }
            },
            '更新重试配置': {
                summary: '更新重试配置',
                description: '调整最大重试次数和重试延迟',
                value: {
                    maxRetries: 5,
                    retryDelay: 600000
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '配置更新成功',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: '配置更新成功' },
                timestamp: { type: 'string', format: 'date-time' }
            }
        }
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Object)
], SatelliteSchedulerController.prototype, "updateConfig", null);
__decorate([
    (0, common_1.Post)('stop'),
    (0, swagger_1.ApiOperation)({
        summary: '停止正在运行的卫星数据增量聚合任务',
        description: '紧急停止当前正在运行的卫星数据增量聚合任务'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '任务停止成功',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: '任务已停止' },
                timestamp: { type: 'string', format: 'date-time' }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SatelliteSchedulerController.prototype, "stopRunningTask", null);
__decorate([
    (0, common_1.Get)('cron-expressions'),
    (0, swagger_1.ApiOperation)({
        summary: '获取预定义的cron表达式',
        description: '获取系统预定义的常用cron表达式，方便用户选择合适的执行时间'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '成功获取cron表达式列表',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: '获取cron表达式成功' },
                data: {
                    type: 'object',
                    additionalProperties: { type: 'string' },
                    example: {
                        'DAILY_3AM': '0 3 * * *',
                        'DAILY_2AM': '0 2 * * *',
                        'EVERY_12_HOURS': '0 3,15 * * *'
                    }
                },
                timestamp: { type: 'string', format: 'date-time' }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Object)
], SatelliteSchedulerController.prototype, "getPredefinedCronExpressions", null);
SatelliteSchedulerController = SatelliteSchedulerController_1 = __decorate([
    (0, common_1.Controller)('api/satellite-scheduler'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiTags)('卫星数据定时任务'),
    __metadata("design:paramtypes", [satellite_scheduler_service_1.SatelliteSchedulerService])
], SatelliteSchedulerController);
exports.SatelliteSchedulerController = SatelliteSchedulerController;
//# sourceMappingURL=satellite-scheduler.controller.js.map