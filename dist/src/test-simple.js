function processContractor(contractor) {
    const knownTranslations = {
        'Zhejiang University': { cn: '浙江大学', en: 'Zhejiang University' },
        'Zhongke Xingrui': { cn: '中科星睿', en: '<PERSON>hong<PERSON> Xingrui' },
        'Zhuhai Orbita Aerospace Science and Technology Co.': { cn: '珠海欧比特宇航科技股份有限公司', en: 'Zhuhai Orbita Aerospace Science and Technology Co.' },
        'Zhuhai Orbita Control Engineering Co. Ltd.': { cn: '珠海欧比特控制工程有限公司', en: 'Zhuhai Orbita Control Engineering Co. Ltd.' },
        'Beijing Institute of Satellite Information Engineering': { cn: '北京卫星信息工程研究所', en: 'Beijing Institute of Satellite Information Engineering' },
        'Beijing Aerospace Propulsion Institute': { cn: '北京航天动力研究所', en: 'Beijing Aerospace Propulsion Institute' },
        'Beijing SPACE VIEW Technology Co., Ltd': { cn: '北京天目创新科技有限公司', en: 'Beijing SPACE VIEW Technology Co., Ltd' },
        'Beijing Smart Satellite Technology Co. Ltd.': { cn: '北京智星卫星科技有限公司', en: 'Beijing Smart Satellite Technology Co. Ltd.' },
        'Shanghai Academy of Spaceflight Technology': { cn: '上海航天技术研究院', en: 'Shanghai Academy of Spaceflight Technology' },
        'Harbin Institute of Technology': { cn: '哈尔滨工业大学', en: 'Harbin Institute of Technology' },
        'Wuhan University': { cn: '武汉大学', en: 'Wuhan University' },
        'Tsinghua University': { cn: '清华大学', en: 'Tsinghua University' },
        'China Academy of Sciences': { cn: '中国科学院', en: 'China Academy of Sciences' },
        'Academy of Military Science': { cn: '中国人民解放军军事科学院', en: 'Academy of Military Science' }
    };
    const partialMatches = [
        { pattern: /Zhejiang University/i, translation: { cn: '浙江大学', en: 'Zhejiang University' } },
        { pattern: /Zhongke/i, translation: { cn: '中科', en: 'Zhongke' } },
        { pattern: /Zhuhai Orbita/i, translation: { cn: '珠海欧比特', en: 'Zhuhai Orbita' } },
        { pattern: /Wuhan University/i, translation: { cn: '武汉大学', en: 'Wuhan University' } },
        { pattern: /Shanghai Academy/i, translation: { cn: '上海航天技术研究院', en: 'Shanghai Academy' } },
        { pattern: /Harbin Institute/i, translation: { cn: '哈尔滨工业大学', en: 'Harbin Institute' } },
        { pattern: /Tsinghua University/i, translation: { cn: '清华大学', en: 'Tsinghua University' } },
        { pattern: /(Chinese|China) Academy of Sciences?/i, translation: { cn: '中国科学院', en: 'Chinese Academy of Sciences' } },
        { pattern: /Academy of Military Science/i, translation: { cn: '中国人民解放军军事科学院', en: 'Academy of Military Science' } }
    ];
    if (knownTranslations[contractor]) {
        return knownTranslations[contractor];
    }
    for (const { pattern, translation } of partialMatches) {
        if (pattern.test(contractor)) {
            return {
                cn: translation.cn,
                en: contractor
            };
        }
    }
    const isEnglish = /[a-zA-Z]/.test(contractor);
    const isChinese = /[\u4e00-\u9fa5]/.test(contractor);
    if (isEnglish && !isChinese) {
        if (/China|Chinese/i.test(contractor)) {
            return {
                cn: `中国${contractor.replace(/China|Chinese/i, '').trim()}`,
                en: contractor
            };
        }
        else if (/^Shanghai/i.test(contractor)) {
            return {
                cn: `上海${contractor.replace(/Shanghai/i, '').trim()}`,
                en: contractor
            };
        }
        else if (/^Shenzhen/i.test(contractor)) {
            return {
                cn: `深圳${contractor.replace(/Shenzhen/i, '').trim()}`,
                en: contractor
            };
        }
        else if (/^Zhuhai/i.test(contractor)) {
            return {
                cn: `珠海${contractor.replace(/Zhuhai/i, '').trim()}`,
                en: contractor
            };
        }
        else if (/^Nanjing/i.test(contractor)) {
            return {
                cn: `南京${contractor.replace(/Nanjing/i, '').trim()}`,
                en: contractor
            };
        }
        else if (/^Xi'an/i.test(contractor)) {
            return {
                cn: `西安${contractor.replace(/Xi'an/i, '').trim()}`,
                en: contractor
            };
        }
        else if (/^Wuhan/i.test(contractor)) {
            return {
                cn: `武汉${contractor.replace(/Wuhan/i, '').trim()}`,
                en: contractor
            };
        }
        else if (/^Tianjin/i.test(contractor)) {
            return {
                cn: `天津${contractor.replace(/Tianjin/i, '').trim()}`,
                en: contractor
            };
        }
        else if (/^Harbin/i.test(contractor)) {
            return {
                cn: `哈尔滨${contractor.replace(/Harbin/i, '').trim()}`,
                en: contractor
            };
        }
        else if (/^Hangzhou|Zhejiang/i.test(contractor)) {
            return {
                cn: `浙江${contractor.replace(/Hangzhou|Zhejiang/i, '').trim()}`,
                en: contractor
            };
        }
        else if (/^Zhongke/i.test(contractor)) {
            return {
                cn: `中科${contractor.replace(/Zhongke/i, '').trim()}`,
                en: contractor
            };
        }
        else {
            return { cn: contractor, en: contractor };
        }
    }
    else if (isChinese && !isEnglish) {
        return { cn: contractor, en: contractor };
    }
    else if (isChinese && isEnglish) {
        return { cn: contractor, en: contractor };
    }
    else {
        return { cn: contractor, en: contractor };
    }
}
const testContractors = [
    'Zhejiang University',
    'Zhongke Xingrui',
    'Zhuhai Orbita Aerospace Science and Technology Co.',
    'Zhuhai Orbita Control Engineering Co. Ltd.',
    'Beijing Institute of Satellite Information Engineering',
    'Beijing Aerospace Propulsion Institute',
    'Beijing SPACE VIEW Technology Co., Ltd',
    'Beijing Smart Satellite Technology Co. Ltd.',
    'Shanghai Academy of Spaceflight Technology',
    'Harbin Institute of Technology',
    'Wuhan University',
    'Tsinghua University',
    'China Academy of Sciences',
    'Academy of Military Science'
];
const results = testContractors.map(contractor => {
    const translation = processContractor(contractor);
    return {
        original: contractor,
        cn: translation.cn,
        en: translation.en
    };
});
console.log('翻译处理结果:');
console.table(results);
//# sourceMappingURL=test-simple.js.map