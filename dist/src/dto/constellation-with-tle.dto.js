"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConstellationsWithTleResponseDto = exports.ConstellationWithTleDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ConstellationWithTleDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '星座名称',
        example: 'Starlink'
    }),
    __metadata("design:type", String)
], ConstellationWithTleDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '该星座下有TLE轨道信息的卫星数量',
        example: 42
    }),
    __metadata("design:type", Number)
], ConstellationWithTleDto.prototype, "satelliteCount", void 0);
exports.ConstellationWithTleDto = ConstellationWithTleDto;
class ConstellationsWithTleResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '星座总数',
        example: 5
    }),
    __metadata("design:type", Number)
], ConstellationsWithTleResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: [ConstellationWithTleDto],
        description: '星座列表'
    }),
    __metadata("design:type", Array)
], ConstellationsWithTleResponseDto.prototype, "constellations", void 0);
exports.ConstellationsWithTleResponseDto = ConstellationsWithTleResponseDto;
//# sourceMappingURL=constellation-with-tle.dto.js.map