export declare enum LLMProvider {
    QWEN = "qwen",
    OPENAI = "openai",
    CLAUDE = "claude",
    GLM = "glm"
}
export declare enum ConfigType {
    TRANSLATION = "translation",
    THEME_EXTRACTION = "theme_extraction"
}
export declare class UpdateLLMConfigDto {
    configType: ConfigType;
    provider?: LLMProvider;
    model?: string;
    apiKey?: string;
    baseURL?: string;
    systemPrompt?: string;
    maxTokens?: number;
    temperature?: number;
    timeout?: number;
    maxRetries?: number;
    retryDelay?: number;
    maxConcurrentRequests?: number;
}
export declare class LLMConfigResponseDto {
    configType: ConfigType;
    provider: LLMProvider;
    model: string;
    baseURL: string;
    apiKey: string;
    systemPrompt: string;
    maxTokens: number;
    temperature: number;
    timeout: number;
    maxRetries: number;
    retryDelay: number;
    maxConcurrentRequests: number;
    lastUpdated: string;
}
export declare class TestConnectionDto {
    configType: ConfigType;
    testText?: string;
}
export declare class TestConnectionResponseDto {
    success: boolean;
    responseTime: number;
    result?: string;
    error?: string;
    timestamp: string;
}
export declare class ResetConfigDto {
    configType: ConfigType;
    confirm?: boolean;
}
export declare class ConfigStatsDto {
    translation: {
        totalRequests: number;
        successfulRequests: number;
        failedRequests: number;
        averageResponseTime: number;
        lastUsed: string;
    };
    themeExtraction: {
        totalRequests: number;
        successfulRequests: number;
        failedRequests: number;
        averageResponseTime: number;
        lastUsed: string;
    };
    timestamp: string;
}
