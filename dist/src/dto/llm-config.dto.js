"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigStatsDto = exports.ResetConfigDto = exports.TestConnectionResponseDto = exports.TestConnectionDto = exports.LLMConfigResponseDto = exports.UpdateLLMConfigDto = exports.ConfigType = exports.LLMProvider = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
var LLMProvider;
(function (LLMProvider) {
    LLMProvider["QWEN"] = "qwen";
    LLMProvider["OPENAI"] = "openai";
    LLMProvider["CLAUDE"] = "claude";
    LLMProvider["GLM"] = "glm";
})(LLMProvider = exports.LLMProvider || (exports.LLMProvider = {}));
var ConfigType;
(function (ConfigType) {
    ConfigType["TRANSLATION"] = "translation";
    ConfigType["THEME_EXTRACTION"] = "theme_extraction";
})(ConfigType = exports.ConfigType || (exports.ConfigType = {}));
class UpdateLLMConfigDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '配置类型',
        enum: ConfigType,
        example: ConfigType.TRANSLATION
    }),
    (0, class_validator_1.IsEnum)(ConfigType),
    __metadata("design:type", String)
], UpdateLLMConfigDto.prototype, "configType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '大模型提供商',
        enum: LLMProvider,
        example: LLMProvider.QWEN
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(LLMProvider),
    __metadata("design:type", String)
], UpdateLLMConfigDto.prototype, "provider", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '模型名称',
        example: 'qwen-turbo'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateLLMConfigDto.prototype, "model", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'API密钥',
        example: 'sk-1b581fd6f319419d9d0e3f2cc855c10d'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateLLMConfigDto.prototype, "apiKey", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'API基础URL',
        example: 'https://dashscope.aliyuncs.com/compatible-mode/v1'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateLLMConfigDto.prototype, "baseURL", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '系统提示词',
        example: '你是一位专业翻译，特别是很擅长在航空航天领域的翻译，将以下内容准确翻译为中文，保留数字和专有名词格式。'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateLLMConfigDto.prototype, "systemPrompt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '最大令牌数',
        example: 4000,
        minimum: 100,
        maximum: 32000
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(100),
    (0, class_validator_1.Max)(32000),
    __metadata("design:type", Number)
], UpdateLLMConfigDto.prototype, "maxTokens", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '温度参数（控制随机性）',
        example: 0.1,
        minimum: 0,
        maximum: 2
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(2),
    __metadata("design:type", Number)
], UpdateLLMConfigDto.prototype, "temperature", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '请求超时时间（毫秒）',
        example: 30000,
        minimum: 5000,
        maximum: 300000
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(5000),
    (0, class_validator_1.Max)(300000),
    __metadata("design:type", Number)
], UpdateLLMConfigDto.prototype, "timeout", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '最大重试次数',
        example: 2,
        minimum: 0,
        maximum: 10
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(10),
    __metadata("design:type", Number)
], UpdateLLMConfigDto.prototype, "maxRetries", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '重试延迟时间（毫秒）',
        example: 1000,
        minimum: 100,
        maximum: 10000
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(100),
    (0, class_validator_1.Max)(10000),
    __metadata("design:type", Number)
], UpdateLLMConfigDto.prototype, "retryDelay", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '最大并发请求数',
        example: 3,
        minimum: 1,
        maximum: 10
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(10),
    __metadata("design:type", Number)
], UpdateLLMConfigDto.prototype, "maxConcurrentRequests", void 0);
exports.UpdateLLMConfigDto = UpdateLLMConfigDto;
class LLMConfigResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '配置类型',
        enum: ConfigType
    }),
    __metadata("design:type", String)
], LLMConfigResponseDto.prototype, "configType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '大模型提供商',
        enum: LLMProvider
    }),
    __metadata("design:type", String)
], LLMConfigResponseDto.prototype, "provider", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '模型名称'
    }),
    __metadata("design:type", String)
], LLMConfigResponseDto.prototype, "model", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'API基础URL'
    }),
    __metadata("design:type", String)
], LLMConfigResponseDto.prototype, "baseURL", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'API密钥（已脱敏）'
    }),
    __metadata("design:type", String)
], LLMConfigResponseDto.prototype, "apiKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '系统提示词'
    }),
    __metadata("design:type", String)
], LLMConfigResponseDto.prototype, "systemPrompt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最大令牌数'
    }),
    __metadata("design:type", Number)
], LLMConfigResponseDto.prototype, "maxTokens", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '温度参数'
    }),
    __metadata("design:type", Number)
], LLMConfigResponseDto.prototype, "temperature", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '请求超时时间（毫秒）'
    }),
    __metadata("design:type", Number)
], LLMConfigResponseDto.prototype, "timeout", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最大重试次数'
    }),
    __metadata("design:type", Number)
], LLMConfigResponseDto.prototype, "maxRetries", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '重试延迟时间（毫秒）'
    }),
    __metadata("design:type", Number)
], LLMConfigResponseDto.prototype, "retryDelay", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最大并发请求数'
    }),
    __metadata("design:type", Number)
], LLMConfigResponseDto.prototype, "maxConcurrentRequests", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '配置最后更新时间'
    }),
    __metadata("design:type", String)
], LLMConfigResponseDto.prototype, "lastUpdated", void 0);
exports.LLMConfigResponseDto = LLMConfigResponseDto;
class TestConnectionDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '配置类型',
        enum: ConfigType,
        example: ConfigType.TRANSLATION
    }),
    (0, class_validator_1.IsEnum)(ConfigType),
    __metadata("design:type", String)
], TestConnectionDto.prototype, "configType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '测试文本',
        example: 'Hello world'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TestConnectionDto.prototype, "testText", void 0);
exports.TestConnectionDto = TestConnectionDto;
class TestConnectionResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '连接测试是否成功'
    }),
    __metadata("design:type", Boolean)
], TestConnectionResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '响应时间（毫秒）'
    }),
    __metadata("design:type", Number)
], TestConnectionResponseDto.prototype, "responseTime", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '测试结果'
    }),
    __metadata("design:type", String)
], TestConnectionResponseDto.prototype, "result", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '错误信息'
    }),
    __metadata("design:type", String)
], TestConnectionResponseDto.prototype, "error", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '测试时间'
    }),
    __metadata("design:type", String)
], TestConnectionResponseDto.prototype, "timestamp", void 0);
exports.TestConnectionResponseDto = TestConnectionResponseDto;
class ResetConfigDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '配置类型',
        enum: ConfigType,
        example: ConfigType.TRANSLATION
    }),
    (0, class_validator_1.IsEnum)(ConfigType),
    __metadata("design:type", String)
], ResetConfigDto.prototype, "configType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '是否确认重置',
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ResetConfigDto.prototype, "confirm", void 0);
exports.ResetConfigDto = ResetConfigDto;
class ConfigStatsDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '翻译配置信息'
    }),
    __metadata("design:type", Object)
], ConfigStatsDto.prototype, "translation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '主题提取配置信息'
    }),
    __metadata("design:type", Object)
], ConfigStatsDto.prototype, "themeExtraction", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '统计时间'
    }),
    __metadata("design:type", String)
], ConfigStatsDto.prototype, "timestamp", void 0);
exports.ConfigStatsDto = ConfigStatsDto;
//# sourceMappingURL=llm-config.dto.js.map