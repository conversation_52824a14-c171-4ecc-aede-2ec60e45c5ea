"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var SatellitePurposeService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SatellitePurposeService = void 0;
const common_1 = require("@nestjs/common");
let SatellitePurposeService = SatellitePurposeService_1 = class SatellitePurposeService {
    constructor() {
        this.logger = new common_1.Logger(SatellitePurposeService_1.name);
    }
    async executeQuery(query) {
        return {
            success: true,
            rows: []
        };
    }
    async getSatellitePurposes() {
        try {
            const mainCategories = [
                { en: 'Amateur Radio', cn: '业余无线电' },
                { en: 'Astronomy', cn: '天文学' },
                { en: 'Communications', cn: '通信' },
                { en: 'Data Relay', cn: '数据中继' },
                { en: 'Earth Observation', cn: '地球观测' },
                { en: 'Earth Science', cn: '地球科学' },
                { en: 'Education', cn: '教育' },
                { en: 'Geodesy', cn: '大地测量' },
                { en: 'Ionospheric Research', cn: '电离层研究' },
                { en: 'Magnetospheric Research', cn: '磁层研究' },
                { en: 'Meteorology', cn: '气象' },
                { en: 'Microgravity Research', cn: '微重力研究' },
                { en: 'Military', cn: '军事' },
                { en: 'Navigation', cn: '导航' },
                { en: 'Optical', cn: '光学' },
                { en: 'Platform', cn: '平台' },
                { en: 'Radar', cn: '雷达' },
                { en: 'Radar Calibration', cn: '雷达校准' },
                { en: 'Remote Sensing', cn: '遥感' },
                { en: 'Satellite Servicing', cn: '卫星服务' },
                { en: 'Science', cn: '科学' },
                { en: 'Search & Rescue', cn: '搜索救援' },
                { en: 'Solar Research', cn: '太阳研究' },
                { en: 'Space Burial', cn: '太空安葬' },
                { en: 'Space Exploration', cn: '太空探索' },
                { en: 'Space Station', cn: '空间站' },
                { en: 'Technology Demonstration', cn: '技术验证' },
                { en: 'Unknown', cn: '未知' },
                { en: 'UV Research', cn: '紫外线研究' },
                { en: 'X-ray Research', cn: 'X射线研究' }
            ];
            this.logger.log(`获取到 ${mainCategories.length} 个卫星用途`);
            return {
                success: true,
                purposes: mainCategories
            };
        }
        catch (error) {
            this.logger.error(`Error fetching satellite purposes: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to fetch satellite purposes');
        }
    }
};
SatellitePurposeService = SatellitePurposeService_1 = __decorate([
    (0, common_1.Injectable)()
], SatellitePurposeService);
exports.SatellitePurposeService = SatellitePurposeService;
async function testSatellitePurposes() {
    const service = new SatellitePurposeService();
    const result = await service.getSatellitePurposes();
    console.log('获取到卫星用途:', result.purposes.length);
    console.log(JSON.stringify(result, null, 2));
}
//# sourceMappingURL=newPurposeMethod.js.map