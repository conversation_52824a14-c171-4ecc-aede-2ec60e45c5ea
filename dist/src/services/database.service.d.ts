import { OnModuleInit } from '@nestjs/common';
import { EntityManager } from 'typeorm';
export declare class DatabaseService implements OnModuleInit {
    private readonly entityManager;
    private readonly logger;
    constructor(entityManager: EntityManager);
    onModuleInit(): Promise<void>;
    executeQuery(sql: string, parameters?: any[]): Promise<any>;
    validateQuery(sql: string): {
        isValid: boolean;
        message?: string;
    };
    filterSatellites(filterDto: any): Promise<any>;
    private extractNestedValues;
    private processMatchResults;
    private calculateSimilarity;
    private longestCommonSubsequence;
    getSatelliteUsers(): Promise<any>;
    getSatellitePurposes(): Promise<any>;
    getLaunchContractors(): Promise<any>;
    getLaunchSites(): Promise<any>;
    getLaunchVehicles(): Promise<any>;
}
