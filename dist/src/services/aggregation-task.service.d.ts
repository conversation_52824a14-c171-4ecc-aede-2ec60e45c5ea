import { Repository } from 'typeorm';
import { AggregationTask } from '../entities/aggregation-task.entity';
export declare class AggregationTaskService {
    private taskRepository;
    private readonly logger;
    constructor(taskRepository: Repository<AggregationTask>);
    createTask(options: {
        task_type: string;
        parameters: any;
    }): Promise<AggregationTask>;
    startTask(taskId: number): Promise<AggregationTask>;
    updateTaskProgress(taskId: number, progress: number, processedRecords: number): Promise<AggregationTask>;
    completeTask(taskId: number, aggregatedRecords: number): Promise<AggregationTask>;
    failTask(taskId: number, errorMessage: string): Promise<AggregationTask>;
    getTasks(page?: number, limit?: number): Promise<{
        tasks: AggregationTask[];
        total: number;
    }>;
    getTaskById(taskId: number): Promise<AggregationTask>;
    getRunningTasks(): Promise<AggregationTask[]>;
    getLatestTask(): Promise<AggregationTask | null>;
}
