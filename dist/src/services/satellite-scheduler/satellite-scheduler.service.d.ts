import { SatelliteService } from '../satellite.service';
import { SatelliteSchedulerConfig, SatelliteTaskStatus, SatelliteTaskExecutionResult } from '../../../config/satellite-scheduler.config';
export declare class SatelliteSchedulerService {
    private readonly satelliteService;
    private readonly logger;
    private readonly config;
    private currentStatus;
    private lastExecutionResult;
    private isRunning;
    constructor(satelliteService: SatelliteService);
    scheduledSatelliteIncrementalAggregate(): Promise<void>;
    executeSatelliteIncrementalAggregate(): Promise<SatelliteTaskExecutionResult>;
    triggerManualExecution(): Promise<{
        success: boolean;
        message: string;
        result?: SatelliteTaskExecutionResult;
    }>;
    getTaskStatus(): {
        currentStatus: SatelliteTaskStatus;
        isRunning: boolean;
        config: SatelliteSchedulerConfig;
        lastExecutionResult: SatelliteTaskExecutionResult | null;
    };
    updateConfig(configUpdate: Partial<SatelliteSchedulerConfig>): void;
    stopRunningTask(): Promise<void>;
}
