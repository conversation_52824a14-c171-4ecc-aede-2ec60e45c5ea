{"version": 3, "file": "satellite-scheduler.service.js", "sourceRoot": "", "sources": ["../../../../src/services/satellite-scheduler/satellite-scheduler.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,+CAAwC;AACxC,4DAAwD;AACxD,2FAMoD;AAO7C,IAAM,yBAAyB,iCAA/B,MAAM,yBAAyB;IASpC,YACmB,gBAAkC;QAAlC,qBAAgB,GAAhB,gBAAgB,CAAkB;QATpC,WAAM,GAAG,IAAI,eAAM,CAAC,2BAAyB,CAAC,IAAI,CAAC,CAAC;QAI7D,kBAAa,GAAwB,gDAAmB,CAAC,IAAI,CAAC;QAC9D,wBAAmB,GAAwC,IAAI,CAAC;QAChE,cAAS,GAAG,KAAK,CAAC;QAMxB,IAAI,CAAC,MAAM,GAAG,IAAA,wDAA2B,GAAE,CAAC;QAG5C,IAAI;YACF,IAAA,6DAAgC,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,IAAI,CAAC,SAAS,CAAC;gBACpD,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;gBAC5B,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc;gBAC1C,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;gBAC9B,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc;aAC3C,CAAC,EAAE,CAAC,CAAC;SACP;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAUK,AAAN,KAAK,CAAC,sCAAsC;QAE1C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YACxB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;YACtC,OAAO;SACR;QAGD,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAC3C,OAAO;SACR;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;QAE1C,IAAI;YACF,MAAM,IAAI,CAAC,oCAAoC,EAAE,CAAC;SACnD;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;SAClD;IACH,CAAC;IAMD,KAAK,CAAC,oCAAoC;QACxC,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,IAAI,MAAM,GAAiC;YACzC,QAAQ,EAAE,iCAAiC;YAC3C,MAAM,EAAE,gDAAmB,CAAC,OAAO;YACnC,SAAS;SACV,CAAC;QAEF,IAAI;YAEF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,aAAa,GAAG,gDAAmB,CAAC,OAAO,CAAC;YAEjD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;YAGrC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,iCAAiC,CAAC;gBACpF,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc;aAC3C,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;YAGzD,MAAM,mCACD,MAAM,KACT,MAAM,EAAE,gDAAmB,CAAC,SAAS,EACrC,OAAO;gBACP,QAAQ,EACR,kBAAkB,EAAE,eAAe,CAAC,kBAAkB,GACvD,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,eAAe,CAAC,OAAO,SAAS,QAAQ,IAAI,CAAC,CAAC;SAElF;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;YAGzD,MAAM,mCACD,MAAM,KACT,MAAM,EAAE,gDAAmB,CAAC,MAAM,EAClC,OAAO;gBACP,QAAQ,EACR,KAAK,EAAE,KAAK,CAAC,OAAO,GACrB,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,KAAK,CAAC,OAAO,SAAS,QAAQ,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;SAKvF;gBAAS;YAER,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC;YACnC,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC;SACnC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAMD,KAAK,CAAC,sBAAsB;QAK1B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;QAGtC,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,uBAAuB;aACjC,CAAC;SACH;QAED,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oCAAoC,EAAE,CAAC;YAEjE,OAAO;gBACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,gDAAmB,CAAC,SAAS;gBACxD,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,gDAAmB,CAAC,SAAS;oBACtD,CAAC,CAAC,mBAAmB,MAAM,CAAC,kBAAkB,QAAQ;oBACtD,CAAC,CAAC,eAAe,MAAM,CAAC,KAAK,EAAE;gBACjC,MAAM;aACP,CAAC;SACH;QAAC,OAAO,KAAU,EAAE;YACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAE7D,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,aAAa,KAAK,CAAC,OAAO,EAAE;aACtC,CAAC;SACH;IACH,CAAC;IAMD,aAAa;QAMX,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;SAC9C,CAAC;IACJ,CAAC;IAMD,YAAY,CAAC,YAA+C;QAC1D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QAGjE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAGzC,IAAI;YACF,IAAA,6DAAgC,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;SAC3B;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9C,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAKD,KAAK,CAAC,eAAe;QACnB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAC/B,OAAO;SACR;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QAMrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;IAC9C,CAAC;CACF,CAAA;AAtLO;IAJL,IAAA,eAAI,EAAC,WAAW,EAAE;QACjB,IAAI,EAAE,mCAAmC;QACzC,QAAQ,EAAE,eAAe;KAC1B,CAAC;;;;uFAqBD;AA1DU,yBAAyB;IADrC,IAAA,mBAAU,GAAE;qCAW0B,oCAAgB;GAV1C,yBAAyB,CA4NrC;AA5NY,8DAAyB"}