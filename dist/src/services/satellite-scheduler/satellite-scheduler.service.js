"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SatelliteSchedulerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SatelliteSchedulerService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const satellite_service_1 = require("../satellite.service");
const satellite_scheduler_config_1 = require("../../../config/satellite-scheduler.config");
let SatelliteSchedulerService = SatelliteSchedulerService_1 = class SatelliteSchedulerService {
    constructor(satelliteService) {
        this.satelliteService = satelliteService;
        this.logger = new common_1.Logger(SatelliteSchedulerService_1.name);
        this.currentStatus = satellite_scheduler_config_1.SatelliteTaskStatus.IDLE;
        this.lastExecutionResult = null;
        this.isRunning = false;
        this.config = (0, satellite_scheduler_config_1.getSatelliteSchedulerConfig)();
        try {
            (0, satellite_scheduler_config_1.validateSatelliteSchedulerConfig)(this.config);
            this.logger.log(`卫星数据定时任务服务初始化完成，配置: ${JSON.stringify({
                enabled: this.config.enabled,
                cronExpression: this.config.cronExpression,
                timezone: this.config.timezone,
                saveToDatabase: this.config.saveToDatabase
            })}`);
        }
        catch (error) {
            this.logger.error(`卫星数据定时任务配置验证失败: ${error.message}`);
            throw error;
        }
    }
    async scheduledSatelliteIncrementalAggregate() {
        if (!this.config.enabled) {
            this.logger.debug('卫星数据定时任务已禁用，跳过执行');
            return;
        }
        if (this.isRunning) {
            this.logger.warn('卫星数据增量聚合任务正在运行中，跳过本次执行');
            return;
        }
        this.logger.log('🕐 卫星数据定时任务触发：开始执行增量聚合');
        try {
            await this.executeSatelliteIncrementalAggregate();
        }
        catch (error) {
            this.logger.error('❌ 卫星数据定时任务执行失败', error.stack);
        }
    }
    async executeSatelliteIncrementalAggregate() {
        const startTime = new Date();
        let result = {
            taskType: 'satellite_incremental_aggregate',
            status: satellite_scheduler_config_1.SatelliteTaskStatus.RUNNING,
            startTime
        };
        try {
            this.isRunning = true;
            this.currentStatus = satellite_scheduler_config_1.SatelliteTaskStatus.RUNNING;
            this.logger.log('🚀 开始执行卫星数据增量聚合任务');
            const aggregateResult = await this.satelliteService.incrementalAggregateSatelliteData({
                saveToDatabase: this.config.saveToDatabase
            });
            const endTime = new Date();
            const duration = endTime.getTime() - startTime.getTime();
            result = Object.assign(Object.assign({}, result), { status: satellite_scheduler_config_1.SatelliteTaskStatus.COMPLETED, endTime,
                duration, totalNewAggregated: aggregateResult.totalNewAggregated });
            this.logger.log(`✅ 卫星数据增量聚合任务完成: ${aggregateResult.message}, 耗时: ${duration}ms`);
        }
        catch (error) {
            const endTime = new Date();
            const duration = endTime.getTime() - startTime.getTime();
            result = Object.assign(Object.assign({}, result), { status: satellite_scheduler_config_1.SatelliteTaskStatus.FAILED, endTime,
                duration, error: error.message });
            this.logger.error(`❌ 卫星数据增量聚合任务失败: ${error.message}, 耗时: ${duration}ms`, error.stack);
        }
        finally {
            this.isRunning = false;
            this.currentStatus = result.status;
            this.lastExecutionResult = result;
        }
        return result;
    }
    async triggerManualExecution() {
        this.logger.log('收到手动触发卫星数据增量聚合任务请求');
        if (this.isRunning) {
            return {
                success: false,
                message: '卫星数据增量聚合任务正在运行中，请稍后再试'
            };
        }
        try {
            const result = await this.executeSatelliteIncrementalAggregate();
            return {
                success: result.status === satellite_scheduler_config_1.SatelliteTaskStatus.COMPLETED,
                message: result.status === satellite_scheduler_config_1.SatelliteTaskStatus.COMPLETED
                    ? `手动触发任务执行成功，新增聚合 ${result.totalNewAggregated} 条卫星数据`
                    : `手动触发任务执行失败: ${result.error}`,
                result
            };
        }
        catch (error) {
            this.logger.error(`手动触发任务失败: ${error.message}`, error.stack);
            return {
                success: false,
                message: `手动触发任务失败: ${error.message}`
            };
        }
    }
    getTaskStatus() {
        return {
            currentStatus: this.currentStatus,
            isRunning: this.isRunning,
            config: this.config,
            lastExecutionResult: this.lastExecutionResult
        };
    }
    updateConfig(configUpdate) {
        this.logger.log(`更新卫星数据定时任务配置: ${JSON.stringify(configUpdate)}`);
        Object.assign(this.config, configUpdate);
        try {
            (0, satellite_scheduler_config_1.validateSatelliteSchedulerConfig)(this.config);
            this.logger.log('配置更新成功');
        }
        catch (error) {
            this.logger.error(`配置更新失败: ${error.message}`);
            throw error;
        }
    }
    async stopRunningTask() {
        if (!this.isRunning) {
            this.logger.log('当前没有正在运行的任务');
            return;
        }
        this.logger.log('停止正在运行的卫星数据增量聚合任务');
        this.logger.warn('任务停止请求已发送，任务将在当前批次完成后停止');
    }
};
__decorate([
    (0, schedule_1.Cron)('0 3 * * *', {
        name: 'satelliteIncrementalAggregateTask',
        timeZone: 'Asia/Shanghai'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SatelliteSchedulerService.prototype, "scheduledSatelliteIncrementalAggregate", null);
SatelliteSchedulerService = SatelliteSchedulerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [satellite_service_1.SatelliteService])
], SatelliteSchedulerService);
exports.SatelliteSchedulerService = SatelliteSchedulerService;
//# sourceMappingURL=satellite-scheduler.service.js.map