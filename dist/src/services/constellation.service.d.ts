import { Repository } from 'typeorm';
import { Satellite } from '../entities/satellite.entity';
import { ConstellationsWithTleResponseDto } from '../dto/constellation-with-tle.dto';
export declare class ConstellationService {
    private readonly satelliteRepository;
    private readonly logger;
    constructor(satelliteRepository: Repository<Satellite>);
    getConstellationsWithTle(): Promise<ConstellationsWithTleResponseDto>;
}
