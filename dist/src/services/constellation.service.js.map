{"version": 3, "file": "constellation.service.js", "sourceRoot": "", "sources": ["../../../src/services/constellation.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,6CAAmD;AACnD,qCAAqC;AACrC,mEAAyD;AACzD,kFAA8G;AAOvG,IAAM,oBAAoB,4BAA1B,MAAM,oBAAoB;IAG/B,YAEmB,mBAA0C;QAA1C,wBAAmB,GAAnB,mBAAmB,CAAuB;QAJ5C,WAAM,GAAG,IAAI,eAAM,CAAC,sBAAoB,CAAC,IAAI,CAAC,CAAC;IAK7D,CAAC;IAMJ,KAAK,CAAC,wBAAwB;QAC5B,IAAI;YACF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YAGpC,MAAM,KAAK,GAAG;;;;;;;;;;;OAWb,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,KAAK,EAAE,CAAC,CAAC;YAEvC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAE/D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,UAAU,CAAC,MAAM,iBAAiB,CAAC,CAAC;YAG3D,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;YAC1D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,UAAU,EAAE,CAAC,CAAC;YAG7C,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;gBACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE;oBACvD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAC,CAAC,UAAU,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;oBACvD,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE;wBAChC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;qBAC5E;oBACD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;iBAC3E;aACF;YAGD,MAAM,kBAAkB,GAAG;gBACzB,UAAU;gBACV,QAAQ;gBACR,aAAa;gBACb,cAAc;gBACd,QAAQ;gBACR,SAAS;gBACT,YAAY;gBACZ,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,MAAM;gBACN,KAAK;gBACL,KAAK;gBACL,UAAU;gBACV,UAAU;gBACV,UAAU;gBACV,QAAQ;gBACR,SAAS;gBACT,SAAS;gBACT,KAAK;gBACL,OAAO;gBACP,OAAO;gBACP,UAAU;gBACV,SAAS;gBACT,UAAU;gBACV,SAAS;gBACT,UAAU;gBACV,QAAQ;gBACR,OAAO;aACR,CAAC;YAGF,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAAkB,CAAC;YACrD,IAAI,cAAc,GAAG,CAAC,CAAC;YAEvB,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;gBAClC,IAAI;oBACF,IAAI,kBAAkB,GAAG,KAAK,CAAC;oBAG/B,IAAI,SAAS,CAAC,aAAa,EAAE;wBAC3B,IAAI,kBAAkB,CAAC;wBAEvB,IAAI,OAAO,SAAS,CAAC,aAAa,KAAK,QAAQ,EAAE;4BAC/C,IAAI;gCACF,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;6BAC1D;4BAAC,OAAO,CAAC,EAAE;gCACV,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,SAAS,CAAC,EAAE,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;6BAC7D;yBACF;6BAAM,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE;4BACjD,kBAAkB,GAAG,SAAS,CAAC,aAAa,CAAC;yBAC9C;6BAAM,IAAI,OAAO,SAAS,CAAC,aAAa,KAAK,QAAQ,IAAI,SAAS,CAAC,aAAa,KAAK,IAAI,EAAE;4BAC1F,kBAAkB,GAAG,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;yBAChD;wBAED,IAAI,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;4BACtE,KAAK,MAAM,IAAI,IAAI,kBAAkB,EAAE;gCACrC,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;oCACrE,MAAM,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;oCAC5C,IAAI,iBAAiB,EAAE;wCACrB,MAAM,KAAK,GAAG,kBAAkB,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wCAC7D,kBAAkB,CAAC,GAAG,CAAC,iBAAiB,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;wCACrD,kBAAkB,GAAG,IAAI,CAAC;qCAC3B;iCACF;6BACF;yBACF;qBACF;oBAGD,MAAM,oBAAoB,GAAG,CAAC,KAAU,EAAY,EAAE;wBACpD,MAAM,KAAK,GAAa,EAAE,CAAC;wBAE3B,IAAI,CAAC,KAAK;4BAAE,OAAO,KAAK,CAAC;wBAEzB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;4BAC7B,IAAI;gCACF,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gCACjC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;oCACzB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wCACpB,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK;4CAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;oCAC5D,CAAC,CAAC,CAAC;iCACJ;6BACF;4BAAC,OAAO,CAAC,EAAE;gCACV,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;6BACnB;yBACF;6BAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;4BAC/B,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gCACnB,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK;oCAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;4BAC5D,CAAC,CAAC,CAAC;yBACJ;6BAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE;4BACtD,IAAI,KAAK,CAAC,KAAK;gCAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;yBACrD;wBAED,OAAO,KAAK,CAAC;oBACf,CAAC,CAAC;oBAGF,MAAM,oBAAoB,GAA2B;wBACnD,OAAO,EAAE,QAAQ;wBACjB,cAAc,EAAE,aAAa;wBAC7B,QAAQ,EAAE,eAAe;wBACzB,SAAS,EAAE,QAAQ;wBACnB,gBAAgB,EAAE,QAAQ;qBAC3B,CAAC;oBAGF,IAAI,CAAC,kBAAkB,EAAE;wBAEvB,MAAM,QAAQ,GAAG;4BACf,GAAG,oBAAoB,CAAC,SAAS,CAAC,cAAc,CAAC;4BACjD,GAAG,oBAAoB,CAAC,SAAS,CAAC,gBAAgB,CAAC;yBACpD,CAAC;wBAEF,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;4BACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,SAAS,CAAC,EAAE,UAAU,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;4BAErE,KAAK,MAAM,aAAa,IAAI,QAAQ,EAAE;gCACpC,IAAI,CAAC,aAAa;oCAAE,SAAS;gCAG7B,KAAK,MAAM,IAAI,IAAI,kBAAkB,EAAE;oCACrC,IAAI,aAAa,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE;wCAE5D,MAAM,UAAU,GAAG,oBAAoB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;wCACtD,MAAM,KAAK,GAAG,kBAAkB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;wCACtD,kBAAkB,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;wCAC9C,kBAAkB,GAAG,IAAI,CAAC;wCAE1B,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,OAAO,CAAC,IAAI,cAAc,GAAG,CAAC,EAAE;4CACjE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,SAAS,CAAC,EAAE,QAAQ,aAAa,QAAQ,IAAI,EAAE,CAAC,CAAC;yCACtF;wCAED,MAAM;qCACP;iCACF;gCAED,IAAI,kBAAkB;oCAAE,MAAM;6BAC/B;yBACF;qBACF;oBAED,IAAI,kBAAkB,EAAE;wBACtB,cAAc,EAAE,CAAC;qBAClB;iBACF;gBAAC,OAAO,KAAK,EAAE;oBACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,SAAS,CAAC,EAAE,aAAa,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;iBACnE;aACF;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,cAAc,OAAO,CAAC,CAAC;YACjD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,kBAAkB,CAAC,IAAI,QAAQ,CAAC,CAAC;YAGzD,MAAM,4BAA4B,GAAG,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,4BAA4B,EAAE,CAAC,CAAC;YAG5D,MAAM,cAAc,GAA8B,EAAE,CAAC;YACrD,kBAAkB,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;gBAEzC,IAAI,IAAI,KAAK,gBAAgB,IAAI,IAAI,KAAK,SAAS,EAAE;oBACnD,cAAc,CAAC,IAAI,CAAC;wBAClB,IAAI;wBACJ,cAAc,EAAE,KAAK;qBACtB,CAAC,CAAC;iBACJ;YACH,CAAC,CAAC,CAAC;YAGH,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC,cAAc,CAAC,CAAC;YAEnE,MAAM,QAAQ,GAAG,IAAI,6DAAgC,EAAE,CAAC;YACxD,QAAQ,CAAC,KAAK,GAAG,cAAc,CAAC,MAAM,CAAC;YACvC,QAAQ,CAAC,cAAc,GAAG,cAAc,CAAC;YAEzC,OAAO,QAAQ,CAAC;SACjB;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACzE,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SACzD;IACH,CAAC;CACF,CAAA;AA5OY,oBAAoB;IADhC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,4BAAS,CAAC,CAAA;qCACU,oBAAU;GALvC,oBAAoB,CA4OhC;AA5OY,oDAAoB"}