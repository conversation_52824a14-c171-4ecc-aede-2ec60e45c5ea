"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var DatabaseService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
let DatabaseService = DatabaseService_1 = class DatabaseService {
    constructor(entityManager) {
        this.entityManager = entityManager;
        this.logger = new common_1.Logger(DatabaseService_1.name);
    }
    async onModuleInit() {
        try {
            this.logger.log('正在检查并启用pg_trgm扩展...');
            await this.entityManager.query('CREATE EXTENSION IF NOT EXISTS pg_trgm;');
            this.logger.log('pg_trgm扩展已启用');
        }
        catch (error) {
            this.logger.error(`启用pg_trgm扩展失败: ${error.message}`, error.stack);
            this.logger.warn('相似性搜索功能可能无法正常工作，请手动启用pg_trgm扩展');
        }
    }
    async executeQuery(sql, parameters = []) {
        try {
            this.logger.debug(`执行SQL查询: ${sql}`);
            this.logger.debug(`参数: ${JSON.stringify(parameters)}`);
            const result = await this.entityManager.query(sql, parameters);
            this.logger.debug(`查询成功，返回 ${result.length} 条记录`);
            return {
                success: true,
                rows: result,
                rowCount: result.length,
                query: sql
            };
        }
        catch (error) {
            this.logger.error(`SQL查询执行失败: ${error.message}`, error.stack);
            throw new Error(`SQL查询执行失败: ${error.message}`);
        }
    }
    validateQuery(sql) {
        const sqlLower = sql.toLowerCase();
        if (!sqlLower.trim().startsWith('select')) {
            return {
                isValid: false,
                message: '只允许SELECT查询'
            };
        }
        const unsafeOperations = [
            'insert into',
            'update ',
            'delete from',
            'drop ',
            'truncate ',
            'alter ',
            'create ',
            'grant ',
            'revoke ',
            'with recursive'
        ];
        for (const op of unsafeOperations) {
            if (sqlLower.includes(op)) {
                return {
                    isValid: false,
                    message: `查询包含不安全的操作: ${op}`
                };
            }
        }
        if (sqlLower.includes(';') && !sqlLower.endsWith(';')) {
            return {
                isValid: false,
                message: '不允许执行多条语句'
            };
        }
        return { isValid: true };
    }
    async filterSatellites(filterDto) {
        try {
            this.logger.debug(`执行卫星筛选: ${JSON.stringify(filterDto)}`);
            const { page = 1, limit = 10, conditionLogic = 'AND' } = filterDto, conditions = __rest(filterDto, ["page", "limit", "conditionLogic"]);
            this.logger.debug(`筛选条件逻辑: ${conditionLogic}`);
            this.logger.debug(`关键词: ${conditions.keyword}, 卫星名称: ${conditions.satelliteName}`);
            if (conditions.cosparId) {
                this.logger.debug(`COSPAR ID: ${conditions.cosparId} (类型: ${typeof conditions.cosparId})`);
            }
            if (conditions.noradId) {
                this.logger.debug(`NORAD ID: ${conditions.noradId} (类型: ${typeof conditions.noradId})`);
            }
            const actualLimit = Math.min(limit, 100);
            const offset = (page - 1) * actualLimit;
            let whereClause = '';
            const params = [];
            let paramIndex = 1;
            if (conditions.keyword) {
                const keywordLower = conditions.keyword.toLowerCase();
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `(
          /* 匹配基本字段 */
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.satellite_name) AS item
            WHERE LOWER(item->>'value') ILIKE '%' || $${paramIndex} || '%'
          ) OR
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.alternative_name) AS item
            WHERE LOWER(item->>'value') ILIKE '%' || $${paramIndex} || '%'
          ) OR
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.purpose) AS item
            WHERE LOWER(item->>'value') ILIKE '%' || $${paramIndex} || '%'
          ) OR
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.detailed_purpose) AS item
            WHERE LOWER(item->>'value') ILIKE '%' || $${paramIndex} || '%'
          ) OR
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.owner) AS item
            WHERE LOWER(item->>'value') ILIKE '%' || $${paramIndex} || '%'
          ) OR
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.country_of_registry) AS item
            WHERE LOWER(item->>'value') ILIKE '%' || $${paramIndex} || '%'
          ) OR
          /* 匹配数值字段 - norad_id */
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.norad_id) AS item
            WHERE item->>'value' ILIKE '%' || $${paramIndex} || '%'
          ) OR
          /* 匹配cospar_id字段 */
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.cospar_id) AS item
            WHERE LOWER(item->>'value') ILIKE '%' || $${paramIndex} || '%'
          ) OR
          /* 匹配status字段 */
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.status) AS item
            WHERE LOWER(item->>'value') ILIKE '%' || $${paramIndex} || '%'
          ) OR
          /* 匹配users字段 */
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.users) AS item
            WHERE LOWER(item->>'value') ILIKE '%' || $${paramIndex} || '%'
          ) OR
          /* 匹配launch_info中的嵌套字段 - 分开写更安全 */
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.launch_info) AS item
            WHERE LOWER(item->'value'->>'launch_site') ILIKE '%' || $${paramIndex} || '%'
          ) OR
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.launch_info) AS item
            WHERE LOWER(item->'value'->>'launch_vehicle') ILIKE '%' || $${paramIndex} || '%'
          ) OR
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.launch_info) AS item
            WHERE LOWER(item->'value'->>'contractor') ILIKE '%' || $${paramIndex} || '%'
          ) OR
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.launch_info) AS item
            WHERE LOWER(item->'value'->>'country_of_contractor') ILIKE '%' || $${paramIndex} || '%'
          ) OR
          /* 匹配orbit_info中的嵌套字段 - 分开写更安全 */
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS item
            WHERE LOWER(item->'value'->>'orbit_class') ILIKE '%' || $${paramIndex} || '%'
          ) OR
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS item
            WHERE LOWER(item->'value'->>'orbit_type') ILIKE '%' || $${paramIndex} || '%'
          ) OR
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS item
            WHERE LOWER(item->'value'->>'orbit_overview') ILIKE '%' || $${paramIndex} || '%'
          ) OR
          /* 其他字段匹配 */
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.constellation) AS item
            WHERE LOWER(item->>'value') ILIKE '%' || $${paramIndex} || '%'
          ) OR
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.payload_description) AS item
            WHERE LOWER(item->>'value') ILIKE '%' || $${paramIndex} || '%'
          ) OR
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.contractor) AS item
            WHERE LOWER(item->>'value') ILIKE '%' || $${paramIndex} || '%'
          ) OR
          /* 匹配purpose中可能存在的数组值 - 使用简化方式 */
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.purpose) AS purpose_item
            WHERE jsonb_typeof(purpose_item->'value') = 'array' AND
                  EXISTS (
                    SELECT 1 FROM jsonb_array_elements_text(purpose_item->'value') AS purpose_text
                    WHERE LOWER(purpose_text::text) ILIKE '%' || $${paramIndex} || '%'
                  )
          )
        )`;
                params.push(keywordLower);
                paramIndex++;
            }
            if (conditions.satelliteName) {
                const satelliteNameLower = conditions.satelliteName.toLowerCase();
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `(
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.satellite_name) AS item
            WHERE LOWER(item->>'value') ILIKE '%' || $${paramIndex} || '%'
          ) OR
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.alternative_name) AS item
            WHERE LOWER(item->>'value') ILIKE '%' || $${paramIndex} || '%'
          )
        )`;
                params.push(satelliteNameLower);
                paramIndex++;
            }
            const isSpaceXOwnerQuery = conditions.owner && conditions.owner.toLowerCase().includes('spacex');
            if (isSpaceXOwnerQuery) {
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `(
          /* 匹配owner为SpaceX的卫星 */
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.owner) AS item
            WHERE LOWER(item->>'value') ILIKE '%' || $${paramIndex} || '%'
          )
          OR
          /* 匹配名称中包含starlink的卫星 */
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.satellite_name) AS name 
            WHERE LOWER(name->>'value') ILIKE '%starlink%'
          )
          OR
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.alternative_name) AS name 
            WHERE LOWER(name->>'value') ILIKE '%starlink%'
          )
        )`;
                params.push(conditions.owner);
                paramIndex++;
                delete conditions.owner;
            }
            if (conditions.cosparId) {
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `EXISTS (
          SELECT 1 FROM jsonb_array_elements(s.cospar_id) AS item
          WHERE item->>'value' = $${paramIndex}
        )`;
                params.push(conditions.cosparId);
                paramIndex++;
            }
            if (conditions.noradId) {
                if (whereClause)
                    whereClause += ' AND ';
                const noradIdAsNumber = parseInt(conditions.noradId);
                whereClause += `EXISTS (
          SELECT 1 FROM jsonb_array_elements(s.norad_id) AS item
          WHERE (item->>'value')::int = $${paramIndex}
        )`;
                params.push(noradIdAsNumber || conditions.noradId);
                paramIndex++;
            }
            if (conditions.constellationName) {
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `(
          /* 匹配constellation字段 */
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.constellation) AS const
            WHERE const->>'value' ILIKE '%' || $${paramIndex} || '%'
          )
          OR 
          /* 匹配satellite_name字段 */
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.satellite_name) AS name
            WHERE name->>'value' ILIKE '%' || $${paramIndex} || '%'
          )
          OR
          /* 匹配alternative_name字段 */
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.alternative_name) AS alt_name
            WHERE alt_name->>'value' ILIKE '%' || $${paramIndex} || '%'
          )
        )`;
                params.push(conditions.constellationName);
                paramIndex++;
            }
            if (conditions.status) {
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `EXISTS (
          SELECT 1 FROM jsonb_array_elements(s.status) AS item
          WHERE LOWER(item->>'value') ILIKE '%' || $${paramIndex} || '%'
        )`;
                params.push(conditions.status);
                paramIndex++;
            }
            if (conditions.countryOfOwner) {
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `EXISTS (
          SELECT 1 FROM jsonb_array_elements(s.country_of_registry) AS item
          WHERE LOWER(item->>'value') ILIKE '%' || $${paramIndex} || '%'
        )`;
                params.push(conditions.countryOfOwner);
                paramIndex++;
            }
            if (conditions.owner) {
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `EXISTS (
          SELECT 1 FROM jsonb_array_elements(s.owner) AS item
          WHERE LOWER(item->>'value') ILIKE '%' || $${paramIndex} || '%'
        )`;
                params.push(conditions.owner);
                paramIndex++;
            }
            if (conditions.users) {
                this.logger.debug(`添加users条件: ${conditions.users}`);
                const usersLower = conditions.users.toLowerCase();
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `(
          s.users IS NOT NULL AND
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.users) AS item
            WHERE LOWER(item->>'value') ILIKE '%' || $${paramIndex} || '%'
          )
        )`;
                params.push(usersLower);
                paramIndex++;
            }
            if (conditions.purpose) {
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `EXISTS (
          SELECT 1 FROM jsonb_array_elements(s.purpose) AS item
          WHERE LOWER(item->>'value') ILIKE '%' || $${paramIndex} || '%'
        )`;
                params.push(conditions.purpose);
                paramIndex++;
            }
            if (conditions.contractor) {
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.launch_info) AS launch_info_item
            WHERE LOWER(launch_info_item->'value'->>'contractor') ILIKE '%' || $${paramIndex} || '%'
          )
        `;
                params.push(conditions.contractor);
                paramIndex++;
            }
            if (conditions.countryOfContractor) {
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.launch_info) AS launch_info_item
            WHERE LOWER(launch_info_item->'value'->>'country_of_contractor') ILIKE '%' || $${paramIndex} || '%'
          )
        `;
                params.push(conditions.countryOfContractor);
                paramIndex++;
            }
            if (conditions.launchSite) {
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.launch_info) AS launch_info_item
            WHERE LOWER(launch_info_item->'value'->>'launch_site') ILIKE '%' || $${paramIndex} || '%'
          )
        `;
                params.push(conditions.launchSite);
                paramIndex++;
            }
            if (conditions.launchVehicle) {
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.launch_info) AS launch_info_item
            WHERE LOWER(launch_info_item->'value'->>'launch_vehicle') ILIKE '%' || $${paramIndex} || '%'
          )
        `;
                params.push(conditions.launchVehicle);
                paramIndex++;
            }
            if (conditions.hasTleData !== undefined) {
                this.logger.debug(`处理hasTleData条件: ${conditions.hasTleData}`);
                if (whereClause)
                    whereClause += ' AND ';
                if (conditions.hasTleData === true) {
                    whereClause += `
            s.orbit_info IS NOT NULL AND 
            jsonb_array_length(s.orbit_info) > 0 AND
            (
              EXISTS (
                SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit_item
                WHERE 
                  orbit_item->'value'->'sources' IS NOT NULL AND
                  jsonb_typeof(orbit_item->'value'->'sources') = 'array' AND
                  jsonb_array_length(orbit_item->'value'->'sources') > 0 AND
                  orbit_item->'value'->'sources' ? 'orbital_tle'
              )
              OR
              EXISTS (
                SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit_item
                WHERE 
                  orbit_item->'sources' IS NOT NULL AND
                  jsonb_typeof(orbit_item->'sources') = 'array' AND
                  jsonb_array_length(orbit_item->'sources') > 0 AND
                  orbit_item->'sources' ? 'orbital_tle'
              )
            )
          `;
                }
                else if (conditions.hasTleData === false) {
                    whereClause += `
            (s.orbit_info IS NULL OR 
            jsonb_array_length(s.orbit_info) = 0 OR
            (
              NOT EXISTS (
                SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit_item
                WHERE 
                  orbit_item->'value'->'sources' IS NOT NULL AND
                  jsonb_typeof(orbit_item->'value'->'sources') = 'array' AND
                  jsonb_array_length(orbit_item->'value'->'sources') > 0 AND
                  orbit_item->'value'->'sources' ? 'orbital_tle'
              )
              AND
              NOT EXISTS (
                SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit_item
                WHERE 
                  orbit_item->'sources' IS NOT NULL AND
                  jsonb_typeof(orbit_item->'sources') = 'array' AND
                  jsonb_array_length(orbit_item->'sources') > 0 AND
                  orbit_item->'sources' ? 'orbital_tle'
              )
            ))
          `;
                }
            }
            if (conditions.orbitClass) {
                this.logger.debug(`处理orbitClass条件: ${conditions.orbitClass}`);
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `
          s.orbit_info IS NOT NULL AND
          jsonb_array_length(s.orbit_info) > 0 AND
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE LOWER(orbit->'value'->>'orbit_class') ILIKE '%' || $${paramIndex} || '%'
          )
        `;
                params.push(conditions.orbitClass);
                paramIndex++;
            }
            if (conditions.orbitType) {
                this.logger.debug(`处理orbitType条件: ${conditions.orbitType}`);
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `
          s.orbit_info IS NOT NULL AND
          jsonb_array_length(s.orbit_info) > 0 AND
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE LOWER(orbit->'value'->>'orbit_type') ILIKE '%' || $${paramIndex} || '%'
          )
        `;
                params.push(conditions.orbitType);
                paramIndex++;
            }
            if (conditions.perigeeKm && (conditions.perigeeKm.min !== undefined || conditions.perigeeKm.max !== undefined)) {
                this.logger.debug(`处理perigeeKm条件: ${JSON.stringify(conditions.perigeeKm)}`);
                const { min, max } = conditions.perigeeKm;
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `s.orbit_info IS NOT NULL AND jsonb_array_length(s.orbit_info) > 0`;
                if (min !== undefined && max !== undefined) {
                    whereClause += ` AND EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE (orbit->'value'->>'perigee_km')::float BETWEEN $${paramIndex} AND $${paramIndex + 1}
          )`;
                    params.push(min);
                    paramIndex++;
                    params.push(max);
                    paramIndex++;
                }
                else if (min !== undefined) {
                    whereClause += ` AND EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE (orbit->'value'->>'perigee_km')::float >= $${paramIndex}
          )`;
                    params.push(min);
                    paramIndex++;
                }
                else if (max !== undefined) {
                    whereClause += ` AND EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE (orbit->'value'->>'perigee_km')::float <= $${paramIndex}
          )`;
                    params.push(max);
                    paramIndex++;
                }
            }
            if (conditions.apogeeKm && (conditions.apogeeKm.min !== undefined || conditions.apogeeKm.max !== undefined)) {
                this.logger.debug(`处理apogeeKm条件: ${JSON.stringify(conditions.apogeeKm)}`);
                const { min, max } = conditions.apogeeKm;
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `s.orbit_info IS NOT NULL AND jsonb_array_length(s.orbit_info) > 0`;
                if (min !== undefined && max !== undefined) {
                    whereClause += ` AND EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE (orbit->'value'->>'apogee_km')::float BETWEEN $${paramIndex} AND $${paramIndex + 1}
          )`;
                    params.push(min);
                    paramIndex++;
                    params.push(max);
                    paramIndex++;
                }
                else if (min !== undefined) {
                    whereClause += ` AND EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE (orbit->'value'->>'apogee_km')::float >= $${paramIndex}
          )`;
                    params.push(min);
                    paramIndex++;
                }
                else if (max !== undefined) {
                    whereClause += ` AND EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE (orbit->'value'->>'apogee_km')::float <= $${paramIndex}
          )`;
                    params.push(max);
                    paramIndex++;
                }
            }
            if (conditions.eccentricity && (conditions.eccentricity.min !== undefined || conditions.eccentricity.max !== undefined)) {
                this.logger.debug(`处理eccentricity条件: ${JSON.stringify(conditions.eccentricity)}`);
                const { min, max } = conditions.eccentricity;
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `s.orbit_info IS NOT NULL AND jsonb_array_length(s.orbit_info) > 0`;
                if (min !== undefined && max !== undefined) {
                    whereClause += ` AND EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE (orbit->'value'->>'eccentricity')::float BETWEEN $${paramIndex} AND $${paramIndex + 1}
          )`;
                    params.push(min);
                    paramIndex++;
                    params.push(max);
                    paramIndex++;
                }
                else if (min !== undefined) {
                    whereClause += ` AND EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE (orbit->'value'->>'eccentricity')::float >= $${paramIndex}
          )`;
                    params.push(min);
                    paramIndex++;
                }
                else if (max !== undefined) {
                    whereClause += ` AND EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE (orbit->'value'->>'eccentricity')::float <= $${paramIndex}
          )`;
                    params.push(max);
                    paramIndex++;
                }
            }
            if (conditions.inclDegrees && (conditions.inclDegrees.min !== undefined || conditions.inclDegrees.max !== undefined)) {
                this.logger.debug(`处理inclDegrees条件: ${JSON.stringify(conditions.inclDegrees)}`);
                const { min, max } = conditions.inclDegrees;
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `s.orbit_info IS NOT NULL AND jsonb_array_length(s.orbit_info) > 0`;
                if (min !== undefined && max !== undefined) {
                    whereClause += ` AND EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE (orbit->'value'->>'incl_degrees')::float BETWEEN $${paramIndex} AND $${paramIndex + 1}
          )`;
                    params.push(min);
                    paramIndex++;
                    params.push(max);
                    paramIndex++;
                }
                else if (min !== undefined) {
                    whereClause += ` AND EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE (orbit->'value'->>'incl_degrees')::float >= $${paramIndex}
          )`;
                    params.push(min);
                    paramIndex++;
                }
                else if (max !== undefined) {
                    whereClause += ` AND EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE (orbit->'value'->>'incl_degrees')::float <= $${paramIndex}
          )`;
                    params.push(max);
                    paramIndex++;
                }
            }
            if (conditions.periodMinutes && (conditions.periodMinutes.min !== undefined || conditions.periodMinutes.max !== undefined)) {
                this.logger.debug(`处理periodMinutes条件: ${JSON.stringify(conditions.periodMinutes)}`);
                const { min, max } = conditions.periodMinutes;
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `s.orbit_info IS NOT NULL AND jsonb_array_length(s.orbit_info) > 0`;
                if (min !== undefined && max !== undefined) {
                    whereClause += ` AND EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE (orbit->'value'->>'period_minutes')::float BETWEEN $${paramIndex} AND $${paramIndex + 1}
          )`;
                    params.push(min);
                    paramIndex++;
                    params.push(max);
                    paramIndex++;
                }
                else if (min !== undefined) {
                    whereClause += ` AND EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE (orbit->'value'->>'period_minutes')::float >= $${paramIndex}
          )`;
                    params.push(min);
                    paramIndex++;
                }
                else if (max !== undefined) {
                    whereClause += ` AND EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE (orbit->'value'->>'period_minutes')::float <= $${paramIndex}
          )`;
                    params.push(max);
                    paramIndex++;
                }
            }
            if (conditions.decayDate && (conditions.decayDate.start || conditions.decayDate.end)) {
                this.logger.debug(`处理decayDate条件: ${JSON.stringify(conditions.decayDate)}`);
                const { start, end } = conditions.decayDate;
                if (whereClause)
                    whereClause += ' AND ';
                if (start && end) {
                    whereClause += `
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE orbit->'value'->>'decay_date' BETWEEN $${paramIndex} AND $${paramIndex + 1}
          )
        `;
                    params.push(start);
                    paramIndex++;
                    params.push(end);
                    paramIndex++;
                }
                else if (start) {
                    whereClause += `
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE orbit->'value'->>'decay_date' >= $${paramIndex}
          )
        `;
                    params.push(start);
                    paramIndex++;
                }
                else if (end) {
                    whereClause += `
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE orbit->'value'->>'decay_date' <= $${paramIndex}
          )
        `;
                    params.push(end);
                    paramIndex++;
                }
            }
            if (conditions.deployedDate && (conditions.deployedDate.start || conditions.deployedDate.end)) {
                this.logger.debug(`处理deployedDate条件: ${JSON.stringify(conditions.deployedDate)}`);
                const { start, end } = conditions.deployedDate;
                if (whereClause)
                    whereClause += ' AND ';
                if (start && end) {
                    whereClause += `
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE orbit->'value'->>'deployed_date' BETWEEN $${paramIndex} AND $${paramIndex + 1}
          )
        `;
                    params.push(start);
                    paramIndex++;
                    params.push(end);
                    paramIndex++;
                }
                else if (start) {
                    whereClause += `
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE orbit->'value'->>'deployed_date' >= $${paramIndex}
          )
        `;
                    params.push(start);
                    paramIndex++;
                }
                else if (end) {
                    whereClause += `
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.orbit_info) AS orbit 
            WHERE orbit->'value'->>'deployed_date' <= $${paramIndex}
          )
        `;
                    params.push(end);
                    paramIndex++;
                }
            }
            if (conditions.launchDate && (conditions.launchDate.start || conditions.launchDate.end)) {
                this.logger.debug(`处理launchDate条件: ${JSON.stringify(conditions.launchDate)}`);
                const { start, end } = conditions.launchDate;
                if (whereClause)
                    whereClause += ' AND ';
                if (start && end) {
                    whereClause += `
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.launch_info) AS launch 
            WHERE launch->'value'->>'launch_date' BETWEEN $${paramIndex} AND $${paramIndex + 1}
          )
        `;
                    params.push(start);
                    paramIndex++;
                    params.push(end);
                    paramIndex++;
                }
                else if (start) {
                    whereClause += `
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.launch_info) AS launch 
            WHERE launch->'value'->>'launch_date' >= $${paramIndex}
          )
        `;
                    params.push(start);
                    paramIndex++;
                }
                else if (end) {
                    whereClause += `
          EXISTS (
            SELECT 1 FROM jsonb_array_elements(s.launch_info) AS launch 
            WHERE launch->'value'->>'launch_date' <= $${paramIndex}
          )
        `;
                    params.push(end);
                    paramIndex++;
                }
            }
            if (conditions.users) {
                this.logger.debug(`添加users条件: ${conditions.users}`);
                const usersLower = conditions.users.toLowerCase();
                if (whereClause)
                    whereClause += ' AND ';
                whereClause += `(
        s.users IS NOT NULL AND
        EXISTS (
          SELECT 1 FROM jsonb_array_elements(s.users) AS item
          WHERE LOWER(item->>'value') ILIKE '%' || $${paramIndex} || '%'
        )
      )`;
                params.push(usersLower);
                paramIndex++;
            }
            let sql = 'SELECT s.* FROM satellites s';
            if (whereClause) {
                sql += ` WHERE ${whereClause}`;
            }
            sql += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
            params.push(actualLimit);
            params.push(offset);
            let countSql = 'SELECT COUNT(*) FROM satellites s';
            if (whereClause) {
                countSql += ` WHERE ${whereClause}`;
            }
            this.logger.debug(`SQL Query: ${sql}`);
            this.logger.debug(`Params: ${JSON.stringify(params)}`);
            let fullSql = sql;
            params.forEach((param, index) => {
                fullSql = fullSql.replace(`$${index + 1}`, typeof param === 'string' ? `'${param}'` : param);
            });
            this.logger.debug(`完整SQL: ${fullSql}`);
            const result = await this.entityManager.query(sql, params);
            const countResult = await this.entityManager.query(countSql, params.slice(0, params.length - 2));
            const totalCount = parseInt(countResult[0].count);
            this.logger.debug(`查询结果: ${result.length}条记录`);
            const processedResults = this.processMatchResults(result, conditions);
            return {
                success: true,
                total: totalCount,
                page: page,
                limit: actualLimit,
                results: processedResults
            };
        }
        catch (error) {
            this.logger.error(`卫星筛选执行失败: ${error.message}`, error.stack);
            throw new common_1.BadRequestException(`卫星筛选执行失败: ${error.message}`);
        }
    }
    extractNestedValues(value) {
        this.logger.debug(`extractNestedValues被调用，参数类型: ${typeof value}`);
        if (typeof value === 'object' && value !== null) {
            this.logger.debug(`对象值: ${JSON.stringify(value, null, 2)}`);
        }
        else {
            this.logger.debug(`非对象值: ${value}`);
        }
        if (!value)
            return [];
        if (typeof value === 'string')
            return [value];
        if (typeof value === 'number')
            return [String(value)];
        if (Array.isArray(value)) {
            const result = [];
            for (const item of value) {
                result.push(...this.extractNestedValues(item));
            }
            this.logger.debug(`从数组提取的值: ${JSON.stringify(result)}`);
            return result;
        }
        if (typeof value === 'object') {
            this.logger.debug(`提取嵌套值，原始对象: ${JSON.stringify(value, null, 2)}`);
            if ('value' in value) {
                this.logger.debug(`从对象中提取value属性: ${JSON.stringify(value.value)}`);
                if (value.value === null || value.value === undefined) {
                    const result = [];
                    for (const key in value) {
                        if (key !== 'value' && value[key] !== null && value[key] !== undefined) {
                            if (typeof value[key] === 'string') {
                                result.push(value[key]);
                            }
                            else if (typeof value[key] === 'number') {
                                result.push(String(value[key]));
                            }
                            else if (typeof value[key] === 'object') {
                                result.push(...this.extractNestedValues(value[key]));
                            }
                        }
                    }
                    return result;
                }
                if (typeof value.value === 'object') {
                    if (Array.isArray(value.value)) {
                        const result = [];
                        for (const item of value.value) {
                            if (typeof item === 'string') {
                                result.push(item);
                            }
                            else if (typeof item === 'number') {
                                result.push(String(item));
                            }
                            else if (item && typeof item === 'object') {
                                result.push(...this.extractNestedValues(item));
                            }
                        }
                        return result;
                    }
                    return this.extractNestedValues(value.value);
                }
                return [String(value.value)];
            }
            const result = [];
            for (const key in value) {
                if (value[key] === null || value[key] === undefined)
                    continue;
                if (typeof value[key] === 'string') {
                    result.push(value[key]);
                }
                else if (typeof value[key] === 'number') {
                    result.push(String(value[key]));
                }
                else if (typeof value[key] === 'object') {
                    result.push(...this.extractNestedValues(value[key]));
                }
            }
            this.logger.debug(`从复杂对象提取的值: ${JSON.stringify(result)}`);
            return result;
        }
        return [String(value)];
    }
    processMatchResults(data, conditions) {
        var _a;
        if (!conditions.keyword && !conditions.satelliteName) {
            return data;
        }
        this.logger.debug(`处理匹配结果，条件: ${JSON.stringify(conditions)}`);
        const processedData = data.map(item => {
            var _a, _b;
            this.logger.debug(`处理记录ID=${item.id}`);
            const result = Object.assign(Object.assign({}, item), { matches: [] });
            if (conditions.satelliteName) {
                const fieldsToCheck = [
                    { name: 'satellite_name', path: 'satellite_name' },
                    { name: 'alternative_name', path: 'alternative_name' }
                ];
                const satelliteNameLower = conditions.satelliteName.toLowerCase();
                for (const field of fieldsToCheck) {
                    if (!item[field.name]) {
                        this.logger.debug(`字段 ${field.name} 不存在`);
                        continue;
                    }
                    let fieldValues = item[field.name];
                    if (!Array.isArray(fieldValues)) {
                        this.logger.debug(`字段 ${field.name} 不是数组，尝试转换`);
                        if (fieldValues && typeof fieldValues === 'object') {
                            fieldValues = [fieldValues];
                        }
                        else {
                            continue;
                        }
                    }
                    this.logger.debug(`处理 ${field.name} 字段，值为: ${JSON.stringify(fieldValues)}`);
                    for (const nameObj of fieldValues) {
                        const values = this.extractNestedValues(nameObj);
                        this.logger.debug(`提取的值: ${JSON.stringify(values)}`);
                        for (const value of values) {
                            if (!value) {
                                this.logger.debug(`值为空: ${value}`);
                                continue;
                            }
                            const valueStr = String(value).toLowerCase();
                            this.logger.debug(`处理值: ${valueStr}`);
                            if (valueStr.includes(satelliteNameLower)) {
                                let similarity = satelliteNameLower.length / valueStr.length;
                                if (valueStr === satelliteNameLower) {
                                    similarity = 1.0;
                                }
                                else {
                                    similarity = Math.max(similarity, 0.5);
                                }
                                result.matches.push({
                                    field: field.name,
                                    value: value,
                                    similarity,
                                    condition: 'satelliteName',
                                    query: conditions.satelliteName
                                });
                                this.logger.debug(`子串匹配: 字段=${field.name}, 值=${valueStr}, 条件=${satelliteNameLower}, 相似度=${similarity}`);
                            }
                        }
                    }
                }
            }
            if (conditions.keyword) {
                const fieldsToCheck = [
                    { name: 'satellite_name', path: 'satellite_name' },
                    { name: 'alternative_name', path: 'alternative_name' },
                    { name: 'purpose', path: 'purpose' },
                    { name: 'detailed_purpose', path: 'detailed_purpose' },
                    { name: 'country_of_registry', path: 'country_of_registry' },
                    { name: 'owner', path: 'owner' },
                    { name: 'contractor', path: 'contractor' },
                    { name: 'payload_description', path: 'payload_description' },
                    { name: 'constellation', path: 'constellation' },
                    { name: 'launch_site', path: 'launch_info' },
                    { name: 'launch_vehicle', path: 'launch_info' },
                    { name: 'contractor', path: 'launch_info' },
                    { name: 'country_of_contractor', path: 'launch_info' },
                    { name: 'orbit_class', path: 'orbit_info' },
                    { name: 'orbit_type', path: 'orbit_info' },
                    { name: 'orbit_overview', path: 'orbit_info' }
                ];
                const keywordLower = conditions.keyword.toLowerCase();
                for (const field of fieldsToCheck) {
                    const fieldPath = field.path.split('.');
                    let fieldValue = item;
                    for (const pathPart of fieldPath) {
                        fieldValue = fieldValue === null || fieldValue === void 0 ? void 0 : fieldValue[pathPart];
                        if (!fieldValue)
                            break;
                    }
                    if (!fieldValue)
                        continue;
                    if (field.path === 'launch_info') {
                        const nestedField = field.name;
                        for (const launchInfo of fieldValue) {
                            if ((_a = launchInfo === null || launchInfo === void 0 ? void 0 : launchInfo.value) === null || _a === void 0 ? void 0 : _a[nestedField]) {
                                const nestedValue = launchInfo.value[nestedField];
                                if (typeof nestedValue === 'string' && nestedValue.toLowerCase().includes(keywordLower)) {
                                    let similarity = keywordLower.length / nestedValue.toLowerCase().length;
                                    if (nestedValue.toLowerCase() === keywordLower) {
                                        similarity = 1.0;
                                    }
                                    else {
                                        similarity = Math.max(similarity, 0.3);
                                    }
                                    result.matches.push({
                                        field: nestedField,
                                        value: nestedValue,
                                        similarity,
                                        condition: 'keyword',
                                        query: conditions.keyword
                                    });
                                    this.logger.debug(`关键词子串匹配: 字段=${nestedField}, 值=${nestedValue}, 关键词=${keywordLower}, 相似度=${similarity}`);
                                }
                            }
                        }
                        continue;
                    }
                    if (field.path === 'orbit_info') {
                        const nestedField = field.name;
                        for (const orbitInfo of fieldValue) {
                            if ((_b = orbitInfo === null || orbitInfo === void 0 ? void 0 : orbitInfo.value) === null || _b === void 0 ? void 0 : _b[nestedField]) {
                                const nestedValue = orbitInfo.value[nestedField];
                                if (typeof nestedValue === 'string' && nestedValue.toLowerCase().includes(keywordLower)) {
                                    let similarity = keywordLower.length / nestedValue.toLowerCase().length;
                                    if (nestedValue.toLowerCase() === keywordLower) {
                                        similarity = 1.0;
                                    }
                                    else {
                                        similarity = Math.max(similarity, 0.3);
                                    }
                                    result.matches.push({
                                        field: nestedField,
                                        value: nestedValue,
                                        similarity,
                                        condition: 'keyword',
                                        query: conditions.keyword
                                    });
                                    this.logger.debug(`关键词子串匹配: 字段=${nestedField}, 值=${nestedValue}, 关键词=${keywordLower}, 相似度=${similarity}`);
                                }
                            }
                        }
                        continue;
                    }
                    if (field.name === 'purpose') {
                        for (const purposeItem of fieldValue) {
                            const purposeValue = purposeItem.value;
                            if (Array.isArray(purposeValue)) {
                                for (const purposeText of purposeValue) {
                                    if (typeof purposeText === 'string' && purposeText.toLowerCase().includes(keywordLower)) {
                                        let similarity = keywordLower.length / purposeText.toLowerCase().length;
                                        if (purposeText.toLowerCase() === keywordLower) {
                                            similarity = 1.0;
                                        }
                                        else {
                                            similarity = Math.max(similarity, 0.3);
                                        }
                                        result.matches.push({
                                            field: 'purpose',
                                            value: purposeText,
                                            similarity,
                                            condition: 'keyword',
                                            query: conditions.keyword
                                        });
                                        this.logger.debug(`关键词子串匹配: 字段=purpose, 值=${purposeText}, 关键词=${keywordLower}, 相似度=${similarity}`);
                                    }
                                }
                            }
                        }
                    }
                    const processFieldValue = (value, fieldName) => {
                        const values = this.extractNestedValues(value);
                        for (const val of values) {
                            if (!val)
                                continue;
                            const valueStr = String(val).toLowerCase();
                            if (valueStr.includes(keywordLower)) {
                                let similarity = keywordLower.length / valueStr.length;
                                if (valueStr === keywordLower) {
                                    similarity = 1.0;
                                }
                                else {
                                    similarity = Math.max(similarity, 0.3);
                                }
                                result.matches.push({
                                    field: fieldName,
                                    value: val,
                                    similarity,
                                    condition: 'keyword',
                                    query: conditions.keyword
                                });
                                this.logger.debug(`关键词子串匹配: 字段=${fieldName}, 值=${valueStr}, 关键词=${keywordLower}, 相似度=${similarity}`);
                            }
                        }
                    };
                    if (Array.isArray(fieldValue)) {
                        for (const element of fieldValue) {
                            processFieldValue(element, field.name);
                        }
                    }
                    else {
                        processFieldValue(fieldValue, field.name);
                    }
                }
            }
            if (result.matches && result.matches.length > 0) {
                const satelliteNameMatches = result.matches.filter((m) => m.condition === 'satelliteName');
                const keywordMatches = result.matches.filter((m) => m.condition === 'keyword');
                const hasSatelliteNameMatch = satelliteNameMatches.length > 0;
                const hasKeywordMatch = keywordMatches.length > 0;
                const similarityInfo = { matches: {} };
                if (hasSatelliteNameMatch) {
                    const maxSimilarity = Math.max(...satelliteNameMatches.map((m) => m.similarity || 0));
                    const bestMatch = satelliteNameMatches.find((m) => m.similarity === maxSimilarity);
                    similarityInfo.matches.satelliteName = {
                        max_similarity: maxSimilarity,
                        matched_field: bestMatch === null || bestMatch === void 0 ? void 0 : bestMatch.field,
                        matched_value: bestMatch === null || bestMatch === void 0 ? void 0 : bestMatch.value,
                        query_value: conditions.satelliteName,
                        all_matches: satelliteNameMatches.map((m) => ({
                            field: m.field,
                            value: m.value,
                            similarity: m.similarity
                        }))
                    };
                    similarityInfo.max_similarity = maxSimilarity;
                }
                if (hasKeywordMatch) {
                    const maxKeywordSimilarity = Math.max(...keywordMatches.map((m) => m.similarity || 0));
                    const bestKeywordMatch = keywordMatches.find((m) => m.similarity === maxKeywordSimilarity);
                    similarityInfo.matches.keyword = {
                        max_similarity: maxKeywordSimilarity,
                        matched_field: bestKeywordMatch === null || bestKeywordMatch === void 0 ? void 0 : bestKeywordMatch.field,
                        matched_value: bestKeywordMatch === null || bestKeywordMatch === void 0 ? void 0 : bestKeywordMatch.value,
                        query_value: conditions.keyword,
                        all_matches: keywordMatches.map((m) => ({
                            field: m.field,
                            value: m.value,
                            similarity: m.similarity
                        }))
                    };
                    if (!hasSatelliteNameMatch || maxKeywordSimilarity > similarityInfo.max_similarity) {
                        similarityInfo.max_similarity = maxKeywordSimilarity;
                    }
                }
                if (hasSatelliteNameMatch && hasKeywordMatch) {
                    similarityInfo.primary_match = similarityInfo.matches.satelliteName.max_similarity >=
                        similarityInfo.matches.keyword.max_similarity ? 'satelliteName' : 'keyword';
                }
                else if (hasSatelliteNameMatch) {
                    similarityInfo.primary_match = 'satelliteName';
                }
                else if (hasKeywordMatch) {
                    similarityInfo.primary_match = 'keyword';
                }
                if (conditions.satelliteName && conditions.keyword &&
                    (!hasSatelliteNameMatch || !hasKeywordMatch)) {
                    this.logger.debug(`AND逻辑但不满足所有条件，设置_similarity_info为null`);
                    result._similarity_info = null;
                }
                else {
                    result._similarity_info = similarityInfo;
                    this.logger.debug(`设置综合_similarity_info: ${JSON.stringify(similarityInfo)}`);
                }
            }
            else {
                result._similarity_info = null;
                this.logger.debug(`没有匹配项，_similarity_info设置为null`);
                this.logger.debug(`记录ID=${item.id}的原始数据结构:`);
                if (item.satellite_name) {
                    this.logger.debug(`satellite_name: ${JSON.stringify(item.satellite_name)}`);
                }
                if (item.alternative_name) {
                    this.logger.debug(`alternative_name: ${JSON.stringify(item.alternative_name)}`);
                }
            }
            return result;
        });
        if (processedData.length > 0) {
            this.logger.debug(`第一条数据的匹配信息: _similarity_info=${JSON.stringify(processedData[0]._similarity_info)}`);
            this.logger.debug(`第一条数据的matches长度: ${((_a = processedData[0].matches) === null || _a === void 0 ? void 0 : _a.length) || 0}`);
        }
        const sortedData = processedData.sort((a, b) => {
            var _a, _b;
            const similarityA = ((_a = a._similarity_info) === null || _a === void 0 ? void 0 : _a.max_similarity) || 0;
            const similarityB = ((_b = b._similarity_info) === null || _b === void 0 ? void 0 : _b.max_similarity) || 0;
            return similarityB - similarityA;
        });
        return sortedData;
    }
    calculateSimilarity(str1, str2) {
        if (!str1 || !str2)
            return 0;
        if (typeof str1 !== 'string' || typeof str2 !== 'string') {
            try {
                str1 = String(str1);
                str2 = String(str2);
            }
            catch (e) {
                this.logger.warn(`相似度计算失败: 无法将输入转换为字符串: ${e.message}`);
                return 0;
            }
        }
        const s1 = str1.toLowerCase();
        const s2 = str2.toLowerCase();
        if (s1 === s2) {
            return 1.0;
        }
        if (s1.includes(s2)) {
            const ratio = s2.length / s1.length;
            if (s2.length < 3) {
                return 0.5 * ratio;
            }
            return 0.9 * ratio;
        }
        else if (s2.includes(s1)) {
            const ratio = s1.length / s2.length;
            if (s1.length < 3) {
                return 0.5 * ratio;
            }
            return 0.9 * ratio;
        }
        const searchWords1 = s1.split(/\s+/);
        const searchWords2 = s2.split(/\s+/);
        let maxWordSimilarity = 0;
        for (const word1 of searchWords1) {
            if (word1.length < 3)
                continue;
            for (const word2 of searchWords2) {
                if (word2.length < 3)
                    continue;
                if (word1 === word2) {
                    const wordImportance = Math.max(word1.length / s1.length, word2.length / s2.length);
                    const similarity = 0.8 * wordImportance;
                    maxWordSimilarity = Math.max(maxWordSimilarity, similarity);
                }
                else if (word1.includes(word2) || word2.includes(word1)) {
                    const longer = word1.length > word2.length ? word1 : word2;
                    const shorter = word1.length > word2.length ? word2 : word1;
                    const ratio = shorter.length / longer.length;
                    if (shorter.length < 3) {
                        continue;
                    }
                    let similarity = 0;
                    if (longer.startsWith(shorter)) {
                        similarity = 0.7 * ratio;
                    }
                    else {
                        similarity = 0.6 * ratio;
                    }
                    const wordImportance = Math.max(word1.length / s1.length, word2.length / s2.length);
                    similarity *= wordImportance;
                    maxWordSimilarity = Math.max(maxWordSimilarity, similarity);
                }
            }
        }
        if (maxWordSimilarity > 0) {
            return maxWordSimilarity;
        }
        const lcsLength = this.longestCommonSubsequence(s1, s2);
        if (lcsLength < 3) {
            return 0.1 * (lcsLength / Math.max(s1.length, s2.length));
        }
        const lcsRatio = lcsLength / Math.max(s1.length, s2.length);
        let prefixMatchLength = 0;
        const minLength = Math.min(s1.length, s2.length);
        for (let i = 0; i < minLength; i++) {
            if (s1[i] === s2[i]) {
                prefixMatchLength++;
            }
            else {
                break;
            }
        }
        if (prefixMatchLength >= 2) {
            const prefixRatio = prefixMatchLength / minLength;
            return Math.max(0.3 * prefixRatio, 0.2 * lcsRatio);
        }
        return 0.2 * lcsRatio;
    }
    longestCommonSubsequence(s1, s2) {
        const m = s1.length;
        const n = s2.length;
        const dp = Array(m + 1).fill(0).map(() => Array(n + 1).fill(0));
        for (let i = 1; i <= m; i++) {
            for (let j = 1; j <= n; j++) {
                if (s1[i - 1] === s2[j - 1]) {
                    dp[i][j] = dp[i - 1][j - 1] + 1;
                }
                else {
                    dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);
                }
            }
        }
        return dp[m][n];
    }
    async getSatelliteUsers() {
        try {
            this.logger.debug('执行获取卫星使用者集合查询');
            const sql = `
        WITH extracted_users AS (
          SELECT 
            jsonb_array_elements(s.users) as user_item
          FROM 
            satellites s
          WHERE 
            s.users IS NOT NULL AND jsonb_array_length(s.users) > 0
        ),
        array_values AS (
          SELECT 
            jsonb_array_elements_text(user_item->'value') as user_value
          FROM 
            extracted_users
          WHERE 
            jsonb_typeof(user_item->'value') = 'array'
        ),
        string_values AS (
          SELECT 
            user_item->>'value' as user_value
          FROM 
            extracted_users
          WHERE 
            jsonb_typeof(user_item->'value') != 'array'
            AND user_item->>'value' IS NOT NULL AND user_item->>'value' != ''
        ),
        combined_values AS (
          SELECT user_value FROM array_values
          UNION ALL
          SELECT user_value FROM string_values
        ),
        split_values AS (
          SELECT 
            trim(regexp_replace(value, '[\\(\\)\\[\\]\\{\\}]', '', 'g')) as user_name
          FROM 
            combined_values,
            regexp_split_to_table(user_value, '[,;/|、，；\\(\\)]') as value
        )
        SELECT DISTINCT user_name
        FROM split_values
        WHERE user_name != ''
        ORDER BY user_name;
      `;
            const result = await this.entityManager.query(sql);
            const rawUsers = result.map((row) => row.user_name.trim());
            const userMap = {};
            const knownTranslations = {
                'Military': { cn: '军事', en: 'Military' },
                'Civil': { cn: '民用', en: 'Civil' },
                'Government': { cn: '政府', en: 'Government' },
                'Commercial': { cn: '商业', en: 'Commercial' },
                'Academic': { cn: '学术', en: 'Academic' },
                'Education': { cn: '教育', en: 'Education' },
                'University': { cn: '大学', en: 'University' },
                'School': { cn: '学校', en: 'School' },
                'Institute': { cn: '研究所', en: 'Institute' },
                'Individual': { cn: '个人', en: 'Individual' },
                'Company': { cn: '公司', en: 'Company' },
                'Non-profit': { cn: '非营利组织', en: 'Non-profit' },
                'Space agency': { cn: '航天机构', en: 'Space agency' }
            };
            for (const user of rawUsers) {
                const isEnglish = /[a-zA-Z]/.test(user);
                const isChinese = /[\u4e00-\u9fa5]/.test(user);
                if (isEnglish && !isChinese) {
                    if (knownTranslations[user]) {
                        userMap[user] = knownTranslations[user];
                    }
                    else {
                        userMap[user] = { cn: user, en: user };
                    }
                }
                else if (isChinese && !isEnglish) {
                    let found = false;
                    for (const [key, value] of Object.entries(knownTranslations)) {
                        if (value.cn === user) {
                            userMap[user] = value;
                            found = true;
                            break;
                        }
                    }
                    if (!found) {
                        userMap[user] = { cn: user, en: user };
                    }
                }
                else if (isChinese && isEnglish) {
                    userMap[user] = { cn: user, en: user };
                }
                else {
                    userMap[user] = { cn: user, en: user };
                }
            }
            const processedUsers = Object.values(userMap).sort((a, b) => a.en.localeCompare(b.en));
            this.logger.debug(`获取到 ${processedUsers.length} 个不同的卫星使用者`);
            return {
                success: true,
                users: processedUsers
            };
        }
        catch (error) {
            this.logger.error(`获取卫星使用者集合失败: ${error.message}`, error.stack);
            throw new Error(`获取卫星使用者集合失败: ${error.message}`);
        }
    }
    async getSatellitePurposes() {
        try {
            const mainCategories = [
                {
                    en: 'Amateur Radio',
                    cn: '业余无线电',
                    subcategories: [
                        { en: 'Amateur Radio', cn: '业余无线电' },
                        { en: 'Amateur Communications', cn: '业余通信' },
                        { en: 'Radio Amateur Communication', cn: '业余无线电通信' }
                    ]
                },
                {
                    en: 'Astronomy',
                    cn: '天文学',
                    subcategories: [
                        { en: 'Astronomy', cn: '天文学' },
                        { en: 'Astronomy (Gamma-ray)', cn: '天文学（伽马射线）' },
                        { en: 'Astronomy (UV)', cn: '天文学（紫外线）' },
                        { en: 'Astronomy and Aeronomy', cn: '天文学和高空大气研究' },
                        { en: 'Astrometry', cn: '天体测量学' },
                        { en: 'Radio Astronomy', cn: '射电天文学' },
                        { en: 'Cosmic Background', cn: '宇宙背景辐射' },
                        { en: 'Cosmic Radiation', cn: '宇宙辐射' },
                        { en: 'Cosmic Rays', cn: '宇宙射线' },
                        { en: 'Cosmology', cn: '宇宙学' }
                    ]
                },
                {
                    en: 'Communications',
                    cn: '通信',
                    subcategories: [
                        { en: 'Communications', cn: '通信' },
                        { en: 'Mobile Communications', cn: '移动通信' },
                        { en: 'Satellite Communications', cn: '卫星通信' },
                        { en: 'Emergency Communications', cn: '应急通信' },
                        { en: 'Communication (IoT)', cn: '物联网通信' },
                        { en: 'Communication (MSS)', cn: '移动卫星服务通信' },
                        { en: 'Direct Broadcasting', cn: '直接广播' },
                        { en: 'Store and Forward Communications', cn: '存储转发通信' },
                        { en: 'Starlink', cn: '星链' },
                        { en: 'Globalstar', cn: '全球星' },
                        { en: 'Orbcomm', cn: '轨道通信' },
                        { en: 'OneWeb', cn: '万维网星座' },
                        { en: 'Intelsat', cn: '国际通信卫星' },
                        { en: 'TV Broadcasting', cn: '电视广播' }
                    ]
                },
                {
                    en: 'Data Relay',
                    cn: '数据中继',
                    subcategories: [
                        { en: 'Data Relay', cn: '数据中继' },
                        { en: 'Data Collection and Relay', cn: '数据收集与中继' },
                        { en: 'Tracking and Data Relay Satellite System', cn: '跟踪与数据中继卫星系统' },
                        { en: 'Store Dump', cn: '存储转储' }
                    ]
                },
                {
                    en: 'Earth Observation',
                    cn: '地球观测',
                    subcategories: [
                        { en: 'Earth Observation', cn: '地球观测' },
                        { en: 'Earth Observation (Radar)', cn: '地球观测（雷达）' },
                        { en: 'Earth Observation (Infra-red)', cn: '地球观测（红外）' },
                        { en: 'Disaster Monitoring', cn: '灾害监测' },
                        { en: 'Ocean Monitoring', cn: '海洋监测' },
                        { en: 'Ocean Observation', cn: '海洋观测' },
                        { en: 'Ocean Surveillance', cn: '海洋监视' },
                        { en: 'Maritime Surveillance', cn: '海事监视' },
                        { en: 'Maritime Tracking', cn: '海事跟踪' },
                        { en: 'Traffic Monitoring', cn: '交通监测' },
                        { en: 'RF Spectrum Monitoring', cn: '射频频谱监测' },
                        { en: 'Whale Monitoring', cn: '鲸鱼监测' }
                    ]
                },
                {
                    en: 'Earth Science',
                    cn: '地球科学',
                    subcategories: [
                        { en: 'Earth Science', cn: '地球科学' },
                        { en: 'Earth Sciences', cn: '地球科学' },
                        { en: 'Earth Resources', cn: '地球资源' }
                    ]
                },
                {
                    en: 'Education',
                    cn: '教育',
                    subcategories: [
                        { en: 'Education', cn: '教育' },
                        { en: 'Educational', cn: '教育用途' }
                    ]
                },
                {
                    en: 'Geodesy',
                    cn: '大地测量',
                    subcategories: [
                        { en: 'Geodesy', cn: '大地测量' },
                        { en: 'Geodetic', cn: '大地测量' }
                    ]
                },
                {
                    en: 'Ionospheric Research',
                    cn: '电离层研究',
                    subcategories: [
                        { en: 'Ionospheric Research', cn: '电离层研究' },
                        { en: 'Ionosphere', cn: '电离层' }
                    ]
                },
                {
                    en: 'Magnetospheric Research',
                    cn: '磁层研究',
                    subcategories: [
                        { en: 'Magnetospheric Research', cn: '磁层研究' },
                        { en: 'Magnetic Field and Plasma', cn: '磁场和等离子体研究' },
                        { en: 'Plasma', cn: '等离子体研究' }
                    ]
                },
                {
                    en: 'Meteorology',
                    cn: '气象',
                    subcategories: [
                        { en: 'Meteorology', cn: '气象' },
                        { en: 'Meteorological', cn: '气象' },
                        { en: 'Weather', cn: '天气' },
                        { en: 'NOAA', cn: '气象卫星' },
                        { en: 'GOES', cn: '地球静止卫星' }
                    ]
                },
                {
                    en: 'Microgravity Research',
                    cn: '微重力研究',
                    subcategories: [
                        { en: 'Microgravity Research', cn: '微重力研究' },
                        { en: 'Microgravity', cn: '微重力' },
                        { en: 'Micro Gravity', cn: '微重力' },
                        { en: 'Micro Gravity and Return', cn: '微重力与返回' }
                    ]
                },
                {
                    en: 'Military',
                    cn: '军事',
                    subcategories: [
                        { en: 'Military', cn: '军事' },
                        { en: 'Defense', cn: '国防' },
                        { en: 'Military Communication', cn: '军事通信' },
                        { en: 'Military Relay', cn: '军事中继' },
                        { en: 'Military Early Warning', cn: '军事预警' },
                        { en: 'Reconnaissance', cn: '侦察' },
                        { en: 'SIGINT', cn: '信号情报' },
                        { en: 'ELINT', cn: '电子情报' },
                        { en: 'Early Warning', cn: '预警系统' },
                        { en: 'Surveillance', cn: '监视' }
                    ]
                },
                {
                    en: 'Navigation',
                    cn: '导航',
                    subcategories: [
                        { en: 'Navigation', cn: '导航' },
                        { en: 'Global Navigation', cn: '全球导航' },
                        { en: 'Global Positioning', cn: '全球定位' },
                        { en: 'Global Positioning System (GPS)', cn: '全球定位系统' },
                        { en: 'GPS Constellation', cn: '全球定位系统星座' },
                        { en: 'Beidou Navigation System', cn: '北斗导航系统' },
                        { en: 'Galileo', cn: '伽利略导航系统' },
                        { en: 'Glonass', cn: '格洛纳斯导航系统' },
                        { en: 'QZSS', cn: '准天顶卫星系统' },
                        { en: 'IRNSS', cn: '印度区域导航卫星系统' },
                        { en: 'Regional Positioning', cn: '区域定位' }
                    ]
                },
                {
                    en: 'Optical',
                    cn: '光学',
                    subcategories: [
                        { en: 'Optical', cn: '光学' },
                        { en: 'Electro-optical', cn: '光电' },
                        { en: 'Visible', cn: '可见光' },
                        { en: 'Infra-Red', cn: '红外' },
                        { en: 'IR', cn: '红外' },
                        { en: 'Lidar', cn: '激光雷达' }
                    ]
                },
                {
                    en: 'Platform',
                    cn: '平台',
                    subcategories: [
                        { en: 'Platform', cn: '平台' }
                    ]
                },
                {
                    en: 'Radar',
                    cn: '雷达',
                    subcategories: [
                        { en: 'Radar', cn: '雷达' },
                        { en: 'Radar (active)', cn: '雷达（主动式）' },
                        { en: 'Radar Satellite', cn: '雷达卫星' }
                    ]
                },
                {
                    en: 'Radar Calibration',
                    cn: '雷达校准',
                    subcategories: [
                        { en: 'Radar Calibration', cn: '雷达校准' }
                    ]
                },
                {
                    en: 'Remote Sensing',
                    cn: '遥感',
                    subcategories: [
                        { en: 'Remote Sensing', cn: '遥感' },
                        { en: 'Yaogan', cn: '遥感' }
                    ]
                },
                {
                    en: 'Satellite Servicing',
                    cn: '卫星服务',
                    subcategories: [
                        { en: 'Satellite Servicing', cn: '卫星服务' },
                        { en: 'In-orbit Service and Recovery', cn: '在轨服务与回收' },
                        { en: 'Satellite Deployment', cn: '卫星部署' },
                        { en: 'Servicing', cn: '维修服务' },
                        { en: 'Retrievable Satellite', cn: '可回收卫星' },
                        { en: 'Reusable Satellite', cn: '可重复使用卫星' },
                        { en: 'Reusable Spacecraft', cn: '可重复使用航天器' },
                        { en: 'Return Capsule', cn: '返回舱' }
                    ]
                },
                {
                    en: 'Science',
                    cn: '科学',
                    subcategories: [
                        { en: 'Science', cn: '科学' },
                        { en: 'Scientific', cn: '科学' },
                        { en: 'Science Research', cn: '科学研究' },
                        { en: 'Basic Research', cn: '基础研究' },
                        { en: 'Research', cn: '研究' },
                        { en: 'Life Science', cn: '生命科学' },
                        { en: 'Particles', cn: '粒子研究' }
                    ]
                },
                {
                    en: 'Search & Rescue',
                    cn: '搜索救援',
                    subcategories: [
                        { en: 'Search & Rescue', cn: '搜索救援' },
                        { en: 'Search and Rescue', cn: '搜索和救援' },
                        { en: 'Search and Rescue Tracking', cn: '搜索和救援跟踪' },
                        { en: 'COSPAS-SARSAT', cn: '搜救卫星系统' }
                    ]
                },
                {
                    en: 'Solar Research',
                    cn: '太阳研究',
                    subcategories: [
                        { en: 'Solar Research', cn: '太阳研究' },
                        { en: 'Solar', cn: '太阳' },
                        { en: 'Solar Cosmic Rays', cn: '太阳宇宙射线' },
                        { en: 'Solar Observatory', cn: '太阳观测台' },
                        { en: 'Solar Observing', cn: '太阳观测' },
                        { en: 'Solar Orbiter', cn: '太阳轨道飞行器' },
                        { en: 'Solar Sail', cn: '太阳帆' }
                    ]
                },
                {
                    en: 'Space Burial',
                    cn: '太空安葬',
                    subcategories: [
                        { en: 'Space Burial', cn: '太空安葬' },
                        { en: 'Celestis', cn: '天葬' },
                        { en: 'Memorial', cn: '纪念' }
                    ]
                },
                {
                    en: 'Space Exploration',
                    cn: '太空探索',
                    subcategories: [
                        { en: 'Space Exploration', cn: '太空探索' },
                        { en: 'Mars', cn: '火星' },
                        { en: 'Mars Lander', cn: '火星着陆器' },
                        { en: 'Mars Orbiter', cn: '火星轨道器' },
                        { en: 'Mars Rover', cn: '火星车' },
                        { en: 'Jupiter', cn: '木星' },
                        { en: 'Jupiter Orbiter', cn: '木星轨道器' },
                        { en: 'Saturn', cn: '土星' },
                        { en: 'Saturn Orbiter', cn: '土星轨道器' },
                        { en: 'Venus', cn: '金星' },
                        { en: 'Venus Flyby', cn: '金星飞越' },
                        { en: 'Venus Orbiter', cn: '金星轨道器' },
                        { en: 'Venus Probe', cn: '金星探测器' },
                        { en: 'Mercury Orbiter', cn: '水星轨道器' },
                        { en: 'Uranus', cn: '天王星' },
                        { en: 'Neptune Flyby', cn: '海王星飞越' },
                        { en: 'Pluto Flyby', cn: '冥王星飞越' },
                        { en: 'Lunar Flyby', cn: '月球飞越' },
                        { en: 'Lunar Impact', cn: '月球撞击' },
                        { en: 'Lunar Lander', cn: '月球着陆器' },
                        { en: 'Lunar Orbiter', cn: '月球轨道器' },
                        { en: 'Asteroid Flyby', cn: '小行星飞越' },
                        { en: 'Asteroid Lander', cn: '小行星着陆器' },
                        { en: 'Comet Flyby', cn: '彗星飞越' },
                        { en: 'Comet Halley Flyby', cn: '哈雷彗星飞越' },
                        { en: 'Multiple Comet Flybys', cn: '多彗星飞越' },
                        { en: 'Phobos Lander', cn: '火卫一着陆器' },
                        { en: 'Titan Lander', cn: '土卫六着陆器' },
                        { en: 'Ganymede Orbiter', cn: '木卫三轨道器' },
                        { en: 'Sample Return', cn: '样本返回' },
                        { en: 'Crewed Spacecraft', cn: '载人航天器' }
                    ]
                },
                {
                    en: 'Space Station',
                    cn: '空间站',
                    subcategories: [
                        { en: 'Space Station', cn: '空间站' },
                        { en: 'ISS', cn: '国际空间站' },
                        { en: 'Chinese Space Station', cn: '中国空间站' },
                        { en: 'Space Station Module', cn: '空间站模块' },
                        { en: 'Space Station Logistics', cn: '空间站物流' },
                        { en: 'Space Station Laboratory Module', cn: '空间站实验室模块' },
                        { en: 'Cargo', cn: '货运' },
                        { en: 'Supply', cn: '补给' }
                    ]
                },
                {
                    en: 'Technology Demonstration',
                    cn: '技术验证',
                    subcategories: [
                        { en: 'Technology Demonstration', cn: '技术验证' },
                        { en: 'Technology Development', cn: '技术开发' },
                        { en: 'Experimental', cn: '实验' },
                        { en: 'Experimental Avionics', cn: '实验性航空电子设备' },
                        { en: 'Experimental Communications', cn: '实验性通信' },
                        { en: 'Technology Experiments', cn: '技术实验' },
                        { en: 'Engineering', cn: '工程学' },
                        { en: 'Mission Extension Technology', cn: '任务延期技术' },
                        { en: 'Reentry Test', cn: '再入测试' },
                        { en: 'Lifting Body Reentry Test', cn: '升力体再入测试' },
                        { en: 'Launch Vehicle Evaluation', cn: '发射载具评估' },
                        { en: 'Vehicle Evaluation', cn: '载具评估' },
                        { en: 'Calibration', cn: '校准' }
                    ]
                },
                {
                    en: 'Unknown',
                    cn: '未知',
                    subcategories: [
                        { en: 'Unknown', cn: '未知' }
                    ]
                },
                {
                    en: 'UV Research',
                    cn: '紫外线研究',
                    subcategories: [
                        { en: 'UV Research', cn: '紫外线研究' },
                        { en: 'UV', cn: '紫外线' },
                        { en: 'UV Astronomy', cn: '紫外线天文学' },
                        { en: 'EUV Spectroscopy', cn: '极紫外光谱学' }
                    ]
                },
                {
                    en: 'X-ray Research',
                    cn: 'X射线研究',
                    subcategories: [
                        { en: 'X-ray Research', cn: 'X射线研究' },
                        { en: 'X-ray', cn: 'X射线' },
                        { en: 'X-ray Astronomy', cn: 'X射线天文学' }
                    ]
                },
                {
                    en: 'Other',
                    cn: '其他',
                    subcategories: [
                        { en: 'Advertising', cn: '广告' },
                        { en: 'Art', cn: '艺术' },
                        { en: 'Aeronomy', cn: '高空大气研究' },
                        { en: 'Atmosphere', cn: '大气层研究' },
                        { en: 'Atmospheric Research', cn: '大气研究' },
                        { en: 'Air Density', cn: '大气密度测量' },
                        { en: 'Ballast', cn: '配重' },
                        { en: 'Entertainment', cn: '娱乐' },
                        { en: 'Gravity Waves', cn: '引力波研究' },
                        { en: 'Micrometeorite Research', cn: '微流星研究' },
                        { en: 'Nuclear Detection', cn: '核探测' },
                        { en: 'Thermospheric Research', cn: '热层研究' },
                        { en: 'Time Capsule', cn: '时间胶囊' },
                        { en: 'Upper Atmosphere', cn: '高层大气' }
                    ]
                }
            ];
            return {
                success: true,
                purposes: mainCategories
            };
        }
        catch (error) {
            this.logger.error(`Error fetching satellite purposes: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to fetch satellite purposes');
        }
    }
    async getLaunchContractors() {
        try {
            this.logger.debug('执行获取发射承包商集合查询');
            const sql = `
        SELECT DISTINCT
          contractor_value as contractor
        FROM (
          SELECT 
            launch_info_item->'value'->>'contractor' as contractor_value
          FROM 
            satellites s,
            jsonb_array_elements(s.launch_info) as launch_info_item
          WHERE 
            launch_info_item->'value'->>'contractor' IS NOT NULL
            AND launch_info_item->'value'->>'contractor' != ''
        ) as contractors
        ORDER BY 
          contractor;
      `;
            const result = await this.entityManager.query(sql);
            const rawContractors = result.map((row) => row.contractor.trim());
            const contractorMap = {};
            const knownTranslations = {
                'CASC': { cn: '中国航天科技集团', en: 'CASC' },
                'China Aerospace Science and Technology Corporation': { cn: '中国航天科技集团', en: 'China Aerospace Science and Technology Corporation' },
                'China Aerospace Science and Technology Corp.': { cn: '中国航天科技集团', en: 'China Aerospace Science and Technology Corp.' },
                'China Aerospace Science and Technology Corp. (CASC)': { cn: '中国航天科技集团', en: 'China Aerospace Science and Technology Corp. (CASC)' },
                'CASIC': { cn: '中国航天科工集团', en: 'CASIC' },
                'China Aerospace Science and Industry Corporation': { cn: '中国航天科工集团', en: 'China Aerospace Science and Industry Corporation' },
                'China Aerospace Science and Industry Corporation (CASIC)': { cn: '中国航天科工集团', en: 'China Aerospace Science and Industry Corporation (CASIC)' },
                'CALT': { cn: '中国运载火箭技术研究院', en: 'CALT' },
                'China Academy of Launch Vehicle Technology': { cn: '中国运载火箭技术研究院', en: 'China Academy of Launch Vehicle Technology' },
                'China Academy of Launch Vehicle Technology (CASIC)': { cn: '中国运载火箭技术研究院', en: 'China Academy of Launch Vehicle Technology (CASIC)' },
                'Chinese Academy of Launch Vehicle Technology (CALT)': { cn: '中国运载火箭技术研究院', en: 'Chinese Academy of Launch Vehicle Technology (CALT)' },
                'CAST': { cn: '中国空间技术研究院', en: 'CAST' },
                'China Academy of Space Technology': { cn: '中国空间技术研究院', en: 'China Academy of Space Technology' },
                'China Academy of Space Technology (CAST)': { cn: '中国空间技术研究院', en: 'China Academy of Space Technology (CAST)' },
                'China Academy of Space Technology (CAST)/ DFH Satellite Co. Ltd.': { cn: '中国空间技术研究院', en: 'China Academy of Space Technology (CAST)/ DFH Satellite Co. Ltd.' },
                'China Academy of Space Technology (CAST)/DFH Satellite Co. Ltd.': { cn: '中国空间技术研究院', en: 'China Academy of Space Technology (CAST)/DFH Satellite Co. Ltd.' },
                'China Academy of Space Technology (CAST)/National Centre for Space Studies (CNES)': { cn: '中国空间技术研究院', en: 'China Academy of Space Technology (CAST)/National Centre for Space Studies (CNES)' },
                'China Academy of Space Technology (CAST/DFH Satellite Co. Ltd.': { cn: '中国空间技术研究院', en: 'China Academy of Space Technology (CAST/DFH Satellite Co. Ltd.' },
                'China Academy of Space Technology/Thales Alenia Space': { cn: '中国空间技术研究院', en: 'China Academy of Space Technology/Thales Alenia Space' },
                'China Academy of Space Tecnology (CAST)': { cn: '中国空间技术研究院', en: 'China Academy of Space Tecnology (CAST)' },
                'China Academy of SpaceTechnology (CAST)': { cn: '中国空间技术研究院', en: 'China Academy of SpaceTechnology (CAST)' },
                'CAST/Changchun Light Technology Institute': { cn: '中国空间技术研究院', en: 'CAST/Changchun Light Technology Institute' },
                'CAST/DFH Satellite Co. Ltd.': { cn: '中国空间技术研究院', en: 'CAST/DFH Satellite Co. Ltd.' },
                'CAST/DFH Satellite Co. Ltd./HIT': { cn: '中国空间技术研究院', en: 'CAST/DFH Satellite Co. Ltd./HIT' },
                'Sun Yat-sen University/CAST': { cn: '中国空间技术研究院', en: 'Sun Yat-sen University/CAST' },
                'Chinese Academy of Space Technology (CAST)': { cn: '中国空间技术研究院', en: 'Chinese Academy of Space Technology (CAST)' },
                'SAST': { cn: '上海航天技术研究院', en: 'SAST' },
                'Shanghai Academy of Spaceflight Technology': { cn: '上海航天技术研究院', en: 'Shanghai Academy of Spaceflight Technology' },
                'Shanghai Academy of Satellite Technology (SAST)': { cn: '上海航天技术研究院', en: 'Shanghai Academy of Satellite Technology (SAST)' },
                'Shanghai Academy of Spaceflight Technology (SAST)': { cn: '上海航天技术研究院', en: 'Shanghai Academy of Spaceflight Technology (SAST)' },
                'Shanghai Academy of Spaceflight Technology (SAST)/Dongfanghong Satellite Co.': { cn: '上海航天技术研究院', en: 'Shanghai Academy of Spaceflight Technology (SAST)/Dongfanghong Satellite Co.' },
                'Shanghai Institute of Satellite Engineering at the Shanghai Academy of Spaceflight Technology': { cn: '上海航天技术研究院', en: 'Shanghai Institute of Satellite Engineering at the Shanghai Academy of Spaceflight Technology' },
                'CGWIC': { cn: '中国长城工业集团', en: 'CGWIC' },
                'China Great Wall Industry Corporation': { cn: '中国长城工业集团', en: 'China Great Wall Industry Corporation' },
                'China Great Wall Industry Corporation (CGWIC)': { cn: '中国长城工业集团', en: 'China Great Wall Industry Corporation (CGWIC)' },
                'ExPace': { cn: '航天科工火箭技术有限公司', en: 'ExPace' },
                'iSpace': { cn: '星际荣耀空间科技有限公司', en: 'iSpace' },
                'Landspace': { cn: '蓝箭航天', en: 'Landspace' },
                'OneSpace': { cn: '零壹空间', en: 'OneSpace' },
                'Galactic Energy': { cn: '星河动力', en: 'Galactic Energy' },
                'Deep Blue Aerospace': { cn: '深蓝航天', en: 'Deep Blue Aerospace' },
                'Space Pioneer': { cn: '天兵科技', en: 'Space Pioneer' },
                'DFH Satellite': { cn: '东方红卫星公司', en: 'DFH Satellite' },
                'DFH Satellite Co. Ltd.': { cn: '东方红卫星公司', en: 'DFH Satellite Co. Ltd.' },
                'DFH Satellite Co. Ltd./CASC': { cn: '东方红卫星公司', en: 'DFH Satellite Co. Ltd./CASC' },
                'Dongfanghong': { cn: '东方红', en: 'Dongfanghong' },
                'Aerospace Dongfanghong Satellite': { cn: '航天东方红卫星有限公司', en: 'Aerospace Dongfanghong Satellite' },
                'Aerospace Dongfanghong Satellite Co. Ltd. (CASC)': { cn: '航天东方红卫星有限公司', en: 'Aerospace Dongfanghong Satellite Co. Ltd. (CASC)' },
                'China Spacesat': { cn: '中国卫星', en: 'China Spacesat' },
                'Chinese Academy of Sciences': { cn: '中国科学院', en: 'Chinese Academy of Sciences' },
                'China Academy of Sciences': { cn: '中国科学院', en: 'China Academy of Sciences' },
                'China Academy of Science': { cn: '中国科学院', en: 'China Academy of Science' },
                'China Academy of Sciences (CAS)/National Space Science Center': { cn: '中国科学院', en: 'China Academy of Sciences (CAS)/National Space Science Center' },
                'Institute of Mechanics of the Chinese Academy of Science (IMCAS)': { cn: '中国科学院力学研究所', en: 'Institute of Mechanics of the Chinese Academy of Science (IMCAS)' },
                'Institute of Software, Chinese Academy of Sciences': { cn: '中国科学院软件研究所', en: 'Institute of Software, Chinese Academy of Sciences' },
                'Innovation Academy for Microsatellites/Chinese Academy of Sciences': { cn: '中国科学院微小卫星创新研究院', en: 'Innovation Academy for Microsatellites/Chinese Academy of Sciences' },
                'Shanghai Institute of Microsatellite Innovation, Chinese Academy of Sciences': { cn: '中国科学院上海微小卫星工程中心', en: 'Shanghai Institute of Microsatellite Innovation, Chinese Academy of Sciences' },
                'Ninth Academy of CASIC': { cn: '中国航天科工集团', en: 'Ninth Academy of CASIC' },
                'Shenzhen Aerospace Dongfanghong Satellite Ltd. (CASC)': { cn: '中国航天科技集团', en: 'Shenzhen Aerospace Dongfanghong Satellite Ltd. (CASC)' },
                'Space Technology Research Institute (part of CASC)': { cn: '中国航天科技集团', en: 'Space Technology Research Institute (part of CASC)' },
                'Chinese Research Institute of Space Technology/Changchun Photomechanical Institute (CAS)': { cn: '中国空间技术研究院/长春光机所', en: 'Chinese Research Institute of Space Technology/Changchun Photomechanical Institute (CAS)' },
                'China Electronic Techology Group': { cn: '中国电子科技集团', en: 'China Electronic Techology Group' },
                'China Earthquake Administration/Italian Institute for Nuclear Physics': { cn: '中国地震局/意大利核物理研究所', en: 'China Earthquake Administration/Italian Institute for Nuclear Physics' },
                'Zhejiang University': { cn: '浙江大学', en: 'Zhejiang University' },
                'Zhongke Xingrui': { cn: '中科星睿', en: 'Zhongke Xingrui' },
                'Zhuhai Orbita Aerospace Science and Technology Co.': { cn: '珠海欧比特宇航科技股份有限公司', en: 'Zhuhai Orbita Aerospace Science and Technology Co.' },
                'Zhuhai Orbita Control Engineering Co. Ltd.': { cn: '珠海欧比特控制工程有限公司', en: 'Zhuhai Orbita Control Engineering Co. Ltd.' },
                'Beijing Aerospace Propulsion Institute': { cn: '北京航天动力研究所', en: 'Beijing Aerospace Propulsion Institute' },
                'Beijing Institute of Satellite Information Engineering': { cn: '北京卫星信息工程研究所', en: 'Beijing Institute of Satellite Information Engineering' },
                'Beijing Institute of Spacecraft Environment Engineering': { cn: '北京卫星环境工程研究所', en: 'Beijing Institute of Spacecraft Environment Engineering' },
                'Beijing Institute of Space Mechanics and Electricity': { cn: '北京空间机电研究所', en: 'Beijing Institute of Space Mechanics and Electricity' },
                'Beijing Institute of Tracking and Telecommunications Technology': { cn: '北京跟踪与通信技术研究所', en: 'Beijing Institute of Tracking and Telecommunications Technology' },
                'Beijing Landview Mapping Information Technology Co. Ltd.': { cn: '北京兰德华信息技术有限公司', en: 'Beijing Landview Mapping Information Technology Co. Ltd.' },
                'Beijing Microelectronics Technology Institute': { cn: '北京微电子技术研究所', en: 'Beijing Microelectronics Technology Institute' },
                'Beijing Smart Satellite Technology Co. Ltd.': { cn: '北京智星卫星科技有限公司', en: 'Beijing Smart Satellite Technology Co. Ltd.' },
                'Beijing SPACE VIEW Technology Co., Ltd': { cn: '北京天目创新科技有限公司', en: 'Beijing SPACE VIEW Technology Co., Ltd' },
                'Beijing Xinwei Telecom Technology Co. Ltd.': { cn: '北京信威通信技术有限公司', en: 'Beijing Xinwei Telecom Technology Co. Ltd.' },
                'Beihang University/APSCO': { cn: '北京航空航天大学/亚太空间合作组织', en: 'Beihang University/APSCO' },
                'Beijing Commsat Technology Development Co. Ltd.': { cn: '北京航天宏图信息技术股份有限公司', en: 'Beijing Commsat Technology Development Co. Ltd.' },
                'Beijing MinoSpace Technology Co., Ltd.': { cn: '北京微纳星空科技有限公司', en: 'Beijing MinoSpace Technology Co., Ltd.' },
                'Beijing ZeroG Space Technology Co., Ltd.': { cn: '北京零重空间科技有限公司', en: 'Beijing ZeroG Space Technology Co., Ltd.' },
                'Beijing ZeroG Technology Co. Ltd.': { cn: '北京零重科技有限公司', en: 'Beijing ZeroG Technology Co. Ltd.' },
                'Beijing Zhixing Space Technology Co. Ltd': { cn: '北京智星空间科技有限公司', en: 'Beijing Zhixing Space Technology Co. Ltd' },
                'Nanjing University of Aeronautics and Astronautics': { cn: '南京航空航天大学', en: 'Nanjing University of Aeronautics and Astronautics' },
                'Harbin Institute of Technology': { cn: '哈尔滨工业大学', en: 'Harbin Institute of Technology' },
                'Xi\'an Aerospace Propulsion Institute': { cn: '西安航天动力研究所', en: 'Xi\'an Aerospace Propulsion Institute' },
                'Shenzhen Aerospace Oriental Red Satellite Ltd.': { cn: '深圳航天东方红卫星有限公司', en: 'Shenzhen Aerospace Oriental Red Satellite Ltd.' },
                'Shenzhen DFH HIT Satellite Ltd.': { cn: '深圳东方红哈工大卫星有限公司', en: 'Shenzhen DFH HIT Satellite Ltd.' },
                'Shenzhen Institutes of Advanced Technology': { cn: '深圳先进技术研究院', en: 'Shenzhen Institutes of Advanced Technology' },
                'Tsinghua University': { cn: '清华大学', en: 'Tsinghua University' },
                'ChangGuang Satellite Technology': { cn: '长光卫星技术有限公司', en: 'ChangGuang Satellite Technology' },
                'Sichuan University': { cn: '四川大学', en: 'Sichuan University' },
                'Tianjin University': { cn: '天津大学', en: 'Tianjin University' },
                'Wuhan University': { cn: '武汉大学', en: 'Wuhan University' },
                'Academy of Military Science': { cn: '中国人民解放军军事科学院', en: 'Academy of Military Science' },
                'SpaceX': { cn: 'SpaceX', en: 'SpaceX' },
                'ULA': { cn: '联合发射联盟', en: 'ULA' },
                'United Launch Alliance': { cn: '联合发射联盟', en: 'United Launch Alliance' },
                'Arianespace': { cn: '阿丽亚娜空间', en: 'Arianespace' },
                'Northrop Grumman': { cn: '诺斯罗普·格鲁曼', en: 'Northrop Grumman' },
                'Rocket Lab': { cn: '火箭实验室', en: 'Rocket Lab' },
                'Roscosmos': { cn: '俄罗斯航天局', en: 'Roscosmos' },
                'ISRO': { cn: '印度空间研究组织', en: 'ISRO' },
                'Indian Space Research Organisation': { cn: '印度空间研究组织', en: 'Indian Space Research Organisation' },
                'JAXA': { cn: '日本宇宙航空研究开发机构', en: 'JAXA' },
                'Japan Aerospace Exploration Agency': { cn: '日本宇宙航空研究开发机构', en: 'Japan Aerospace Exploration Agency' },
                'Blue Origin': { cn: '蓝色起源', en: 'Blue Origin' },
                'Virgin Orbit': { cn: '维珍轨道', en: 'Virgin Orbit' },
                'Virgin Galactic': { cn: '维珍银河', en: 'Virgin Galactic' },
                'Mitsubishi Heavy Industries': { cn: '三菱重工', en: 'Mitsubishi Heavy Industries' },
                'NASA': { cn: '美国国家航空航天局', en: 'NASA' },
                'National Aeronautics and Space Administration': { cn: '美国国家航空航天局', en: 'National Aeronautics and Space Administration' },
                'ESA': { cn: '欧洲航天局', en: 'ESA' },
                'European Space Agency': { cn: '欧洲航天局', en: 'European Space Agency' }
            };
            const partialMatches = [
                { pattern: /\bCASC\b/i, translation: { cn: '中国航天科技集团', en: 'CASC' } },
                { pattern: /China Aerospace Science and Technology/i, translation: { cn: '中国航天科技集团', en: 'China Aerospace Science and Technology Corp.' } },
                { pattern: /\bCASIC\b/i, translation: { cn: '中国航天科工集团', en: 'CASIC' } },
                { pattern: /China Aerospace Science and Industry/i, translation: { cn: '中国航天科工集团', en: 'China Aerospace Science and Industry Corporation' } },
                { pattern: /\bCALT\b/i, translation: { cn: '中国运载火箭技术研究院', en: 'CALT' } },
                { pattern: /(Chinese|China) Academy of Launch Vehicle Technology/i, translation: { cn: '中国运载火箭技术研究院', en: 'China Academy of Launch Vehicle Technology' } },
                { pattern: /\bCAST\b/i, translation: { cn: '中国空间技术研究院', en: 'CAST' } },
                { pattern: /(Chinese|China) Academy of Space Technology/i, translation: { cn: '中国空间技术研究院', en: 'China Academy of Space Technology' } },
                { pattern: /\bSAST\b/i, translation: { cn: '上海航天技术研究院', en: 'SAST' } },
                { pattern: /Shanghai Academy of (Spaceflight|Satellite)/i, translation: { cn: '上海航天技术研究院', en: 'Shanghai Academy of Spaceflight Technology' } },
                { pattern: /\bCGWIC\b/i, translation: { cn: '中国长城工业集团', en: 'CGWIC' } },
                { pattern: /China Great Wall Industry/i, translation: { cn: '中国长城工业集团', en: 'China Great Wall Industry Corporation' } },
                { pattern: /Dongfanghong/i, translation: { cn: '东方红', en: 'Dongfanghong' } },
                { pattern: /DFH Satellite/i, translation: { cn: '东方红卫星公司', en: 'DFH Satellite' } },
                { pattern: /(Chinese|China) Academy of Sciences?/i, translation: { cn: '中国科学院', en: 'Chinese Academy of Sciences' } },
                { pattern: /China Spacesat/i, translation: { cn: '中国卫星', en: 'China Spacesat' } },
                { pattern: /China Electronic/i, translation: { cn: '中国电子科技集团', en: 'China Electronic Technology Group' } },
                { pattern: /Academy of Military Science/i, translation: { cn: '中国人民解放军军事科学院', en: 'Academy of Military Science' } },
                { pattern: /Zhejiang University/i, translation: { cn: '浙江大学', en: 'Zhejiang University' } },
                { pattern: /Zhongke/i, translation: { cn: '中科', en: 'Zhongke' } },
                { pattern: /Zhuhai Orbita/i, translation: { cn: '珠海欧比特', en: 'Zhuhai Orbita' } },
                { pattern: /Nanjing University/i, translation: { cn: '南京大学', en: 'Nanjing University' } },
                { pattern: /Harbin Institute/i, translation: { cn: '哈尔滨工业大学', en: 'Harbin Institute of Technology' } },
                { pattern: /Xi'an Aerospace/i, translation: { cn: '西安航天', en: 'Xi\'an Aerospace' } },
                { pattern: /Shenzhen (Aerospace|DFH|Institutes)/i, translation: { cn: '深圳', en: 'Shenzhen' } },
                { pattern: /Tsinghua University/i, translation: { cn: '清华大学', en: 'Tsinghua University' } },
                { pattern: /ChangGuang Satellite/i, translation: { cn: '长光卫星', en: 'ChangGuang Satellite' } },
                { pattern: /Sichuan University/i, translation: { cn: '四川大学', en: 'Sichuan University' } },
                { pattern: /Tianjin University/i, translation: { cn: '天津大学', en: 'Tianjin University' } },
                { pattern: /Wuhan University/i, translation: { cn: '武汉大学', en: 'Wuhan University' } }
            ];
            for (const contractor of rawContractors) {
                if (knownTranslations[contractor]) {
                    contractorMap[contractor] = knownTranslations[contractor];
                    continue;
                }
                let matched = false;
                for (const { pattern, translation } of partialMatches) {
                    if (pattern.test(contractor)) {
                        contractorMap[contractor] = {
                            cn: translation.cn,
                            en: contractor
                        };
                        matched = true;
                        break;
                    }
                }
                if (matched)
                    continue;
                const isEnglish = /[a-zA-Z]/.test(contractor);
                const isChinese = /[\u4e00-\u9fa5]/.test(contractor);
                if (isEnglish && !isChinese) {
                    if (/China|Chinese/i.test(contractor)) {
                        contractorMap[contractor] = {
                            cn: `中国${contractor.replace(/China|Chinese/i, '').trim()}`,
                            en: contractor
                        };
                    }
                    else if (/^Shanghai/i.test(contractor)) {
                        contractorMap[contractor] = {
                            cn: `上海${contractor.replace(/Shanghai/i, '').trim()}`,
                            en: contractor
                        };
                    }
                    else if (/^Shenzhen/i.test(contractor)) {
                        contractorMap[contractor] = {
                            cn: `深圳${contractor.replace(/Shenzhen/i, '').trim()}`,
                            en: contractor
                        };
                    }
                    else if (/^Zhuhai/i.test(contractor)) {
                        contractorMap[contractor] = {
                            cn: `珠海${contractor.replace(/Zhuhai/i, '').trim()}`,
                            en: contractor
                        };
                    }
                    else if (/^Nanjing/i.test(contractor)) {
                        contractorMap[contractor] = {
                            cn: `南京${contractor.replace(/Nanjing/i, '').trim()}`,
                            en: contractor
                        };
                    }
                    else if (/^Xi'an/i.test(contractor)) {
                        contractorMap[contractor] = {
                            cn: `西安${contractor.replace(/Xi'an/i, '').trim()}`,
                            en: contractor
                        };
                    }
                    else if (/^Wuhan/i.test(contractor)) {
                        contractorMap[contractor] = {
                            cn: `武汉${contractor.replace(/Wuhan/i, '').trim()}`,
                            en: contractor
                        };
                    }
                    else if (/^Tianjin/i.test(contractor)) {
                        contractorMap[contractor] = {
                            cn: `天津${contractor.replace(/Tianjin/i, '').trim()}`,
                            en: contractor
                        };
                    }
                    else if (/^Harbin/i.test(contractor)) {
                        contractorMap[contractor] = {
                            cn: `哈尔滨${contractor.replace(/Harbin/i, '').trim()}`,
                            en: contractor
                        };
                    }
                    else if (/^Hangzhou|Zhejiang/i.test(contractor)) {
                        contractorMap[contractor] = {
                            cn: `浙江${contractor.replace(/Hangzhou|Zhejiang/i, '').trim()}`,
                            en: contractor
                        };
                    }
                    else if (/^Zhongke/i.test(contractor)) {
                        contractorMap[contractor] = {
                            cn: `中科${contractor.replace(/Zhongke/i, '').trim()}`,
                            en: contractor
                        };
                    }
                    else {
                        contractorMap[contractor] = { cn: contractor, en: contractor };
                    }
                }
                else if (isChinese && !isEnglish) {
                    let found = false;
                    for (const [key, value] of Object.entries(knownTranslations)) {
                        if (value.cn === contractor) {
                            contractorMap[contractor] = value;
                            found = true;
                            break;
                        }
                    }
                    if (!found) {
                        contractorMap[contractor] = { cn: contractor, en: contractor };
                    }
                }
                else if (isChinese && isEnglish) {
                    contractorMap[contractor] = { cn: contractor, en: contractor };
                }
                else {
                    contractorMap[contractor] = { cn: contractor, en: contractor };
                }
            }
            const processedContractors = Object.values(contractorMap).sort((a, b) => a.en.localeCompare(b.en));
            this.logger.debug(`获取到 ${processedContractors.length} 个不同的发射承包商`);
            return {
                success: true,
                contractors: processedContractors
            };
        }
        catch (error) {
            this.logger.error(`获取发射承包商集合失败: ${error.message}`, error.stack);
            throw new Error(`获取发射承包商集合失败: ${error.message}`);
        }
    }
    async getLaunchSites() {
        try {
            this.logger.debug('执行获取发射地点集合查询');
            const sql = `
        SELECT DISTINCT
          launch_site_value as launch_site
        FROM (
          SELECT 
            launch_info_item->'value'->>'launch_site' as launch_site_value
          FROM 
            satellites s,
            jsonb_array_elements(s.launch_info) as launch_info_item
          WHERE 
            launch_info_item->'value'->>'launch_site' IS NOT NULL
            AND launch_info_item->'value'->>'launch_site' != ''
        ) as launch_sites
        ORDER BY 
          launch_site;
      `;
            const result = await this.entityManager.query(sql);
            const rawLaunchSites = result.map((row) => row.launch_site.trim());
            const launchSiteMap = {};
            const knownTranslations = {
                'JIUQUAN SATELLITE LAUNCH CENTER': { cn: '酒泉卫星发射中心', en: 'JIUQUAN SATELLITE LAUNCH CENTER' },
                'JIUQUAN SATELLITE LAUNCH CENTER (JSLC)': { cn: '酒泉卫星发射中心', en: 'JIUQUAN SATELLITE LAUNCH CENTER (JSLC)' },
                'JIUQUAN, CHINA': { cn: '酒泉卫星发射中心', en: 'JIUQUAN, CHINA' },
                'TAIYUAN SATELLITE LAUNCH CENTER': { cn: '太原卫星发射中心', en: 'TAIYUAN SATELLITE LAUNCH CENTER' },
                'TAIYUAN SATELLITE LAUNCH CENTER (TSLC)': { cn: '太原卫星发射中心', en: 'TAIYUAN SATELLITE LAUNCH CENTER (TSLC)' },
                'TAIYUAN, CHINA': { cn: '太原卫星发射中心', en: 'TAIYUAN, CHINA' },
                'XICHANG SATELLITE LAUNCH CENTER': { cn: '西昌卫星发射中心', en: 'XICHANG SATELLITE LAUNCH CENTER' },
                'XICHANG SATELLITE LAUNCH CENTER (XSLC)': { cn: '西昌卫星发射中心', en: 'XICHANG SATELLITE LAUNCH CENTER (XSLC)' },
                'XICHANG, CHINA': { cn: '西昌卫星发射中心', en: 'XICHANG, CHINA' },
                'WENCHANG SATELLITE LAUNCH CENTER': { cn: '文昌航天发射场', en: 'WENCHANG SATELLITE LAUNCH CENTER' },
                'WENCHANG SPACECRAFT LAUNCH SITE': { cn: '文昌航天发射场', en: 'WENCHANG SPACECRAFT LAUNCH SITE' },
                'WENCHANG, CHINA': { cn: '文昌航天发射场', en: 'WENCHANG, CHINA' },
                'CAPE CANAVERAL': { cn: '卡纳维拉尔角', en: 'CAPE CANAVERAL' },
                'CAPE CANAVERAL AIR FORCE STATION': { cn: '卡纳维拉尔角空军基地', en: 'CAPE CANAVERAL AIR FORCE STATION' },
                'CAPE CANAVERAL AIR FORCE STATION (CCAFS)': { cn: '卡纳维拉尔角空军基地', en: 'CAPE CANAVERAL AIR FORCE STATION (CCAFS)' },
                'CAPE CANAVERAL SPACE FORCE STATION': { cn: '卡纳维拉尔角太空部队基地', en: 'CAPE CANAVERAL SPACE FORCE STATION' },
                'CAPE CANAVERAL SPACE FORCE STATION (CCSFS)': { cn: '卡纳维拉尔角太空部队基地', en: 'CAPE CANAVERAL SPACE FORCE STATION (CCSFS)' },
                'KENNEDY SPACE CENTER': { cn: '肯尼迪航天中心', en: 'KENNEDY SPACE CENTER' },
                'KENNEDY SPACE CENTER (KSC)': { cn: '肯尼迪航天中心', en: 'KENNEDY SPACE CENTER (KSC)' },
                'VANDENBERG AIR FORCE BASE': { cn: '范登堡空军基地', en: 'VANDENBERG AIR FORCE BASE' },
                'VANDENBERG AIR FORCE BASE (VAFB)': { cn: '范登堡空军基地', en: 'VANDENBERG AIR FORCE BASE (VAFB)' },
                'VANDENBERG SPACE FORCE BASE': { cn: '范登堡太空部队基地', en: 'VANDENBERG SPACE FORCE BASE' },
                'VANDENBERG SPACE FORCE BASE (VSFB)': { cn: '范登堡太空部队基地', en: 'VANDENBERG SPACE FORCE BASE (VSFB)' },
                'WALLOPS FLIGHT FACILITY': { cn: '沃勒普斯飞行设施', en: 'WALLOPS FLIGHT FACILITY' },
                'WALLOPS ISLAND': { cn: '沃勒普斯岛', en: 'WALLOPS ISLAND' },
                'WALLOPS ISLAND FLIGHT FACILITY': { cn: '沃勒普斯岛飞行设施', en: 'WALLOPS ISLAND FLIGHT FACILITY' },
                'BAIKONUR COSMODROME': { cn: '拜科努尔航天发射场', en: 'BAIKONUR COSMODROME' },
                'BAIKONUR COSMODROME, KAZAKHSTAN': { cn: '拜科努尔航天发射场，哈萨克斯坦', en: 'BAIKONUR COSMODROME, KAZAKHSTAN' },
                'PLESETSK COSMODROME': { cn: '普列谢茨克航天发射场', en: 'PLESETSK COSMODROME' },
                'PLESETSK MISSILE AND SPACE COMPLEX': { cn: '普列谢茨克导弹和航天综合体', en: 'PLESETSK MISSILE AND SPACE COMPLEX' },
                'VOSTOCHNY COSMODROME': { cn: '东方航天发射场', en: 'VOSTOCHNY COSMODROME' },
                'KAPUSTIN YAR': { cn: '卡普斯京亚尔', en: 'KAPUSTIN YAR' },
                'DOMBAROVSKIY': { cn: '多姆巴罗夫斯基', en: 'DOMBAROVSKIY' },
                'SVOBODNY COSMODROME': { cn: '斯沃博德尼航天发射场', en: 'SVOBODNY COSMODROME' },
                'YASNY LAUNCH BASE': { cn: '亚斯尼发射基地', en: 'YASNY LAUNCH BASE' },
                'TYURATAM MISSILE AND SPACE COMPLEX (TTMTR)': { cn: '秋拉塔姆导弹和航天综合体', en: 'TYURATAM MISSILE AND SPACE COMPLEX (TTMTR)' },
                'KOUROU, FRENCH GUIANA': { cn: '库鲁，法属圭亚那', en: 'KOUROU, FRENCH GUIANA' },
                'GUIANA SPACE CENTER': { cn: '圭亚那航天中心', en: 'GUIANA SPACE CENTER' },
                'GUIANA SPACE CENTER (CSG)': { cn: '圭亚那航天中心', en: 'GUIANA SPACE CENTER (CSG)' },
                'TANEGASHIMA SPACE CENTER': { cn: '种子岛宇宙中心', en: 'TANEGASHIMA SPACE CENTER' },
                'TANEGASHIMA SPACE CENTER (TNSC)': { cn: '种子岛宇宙中心', en: 'TANEGASHIMA SPACE CENTER (TNSC)' },
                'UCHINOURA SPACE CENTER': { cn: '内之浦宇宙空间观测所', en: 'UCHINOURA SPACE CENTER' },
                'SATISH DHAWAN SPACE CENTER': { cn: '萨蒂什·达万航天中心', en: 'SATISH DHAWAN SPACE CENTER' },
                'SATISH DHAWAN SPACE CENTER (SDSC)': { cn: '萨蒂什·达万航天中心', en: 'SATISH DHAWAN SPACE CENTER (SDSC)' },
                'SRIHARIKOTA, INDIA': { cn: '斯里哈里科塔，印度', en: 'SRIHARIKOTA, INDIA' },
                'SEMNAN SATELLITE LAUNCH CENTER': { cn: '塞姆南卫星发射中心', en: 'SEMNAN SATELLITE LAUNCH CENTER' },
                'NARO SPACE CENTER, SOUTH KOREA': { cn: '罗老宇航中心，韩国', en: 'NARO SPACE CENTER, SOUTH KOREA' },
                'WOOMERA TEST RANGE': { cn: '沃默拉试验场', en: 'WOOMERA TEST RANGE' },
                'SHAHROUD': { cn: '沙赫鲁德', en: 'SHAHROUD' },
                'PALMACHIM AIR FORCE BASE': { cn: '帕尔马希姆空军基地', en: 'PALMACHIM AIR FORCE BASE' },
                'SOHAE SATELLITE LAUNCHING STATION': { cn: '西海卫星发射场', en: 'SOHAE SATELLITE LAUNCHING STATION' },
                'ALCANTARA LAUNCH CENTER': { cn: '阿尔坎塔拉发射中心', en: 'ALCANTARA LAUNCH CENTER' },
                'ROCKET LAB LAUNCH COMPLEX 1': { cn: '火箭实验室发射复合体1', en: 'ROCKET LAB LAUNCH COMPLEX 1' },
                'PACIFIC SPACEPORT COMPLEX, ALASKA': { cn: '阿拉斯加太平洋太空港', en: 'PACIFIC SPACEPORT COMPLEX, ALASKA' },
                'KODIAK LAUNCH COMPLEX': { cn: '科迪亚克发射综合体', en: 'KODIAK LAUNCH COMPLEX' },
                'KWAJALEIN ATOLL': { cn: '夸贾林环礁', en: 'KWAJALEIN ATOLL' },
                'MAHIA PENINSULA, NEW ZEALAND': { cn: '新西兰玛希亚半岛', en: 'MAHIA PENINSULA, NEW ZEALAND' },
                'BARENTS SEA': { cn: '巴伦支海', en: 'BARENTS SEA' },
                'SEA LAUNCH PLATFORM': { cn: '海上发射平台', en: 'SEA LAUNCH PLATFORM' },
                'LAUNCH PLATFORM, BARENTS SEA': { cn: '发射平台，巴伦支海', en: 'LAUNCH PLATFORM, BARENTS SEA' },
                'MO I RANA, NORWAY': { cn: '挪威莫伊拉纳', en: 'MO I RANA, NORWAY' },
                'ORBITAL PLATFORM ODYSSEY': { cn: '奥德赛轨道平台', en: 'ORBITAL PLATFORM ODYSSEY' },
                'WHITE SANDS MISSILE RANGE': { cn: '白沙导弹靶场', en: 'WHITE SANDS MISSILE RANGE' },
                'MOJAVE AIR AND SPACE PORT': { cn: '莫哈维航空航天港', en: 'MOJAVE AIR AND SPACE PORT' },
                'SPACEPORT AMERICA': { cn: '美国太空港', en: 'SPACEPORT AMERICA' },
                'INTERNATIONAL SPACE STATION': { cn: '国际空间站', en: 'INTERNATIONAL SPACE STATION' },
                'YELLOW SEA': { cn: '黄海', en: 'YELLOW SEA' }
            };
            const partialMatches = [
                { pattern: /JIUQUAN/i, translation: { cn: '酒泉卫星发射中心', en: 'JIUQUAN SATELLITE LAUNCH CENTER' } },
                { pattern: /TAIYUAN/i, translation: { cn: '太原卫星发射中心', en: 'TAIYUAN SATELLITE LAUNCH CENTER' } },
                { pattern: /XICHANG/i, translation: { cn: '西昌卫星发射中心', en: 'XICHANG SATELLITE LAUNCH CENTER' } },
                { pattern: /WENCHANG/i, translation: { cn: '文昌航天发射场', en: 'WENCHANG SATELLITE LAUNCH CENTER' } },
                { pattern: /CAPE CANAVERAL/i, translation: { cn: '卡纳维拉尔角', en: 'CAPE CANAVERAL' } },
                { pattern: /KENNEDY/i, translation: { cn: '肯尼迪航天中心', en: 'KENNEDY SPACE CENTER' } },
                { pattern: /VANDENBERG/i, translation: { cn: '范登堡', en: 'VANDENBERG' } },
                { pattern: /WALLOPS/i, translation: { cn: '沃勒普斯', en: 'WALLOPS' } },
                { pattern: /BAIKONUR/i, translation: { cn: '拜科努尔航天发射场', en: 'BAIKONUR COSMODROME' } },
                { pattern: /PLESETSK/i, translation: { cn: '普列谢茨克航天发射场', en: 'PLESETSK COSMODROME' } },
                { pattern: /VOSTOCHNY/i, translation: { cn: '东方航天发射场', en: 'VOSTOCHNY COSMODROME' } },
                { pattern: /KOUROU|GUIANA/i, translation: { cn: '圭亚那航天中心', en: 'GUIANA SPACE CENTER' } },
                { pattern: /TANEGASHIMA/i, translation: { cn: '种子岛宇宙中心', en: 'TANEGASHIMA SPACE CENTER' } },
                { pattern: /UCHINOURA/i, translation: { cn: '内之浦宇宙空间观测所', en: 'UCHINOURA SPACE CENTER' } },
                { pattern: /SATISH|SRIHARIKOTA/i, translation: { cn: '萨蒂什·达万航天中心', en: 'SATISH DHAWAN SPACE CENTER' } },
                { pattern: /TYURATAM/i, translation: { cn: '秋拉塔姆导弹和航天综合体', en: 'TYURATAM MISSILE AND SPACE COMPLEX' } }
            ];
            for (const launchSite of rawLaunchSites) {
                if (knownTranslations[launchSite]) {
                    launchSiteMap[launchSite] = knownTranslations[launchSite];
                    continue;
                }
                let matched = false;
                for (const { pattern, translation } of partialMatches) {
                    if (pattern.test(launchSite)) {
                        launchSiteMap[launchSite] = {
                            cn: translation.cn,
                            en: launchSite
                        };
                        matched = true;
                        break;
                    }
                }
                if (matched)
                    continue;
                const isEnglish = /[a-zA-Z]/.test(launchSite);
                const isChinese = /[\u4e00-\u9fa5]/.test(launchSite);
                if (isEnglish && !isChinese) {
                    launchSiteMap[launchSite] = { cn: launchSite, en: launchSite };
                }
                else if (isChinese && !isEnglish) {
                    let found = false;
                    for (const [key, value] of Object.entries(knownTranslations)) {
                        if (value.cn === launchSite) {
                            launchSiteMap[launchSite] = value;
                            found = true;
                            break;
                        }
                    }
                    if (!found) {
                        launchSiteMap[launchSite] = { cn: launchSite, en: launchSite };
                    }
                }
                else if (isChinese && isEnglish) {
                    launchSiteMap[launchSite] = { cn: launchSite, en: launchSite };
                }
                else {
                    launchSiteMap[launchSite] = { cn: launchSite, en: launchSite };
                }
            }
            const processedLaunchSites = Object.values(launchSiteMap).sort((a, b) => a.en.localeCompare(b.en));
            this.logger.debug(`获取到 ${processedLaunchSites.length} 个不同的发射地点`);
            return {
                success: true,
                launch_sites: processedLaunchSites
            };
        }
        catch (error) {
            this.logger.error(`获取发射地点集合失败: ${error.message}`, error.stack);
            throw new Error(`获取发射地点集合失败: ${error.message}`);
        }
    }
    async getLaunchVehicles() {
        try {
            this.logger.debug('执行获取发射载具集合查询');
            const sql = `
        SELECT DISTINCT
          launch_vehicle_value as launch_vehicle
        FROM (
          SELECT 
            launch_info_item->'value'->>'launch_vehicle' as launch_vehicle_value
          FROM 
            satellites s,
            jsonb_array_elements(s.launch_info) as launch_info_item
          WHERE 
            launch_info_item->'value'->>'launch_vehicle' IS NOT NULL
            AND launch_info_item->'value'->>'launch_vehicle' != ''
        ) as launch_vehicles
        ORDER BY 
          launch_vehicle;
      `;
            const result = await this.entityManager.query(sql);
            const rawLaunchVehicles = result.map((row) => row.launch_vehicle.trim());
            const launchVehicleMap = {};
            const knownTranslations = {
                'LONG MARCH 1': { cn: '长征一号', en: 'LONG MARCH 1' },
                'LONG MARCH 2': { cn: '长征二号', en: 'LONG MARCH 2' },
                'LONG MARCH 2C': { cn: '长征二号丙', en: 'LONG MARCH 2C' },
                'LONG MARCH 2D': { cn: '长征二号丁', en: 'LONG MARCH 2D' },
                'LONG MARCH 2F': { cn: '长征二号F', en: 'LONG MARCH 2F' },
                'LONG MARCH 3': { cn: '长征三号', en: 'LONG MARCH 3' },
                'LONG MARCH 3A': { cn: '长征三号甲', en: 'LONG MARCH 3A' },
                'LONG MARCH 3B': { cn: '长征三号乙', en: 'LONG MARCH 3B' },
                'LONG MARCH 3C': { cn: '长征三号丙', en: 'LONG MARCH 3C' },
                'LONG MARCH 4': { cn: '长征四号', en: 'LONG MARCH 4' },
                'LONG MARCH 4B': { cn: '长征四号乙', en: 'LONG MARCH 4B' },
                'LONG MARCH 4C': { cn: '长征四号丙', en: 'LONG MARCH 4C' },
                'LONG MARCH 5': { cn: '长征五号', en: 'LONG MARCH 5' },
                'LONG MARCH 5B': { cn: '长征五号乙', en: 'LONG MARCH 5B' },
                'LONG MARCH 6': { cn: '长征六号', en: 'LONG MARCH 6' },
                'LONG MARCH 6A': { cn: '长征六号甲', en: 'LONG MARCH 6A' },
                'LONG MARCH 7': { cn: '长征七号', en: 'LONG MARCH 7' },
                'LONG MARCH 7A': { cn: '长征七号甲', en: 'LONG MARCH 7A' },
                'LONG MARCH 8': { cn: '长征八号', en: 'LONG MARCH 8' },
                'LONG MARCH 9': { cn: '长征九号', en: 'LONG MARCH 9' },
                'LONG MARCH 11': { cn: '长征十一号', en: 'LONG MARCH 11' },
                'CZ-1': { cn: '长征一号', en: 'CZ-1' },
                'CZ-2': { cn: '长征二号', en: 'CZ-2' },
                'CZ-2C': { cn: '长征二号丙', en: 'CZ-2C' },
                'CZ-2D': { cn: '长征二号丁', en: 'CZ-2D' },
                'CZ-2F': { cn: '长征二号F', en: 'CZ-2F' },
                'CZ-3': { cn: '长征三号', en: 'CZ-3' },
                'CZ-3A': { cn: '长征三号甲', en: 'CZ-3A' },
                'CZ-3B': { cn: '长征三号乙', en: 'CZ-3B' },
                'CZ-3C': { cn: '长征三号丙', en: 'CZ-3C' },
                'CZ-4': { cn: '长征四号', en: 'CZ-4' },
                'CZ-4B': { cn: '长征四号乙', en: 'CZ-4B' },
                'CZ-4C': { cn: '长征四号丙', en: 'CZ-4C' },
                'CZ-5': { cn: '长征五号', en: 'CZ-5' },
                'CZ-5B': { cn: '长征五号乙', en: 'CZ-5B' },
                'CZ-6': { cn: '长征六号', en: 'CZ-6' },
                'CZ-6A': { cn: '长征六号甲', en: 'CZ-6A' },
                'CZ-7': { cn: '长征七号', en: 'CZ-7' },
                'CZ-7A': { cn: '长征七号甲', en: 'CZ-7A' },
                'CZ-8': { cn: '长征八号', en: 'CZ-8' },
                'CZ-9': { cn: '长征九号', en: 'CZ-9' },
                'CZ-11': { cn: '长征十一号', en: 'CZ-11' },
                'KUAIZHOU': { cn: '快舟', en: 'KUAIZHOU' },
                'KUAIZHOU-1A': { cn: '快舟一号甲', en: 'KUAIZHOU-1A' },
                'KUAIZHOU-11': { cn: '快舟十一号', en: 'KUAIZHOU-11' },
                'HYPERBOLA-1': { cn: '双曲线一号', en: 'HYPERBOLA-1' },
                'HYPERBOLA-2': { cn: '双曲线二号', en: 'HYPERBOLA-2' },
                'CERES-1': { cn: '谷神星一号', en: 'CERES-1' },
                'JIELONG-1': { cn: '捷龙一号', en: 'JIELONG-1' },
                'JIELONG-3': { cn: '捷龙三号', en: 'JIELONG-3' },
                'FALCON 9': { cn: '猎鹰9号', en: 'FALCON 9' },
                'FALCON 9 BLOCK 5': { cn: '猎鹰9号Block 5', en: 'FALCON 9 BLOCK 5' },
                'FALCON HEAVY': { cn: '猎鹰重型', en: 'FALCON HEAVY' },
                'ATLAS V': { cn: '宇宙神五号', en: 'ATLAS V' },
                'ATLAS V 401': { cn: '宇宙神五号401', en: 'ATLAS V 401' },
                'ATLAS V 411': { cn: '宇宙神五号411', en: 'ATLAS V 411' },
                'ATLAS V 421': { cn: '宇宙神五号421', en: 'ATLAS V 421' },
                'ATLAS V 431': { cn: '宇宙神五号431', en: 'ATLAS V 431' },
                'ATLAS V 501': { cn: '宇宙神五号501', en: 'ATLAS V 501' },
                'ATLAS V 511': { cn: '宇宙神五号511', en: 'ATLAS V 511' },
                'ATLAS V 521': { cn: '宇宙神五号521', en: 'ATLAS V 521' },
                'ATLAS V 531': { cn: '宇宙神五号531', en: 'ATLAS V 531' },
                'ATLAS V 541': { cn: '宇宙神五号541', en: 'ATLAS V 541' },
                'ATLAS V 551': { cn: '宇宙神五号551', en: 'ATLAS V 551' },
                'DELTA IV': { cn: '德尔塔四号', en: 'DELTA IV' },
                'DELTA IV HEAVY': { cn: '德尔塔四号重型', en: 'DELTA IV HEAVY' },
                'DELTA IV MEDIUM': { cn: '德尔塔四号中型', en: 'DELTA IV MEDIUM' },
                'ANTARES': { cn: '安塔瑞斯', en: 'ANTARES' },
                'ELECTRON': { cn: '电子号', en: 'ELECTRON' },
                'PEGASUS': { cn: '飞马座', en: 'PEGASUS' },
                'PEGASUS XL': { cn: '飞马座XL', en: 'PEGASUS XL' },
                'NEW SHEPARD': { cn: '新谢泼德', en: 'NEW SHEPARD' },
                'MINOTAUR': { cn: '弥诺陶', en: 'MINOTAUR' },
                'MINOTAUR I': { cn: '弥诺陶一号', en: 'MINOTAUR I' },
                'MINOTAUR IV': { cn: '弥诺陶四号', en: 'MINOTAUR IV' },
                'MINOTAUR V': { cn: '弥诺陶五号', en: 'MINOTAUR V' },
                'MINOTAUR-C': { cn: '弥诺陶-C', en: 'MINOTAUR-C' },
                'SOYUZ': { cn: '联盟号', en: 'SOYUZ' },
                'SOYUZ 2': { cn: '联盟2号', en: 'SOYUZ 2' },
                'SOYUZ 2.1A': { cn: '联盟2.1A', en: 'SOYUZ 2.1A' },
                'SOYUZ 2.1B': { cn: '联盟2.1B', en: 'SOYUZ 2.1B' },
                'SOYUZ 2.1V': { cn: '联盟2.1V', en: 'SOYUZ 2.1V' },
                'SOYUZ-FG': { cn: '联盟-FG', en: 'SOYUZ-FG' },
                'SOYUZ-ST': { cn: '联盟-ST', en: 'SOYUZ-ST' },
                'SOYUZ-ST-A': { cn: '联盟-ST-A', en: 'SOYUZ-ST-A' },
                'SOYUZ-ST-B': { cn: '联盟-ST-B', en: 'SOYUZ-ST-B' },
                'PROTON': { cn: '质子号', en: 'PROTON' },
                'PROTON-M': { cn: '质子-M', en: 'PROTON-M' },
                'PROTON-K': { cn: '质子-K', en: 'PROTON-K' },
                'ANGARA': { cn: '安加拉号', en: 'ANGARA' },
                'ANGARA 1.2': { cn: '安加拉1.2', en: 'ANGARA 1.2' },
                'ANGARA A5': { cn: '安加拉A5', en: 'ANGARA A5' },
                'ROCKOT': { cn: '火箭号', en: 'ROCKOT' },
                'DNEPR': { cn: '第聂伯号', en: 'DNEPR' },
                'STRELA': { cn: '箭号', en: 'STRELA' },
                'VOLNA': { cn: '浪号', en: 'VOLNA' },
                'SHTIL': { cn: '风暴号', en: 'SHTIL' },
                'ZENIT': { cn: '天顶号', en: 'ZENIT' },
                'ZENIT-2': { cn: '天顶-2', en: 'ZENIT-2' },
                'ZENIT-3F': { cn: '天顶-3F', en: 'ZENIT-3F' },
                'ZENIT-3SL': { cn: '天顶-3SL', en: 'ZENIT-3SL' },
                'ZENIT-3SLBF': { cn: '天顶-3SLBF', en: 'ZENIT-3SLBF' },
                'ARIANE 5': { cn: '阿丽亚娜5号', en: 'ARIANE 5' },
                'ARIANE 5 ECA': { cn: '阿丽亚娜5号ECA', en: 'ARIANE 5 ECA' },
                'ARIANE 5 ES': { cn: '阿丽亚娜5号ES', en: 'ARIANE 5 ES' },
                'ARIANE 6': { cn: '阿丽亚娜6号', en: 'ARIANE 6' },
                'VEGA': { cn: '织女星号', en: 'VEGA' },
                'VEGA-C': { cn: '织女星-C', en: 'VEGA-C' },
                'H-2A': { cn: 'H-2A', en: 'H-2A' },
                'H-2B': { cn: 'H-2B', en: 'H-2B' },
                'H3': { cn: 'H3', en: 'H3' },
                'EPSILON': { cn: '艾普西龙', en: 'EPSILON' },
                'PSLV': { cn: '极地卫星运载火箭', en: 'PSLV' },
                'PSLV-CA': { cn: '极地卫星运载火箭-CA', en: 'PSLV-CA' },
                'PSLV-DL': { cn: '极地卫星运载火箭-DL', en: 'PSLV-DL' },
                'PSLV-XL': { cn: '极地卫星运载火箭-XL', en: 'PSLV-XL' },
                'GSLV': { cn: '地球同步卫星运载火箭', en: 'GSLV' },
                'GSLV MK II': { cn: '地球同步卫星运载火箭MK II', en: 'GSLV MK II' },
                'GSLV MK III': { cn: '地球同步卫星运载火箭MK III', en: 'GSLV MK III' },
                'LVM3': { cn: 'LVM3', en: 'LVM3' },
                'SAFIR': { cn: '使者号', en: 'SAFIR' },
                'QASED': { cn: '信使号', en: 'QASED' },
                'SIMORGH': { cn: '凤凰号', en: 'SIMORGH' },
                'UNHA-3': { cn: '银河3号', en: 'UNHA-3' },
                'SHAVIT': { cn: '彗星号', en: 'SHAVIT' },
                'KAITUOZHE': { cn: '开拓者号', en: 'KAITUOZHE' },
                'NURI': { cn: '世界号', en: 'NURI' },
                'VLS-1': { cn: 'VLS-1', en: 'VLS-1' }
            };
            const partialMatches = [
                { pattern: /LONG MARCH|CZ-/i, translation: { cn: '长征', en: 'LONG MARCH' } },
                { pattern: /FALCON/i, translation: { cn: '猎鹰', en: 'FALCON' } },
                { pattern: /ATLAS/i, translation: { cn: '宇宙神', en: 'ATLAS' } },
                { pattern: /DELTA/i, translation: { cn: '德尔塔', en: 'DELTA' } },
                { pattern: /ANTARES/i, translation: { cn: '安塔瑞斯', en: 'ANTARES' } },
                { pattern: /ELECTRON/i, translation: { cn: '电子号', en: 'ELECTRON' } },
                { pattern: /PEGASUS/i, translation: { cn: '飞马座', en: 'PEGASUS' } },
                { pattern: /SHEPARD/i, translation: { cn: '谢泼德', en: 'SHEPARD' } },
                { pattern: /MINOTAUR/i, translation: { cn: '弥诺陶', en: 'MINOTAUR' } },
                { pattern: /SOYUZ/i, translation: { cn: '联盟号', en: 'SOYUZ' } },
                { pattern: /PROTON/i, translation: { cn: '质子号', en: 'PROTON' } },
                { pattern: /ANGARA/i, translation: { cn: '安加拉号', en: 'ANGARA' } },
                { pattern: /ROCKOT/i, translation: { cn: '火箭号', en: 'ROCKOT' } },
                { pattern: /DNEPR/i, translation: { cn: '第聂伯号', en: 'DNEPR' } },
                { pattern: /STRELA/i, translation: { cn: '箭号', en: 'STRELA' } },
                { pattern: /VOLNA/i, translation: { cn: '浪号', en: 'VOLNA' } },
                { pattern: /SHTIL/i, translation: { cn: '风暴号', en: 'SHTIL' } },
                { pattern: /ZENIT/i, translation: { cn: '天顶号', en: 'ZENIT' } },
                { pattern: /ARIANE/i, translation: { cn: '阿丽亚娜', en: 'ARIANE' } },
                { pattern: /VEGA/i, translation: { cn: '织女星号', en: 'VEGA' } },
                { pattern: /H-2|H2|H3/i, translation: { cn: 'H系列', en: 'H-SERIES' } },
                { pattern: /EPSILON/i, translation: { cn: '艾普西龙', en: 'EPSILON' } },
                { pattern: /PSLV/i, translation: { cn: '极地卫星运载火箭', en: 'PSLV' } },
                { pattern: /GSLV/i, translation: { cn: '地球同步卫星运载火箭', en: 'GSLV' } },
                { pattern: /KUAIZHOU/i, translation: { cn: '快舟', en: 'KUAIZHOU' } },
                { pattern: /HYPERBOLA/i, translation: { cn: '双曲线', en: 'HYPERBOLA' } },
                { pattern: /CERES/i, translation: { cn: '谷神星', en: 'CERES' } },
                { pattern: /JIELONG/i, translation: { cn: '捷龙', en: 'JIELONG' } }
            ];
            for (const launchVehicle of rawLaunchVehicles) {
                if (knownTranslations[launchVehicle]) {
                    launchVehicleMap[launchVehicle] = knownTranslations[launchVehicle];
                    continue;
                }
                let matched = false;
                for (const { pattern, translation } of partialMatches) {
                    if (pattern.test(launchVehicle)) {
                        launchVehicleMap[launchVehicle] = {
                            cn: `${translation.cn}${launchVehicle.replace(pattern, '')}`,
                            en: launchVehicle
                        };
                        matched = true;
                        break;
                    }
                }
                if (matched)
                    continue;
                const isEnglish = /[a-zA-Z]/.test(launchVehicle);
                const isChinese = /[\u4e00-\u9fa5]/.test(launchVehicle);
                if (isEnglish && !isChinese) {
                    launchVehicleMap[launchVehicle] = { cn: launchVehicle, en: launchVehicle };
                }
                else if (isChinese && !isEnglish) {
                    let found = false;
                    for (const [key, value] of Object.entries(knownTranslations)) {
                        if (value.cn === launchVehicle) {
                            launchVehicleMap[launchVehicle] = value;
                            found = true;
                            break;
                        }
                    }
                    if (!found) {
                        launchVehicleMap[launchVehicle] = { cn: launchVehicle, en: launchVehicle };
                    }
                }
                else if (isChinese && isEnglish) {
                    launchVehicleMap[launchVehicle] = { cn: launchVehicle, en: launchVehicle };
                }
                else {
                    launchVehicleMap[launchVehicle] = { cn: launchVehicle, en: launchVehicle };
                }
            }
            const processedLaunchVehicles = Object.values(launchVehicleMap).sort((a, b) => a.en.localeCompare(b.en));
            this.logger.debug(`获取到 ${processedLaunchVehicles.length} 个不同的发射载具`);
            return {
                success: true,
                launch_vehicles: processedLaunchVehicles
            };
        }
        catch (error) {
            this.logger.error(`获取发射载具集合失败: ${error.message}`, error.stack);
            throw new Error(`获取发射载具集合失败: ${error.message}`);
        }
    }
};
DatabaseService = DatabaseService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectEntityManager)()),
    __metadata("design:paramtypes", [typeorm_2.EntityManager])
], DatabaseService);
exports.DatabaseService = DatabaseService;
//# sourceMappingURL=database.service.js.map