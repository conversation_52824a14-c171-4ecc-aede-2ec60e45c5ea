{"version": 3, "file": "llm-config.service.js", "sourceRoot": "", "sources": ["../../../src/services/llm-config.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAAuG;AACvG,0DAQ+B;AAC/B,uFAEuD;AACvD,wDAOiC;AAO1B,IAAM,gBAAgB,wBAAtB,MAAM,gBAAgB;IAwB3B,YACmB,kBAAsC;QAAtC,uBAAkB,GAAlB,kBAAkB,CAAoB;QAxBxC,WAAM,GAAG,IAAI,eAAM,CAAC,kBAAgB,CAAC,IAAI,CAAC,CAAC;QAGpD,gBAAW,GAAG,IAAI,GAAG,EAAe,CAAC;QAGrC,UAAK,GAAG;YACd,WAAW,EAAE;gBACX,aAAa,EAAE,CAAC;gBAChB,kBAAkB,EAAE,CAAC;gBACrB,cAAc,EAAE,CAAC;gBACjB,iBAAiB,EAAE,CAAC;gBACpB,QAAQ,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACnC;YACD,eAAe,EAAE;gBACf,aAAa,EAAE,CAAC;gBAChB,kBAAkB,EAAE,CAAC;gBACrB,cAAc,EAAE,CAAC;gBACjB,iBAAiB,EAAE,CAAC;gBACpB,QAAQ,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACnC;SACF,CAAC;QAKA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAClC,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAKO,eAAe;QAErB,IAAI;YACF,MAAM,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC;YAC/D,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG;gBACvB,aAAa,EAAE,gBAAgB,CAAC,SAAS;gBACzC,kBAAkB,EAAE,gBAAgB,CAAC,SAAS;gBAC9C,cAAc,EAAE,CAAC;gBACjB,iBAAiB,EAAE,gBAAgB,CAAC,SAAS;gBAC7C,QAAQ,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACnC,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SACxC;IACH,CAAC;IAOD,KAAK,CAAC,SAAS,CAAC,UAAsB;QACpC,IAAI;YACF,IAAI,MAAiD,CAAC;YAEtD,IAAI,UAAU,KAAK,2BAAU,CAAC,WAAW,EAAE;gBACzC,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,EAAE,CAAC;aACzD;iBAAM;gBACL,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,EAAE,CAAC;aAC7D;YAGD,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAEpD,MAAM,QAAQ,GAAyB;gBACrC,UAAU;gBACV,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,EAAE;gBAC7B,MAAM,EAAE,YAAY;gBACpB,YAAY,EAAG,MAAc,CAAC,YAAY,IAAI,EAAE;gBAChD,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;gBACnC,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;gBACtC,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,KAAK;gBAChC,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,CAAC;gBAClC,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,IAAI;gBACrC,qBAAqB,EAAE,MAAM,CAAC,qBAAqB,IAAI,CAAC;gBACxD,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,UAAU,cAAc,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aAC3F,CAAC;YAEF,OAAO,QAAQ,CAAC;SACjB;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,UAAU,OAAO,EAAE,KAAK,CAAC,CAAC;YACjD,MAAM,IAAI,qCAA4B,CAAC,WAAW,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SACpE;IACH,CAAC;IAMD,KAAK,CAAC,aAAa;QACjB,MAAM,OAAO,GAAG,EAAE,CAAC;QAEnB,IAAI;YACF,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,2BAAU,CAAC,WAAW,CAAC,CAAC;YACvE,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,2BAAU,CAAC,gBAAgB,CAAC,CAAC;YAEhF,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,qBAAqB,CAAC,CAAC;YAEvD,OAAO,OAAO,CAAC;SAChB;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YACtC,MAAM,IAAI,qCAA4B,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SACtE;IACH,CAAC;IAOD,KAAK,CAAC,YAAY,CAAC,SAA6B;QAC9C,IAAI;YACF,MAAM,EAAE,UAAU,KAAoB,SAAS,EAAxB,UAAU,UAAK,SAAS,EAAzC,cAA6B,CAAY,CAAC;YAGhD,MAAM,eAAe,GAAG,MAAM,CAAC,WAAW,CACxC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,KAAK,SAAS,CAAC,CACvE,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,UAAU,KAAK,EAAE,eAAe,CAAC,CAAC;YAGzD,IAAI,UAAU,KAAK,2BAAU,CAAC,WAAW,EAAE;gBACzC,IAAI,CAAC,kBAAkB,CAAC,uBAAuB,CAAC,eAAe,CAAC,CAAC;aAClE;iBAAM;gBACL,IAAI,CAAC,kBAAkB,CAAC,2BAA2B,CAAC,eAAe,CAAC,CAAC;aACtE;YAGD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,UAAU,cAAc,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC;YAE5E,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,UAAU,QAAQ,CAAC,CAAC;YAGvC,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;SACzC;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,SAAS,CAAC,UAAU,OAAO,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,IAAI,4BAAmB,CAAC,WAAW,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SAC3D;IACH,CAAC;IAOD,KAAK,CAAC,cAAc,CAAC,OAA0B;QAC7C,MAAM,EAAE,UAAU,EAAE,QAAQ,GAAG,eAAe,EAAE,GAAG,OAAO,CAAC;QAC3D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI;YACF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,UAAU,MAAM,CAAC,CAAC;YAEzC,IAAI,MAAc,CAAC;YAEnB,IAAI,UAAU,KAAK,2BAAU,CAAC,WAAW,EAAE;gBAEzC,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;gBAC/D,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;aAC5D;iBAAM;gBAEL,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBACvE,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;aAC5D;YAED,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,YAAY;gBACZ,MAAM;gBACN,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC5C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,UAAU,SAAS,EAAE,KAAK,CAAC,CAAC;YACnD,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;YAElD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,YAAY;gBACZ,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;SACH;IACH,CAAC;IAOD,KAAK,CAAC,WAAW,CAAC,QAAwB;QACxC,MAAM,EAAE,UAAU,EAAE,OAAO,GAAG,KAAK,EAAE,GAAG,QAAQ,CAAC;QAEjD,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,4BAAmB,CAAC,YAAY,CAAC,CAAC;SAC7C;QAED,IAAI;YACF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,UAAU,QAAQ,CAAC,CAAC;YAE3C,IAAI,aAAwD,CAAC;YAE7D,IAAI,UAAU,KAAK,2BAAU,CAAC,WAAW,EAAE;gBACzC,aAAa,GAAG,IAAA,iCAAoB,EAAC,SAAS,CAAC,CAAC;gBAChD,IAAI,CAAC,kBAAkB,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;aAChE;iBAAM;gBACL,aAAa,GAAG,IAAA,qCAAwB,EAAC,SAAS,CAAC,CAAC;gBACpD,IAAI,CAAC,kBAAkB,CAAC,2BAA2B,CAAC,aAAa,CAAC,CAAC;aACpE;YAGD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,UAAU,cAAc,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC;YAE5E,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,UAAU,QAAQ,CAAC,CAAC;YAEvC,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;SACzC;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,UAAU,OAAO,EAAE,KAAK,CAAC,CAAC;YACjD,MAAM,IAAI,4BAAmB,CAAC,WAAW,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SAC3D;IACH,CAAC;IAMD,KAAK,CAAC,QAAQ;QACZ,IAAI;YAEF,MAAM,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC;YAClE,MAAM,uBAAuB,GAAG,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,CAAC;YAE1E,OAAO;gBACL,WAAW,EAAE;oBACX,aAAa,EAAE,mBAAmB,CAAC,SAAS;oBAC5C,kBAAkB,EAAE,mBAAmB,CAAC,SAAS,GAAG,uBAAuB,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK;oBAChG,cAAc,EAAE,uBAAuB,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK;oBAC5D,mBAAmB,EAAE,mBAAmB,CAAC,SAAS,GAAG,CAAC;wBACpD,CAAC,CAAC,mBAAmB,CAAC,SAAS,GAAG,mBAAmB,CAAC,SAAS;wBAC/D,CAAC,CAAC,CAAC;oBACL,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ;iBAC1C;gBACD,eAAe,EAAE;oBACf,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,aAAa;oBACvD,kBAAkB,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,kBAAkB;oBACjE,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,cAAc;oBACzD,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,aAAa,GAAG,CAAC;wBAC/D,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,aAAa;wBACzF,CAAC,CAAC,CAAC;oBACL,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,QAAQ;iBAC9C;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YACtC,MAAM,IAAI,qCAA4B,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SACtE;IACH,CAAC;IAOO,UAAU,CAAC,MAAc;QAC/B,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAChC,OAAO,MAAM,CAAC;SACf;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACtC,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACnD,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QAE1D,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,EAAE,CAAC;IACvC,CAAC;IAQO,WAAW,CAAC,UAAsB,EAAE,OAAgB,EAAE,YAAoB;QAChF,MAAM,QAAQ,GAAG,UAAU,KAAK,2BAAU,CAAC,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,iBAAiB,CAAC;QAE3F,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;QACrC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,iBAAiB,IAAI,YAAY,CAAC;QACvD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAEzD,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,kBAAkB,EAAE,CAAC;SAC3C;aAAM;YACL,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,cAAc,EAAE,CAAC;SACvC;IACH,CAAC;IAKD,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,KAAK,GAAG;YACX,WAAW,EAAE;gBACX,aAAa,EAAE,CAAC;gBAChB,kBAAkB,EAAE,CAAC;gBACrB,cAAc,EAAE,CAAC;gBACjB,iBAAiB,EAAE,CAAC;gBACpB,QAAQ,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACnC;YACD,eAAe,EAAE;gBACf,aAAa,EAAE,CAAC;gBAChB,kBAAkB,EAAE,CAAC;gBACrB,cAAc,EAAE,CAAC;gBACjB,iBAAiB,EAAE,CAAC;gBACpB,QAAQ,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACnC;SACF,CAAC;QAGF,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,CAAC;QAE5C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IAC7B,CAAC;CACF,CAAA;AA1UY,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;qCA0B4B,wCAAkB;GAzB9C,gBAAgB,CA0U5B;AA1UY,4CAAgB"}