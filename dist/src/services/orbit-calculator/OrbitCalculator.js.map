{"version": 3, "file": "OrbitCalculator.js", "sourceRoot": "", "sources": ["../../../../src/services/orbit-calculator/OrbitCalculator.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAKA,0CAA0C;AAM1C,MAAa,eAAe;IAK1B,YAAY,MAA8B;QAFzB,cAAS,GAAW,GAAG,CAAC;QAGvC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,CAAC,MAAM,GAAG;YACZ,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,KAAK,EAAE,OAAO,CAAC,KAAK;SACrB,CAAC;IACJ,CAAC;IASO,WAAW,CAAC,KAAa;QAE/B,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QAEvD,MAAM,MAAM,GAAG,IAAI,GAAG,UAAU,CAAC;QACjC,OAAO,MAAM,GAAG,GAAG,CAAC;IACtB,CAAC;IAOO,eAAe,CAAC,KAAa;QAEnC,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QACpD,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QAGtD,MAAM,IAAI,GAAG,SAAS,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,GAAG,SAAS,CAAC;QAGlE,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC7C,KAAK,CAAC,kBAAkB,CAAC,SAAS,GAAG,QAAQ,CAAC,CAAC;QAE/C,OAAO,KAAK,CAAC;IACf,CAAC;IAQM,KAAK,CAAC,iBAAiB,CAC5B,OAAsB,EACtB,OAAa,IAAI,IAAI,EAAE;QAEvB,IAAI;YAEF,IAAI,CAAC,OAAO,EAAE;gBACZ,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC;aAC7B;YAED,MAAM,EAAE,KAAK,GAAG,SAAS,EAAE,IAAI,GAAG,mBAAmB,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;YAGhF,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,EAAE;gBACpB,MAAM,IAAI,KAAK,CAAC,KAAK,KAAK,WAAW,CAAC,CAAC;aACxC;YAGD,IAAI,KAAK,CAAC,MAAM,GAAG,EAAE,IAAI,KAAK,CAAC,MAAM,GAAG,EAAE,EAAE;gBAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,KAAK,sBAAsB,KAAK,CAAC,MAAM,WAAW,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;aAEzF;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAGvC,IAAI,MAAM,CAAC;YACX,IAAI;gBACF,MAAM,GAAG,SAAS,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBAChD,IAAI,CAAC,MAAM,EAAE;oBACX,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;iBAClC;aACF;YAAC,OAAO,CAAC,EAAE;gBACV,MAAM,IAAI,KAAK,CAAC,OAAO,KAAK,aAAa,CAAC,CAAC,OAAO,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;aACpE;YAGD,IAAI,mBAAmB,CAAC;YACxB,IAAI;gBACF,mBAAmB,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;gBACxD,IAAI,CAAC,mBAAmB,EAAE;oBACxB,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;iBACjC;gBACD,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE;oBACjC,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;iBAChC;gBACD,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE;oBACjC,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;iBAChC;aACF;YAAC,OAAO,CAAC,EAAE;gBACV,MAAM,IAAI,KAAK,CAAC,OAAO,KAAK,UAAU,CAAC,CAAC,OAAO,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;aACjE;YAGD,MAAM,QAAQ,GAAG,mBAAmB,CAAC,QAAqC,CAAC;YAC3E,MAAM,QAAQ,GAAG,mBAAmB,CAAC,QAAqC,CAAC;YAG3E,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,cAAc,GAAG,SAAS,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAE/D,OAAO;gBACL,KAAK;gBACL,IAAI;gBACJ,KAAK;gBACL,QAAQ,EAAE;oBACR,CAAC,EAAE,QAAQ,CAAC,CAAC;oBACb,CAAC,EAAE,QAAQ,CAAC,CAAC;oBACb,CAAC,EAAE,QAAQ,CAAC,CAAC;iBACd;gBACD,QAAQ,EAAE;oBACR,CAAC,EAAE,QAAQ,CAAC,CAAC;oBACb,CAAC,EAAE,QAAQ,CAAC,CAAC;oBACb,CAAC,EAAE,QAAQ,CAAC,CAAC;iBACd;gBACD,QAAQ,EAAE,SAAS,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC;gBACvD,SAAS,EAAE,SAAS,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC;gBAC1D,QAAQ,EAAE,cAAc,CAAC,MAAM;gBAC/B,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;aACpC,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YACtC,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IASM,KAAK,CAAC,kBAAkB,CAC7B,UAA2B,EAC3B,OAAa,IAAI,IAAI,EAAE,EACvB,UAGI,EAAE;;QAQN,MAAM,EACJ,eAAe,GAAG,IAAI,EACtB,SAAS,GAAG,IAAI,CAAC,SAAS,EAC3B,GAAG,OAAO,CAAC;QAEZ,MAAM,SAAS,GAAyB,EAAE,CAAC;QAC3C,MAAM,MAAM,GAA2C,EAAE,CAAC;QAG1D,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YAC7C,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;SACnC;QAED,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAClC,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC;SAC9B;QAGD,IAAI,CAAC,CAAC,IAAI,YAAY,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE;YACpD,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC;SAC5B;QAGD,MAAM,OAAO,GAAsB,EAAE,CAAC;QACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE;YACrD,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;SAClD;QAED,IAAI;YAEF,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE;gBAE3B,MAAM,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;oBACxC,IAAI,CAAC,GAAG,EAAE;wBACR,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;wBACjC,MAAM,CAAC,IAAI,CAAC;4BACV,KAAK,EAAE,MAAM;4BACb,KAAK,EAAE,IAAI,KAAK,CAAC,QAAQ,CAAC;yBAC3B,CAAC,CAAC;wBACH,OAAO,KAAK,CAAC;qBACd;oBAGD,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;wBAC5B,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,IAAI,MAAM,CAAC;wBAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,KAAK,eAAe,CAAC,CAAC;wBAC5C,MAAM,CAAC,IAAI,CAAC;4BACV,KAAK,EAAE,KAAK;4BACZ,KAAK,EAAE,IAAI,KAAK,CAAC,UAAU,CAAC;yBAC7B,CAAC,CAAC;wBACH,OAAO,KAAK,CAAC;qBACd;oBAED,OAAO,IAAI,CAAC;gBACd,CAAC,CAAC,CAAC;gBAEH,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;oBAC/B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;oBACxC,SAAS;iBACV;gBAED,MAAM,aAAa,GAAG,cAAc,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;oBACrD,IAAI;wBACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;wBACzD,OAAO;4BACL,OAAO,EAAE,IAAa;4BACtB,QAAQ;yBACT,CAAC;qBACH;oBAAC,OAAO,KAAK,EAAE;wBACd,IAAI,CAAC,eAAe,EAAE;4BACpB,MAAM,KAAK,CAAC;yBACb;wBACD,OAAO;4BACL,OAAO,EAAE,KAAc;4BACvB,KAAK,EAAE;gCACL,KAAK,EAAE,GAAG,CAAC,KAAK,IAAI,MAAM;gCAC1B,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;6BACjE;yBACF,CAAC;qBACH;gBACH,CAAC,CAAC,CAAC;gBAGH,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBAGjD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;oBAC5B,IAAI;wBACF,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,OAAO,EAAE;4BACnB,IAAI,MAAM,CAAC,QAAQ,EAAE;gCACnB,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;6BACjC;iCAAM;gCACL,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;6BAC5C;yBACF;6BAAM,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,EAAE;4BAExB,MAAM,KAAK,GAAG,CAAA,MAAA,MAAM,CAAC,KAAK,0CAAE,KAAK,KAAI,MAAM,CAAC;4BAC5C,MAAM,QAAQ,GAAG,MAAA,MAAM,CAAC,KAAK,0CAAE,KAAK,CAAC;4BACrC,MAAM,YAAY,GAAG,QAAQ,YAAY,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;gCAC1C,CAAC,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;4BAEpE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,KAAK,QAAQ,EAAE,YAAY,CAAC,CAAC;4BAEvD,MAAM,CAAC,IAAI,CAAC;gCACV,KAAK,EAAE,KAAK;gCACZ,KAAK,EAAE,QAAQ,YAAY,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;6BAC9E,CAAC,CAAC;yBACJ;6BAAM;4BAEL,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,EACvB,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CACpC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;4BACpE,MAAM,CAAC,IAAI,CAAC;gCACV,KAAK,EAAE,MAAM;gCACb,KAAK,EAAE,IAAI,KAAK,CAAC,UAAU,CAAC;6BAC7B,CAAC,CAAC;yBACJ;qBACF;oBAAC,OAAO,CAAC,EAAE;wBAEV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;wBACvC,MAAM,CAAC,IAAI,CAAC;4BACV,KAAK,EAAE,MAAM;4BACb,KAAK,EAAE,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;yBACrD,CAAC,CAAC;qBACJ;iBACF;aACF;YAED,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC;SAC9B;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;YAC3C,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IASa,wBAAwB,CACpC,UAA2B,EAC3B,OAAa,IAAI,IAAI,EAAE,EACvB,YAAoB,IAAI,CAAC,SAAS;;YASlC,MAAM,OAAO,GAAsB,EAAE,CAAC;YACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE;gBACrD,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;aAClD;YAGD,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE;gBAC3B,MAAM,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;oBAC5C,IAAI;wBACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;wBACzD,OAAO,EAAE,QAAQ,EAAE,CAAC;qBACrB;oBAAC,OAAO,KAAK,EAAE;wBACd,OAAO;4BACL,KAAK,EAAE;gCACL,KAAK,EAAE,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,KAAK,KAAI,MAAM;gCAC3B,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;6BACjE;yBACF,CAAC;qBACH;gBACH,CAAC,CAAC,CAAC;gBAGH,MAAM,OAAO,GAAG,cAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA,CAAC;gBAGjD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;oBAC5B,oBAAM,MAAM,CAAA,CAAC;iBACd;aACF;QACH,CAAC;KAAA;CACF;AA/VD,0CA+VC"}