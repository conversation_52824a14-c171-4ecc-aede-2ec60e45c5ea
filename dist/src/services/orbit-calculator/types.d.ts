export interface ISatellitePosition {
    satId: string;
    name: string;
    epoch: Date;
    position: {
        x: number;
        y: number;
        z: number;
    };
    velocity: {
        x: number;
        y: number;
        z: number;
    };
    latitude: number;
    longitude: number;
    altitude: number;
    algorithm: 'SGP4' | 'SDP4';
}
export interface ITLESatellite {
    satId: string;
    name: string;
    line1: string;
    line2: string;
}
export interface IOrbitCalculatorConfig {
    wgs84: {
        earthRadius: number;
        mu: number;
        j2: number;
        j3: number;
        j4: number;
    };
}
