"use strict";
var __await = (this && this.__await) || function (v) { return this instanceof __await ? (this.v = v, this) : new __await(v); }
var __asyncGenerator = (this && this.__asyncGenerator) || function (thisArg, _arguments, generator) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var g = generator.apply(thisArg, _arguments || []), i, q = [];
    return i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function () { return this; }, i;
    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }
    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }
    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }
    function fulfill(value) { resume("next", value); }
    function reject(value) { resume("throw", value); }
    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrbitCalculator = void 0;
const satellite = require("satellite.js");
class OrbitCalculator {
    constructor(config) {
        this.batchSize = 100;
        this.config = config;
        this.logger = {
            log: console.log,
            debug: console.debug,
            info: console.info,
            warn: console.warn,
            error: console.error
        };
    }
    isDeepSpace(line2) {
        const meanMotion = parseFloat(line2.substring(52, 63));
        const period = 1440 / meanMotion;
        return period > 225;
    }
    getEpochFromTLE(line1) {
        const epochYear = parseInt(line1.substring(18, 20));
        const epochDays = parseFloat(line1.substring(20, 32));
        const year = epochYear < 57 ? 2000 + epochYear : 1900 + epochYear;
        const epoch = new Date(Date.UTC(year, 0, 1));
        epoch.setUTCMilliseconds(epochDays * 86400000);
        return epoch;
    }
    async calculatePosition(satInfo, time = new Date()) {
        try {
            if (!satInfo) {
                throw new Error('卫星信息不能为空');
            }
            const { satId = 'UNKNOWN', name = 'Unknown Satellite', line1, line2 } = satInfo;
            if (!line1 || !line2) {
                throw new Error(`卫星${satId}的TLE数据不完整`);
            }
            if (line1.length < 69 || line2.length < 69) {
                this.logger.warn(`卫星${satId}的TLE数据长度不标准: line1=${line1.length}, line2=${line2.length}`);
            }
            const epoch = this.getEpochFromTLE(line1);
            const isDeep = this.isDeepSpace(line2);
            let satrec;
            try {
                satrec = satellite.twoline2satrec(line1, line2);
                if (!satrec) {
                    throw new Error('返回的satrec对象为空');
                }
            }
            catch (e) {
                throw new Error(`解析卫星${satId}的TLE数据失败: ${e.message || String(e)}`);
            }
            let positionAndVelocity;
            try {
                positionAndVelocity = satellite.propagate(satrec, time);
                if (!positionAndVelocity) {
                    throw new Error('返回的位置和速度对象为空');
                }
                if (!positionAndVelocity.position) {
                    throw new Error('没有返回有效的位置数据');
                }
                if (!positionAndVelocity.velocity) {
                    throw new Error('没有返回有效的速度数据');
                }
            }
            catch (e) {
                throw new Error(`计算卫星${satId}的位置失败: ${e.message || String(e)}`);
            }
            const position = positionAndVelocity.position;
            const velocity = positionAndVelocity.velocity;
            const gmst = satellite.gstime(time);
            const geodeticCoords = satellite.eciToGeodetic(position, gmst);
            return {
                satId,
                name,
                epoch,
                position: {
                    x: position.x,
                    y: position.y,
                    z: position.z
                },
                velocity: {
                    x: velocity.x,
                    y: velocity.y,
                    z: velocity.z
                },
                latitude: satellite.degreesLat(geodeticCoords.latitude),
                longitude: satellite.degreesLong(geodeticCoords.longitude),
                altitude: geodeticCoords.height,
                algorithm: isDeep ? 'SDP4' : 'SGP4'
            };
        }
        catch (error) {
            this.logger.error('计算卫星位置失败:', error);
            throw error;
        }
    }
    async calculatePositions(satellites, time = new Date(), options = {}) {
        var _a, _b;
        const { continueOnError = true, batchSize = this.batchSize } = options;
        const positions = [];
        const errors = [];
        if (!satellites || !Array.isArray(satellites)) {
            throw new Error('卫星列表不能为空且必须是数组');
        }
        if (satellites.length === 0) {
            this.logger.warn('卫星列表为空，无法进行计算');
            return { positions, errors };
        }
        if (!(time instanceof Date) || isNaN(time.getTime())) {
            throw new Error('无效的时间参数');
        }
        const batches = [];
        for (let i = 0; i < satellites.length; i += batchSize) {
            batches.push(satellites.slice(i, i + batchSize));
        }
        try {
            for (const batch of batches) {
                const validatedBatch = batch.filter(sat => {
                    if (!sat) {
                        this.logger.warn('发现空的卫星数据，已跳过');
                        errors.push({
                            satId: '未知卫星',
                            error: new Error('卫星数据为空')
                        });
                        return false;
                    }
                    if (!sat.line1 || !sat.line2) {
                        const satId = sat.satId || '未知卫星';
                        this.logger.warn(`卫星${satId}的TLE数据不完整，已跳过`);
                        errors.push({
                            satId: satId,
                            error: new Error('TLE数据不完整')
                        });
                        return false;
                    }
                    return true;
                });
                if (validatedBatch.length === 0) {
                    this.logger.warn('当前批次中没有有效的卫星数据，跳过处理');
                    continue;
                }
                const batchPromises = validatedBatch.map(async (sat) => {
                    try {
                        const position = await this.calculatePosition(sat, time);
                        return {
                            success: true,
                            position
                        };
                    }
                    catch (error) {
                        if (!continueOnError) {
                            throw error;
                        }
                        return {
                            success: false,
                            error: {
                                satId: sat.satId || '未知卫星',
                                error: error instanceof Error ? error : new Error(String(error))
                            }
                        };
                    }
                });
                const results = await Promise.all(batchPromises);
                for (const result of results) {
                    try {
                        if (result === null || result === void 0 ? void 0 : result.success) {
                            if (result.position) {
                                positions.push(result.position);
                            }
                            else {
                                this.logger.warn('计算结果标记为成功但没有position数据');
                            }
                        }
                        else if (result === null || result === void 0 ? void 0 : result.error) {
                            const satId = ((_a = result.error) === null || _a === void 0 ? void 0 : _a.satId) || '未知卫星';
                            const errorObj = (_b = result.error) === null || _b === void 0 ? void 0 : _b.error;
                            const errorMessage = errorObj instanceof Error ? errorObj :
                                (typeof errorObj === 'string' ? errorObj : '未知错误');
                            this.logger.error(`计算卫星 ${satId} 位置失败:`, errorMessage);
                            errors.push({
                                satId: satId,
                                error: errorObj instanceof Error ? errorObj : new Error(String(errorMessage))
                            });
                        }
                        else {
                            this.logger.error('计算卫星位置返回了无效的结果格式:', JSON.stringify(result, (key, value) => value instanceof Error ? value.toString() : value));
                            errors.push({
                                satId: '未知卫星',
                                error: new Error('计算结果格式无效')
                            });
                        }
                    }
                    catch (e) {
                        this.logger.error('处理卫星计算结果时发生错误:', e);
                        errors.push({
                            satId: '未知卫星',
                            error: e instanceof Error ? e : new Error(String(e))
                        });
                    }
                }
            }
            return { positions, errors };
        }
        catch (error) {
            this.logger.error('批量计算卫星位置时发生错误:', error);
            throw error;
        }
    }
    calculatePositionsStream(satellites, time = new Date(), batchSize = this.batchSize) {
        return __asyncGenerator(this, arguments, function* calculatePositionsStream_1() {
            const batches = [];
            for (let i = 0; i < satellites.length; i += batchSize) {
                batches.push(satellites.slice(i, i + batchSize));
            }
            for (const batch of batches) {
                const batchPromises = batch.map(async (sat) => {
                    try {
                        const position = await this.calculatePosition(sat, time);
                        return { position };
                    }
                    catch (error) {
                        return {
                            error: {
                                satId: (sat === null || sat === void 0 ? void 0 : sat.satId) || '未知卫星',
                                error: error instanceof Error ? error : new Error(String(error))
                            }
                        };
                    }
                });
                const results = yield __await(Promise.all(batchPromises));
                for (const result of results) {
                    yield yield __await(result);
                }
            }
        });
    }
}
exports.OrbitCalculator = OrbitCalculator;
//# sourceMappingURL=OrbitCalculator.js.map