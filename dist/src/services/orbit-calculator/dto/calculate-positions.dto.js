"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculatePositionsResponseDto = exports.CalculationErrorDto = exports.SatellitePositionDto = exports.CalculatePositionsRequestDto = exports.TLESatelliteDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class TLESatelliteDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星ID (如NORAD编号)',
        example: '25544',
        required: true
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], TLESatelliteDto.prototype, "satId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星名称',
        example: 'ISS (ZARYA)',
        required: true
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], TLESatelliteDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'TLE数据第一行',
        example: '1 25544U 98067A   24054.88088078  .00014558  00000+0  26729-3 0  9990',
        required: true
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], TLESatelliteDto.prototype, "line1", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'TLE数据第二行',
        example: '2 25544  51.6416 150.2506 0004789  89.9089  32.8809 15.49512781434577',
        required: true
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], TLESatelliteDto.prototype, "line2", void 0);
exports.TLESatelliteDto = TLESatelliteDto;
class CalculatePositionsRequestDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星TLE数据列表',
        type: [TLESatelliteDto],
        example: [
            {
                satId: '25544',
                name: 'ISS (ZARYA)',
                line1: '1 25544U 98067A   24054.88088078  .00014558  00000+0  26729-3 0  9990',
                line2: '2 25544  51.6416 150.2506 0004789  89.9089  32.8809 15.49512781434577'
            },
            {
                satId: '48274',
                name: 'STARLINK-3432',
                line1: '1 48274U 21041AF  24054.86885282  .00002571  00000+0  16843-3 0  9994',
                line2: '2 48274  53.0559  30.0121 0001038  89.7222 270.3898 15.06396635146374'
            }
        ],
        isArray: true,
        required: true
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => TLESatelliteDto),
    __metadata("design:type", Array)
], CalculatePositionsRequestDto.prototype, "satellites", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '计算时间点(UTC)',
        example: '2024-02-24T08:30:00Z',
        required: false
    }),
    (0, class_validator_1.IsDateString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CalculatePositionsRequestDto.prototype, "time", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '发生错误时是否继续计算其他卫星',
        example: true,
        required: false,
        default: true
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CalculatePositionsRequestDto.prototype, "continueOnError", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '每批处理的卫星数量',
        example: 100,
        required: false,
        minimum: 1,
        maximum: 1000,
        default: 100
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(1000),
    __metadata("design:type", Number)
], CalculatePositionsRequestDto.prototype, "batchSize", void 0);
exports.CalculatePositionsRequestDto = CalculatePositionsRequestDto;
class SatellitePositionDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星ID',
        example: '25544'
    }),
    __metadata("design:type", String)
], SatellitePositionDto.prototype, "satId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星名称',
        example: 'ISS (ZARYA)'
    }),
    __metadata("design:type", String)
], SatellitePositionDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'TLE历元时间',
        example: '2024-02-23T21:08:28.000Z'
    }),
    __metadata("design:type", Date)
], SatellitePositionDto.prototype, "epoch", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ECI坐标系位置(km)',
        example: {
            x: -6145.934,
            y: 2031.647,
            z: 2466.712
        }
    }),
    __metadata("design:type", Object)
], SatellitePositionDto.prototype, "position", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ECI坐标系速度(km/s)',
        example: {
            x: -1.523,
            y: -5.537,
            z: 4.685
        }
    }),
    __metadata("design:type", Object)
], SatellitePositionDto.prototype, "velocity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '地理纬度(度)',
        example: 23.4558
    }),
    __metadata("design:type", Number)
], SatellitePositionDto.prototype, "latitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '地理经度(度)',
        example: 161.7878
    }),
    __metadata("design:type", Number)
], SatellitePositionDto.prototype, "longitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '地理高度(km)',
        example: 408.123
    }),
    __metadata("design:type", Number)
], SatellitePositionDto.prototype, "altitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '使用的轨道计算算法',
        enum: ['SGP4', 'SDP4'],
        example: 'SGP4'
    }),
    __metadata("design:type", String)
], SatellitePositionDto.prototype, "algorithm", void 0);
exports.SatellitePositionDto = SatellitePositionDto;
class CalculationErrorDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星ID',
        example: '25544'
    }),
    __metadata("design:type", String)
], CalculationErrorDto.prototype, "satId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '错误信息',
        example: 'Failed to parse TLE data'
    }),
    __metadata("design:type", String)
], CalculationErrorDto.prototype, "error", void 0);
exports.CalculationErrorDto = CalculationErrorDto;
class CalculatePositionsResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '成功计算的卫星位置列表',
        type: [SatellitePositionDto],
        example: [{
                satId: '25544',
                name: 'ISS (ZARYA)',
                epoch: '2024-02-23T21:08:28.000Z',
                position: {
                    x: -6145.934,
                    y: 2031.647,
                    z: 2466.712
                },
                velocity: {
                    x: -1.523,
                    y: -5.537,
                    z: 4.685
                },
                latitude: 23.4558,
                longitude: 161.7878,
                altitude: 408.123,
                algorithm: 'SGP4'
            }]
    }),
    __metadata("design:type", Array)
], CalculatePositionsResponseDto.prototype, "positions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '计算失败的卫星列表',
        type: [CalculationErrorDto],
        example: []
    }),
    __metadata("design:type", Array)
], CalculatePositionsResponseDto.prototype, "errors", void 0);
exports.CalculatePositionsResponseDto = CalculatePositionsResponseDto;
//# sourceMappingURL=calculate-positions.dto.js.map