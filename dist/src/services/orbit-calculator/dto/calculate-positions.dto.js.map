{"version": 3, "file": "calculate-positions.dto.js", "sourceRoot": "", "sources": ["../../../../../src/services/orbit-calculator/dto/calculate-positions.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAC9C,qDAAyI;AACzI,yDAAyC;AAEzC,MAAa,eAAe;CAoC3B;AAnCC;IAAC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,iBAAiB;QAC9B,OAAO,EAAE,OAAO;QAChB,QAAQ,EAAE,IAAI;KACf,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;8CACC;AAEd;IAAC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,aAAa;QACtB,QAAQ,EAAE,IAAI;KACf,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;6CACA;AAEb;IAAC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,UAAU;QACvB,OAAO,EAAE,uEAAuE;QAChF,QAAQ,EAAE,IAAI;KACf,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;8CACC;AAEd;IAAC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,UAAU;QACvB,OAAO,EAAE,uEAAuE;QAChF,QAAQ,EAAE,IAAI;KACf,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;8CACC;AAnChB,0CAoCC;AAED,MAAa,4BAA4B;CA0DxC;AAzDC;IAAC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,WAAW;QACxB,IAAI,EAAE,CAAC,eAAe,CAAC;QACvB,OAAO,EAAE;YACP;gBACE,KAAK,EAAE,OAAO;gBACd,IAAI,EAAE,aAAa;gBACnB,KAAK,EAAE,uEAAuE;gBAC9E,KAAK,EAAE,uEAAuE;aAC/E;YACD;gBACE,KAAK,EAAE,OAAO;gBACd,IAAI,EAAE,eAAe;gBACrB,KAAK,EAAE,uEAAuE;gBAC9E,KAAK,EAAE,uEAAuE;aAC/E;SACF;QACD,OAAO,EAAE,IAAI;QACb,QAAQ,EAAE,IAAI;KACf,CAAC;IACD,IAAA,yBAAO,GAAE;IACT,IAAA,gCAAc,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IAC9B,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,eAAe,CAAC;;gEACE;AAE9B;IAAC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,YAAY;QACzB,OAAO,EAAE,sBAAsB;QAC/B,QAAQ,EAAE,KAAK;KAChB,CAAC;IACD,IAAA,8BAAY,GAAE;IACd,IAAA,4BAAU,GAAE;;0DACC;AAEd;IAAC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,iBAAiB;QAC9B,OAAO,EAAE,IAAI;QACb,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,IAAI;KACd,CAAC;IACD,IAAA,2BAAS,GAAE;IACX,IAAA,4BAAU,GAAE;;qEACa;AAE1B;IAAC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,WAAW;QACxB,OAAO,EAAE,GAAG;QACZ,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,GAAG;KACb,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;IACZ,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,IAAI,CAAC;;+DACS;AAzDrB,oEA0DC;AAED,MAAa,oBAAoB;CAuEhC;AAtEC;IAAC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,OAAO;KACjB,CAAC;;mDACY;AAEd;IAAC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,aAAa;KACvB,CAAC;;kDACW;AAEb;IAAC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,SAAS;QACtB,OAAO,EAAE,0BAA0B;KACpC,CAAC;8BACK,IAAI;mDAAC;AAEZ;IAAC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,cAAc;QAC3B,OAAO,EAAE;YACP,CAAC,EAAE,CAAC,QAAQ;YACZ,CAAC,EAAE,QAAQ;YACX,CAAC,EAAE,QAAQ;SACZ;KACF,CAAC;;sDAKA;AAEF;IAAC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,gBAAgB;QAC7B,OAAO,EAAE;YACP,CAAC,EAAE,CAAC,KAAK;YACT,CAAC,EAAE,CAAC,KAAK;YACT,CAAC,EAAE,KAAK;SACT;KACF,CAAC;;sDAKA;AAEF;IAAC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,SAAS;QACtB,OAAO,EAAE,OAAO;KACjB,CAAC;;sDACe;AAEjB;IAAC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,SAAS;QACtB,OAAO,EAAE,QAAQ;KAClB,CAAC;;uDACgB;AAElB;IAAC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,UAAU;QACvB,OAAO,EAAE,OAAO;KACjB,CAAC;;sDACe;AAEjB;IAAC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,WAAW;QACxB,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;QACtB,OAAO,EAAE,MAAM;KAChB,CAAC;;uDACyB;AAtE7B,oDAuEC;AAED,MAAa,mBAAmB;CAY/B;AAXC;IAAC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,OAAO;KACjB,CAAC;;kDACY;AAEd;IAAC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,0BAA0B;KACpC,CAAC;;kDACY;AAXhB,kDAYC;AAED,MAAa,6BAA6B;CAgCzC;AA/BC;IAAC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,aAAa;QAC1B,IAAI,EAAE,CAAC,oBAAoB,CAAC;QAC5B,OAAO,EAAE,CAAC;gBACR,KAAK,EAAE,OAAO;gBACd,IAAI,EAAE,aAAa;gBACnB,KAAK,EAAE,0BAA0B;gBACjC,QAAQ,EAAE;oBACR,CAAC,EAAE,CAAC,QAAQ;oBACZ,CAAC,EAAE,QAAQ;oBACX,CAAC,EAAE,QAAQ;iBACZ;gBACD,QAAQ,EAAE;oBACR,CAAC,EAAE,CAAC,KAAK;oBACT,CAAC,EAAE,CAAC,KAAK;oBACT,CAAC,EAAE,KAAK;iBACT;gBACD,QAAQ,EAAE,OAAO;gBACjB,SAAS,EAAE,QAAQ;gBACnB,QAAQ,EAAE,OAAO;gBACjB,SAAS,EAAE,MAAM;aAClB,CAAC;KACH,CAAC;;gEACgC;AAElC;IAAC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,WAAW;QACxB,IAAI,EAAE,CAAC,mBAAmB,CAAC;QAC3B,OAAO,EAAE,EAAE;KACZ,CAAC;;6DAC4B;AA/BhC,sEAgCC"}