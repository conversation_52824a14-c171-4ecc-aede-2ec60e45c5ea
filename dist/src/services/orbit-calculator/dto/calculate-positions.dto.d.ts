export declare class TLESatelliteDto {
    satId: string;
    name: string;
    line1: string;
    line2: string;
}
export declare class CalculatePositionsRequestDto {
    satellites: TLESatelliteDto[];
    time?: string;
    continueOnError?: boolean;
    batchSize?: number;
}
export declare class SatellitePositionDto {
    satId: string;
    name: string;
    epoch: Date;
    position: {
        x: number;
        y: number;
        z: number;
    };
    velocity: {
        x: number;
        y: number;
        z: number;
    };
    latitude: number;
    longitude: number;
    altitude: number;
    algorithm: 'SGP4' | 'SDP4';
}
export declare class CalculationErrorDto {
    satId: string;
    error: string;
}
export declare class CalculatePositionsResponseDto {
    positions: SatellitePositionDto[];
    errors: CalculationErrorDto[];
}
