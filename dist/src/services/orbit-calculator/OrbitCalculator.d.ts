import { ISatellitePosition, ITLESatellite, IOrbitCalculatorConfig } from './types';
export declare class OrbitCalculator {
    private readonly config;
    private readonly logger;
    private readonly batchSize;
    constructor(config: IOrbitCalculatorConfig);
    private isDeepSpace;
    private getEpochFromTLE;
    calculatePosition(satInfo: ITLESatellite, time?: Date): Promise<ISatellitePosition>;
    calculatePositions(satellites: ITLESatellite[], time?: Date, options?: {
        continueOnError?: boolean;
        batchSize?: number;
    }): Promise<{
        positions: ISatellitePosition[];
        errors: Array<{
            satId: string;
            error: Error;
        }>;
    }>;
    calculatePositionsStream(satellites: ITLESatellite[], time?: Date, batchSize?: number): AsyncGenerator<{
        position?: ISatellitePosition;
        error?: {
            satId: string;
            error: Error;
        };
    }>;
}
