"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var NewsSchedulerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.NewsSchedulerService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const elasticsearch_news_service_1 = require("../../elasticsearch/services/elasticsearch.news.service");
const news_scheduler_config_1 = require("../../../config/news-scheduler.config");
let NewsSchedulerService = NewsSchedulerService_1 = class NewsSchedulerService {
    constructor(newsService) {
        this.newsService = newsService;
        this.logger = new common_1.Logger(NewsSchedulerService_1.name);
        this.currentStatus = news_scheduler_config_1.TaskStatus.IDLE;
        this.lastExecutionResults = [];
        this.isRunning = false;
        this.config = (0, news_scheduler_config_1.getNewsSchedulerConfig)();
        try {
            (0, news_scheduler_config_1.validateNewsSchedulerConfig)(this.config);
            this.logger.log(`新闻定时任务服务初始化完成，配置: ${JSON.stringify({
                enabled: this.config.enabled,
                cronExpression: this.config.cronExpression,
                timezone: this.config.timezone
            })}`);
        }
        catch (error) {
            this.logger.error(`新闻定时任务配置验证失败: ${error.message}`);
            throw error;
        }
    }
    async scheduledNewsProcessing() {
        if (!this.config.enabled) {
            this.logger.debug('新闻定时任务已禁用，跳过执行');
            return;
        }
        if (this.isRunning) {
            this.logger.warn('新闻处理任务正在运行中，跳过本次执行');
            return;
        }
        this.logger.log('🕐 新闻处理定时任务触发：开始执行新闻翻译和主题提取');
        try {
            await this.executeNewsProcessingPipeline();
        }
        catch (error) {
            this.logger.error('❌ 新闻处理定时任务执行失败', error.stack);
        }
    }
    async executeNewsProcessingPipeline() {
        var _a, _b, _c;
        this.isRunning = true;
        this.currentStatus = news_scheduler_config_1.TaskStatus.RUNNING;
        this.lastExecutionResults = [];
        const pipelineStartTime = new Date();
        this.logger.log('🚀 开始执行新闻处理流水线');
        try {
            this.logger.log('📝 开始执行新闻翻译和主题提取任务（优化版一次性调用）');
            const translationResult = await this.executeTranslationTask();
            this.lastExecutionResults.push(translationResult);
            if (translationResult.status === news_scheduler_config_1.TaskStatus.FAILED) {
                throw new Error(`翻译和主题提取任务失败: ${translationResult.error}`);
            }
            const stats = translationResult.statistics;
            const translationStats = `翻译: 成功${(stats === null || stats === void 0 ? void 0 : stats.success) || 0}，失败${(stats === null || stats === void 0 ? void 0 : stats.failed) || 0}`;
            const themeStats = (stats === null || stats === void 0 ? void 0 : stats.themeExtraction)
                ? `，主题提取: 成功${(_a = stats === null || stats === void 0 ? void 0 : stats.themeExtraction) === null || _a === void 0 ? void 0 : _a.success}，失败${(_b = stats === null || stats === void 0 ? void 0 : stats.themeExtraction) === null || _b === void 0 ? void 0 : _b.failed}，跳过${(_c = stats === null || stats === void 0 ? void 0 : stats.themeExtraction) === null || _c === void 0 ? void 0 : _c.skipped}`
                : '';
            this.logger.log(`✅ 翻译和主题提取任务完成，耗时: ${translationResult.duration}ms，${translationStats}${themeStats}`);
            const pipelineEndTime = new Date();
            const pipelineDuration = pipelineEndTime.getTime() - pipelineStartTime.getTime();
            this.currentStatus = news_scheduler_config_1.TaskStatus.COMPLETED;
            this.logger.log(`🎉 新闻处理流水线执行完成！总耗时: ${pipelineDuration}ms（使用优化的一次性调用，大幅减少API调用次数）`);
        }
        catch (error) {
            this.currentStatus = news_scheduler_config_1.TaskStatus.FAILED;
            this.logger.error(`❌ 新闻处理流水线执行失败: ${error.message}`, error.stack);
            throw error;
        }
        finally {
            this.isRunning = false;
        }
    }
    async executeTranslationTask() {
        const startTime = new Date();
        const result = {
            taskType: 'translation',
            status: news_scheduler_config_1.TaskStatus.RUNNING,
            startTime
        };
        try {
            const translationParams = {
                batchSize: this.config.translationConfig.batchSize,
                maxDocs: this.config.translationConfig.maxDocs,
                forceRetranslate: this.config.translationConfig.forceReprocess,
                specificIndexes: this.config.translationConfig.specificIndexes,
                llmMode: this.config.translationConfig.llmMode,
                customModel: this.config.translationConfig.customModel,
                autoExtractThemes: true
            };
            this.logger.debug(`翻译任务参数: ${JSON.stringify(translationParams)}`);
            const statistics = await this.newsService.translateNews(translationParams);
            const endTime = new Date();
            result.status = news_scheduler_config_1.TaskStatus.COMPLETED;
            result.endTime = endTime;
            result.duration = endTime.getTime() - startTime.getTime();
            result.statistics = statistics;
            return result;
        }
        catch (error) {
            const endTime = new Date();
            result.status = news_scheduler_config_1.TaskStatus.FAILED;
            result.endTime = endTime;
            result.duration = endTime.getTime() - startTime.getTime();
            result.error = error.message;
            this.logger.error(`翻译任务执行失败: ${error.message}`, error.stack);
            return result;
        }
    }
    async executeThemeExtractionTask() {
        const startTime = new Date();
        const result = {
            taskType: 'theme_extraction',
            status: news_scheduler_config_1.TaskStatus.RUNNING,
            startTime
        };
        try {
            const themeExtractionParams = {
                batchSize: this.config.themeExtractionConfig.batchSize,
                maxDocs: this.config.themeExtractionConfig.maxDocs,
                forceReextract: this.config.themeExtractionConfig.forceReprocess,
                specificIndexes: this.config.themeExtractionConfig.specificIndexes,
                llmMode: this.config.themeExtractionConfig.llmMode,
                customModel: this.config.themeExtractionConfig.customModel
            };
            this.logger.debug(`主题提取和内容类型识别任务参数: ${JSON.stringify(themeExtractionParams)}`);
            const statistics = await this.newsService.extractNewsThemes(themeExtractionParams);
            const endTime = new Date();
            result.status = news_scheduler_config_1.TaskStatus.COMPLETED;
            result.endTime = endTime;
            result.duration = endTime.getTime() - startTime.getTime();
            result.statistics = statistics;
            return result;
        }
        catch (error) {
            const endTime = new Date();
            result.status = news_scheduler_config_1.TaskStatus.FAILED;
            result.endTime = endTime;
            result.duration = endTime.getTime() - startTime.getTime();
            result.error = error.message;
            this.logger.error(`主题提取和内容类型识别任务执行失败: ${error.message}`, error.stack);
            return result;
        }
    }
    async triggerManualExecution() {
        if (this.isRunning) {
            return {
                success: false,
                message: '新闻处理任务正在运行中，请稍后再试'
            };
        }
        this.logger.log('🔧 手动触发新闻处理任务');
        try {
            await this.executeNewsProcessingPipeline();
            return {
                success: true,
                message: '新闻处理任务执行成功',
                results: this.lastExecutionResults
            };
        }
        catch (error) {
            return {
                success: false,
                message: `新闻处理任务执行失败: ${error.message}`,
                results: this.lastExecutionResults
            };
        }
    }
    getTaskStatus() {
        return {
            currentStatus: this.currentStatus,
            isRunning: this.isRunning,
            config: this.config,
            lastExecutionResults: this.lastExecutionResults
        };
    }
    updateConfig(newConfig) {
        Object.assign(this.config, newConfig);
        try {
            (0, news_scheduler_config_1.validateNewsSchedulerConfig)(this.config);
            this.logger.log(`配置已更新: ${JSON.stringify(newConfig)}`);
        }
        catch (error) {
            this.logger.error(`配置更新失败: ${error.message}`);
            throw error;
        }
    }
    async stopRunningTask() {
        if (!this.isRunning) {
            this.logger.warn('没有正在运行的任务');
            return;
        }
        this.logger.warn('🛑 紧急停止正在运行的新闻处理任务');
        this.isRunning = false;
        this.currentStatus = news_scheduler_config_1.TaskStatus.FAILED;
        const stopResult = {
            taskType: 'translation',
            status: news_scheduler_config_1.TaskStatus.FAILED,
            startTime: new Date(),
            endTime: new Date(),
            error: '任务被手动停止'
        };
        this.lastExecutionResults.push(stopResult);
    }
};
__decorate([
    (0, schedule_1.Cron)('0 5,12,18 * * *', {
        name: 'newsProcessingTask',
        timeZone: 'Asia/Shanghai'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], NewsSchedulerService.prototype, "scheduledNewsProcessing", null);
NewsSchedulerService = NewsSchedulerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [elasticsearch_news_service_1.ElasticsearchNewsService])
], NewsSchedulerService);
exports.NewsSchedulerService = NewsSchedulerService;
//# sourceMappingURL=news-scheduler.service.js.map