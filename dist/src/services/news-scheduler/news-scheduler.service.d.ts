import { ElasticsearchNewsService } from '../../elasticsearch/services/elasticsearch.news.service';
import { NewsSchedulerConfig, TaskStatus, TaskExecutionResult } from '../../../config/news-scheduler.config';
export declare class NewsSchedulerService {
    private readonly newsService;
    private readonly logger;
    private readonly config;
    private currentStatus;
    private lastExecutionResults;
    private isRunning;
    constructor(newsService: ElasticsearchNewsService);
    scheduledNewsProcessing(): Promise<void>;
    executeNewsProcessingPipeline(): Promise<void>;
    private executeTranslationTask;
    private executeThemeExtractionTask;
    triggerManualExecution(): Promise<{
        success: boolean;
        message: string;
        results?: TaskExecutionResult[];
    }>;
    getTaskStatus(): {
        currentStatus: TaskStatus;
        isRunning: boolean;
        config: NewsSchedulerConfig;
        lastExecutionResults: TaskExecutionResult[];
    };
    updateConfig(newConfig: Partial<NewsSchedulerConfig>): void;
    stopRunningTask(): Promise<void>;
}
