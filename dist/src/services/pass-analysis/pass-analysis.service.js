"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PassAnalysisService = void 0;
const common_1 = require("@nestjs/common");
const OrbitCalculator_1 = require("../orbit-calculator/OrbitCalculator");
let PassAnalysisService = class PassAnalysisService {
    constructor(orbitCalculator) {
        this.orbitCalculator = orbitCalculator;
        this.earthRadius = 6378.137;
    }
    deg2rad(degrees) {
        return degrees * Math.PI / 180;
    }
    rad2deg(radians) {
        return radians * 180 / Math.PI;
    }
    calculateGroundStationECI(groundStation, time) {
        const msPerDay = 86400000;
        const j2000 = new Date('2000-01-01T12:00:00Z');
        const daysSinceJ2000 = (time.getTime() - j2000.getTime()) / msPerDay;
        const gmst = (280.4606 + 360.9856473 * daysSinceJ2000) % 360;
        const longitude = groundStation.longitude;
        const lst = (gmst + longitude) % 360;
        const lstRad = this.deg2rad(lst);
        const latRad = this.deg2rad(groundStation.latitude);
        const r = this.earthRadius + groundStation.altitude / 1000;
        return {
            x: r * Math.cos(latRad) * Math.cos(lstRad),
            y: r * Math.cos(latRad) * Math.sin(lstRad),
            z: r * Math.sin(latRad)
        };
    }
    calculateLookAngles(groundStation, satPosition, time) {
        const groundECI = this.calculateGroundStationECI(groundStation, time);
        const dx = satPosition.x - groundECI.x;
        const dy = satPosition.y - groundECI.y;
        const dz = satPosition.z - groundECI.z;
        const range = Math.sqrt(dx * dx + dy * dy + dz * dz);
        const msPerDay = 86400000;
        const j2000 = new Date('2000-01-01T12:00:00Z');
        const daysSinceJ2000 = (time.getTime() - j2000.getTime()) / msPerDay;
        const gmst = (280.4606 + 360.9856473 * daysSinceJ2000) % 360;
        const gstRad = this.deg2rad(gmst);
        const lonRad = this.deg2rad(groundStation.longitude);
        const latRad = this.deg2rad(groundStation.latitude);
        const rotLon = gstRad + lonRad;
        const sinLat = Math.sin(latRad);
        const cosLat = Math.cos(latRad);
        const sinLon = Math.sin(rotLon);
        const cosLon = Math.cos(rotLon);
        const s = sinLat * cosLon * dx + sinLat * sinLon * dy - cosLat * dz;
        const e = -sinLon * dx + cosLon * dy;
        const z = cosLat * cosLon * dx + cosLat * sinLon * dy + sinLat * dz;
        let azimuth = this.rad2deg(Math.atan2(e, s)) % 360;
        if (azimuth < 0)
            azimuth += 360;
        const elevation = this.rad2deg(Math.asin(z / range));
        return {
            azimuth,
            elevation,
            range
        };
    }
    isVisible(elevation, minElevation = 0) {
        return elevation >= minElevation;
    }
    async analyzeSingleSatellite(params, satellite) {
        const passes = [];
        let currentPass = null;
        const timeStep = params.timeStep || 60;
        const minElevation = params.minElevation || 0;
        const totalSeconds = (params.endTime.getTime() - params.startTime.getTime()) / 1000;
        const steps = Math.ceil(totalSeconds / timeStep);
        for (let i = 0; i <= steps; i++) {
            const currentTime = new Date(params.startTime.getTime() + i * timeStep * 1000);
            const satPosition = await this.orbitCalculator.calculatePosition({
                satId: satellite.satId,
                name: satellite.name,
                line1: satellite.line1,
                line2: satellite.line2
            }, currentTime);
            if (!satPosition)
                continue;
            const lookAngles = this.calculateLookAngles(params.groundStation, satPosition.position, currentTime);
            const passPoint = Object.assign({ time: currentTime }, lookAngles);
            const isVisible = this.isVisible(lookAngles.elevation, minElevation);
            if (isVisible) {
                if (!currentPass) {
                    currentPass = {
                        startPass: passPoint,
                        positions: [passPoint]
                    };
                }
                else {
                    currentPass.positions.push(passPoint);
                }
            }
            else if (currentPass) {
                const endPass = currentPass.positions[currentPass.positions.length - 1];
                passes.push({
                    satellite: {
                        satId: satellite.satId,
                        name: satellite.name,
                        organization: satellite.organization,
                        company: satellite.company,
                        country: satellite.country,
                        type: satellite.type
                    },
                    startPass: currentPass.startPass,
                    endPass: endPass,
                    duration: (endPass.time.getTime() - currentPass.startPass.time.getTime()) / 1000
                });
                currentPass = null;
            }
        }
        return passes;
    }
    async analyzeMultipleSatellites(params) {
        const allPasses = [];
        const passPromises = params.satellites.map(satellite => this.analyzeSingleSatellite(params, satellite));
        const results = await Promise.all(passPromises);
        results.forEach(passes => {
            allPasses.push(...passes);
        });
        return allPasses.sort((a, b) => a.startPass.time.getTime() - b.startPass.time.getTime());
    }
};
PassAnalysisService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [OrbitCalculator_1.OrbitCalculator])
], PassAnalysisService);
exports.PassAnalysisService = PassAnalysisService;
//# sourceMappingURL=pass-analysis.service.js.map