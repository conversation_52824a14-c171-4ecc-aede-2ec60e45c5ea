"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PassAnalysisResponseDto = exports.PassInfoDto = exports.SatelliteBasicInfoDto = exports.PassPointDto = exports.PassAnalysisRequestDto = exports.SatelliteInfoDto = exports.GroundStationDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
class GroundStationDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '地面站名称',
        example: '北京站',
        required: true
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GroundStationDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '经度(度)',
        example: 116.3833,
        required: true
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GroundStationDto.prototype, "longitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '纬度(度)',
        example: 39.9167,
        required: true
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GroundStationDto.prototype, "latitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '海拔高度(米)',
        example: 43.5,
        required: true
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GroundStationDto.prototype, "altitude", void 0);
exports.GroundStationDto = GroundStationDto;
class SatelliteInfoDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星编号',
        example: '25544',
        required: true
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SatelliteInfoDto.prototype, "satId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星名称',
        example: 'ISS (ZARYA)',
        required: true
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SatelliteInfoDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '所属机构',
        example: 'NASA',
        required: true
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SatelliteInfoDto.prototype, "organization", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '所属公司',
        example: 'NASA',
        required: true
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SatelliteInfoDto.prototype, "company", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '所属国家',
        example: 'USA',
        required: true
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SatelliteInfoDto.prototype, "country", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星类型',
        example: '空间站',
        required: true
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SatelliteInfoDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'TLE数据第一行',
        example: '1 25544U 98067A   24054.88088078  .00014558  00000+0  26729-3 0  9990',
        required: true
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SatelliteInfoDto.prototype, "line1", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'TLE数据第二行',
        example: '2 25544  51.6416 150.2506 0004789  89.9089  32.8809 15.49512781434577',
        required: true
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SatelliteInfoDto.prototype, "line2", void 0);
exports.SatelliteInfoDto = SatelliteInfoDto;
class PassAnalysisRequestDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '地面站信息',
        type: GroundStationDto,
        required: true
    }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => GroundStationDto),
    __metadata("design:type", GroundStationDto)
], PassAnalysisRequestDto.prototype, "groundStation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星信息列表',
        type: [SatelliteInfoDto],
        required: true
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => SatelliteInfoDto),
    __metadata("design:type", Array)
], PassAnalysisRequestDto.prototype, "satellites", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '开始时间(UTC)',
        example: '2024-02-24T00:00:00Z',
        required: true
    }),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], PassAnalysisRequestDto.prototype, "startTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '结束时间(UTC)',
        example: '2024-02-25T00:00:00Z',
        required: true
    }),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], PassAnalysisRequestDto.prototype, "endTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最小仰角(度)',
        example: 10,
        required: false,
        minimum: 0,
        maximum: 90,
        default: 0
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(90),
    __metadata("design:type", Number)
], PassAnalysisRequestDto.prototype, "minElevation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '时间步长(秒)',
        example: 60,
        required: false,
        minimum: 1,
        maximum: 3600,
        default: 60
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(3600),
    __metadata("design:type", Number)
], PassAnalysisRequestDto.prototype, "timeStep", void 0);
exports.PassAnalysisRequestDto = PassAnalysisRequestDto;
class PassPointDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '时间(UTC)',
        example: '2024-02-24T02:30:00Z'
    }),
    __metadata("design:type", Date)
], PassPointDto.prototype, "time", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '方位角(度)',
        example: 123.45
    }),
    __metadata("design:type", Number)
], PassPointDto.prototype, "azimuth", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '仰角(度)',
        example: 45.67
    }),
    __metadata("design:type", Number)
], PassPointDto.prototype, "elevation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '距离(千米)',
        example: 789.12
    }),
    __metadata("design:type", Number)
], PassPointDto.prototype, "range", void 0);
exports.PassPointDto = PassPointDto;
class SatelliteBasicInfoDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星编号',
        example: '25544'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SatelliteBasicInfoDto.prototype, "satId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星名称',
        example: 'ISS (ZARYA)'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SatelliteBasicInfoDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '所属机构',
        example: 'NASA'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SatelliteBasicInfoDto.prototype, "organization", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '所属公司',
        example: 'NASA'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SatelliteBasicInfoDto.prototype, "company", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '所属国家',
        example: 'USA'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SatelliteBasicInfoDto.prototype, "country", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星类型',
        example: '空间站'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SatelliteBasicInfoDto.prototype, "type", void 0);
exports.SatelliteBasicInfoDto = SatelliteBasicInfoDto;
class PassInfoDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星信息',
        type: SatelliteBasicInfoDto
    }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => SatelliteBasicInfoDto),
    __metadata("design:type", SatelliteBasicInfoDto)
], PassInfoDto.prototype, "satellite", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '入境信息',
        type: PassPointDto
    }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => PassPointDto),
    __metadata("design:type", PassPointDto)
], PassInfoDto.prototype, "startPass", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '离境信息',
        type: PassPointDto
    }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => PassPointDto),
    __metadata("design:type", PassPointDto)
], PassInfoDto.prototype, "endPass", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '过境时长(秒)',
        example: 600
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], PassInfoDto.prototype, "duration", void 0);
exports.PassInfoDto = PassInfoDto;
class PassAnalysisResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '过境信息列表',
        type: [PassInfoDto]
    }),
    __metadata("design:type", Array)
], PassAnalysisResponseDto.prototype, "passes", void 0);
exports.PassAnalysisResponseDto = PassAnalysisResponseDto;
//# sourceMappingURL=pass-analysis.dto.js.map