export declare class GroundStationDto {
    name: string;
    longitude: number;
    latitude: number;
    altitude: number;
}
export declare class SatelliteInfoDto {
    satId: string;
    name: string;
    organization: string;
    company: string;
    country: string;
    type: string;
    line1: string;
    line2: string;
}
export declare class PassAnalysisRequestDto {
    groundStation: GroundStationDto;
    satellites: SatelliteInfoDto[];
    startTime: string;
    endTime: string;
    minElevation?: number;
    timeStep?: number;
}
export declare class PassPointDto {
    time: Date;
    azimuth: number;
    elevation: number;
    range: number;
}
export declare class SatelliteBasicInfoDto {
    satId: string;
    name: string;
    organization: string;
    company: string;
    country: string;
    type: string;
}
export declare class PassInfoDto {
    satellite: SatelliteBasicInfoDto;
    startPass: PassPointDto;
    endPass: PassPointDto;
    duration: number;
}
export declare class PassAnalysisResponseDto {
    passes: PassInfoDto[];
}
