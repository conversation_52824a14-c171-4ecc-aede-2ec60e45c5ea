export interface IGroundStation {
    name: string;
    longitude: number;
    latitude: number;
    altitude: number;
}
export interface ISatelliteInfo {
    satId: string;
    name: string;
    organization: string;
    company: string;
    country: string;
    type: string;
    line1: string;
    line2: string;
}
export interface IPassPoint {
    time: Date;
    azimuth: number;
    elevation: number;
    range: number;
}
export interface IPassInfo {
    satellite: {
        satId: string;
        name: string;
        organization: string;
        company: string;
        country: string;
        type: string;
    };
    startPass: IPassPoint;
    endPass: IPassPoint;
    duration: number;
}
export interface IPassAnalysisParams {
    groundStation: IGroundStation;
    satellites: ISatelliteInfo[];
    startTime: Date;
    endTime: Date;
    minElevation?: number;
    timeStep?: number;
}
