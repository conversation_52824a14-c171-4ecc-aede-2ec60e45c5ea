import { OrbitCalculator } from '../orbit-calculator/OrbitCalculator';
import { IPassInfo, IPassAnalysisParams } from './types';
export declare class PassAnalysisService {
    private readonly orbitCalculator;
    private readonly earthRadius;
    constructor(orbitCalculator: OrbitCalculator);
    private deg2rad;
    private rad2deg;
    private calculateGroundStationECI;
    private calculateLookAngles;
    private isVisible;
    private analyzeSingleSatellite;
    analyzeMultipleSatellites(params: IPassAnalysisParams): Promise<IPassInfo[]>;
}
