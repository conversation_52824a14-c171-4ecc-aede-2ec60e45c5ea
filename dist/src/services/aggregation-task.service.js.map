{"version": 3, "file": "aggregation-task.service.js", "sourceRoot": "", "sources": ["../../../src/services/aggregation-task.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,6CAAmD;AACnD,qCAAqC;AACrC,iFAAsE;AAO/D,IAAM,sBAAsB,8BAA5B,MAAM,sBAAsB;IAGjC,YAEU,cAA2C;QAA3C,mBAAc,GAAd,cAAc,CAA6B;QAJpC,WAAM,GAAG,IAAI,eAAM,CAAC,wBAAsB,CAAC,IAAI,CAAC,CAAC;IAK/D,CAAC;IAOJ,KAAK,CAAC,UAAU,CAAC,OAGhB;QACC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAExD,MAAM,IAAI,GAAG,IAAI,yCAAe,EAAE,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACnC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACxB,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACrC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAElB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAOD,KAAK,CAAC,SAAS,CAAC,MAAc;QAC5B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,IAAI,EAAE;YACT,MAAM,IAAI,KAAK,CAAC,SAAS,MAAM,KAAK,CAAC,CAAC;SACvC;QAED,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACxB,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAE7B,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IASD,KAAK,CAAC,kBAAkB,CACtB,MAAc,EACd,QAAgB,EAChB,gBAAwB;QAExB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,IAAI,EAAE;YACT,MAAM,IAAI,KAAK,CAAC,SAAS,MAAM,KAAK,CAAC,CAAC;SACvC;QAED,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAE1C,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAQD,KAAK,CAAC,YAAY,CAChB,MAAc,EACd,iBAAyB;QAEzB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,IAAI,EAAE;YACT,MAAM,IAAI,KAAK,CAAC,SAAS,MAAM,KAAK,CAAC,CAAC;SACvC;QAED,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QAC3B,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;QAEpB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAQD,KAAK,CAAC,QAAQ,CACZ,MAAc,EACd,YAAoB;QAEpB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,IAAI,EAAE;YACT,MAAM,IAAI,KAAK,CAAC,SAAS,MAAM,KAAK,CAAC,CAAC;SACvC;QAED,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QAC3B,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAElC,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAQD,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,EAAE,KAAK,GAAG,EAAE;QACjC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC;YAC5D,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;YAC5B,IAAI,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK;YACxB,IAAI,EAAE,KAAK;SACZ,CAAC,CAAC;QAEH,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;IAC1B,CAAC;IAOD,KAAK,CAAC,WAAW,CAAC,MAAc;QAC9B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,IAAI,EAAE;YACT,MAAM,IAAI,KAAK,CAAC,SAAS,MAAM,KAAK,CAAC,CAAC;SACvC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAMD,KAAK,CAAC,eAAe;QACnB,IAAI;YACF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YACjC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;gBAC3C,KAAK,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE;gBAC5B,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;aAC9B,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC;YACrD,OAAO,KAAK,CAAC;SACd;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAChE,OAAO,EAAE,CAAC;SACX;IACH,CAAC;IAMD,KAAK,CAAC,aAAa;QACjB,IAAI;YACF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAG9B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;gBAC3C,KAAK,EAAE;oBACL,SAAS,EAAE,MAAM;iBAClB;gBACD,IAAI,EAAE,CAAC;aACR,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAEhD,IAAI,IAAI,EAAE;gBACR,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,IAAI,CAAC,EAAE,SAAS,IAAI,CAAC,SAAS,SAAS,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;aAC1F;iBAAM;gBACL,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;aAChC;YAED,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC;SACb;IACH,CAAC;CACF,CAAA;AAlMY,sBAAsB;IADlC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,yCAAe,CAAC,CAAA;qCACV,oBAAU;GALzB,sBAAsB,CAkMlC;AAlMY,wDAAsB"}