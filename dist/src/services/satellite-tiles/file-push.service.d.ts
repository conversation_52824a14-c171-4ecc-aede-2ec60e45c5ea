import { FilePushTargetDto, FilePushResultDto, FileChunkingDto } from './dto/generate-tiles.dto';
export declare class FilePushService {
    private readonly logger;
    pushFiles(filePaths: string[], targets: FilePushTargetDto[], options?: {
        enableGzip?: boolean;
        chunking?: FileChunkingDto;
        callbackUrl?: string;
    }): Promise<FilePushResultDto[]>;
    private pushToTarget;
    private pushToHttp;
    private pushToLocal;
    private pushToFtp;
    private pushToSftp;
    private pushToS3;
    private pushToOss;
    private chunkFiles;
    private compressFiles;
    private sendCallback;
    cleanupTempFiles(filePaths: string[]): Promise<void>;
    getFileSizes(filePaths: string[]): Record<string, number>;
}
