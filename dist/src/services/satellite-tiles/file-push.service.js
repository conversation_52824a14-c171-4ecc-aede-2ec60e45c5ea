"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var FilePushService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FilePushService = void 0;
const common_1 = require("@nestjs/common");
const fs = require("fs");
const path = require("path");
const zlib = require("zlib");
const axios_1 = require("axios");
let FilePushService = FilePushService_1 = class FilePushService {
    constructor() {
        this.logger = new common_1.Logger(FilePushService_1.name);
    }
    async pushFiles(filePaths, targets, options = {}) {
        var _a;
        const results = [];
        for (const target of targets) {
            try {
                this.logger.log(`开始推送文件到目标: ${target.target}`);
                const startTime = Date.now();
                let filesToPush = filePaths;
                let totalSize = 0;
                if ((_a = options.chunking) === null || _a === void 0 ? void 0 : _a.enabled) {
                    filesToPush = await this.chunkFiles(filePaths, options.chunking);
                }
                if (options.enableGzip) {
                    filesToPush = await this.compressFiles(filesToPush);
                }
                for (const filePath of filesToPush) {
                    if (fs.existsSync(filePath)) {
                        totalSize += fs.statSync(filePath).size;
                    }
                }
                await this.pushToTarget(filesToPush, target);
                const duration = Date.now() - startTime;
                results.push({
                    target: target.target,
                    success: true,
                    files: filesToPush.map(fp => path.basename(fp)),
                    duration,
                    dataSize: totalSize
                });
                this.logger.log(`✅ 文件推送成功: ${target.target}, 耗时: ${duration}ms, 大小: ${(totalSize / 1024 / 1024).toFixed(2)}MB`);
            }
            catch (error) {
                this.logger.error(`❌ 文件推送失败: ${target.target}`, error.stack);
                results.push({
                    target: target.target,
                    success: false,
                    files: [],
                    duration: 0,
                    dataSize: 0,
                    error: error.message
                });
            }
        }
        if (options.callbackUrl) {
            await this.sendCallback(options.callbackUrl, results);
        }
        return results;
    }
    async pushToTarget(filePaths, target) {
        switch (target.type) {
            case 'http':
                await this.pushToHttp(filePaths, target);
                break;
            case 'local':
                await this.pushToLocal(filePaths, target);
                break;
            case 'ftp':
                await this.pushToFtp(filePaths, target);
                break;
            case 'sftp':
                await this.pushToSftp(filePaths, target);
                break;
            case 's3':
                await this.pushToS3(filePaths, target);
                break;
            case 'oss':
                await this.pushToOss(filePaths, target);
                break;
            default:
                throw new Error(`不支持的推送类型: ${target.type}`);
        }
    }
    async pushToHttp(filePaths, target) {
        var _a, _b, _c, _d;
        const timeout = ((_a = target.options) === null || _a === void 0 ? void 0 : _a.timeout) || 60000;
        const retries = ((_b = target.options) === null || _b === void 0 ? void 0 : _b.retries) || 3;
        for (const filePath of filePaths) {
            if (!fs.existsSync(filePath)) {
                throw new Error(`文件不存在: ${filePath}`);
            }
            const fileName = path.basename(filePath);
            const fileContent = fs.readFileSync(filePath);
            let lastError = null;
            for (let attempt = 1; attempt <= retries; attempt++) {
                try {
                    const headers = {
                        'Content-Type': 'application/octet-stream',
                        'Content-Length': fileContent.length.toString(),
                        'X-File-Name': fileName,
                        'X-File-Size': fileContent.length.toString()
                    };
                    if ((_c = target.credentials) === null || _c === void 0 ? void 0 : _c.token) {
                        headers['Authorization'] = `Bearer ${target.credentials.token}`;
                    }
                    if ((_d = target.credentials) === null || _d === void 0 ? void 0 : _d.apiKey) {
                        headers['X-API-Key'] = target.credentials.apiKey;
                    }
                    const response = await axios_1.default.post(target.target, fileContent, {
                        headers,
                        timeout
                    });
                    if (response.status >= 200 && response.status < 300) {
                        this.logger.log(`✅ HTTP推送成功: ${fileName} -> ${target.target}`);
                        break;
                    }
                    else {
                        throw new Error(`HTTP响应错误: ${response.status} ${response.statusText}`);
                    }
                }
                catch (error) {
                    lastError = error;
                    this.logger.warn(`⚠️  HTTP推送失败 (尝试 ${attempt}/${retries}): ${error.message}`);
                    if (attempt < retries) {
                        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
                    }
                }
            }
            if (lastError) {
                throw lastError;
            }
        }
    }
    async pushToLocal(filePaths, target) {
        const targetDir = target.target;
        if (!fs.existsSync(targetDir)) {
            fs.mkdirSync(targetDir, { recursive: true });
        }
        for (const filePath of filePaths) {
            if (!fs.existsSync(filePath)) {
                throw new Error(`文件不存在: ${filePath}`);
            }
            const fileName = path.basename(filePath);
            const targetPath = path.join(targetDir, fileName);
            fs.copyFileSync(filePath, targetPath);
            this.logger.log(`✅ 本地推送成功: ${fileName} -> ${targetPath}`);
        }
    }
    async pushToFtp(filePaths, target) {
        throw new Error('FTP推送功能待实现');
    }
    async pushToSftp(filePaths, target) {
        throw new Error('SFTP推送功能待实现');
    }
    async pushToS3(filePaths, target) {
        throw new Error('S3推送功能待实现');
    }
    async pushToOss(filePaths, target) {
        throw new Error('OSS推送功能待实现');
    }
    async chunkFiles(filePaths, chunking) {
        const chunkedFiles = [];
        const chunkSizeBytes = (chunking.chunkSize || 10) * 1024 * 1024;
        for (const filePath of filePaths) {
            if (!fs.existsSync(filePath)) {
                continue;
            }
            const fileSize = fs.statSync(filePath).size;
            if (fileSize <= chunkSizeBytes) {
                chunkedFiles.push(filePath);
                continue;
            }
            const fileName = path.basename(filePath, path.extname(filePath));
            const fileExt = path.extname(filePath);
            const fileDir = path.dirname(filePath);
            const fileContent = fs.readFileSync(filePath);
            const totalChunks = Math.ceil(fileSize / chunkSizeBytes);
            this.logger.log(`📦 开始分片文件: ${filePath}, 大小: ${(fileSize / 1024 / 1024).toFixed(2)}MB, 分片数: ${totalChunks}`);
            for (let i = 0; i < totalChunks; i++) {
                const start = i * chunkSizeBytes;
                const end = Math.min(start + chunkSizeBytes, fileSize);
                const chunkContent = fileContent.slice(start, end);
                const chunkFileName = (chunking.namePattern || '{name}_chunk_{index}{ext}')
                    .replace('{name}', fileName)
                    .replace('{index}', (i + 1).toString().padStart(3, '0'))
                    .replace('{ext}', fileExt);
                const chunkFilePath = path.join(fileDir, chunkFileName);
                fs.writeFileSync(chunkFilePath, chunkContent);
                chunkedFiles.push(chunkFilePath);
                this.logger.log(`📦 生成分片: ${chunkFileName}, 大小: ${(chunkContent.length / 1024 / 1024).toFixed(2)}MB`);
            }
        }
        return chunkedFiles;
    }
    async compressFiles(filePaths) {
        const compressedFiles = [];
        for (const filePath of filePaths) {
            if (!fs.existsSync(filePath)) {
                continue;
            }
            const compressedPath = filePath + '.gz';
            const fileContent = fs.readFileSync(filePath);
            const compressed = zlib.gzipSync(fileContent, { level: 9 });
            fs.writeFileSync(compressedPath, compressed);
            const originalSize = fileContent.length;
            const compressedSize = compressed.length;
            const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);
            this.logger.log(`🗜️  文件压缩完成: ${path.basename(filePath)}, 压缩率: ${compressionRatio}%, ${(originalSize / 1024 / 1024).toFixed(2)}MB -> ${(compressedSize / 1024 / 1024).toFixed(2)}MB`);
            compressedFiles.push(compressedPath);
        }
        return compressedFiles;
    }
    async sendCallback(callbackUrl, results) {
        try {
            const payload = {
                timestamp: new Date().toISOString(),
                results,
                summary: {
                    total: results.length,
                    success: results.filter(r => r.success).length,
                    failed: results.filter(r => !r.success).length,
                    totalDataSize: results.reduce((sum, r) => sum + r.dataSize, 0)
                }
            };
            await axios_1.default.post(callbackUrl, payload, {
                headers: { 'Content-Type': 'application/json' },
                timeout: 10000
            });
            this.logger.log(`✅ 回调通知发送成功: ${callbackUrl}`);
        }
        catch (error) {
            this.logger.error(`❌ 回调通知发送失败: ${callbackUrl}`, error.message);
        }
    }
    async cleanupTempFiles(filePaths) {
        for (const filePath of filePaths) {
            try {
                if (fs.existsSync(filePath) && (filePath.endsWith('.gz') || filePath.includes('_chunk_'))) {
                    fs.unlinkSync(filePath);
                    this.logger.log(`🗑️  清理临时文件: ${path.basename(filePath)}`);
                }
            }
            catch (error) {
                this.logger.warn(`⚠️  清理临时文件失败: ${filePath}`, error.message);
            }
        }
    }
    getFileSizes(filePaths) {
        const sizes = {};
        for (const filePath of filePaths) {
            if (fs.existsSync(filePath)) {
                const fileName = path.basename(filePath);
                sizes[fileName] = fs.statSync(filePath).size;
            }
        }
        return sizes;
    }
};
FilePushService = FilePushService_1 = __decorate([
    (0, common_1.Injectable)()
], FilePushService);
exports.FilePushService = FilePushService;
//# sourceMappingURL=file-push.service.js.map