import { ElasticsearchOrbitService } from '../../elasticsearch/services/elasticsearch.orbit.service';
import { OrbitCalculator } from '../orbit-calculator/OrbitCalculator';
import { FilePushService } from './file-push.service';
import { ISatellitePointCloud, IPointCloudMetadata, IPointCloudGenerationConfig, IPointCloudGenerationTask } from './types';
export declare class SatelliteTilesService {
    private readonly orbitService;
    private readonly orbitCalculator;
    private readonly filePushService;
    private readonly logger;
    private readonly outputDir;
    private readonly satellitesFilePath;
    private readonly metadataFilePath;
    private currentTask;
    private readonly orbitClassColors;
    constructor(orbitService: ElasticsearchOrbitService, orbitCalculator: OrbitCalculator, filePushService: FilePushService);
    scheduledGeneratePointCloud(): Promise<void>;
    generateSatellitePointCloudWithPush(dto: any): Promise<{
        success: boolean;
        taskId: string;
        totalSatellites: number;
        generationDuration: number;
        outputPath: string;
        message: string;
        pushResults?: any[];
        generatedFiles?: string[];
        fileSizes?: Record<string, number>;
    }>;
    generateSatellitePointCloud(config: IPointCloudGenerationConfig): Promise<{
        success: boolean;
        taskId: string;
        totalSatellites: number;
        generationDuration: number;
        outputPath: string;
        message: string;
    }>;
    getCurrentTaskStatus(): IPointCloudGenerationTask | null;
    getMetadata(): Promise<IPointCloudMetadata | null>;
    getSatellitePointCloudData(): Promise<ISatellitePointCloud | null>;
    private ensureOutputDirectory;
    private convertTLEDataToSatellites;
    private generatePointCloudItems;
    private extractOrbitClass;
    private generateStatistics;
    private savePointCloudData;
    private updateMetadata;
}
