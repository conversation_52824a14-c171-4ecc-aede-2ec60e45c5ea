"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TilesStatusResponseDto = exports.GenerateTilesResponseDto = exports.FilePushResultDto = exports.GenerateSatelliteTilesDto = exports.FileChunkingDto = exports.FilePushTargetDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class FilePushTargetDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '推送类型',
        enum: ['local', 'ftp', 'sftp', 'http', 's3', 'oss'],
        example: 'http'
    }),
    (0, class_validator_1.IsEnum)(['local', 'ftp', 'sftp', 'http', 's3', 'oss']),
    __metadata("design:type", String)
], FilePushTargetDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '目标地址或路径',
        example: 'https://frontend.example.com/api/tiles/upload'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FilePushTargetDto.prototype, "target", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '认证信息 (可选)',
        example: { token: 'bearer_token', key: 'access_key' },
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], FilePushTargetDto.prototype, "credentials", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '推送选项 (可选)',
        example: { timeout: 30000, retries: 3 },
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], FilePushTargetDto.prototype, "options", void 0);
exports.FilePushTargetDto = FilePushTargetDto;
class FileChunkingDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否启用分片',
        example: true,
        default: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], FileChunkingDto.prototype, "enabled", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '分片大小 (MB)',
        example: 10,
        minimum: 1,
        maximum: 100,
        default: 10
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], FileChunkingDto.prototype, "chunkSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '分片文件命名模式',
        example: 'satellites_chunk_{index}.json',
        default: 'satellites_chunk_{index}.json'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FileChunkingDto.prototype, "namePattern", void 0);
exports.FileChunkingDto = FileChunkingDto;
class GenerateSatelliteTilesDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否强制重新生成（忽略缓存）',
        example: false,
        required: false,
        default: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], GenerateSatelliteTilesDto.prototype, "forceRegenerate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否按轨道类型着色',
        example: true,
        required: false,
        default: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], GenerateSatelliteTilesDto.prototype, "colorByOrbitClass", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否包含统计信息',
        example: true,
        required: false,
        default: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], GenerateSatelliteTilesDto.prototype, "includeStatistics", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '坐标系统',
        example: 'cartesian',
        enum: ['cartesian', 'geographic'],
        required: false,
        default: 'cartesian'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['cartesian', 'geographic']),
    __metadata("design:type", String)
], GenerateSatelliteTilesDto.prototype, "coordinateSystem", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '压缩级别 (0-9)',
        example: 6,
        minimum: 0,
        maximum: 9,
        required: false,
        default: 6
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(9),
    __metadata("design:type", Number)
], GenerateSatelliteTilesDto.prototype, "compressionLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '文件推送目标列表',
        type: [FilePushTargetDto],
        required: false,
        example: [{
                type: 'http',
                target: 'https://frontend.example.com/api/tiles/upload',
                credentials: { token: 'bearer_token' },
                options: { timeout: 30000 }
            }]
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => FilePushTargetDto),
    __metadata("design:type", Array)
], GenerateSatelliteTilesDto.prototype, "pushTargets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '文件分片配置',
        type: FileChunkingDto,
        required: false,
        example: {
            enabled: true,
            chunkSize: 10,
            namePattern: 'satellites_chunk_{index}.json'
        }
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FileChunkingDto),
    __metadata("design:type", FileChunkingDto)
], GenerateSatelliteTilesDto.prototype, "chunking", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否启用gzip压缩',
        example: true,
        required: false,
        default: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], GenerateSatelliteTilesDto.prototype, "enableGzip", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否启用增量更新',
        example: false,
        required: false,
        default: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], GenerateSatelliteTilesDto.prototype, "enableIncremental", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '生成完成后的回调URL',
        example: 'https://frontend.example.com/api/tiles/callback',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], GenerateSatelliteTilesDto.prototype, "callbackUrl", void 0);
exports.GenerateSatelliteTilesDto = GenerateSatelliteTilesDto;
class FilePushResultDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '推送目标',
        example: 'https://frontend.example.com/api/tiles/upload'
    }),
    __metadata("design:type", String)
], FilePushResultDto.prototype, "target", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '推送是否成功',
        example: true
    }),
    __metadata("design:type", Boolean)
], FilePushResultDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '推送的文件列表',
        example: ['satellites.json', 'metadata.json']
    }),
    __metadata("design:type", Array)
], FilePushResultDto.prototype, "files", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '推送耗时 (毫秒)',
        example: 5000
    }),
    __metadata("design:type", Number)
], FilePushResultDto.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '错误信息 (如果失败)',
        example: null,
        required: false
    }),
    __metadata("design:type", String)
], FilePushResultDto.prototype, "error", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '推送的数据大小 (字节)',
        example: 1048576
    }),
    __metadata("design:type", Number)
], FilePushResultDto.prototype, "dataSize", void 0);
exports.FilePushResultDto = FilePushResultDto;
class GenerateTilesResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '生成是否成功',
        example: true
    }),
    __metadata("design:type", Boolean)
], GenerateTilesResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '响应消息',
        example: '成功生成 5000 颗卫星的点云数据'
    }),
    __metadata("design:type", String)
], GenerateTilesResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '任务ID',
        example: 'task_2025-01-06T14_00_00_000Z'
    }),
    __metadata("design:type", String)
], GenerateTilesResponseDto.prototype, "taskId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星总数',
        example: 5000
    }),
    __metadata("design:type", Number)
], GenerateTilesResponseDto.prototype, "totalSatellites", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '生成耗时 (毫秒)',
        example: 15000
    }),
    __metadata("design:type", Number)
], GenerateTilesResponseDto.prototype, "generationDuration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '输出路径',
        example: '/tiles/satellites.json'
    }),
    __metadata("design:type", String)
], GenerateTilesResponseDto.prototype, "outputPath", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '文件推送结果列表',
        type: [FilePushResultDto],
        required: false
    }),
    __metadata("design:type", Array)
], GenerateTilesResponseDto.prototype, "pushResults", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '生成的文件列表',
        example: ['satellites.json', 'metadata.json'],
        required: false
    }),
    __metadata("design:type", Array)
], GenerateTilesResponseDto.prototype, "generatedFiles", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '文件大小信息 (字节)',
        example: { 'satellites.json': 1048576, 'metadata.json': 2048 },
        required: false
    }),
    __metadata("design:type", Object)
], GenerateTilesResponseDto.prototype, "fileSizes", void 0);
exports.GenerateTilesResponseDto = GenerateTilesResponseDto;
class TilesStatusResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '生成状态',
        enum: ['idle', 'generating', 'error'],
        example: 'idle'
    }),
    __metadata("design:type", String)
], TilesStatusResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最后生成时间',
        example: '2025-01-06T14:00:00Z'
    }),
    __metadata("design:type", String)
], TilesStatusResponseDto.prototype, "lastGenerated", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '下次计划生成时间',
        example: '2025-01-06T18:00:00Z'
    }),
    __metadata("design:type", String)
], TilesStatusResponseDto.prototype, "nextScheduled", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星总数',
        example: 5000
    }),
    __metadata("design:type", Number)
], TilesStatusResponseDto.prototype, "totalSatellites", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '生成耗时 (毫秒)',
        example: 15000
    }),
    __metadata("design:type", Number)
], TilesStatusResponseDto.prototype, "generationDuration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '错误信息',
        example: null,
        required: false
    }),
    __metadata("design:type", String)
], TilesStatusResponseDto.prototype, "errorMessage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '数据版本',
        example: '1.0.0'
    }),
    __metadata("design:type", String)
], TilesStatusResponseDto.prototype, "version", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '当前进度 (0-100)',
        example: 100,
        required: false
    }),
    __metadata("design:type", Number)
], TilesStatusResponseDto.prototype, "progress", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最后推送结果',
        type: [FilePushResultDto],
        required: false
    }),
    __metadata("design:type", Array)
], TilesStatusResponseDto.prototype, "lastPushResults", void 0);
exports.TilesStatusResponseDto = TilesStatusResponseDto;
//# sourceMappingURL=generate-tiles.dto.js.map