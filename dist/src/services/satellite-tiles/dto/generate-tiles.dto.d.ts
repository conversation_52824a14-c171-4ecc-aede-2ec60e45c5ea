export declare class FilePushTargetDto {
    type: 'local' | 'ftp' | 'sftp' | 'http' | 's3' | 'oss';
    target: string;
    credentials?: Record<string, string>;
    options?: Record<string, any>;
}
export declare class FileChunkingDto {
    enabled?: boolean;
    chunkSize?: number;
    namePattern?: string;
}
export declare class GenerateSatelliteTilesDto {
    forceRegenerate?: boolean;
    colorByOrbitClass?: boolean;
    includeStatistics?: boolean;
    coordinateSystem?: 'cartesian' | 'geographic';
    compressionLevel?: number;
    pushTargets?: FilePushTargetDto[];
    chunking?: FileChunkingDto;
    enableGzip?: boolean;
    enableIncremental?: boolean;
    callbackUrl?: string;
}
export declare class FilePushResultDto {
    target: string;
    success: boolean;
    files: string[];
    duration: number;
    error?: string;
    dataSize: number;
}
export declare class GenerateTilesResponseDto {
    success: boolean;
    message: string;
    taskId: string;
    totalSatellites: number;
    generationDuration: number;
    outputPath: string;
    pushResults?: FilePushResultDto[];
    generatedFiles?: string[];
    fileSizes?: Record<string, number>;
}
export declare class TilesStatusResponseDto {
    status: 'idle' | 'generating' | 'error';
    lastGenerated: string;
    nextScheduled: string;
    totalSatellites: number;
    generationDuration: number;
    errorMessage?: string;
    version: string;
    progress?: number;
    lastPushResults?: FilePushResultDto[];
}
