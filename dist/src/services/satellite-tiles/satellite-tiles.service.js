"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SatelliteTilesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SatelliteTilesService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const fs = require("fs");
const path = require("path");
const elasticsearch_orbit_service_1 = require("../../elasticsearch/services/elasticsearch.orbit.service");
const OrbitCalculator_1 = require("../orbit-calculator/OrbitCalculator");
const file_push_service_1 = require("./file-push.service");
let SatelliteTilesService = SatelliteTilesService_1 = class SatelliteTilesService {
    constructor(orbitService, orbitCalculator, filePushService) {
        this.orbitService = orbitService;
        this.orbitCalculator = orbitCalculator;
        this.filePushService = filePushService;
        this.logger = new common_1.Logger(SatelliteTilesService_1.name);
        this.outputDir = path.join(process.cwd(), 'public', 'tiles');
        this.satellitesFilePath = path.join(this.outputDir, 'satellites.json');
        this.metadataFilePath = path.join(this.outputDir, 'metadata.json');
        this.currentTask = null;
        this.orbitClassColors = {
            'LEO': { r: 0, g: 255, b: 0 },
            'MEO': { r: 255, g: 165, b: 0 },
            'GEO': { r: 255, g: 0, b: 0 },
            'HEO': { r: 255, g: 0, b: 255 },
            'SSO': { r: 0, g: 255, b: 255 },
            'Polar': { r: 0, g: 0, b: 255 },
            'Elliptical': { r: 255, g: 255, b: 0 },
            'Unknown': { r: 128, g: 128, b: 128 }
        };
        this.ensureOutputDirectory();
    }
    async scheduledGeneratePointCloud() {
        this.logger.log('🕐 定时任务触发：开始生成卫星点云数据');
        try {
            await this.generateSatellitePointCloud({
                outputPath: this.outputDir,
                colorByOrbitClass: true,
                includeStatistics: true,
                coordinateSystem: 'cartesian',
                compressionLevel: 6
            });
            this.logger.log('✅ 定时任务完成：卫星点云数据生成成功');
        }
        catch (error) {
            this.logger.error('❌ 定时任务失败：卫星点云数据生成失败', error.stack);
        }
    }
    async generateSatellitePointCloudWithPush(dto) {
        var _a, _b, _c, _d;
        const config = {
            outputPath: this.outputDir,
            colorByOrbitClass: (_a = dto.colorByOrbitClass) !== null && _a !== void 0 ? _a : true,
            includeStatistics: (_b = dto.includeStatistics) !== null && _b !== void 0 ? _b : true,
            coordinateSystem: (_c = dto.coordinateSystem) !== null && _c !== void 0 ? _c : 'cartesian',
            compressionLevel: (_d = dto.compressionLevel) !== null && _d !== void 0 ? _d : 6
        };
        const baseResult = await this.generateSatellitePointCloud(config);
        const filesToPush = [this.satellitesFilePath, this.metadataFilePath];
        const generatedFiles = filesToPush.map(fp => path.basename(fp));
        const fileSizes = this.filePushService.getFileSizes(filesToPush);
        let pushResults = [];
        if (dto.pushTargets && dto.pushTargets.length > 0) {
            this.logger.log(`📤 开始推送文件到 ${dto.pushTargets.length} 个目标...`);
            try {
                pushResults = await this.filePushService.pushFiles(filesToPush, dto.pushTargets, {
                    enableGzip: dto.enableGzip,
                    chunking: dto.chunking,
                    callbackUrl: dto.callbackUrl
                });
                const tempFiles = pushResults.flatMap(result => result.files.filter((file) => file.endsWith('.gz') || file.includes('_chunk_')).map((file) => path.join(this.outputDir, file)));
                if (tempFiles.length > 0) {
                    await this.filePushService.cleanupTempFiles(tempFiles);
                }
                this.logger.log(`✅ 文件推送完成，成功: ${pushResults.filter(r => r.success).length}/${pushResults.length}`);
            }
            catch (error) {
                this.logger.error('❌ 文件推送过程中发生错误', error.stack);
            }
        }
        return Object.assign(Object.assign({}, baseResult), { pushResults,
            generatedFiles,
            fileSizes });
    }
    async generateSatellitePointCloud(config) {
        const startTime = Date.now();
        const taskId = `task_${new Date().toISOString().replace(/[:.]/g, '_')}`;
        if (this.currentTask && this.currentTask.status === 'running') {
            throw new Error('已有点云生成任务正在运行中，请稍后再试');
        }
        this.currentTask = {
            id: taskId,
            status: 'running',
            startTime: new Date(),
            progress: 0,
            totalSatellites: 0,
            processedSatellites: 0
        };
        try {
            this.logger.log(`🚀 开始生成卫星点云数据，任务ID: ${taskId}`);
            this.logger.log('📡 正在获取所有卫星TLE数据...');
            this.currentTask.progress = 10;
            const tleResponse = await this.orbitService.getAllSatelliteTleData();
            if (!tleResponse.success || !tleResponse.results) {
                throw new Error('获取卫星TLE数据失败');
            }
            const tleData = tleResponse.results;
            this.currentTask.totalSatellites = tleData.length;
            this.logger.log(`📊 获取到 ${tleData.length} 颗卫星的TLE数据`);
            this.logger.log('🔄 正在转换TLE数据格式...');
            this.currentTask.progress = 20;
            const satellites = this.convertTLEDataToSatellites(tleData);
            this.logger.log('🧮 正在计算卫星位置...');
            this.currentTask.progress = 30;
            const calculationTime = new Date();
            const positionResults = await this.orbitCalculator.calculatePositions(satellites, calculationTime, {
                continueOnError: true,
                batchSize: 100
            });
            this.logger.log(`✅ 成功计算 ${positionResults.positions.length} 颗卫星位置`);
            if (positionResults.errors.length > 0) {
                this.logger.warn(`⚠️  ${positionResults.errors.length} 颗卫星计算失败`);
            }
            this.logger.log('🎨 正在生成点云数据...');
            this.currentTask.progress = 60;
            const pointCloudItems = this.generatePointCloudItems(positionResults.positions, tleData, config);
            this.logger.log('📈 正在生成统计信息...');
            this.currentTask.progress = 80;
            const statistics = this.generateStatistics(pointCloudItems);
            const pointCloudData = {
                generatedAt: new Date().toISOString(),
                totalSatellites: pointCloudItems.length,
                calculationTime: calculationTime.toISOString(),
                satellites: pointCloudItems,
                statistics
            };
            this.logger.log('💾 正在保存点云文件...');
            this.currentTask.progress = 90;
            await this.savePointCloudData(pointCloudData);
            await this.updateMetadata(pointCloudData, startTime);
            const generationDuration = Date.now() - startTime;
            this.currentTask.status = 'completed';
            this.currentTask.endTime = new Date();
            this.currentTask.progress = 100;
            this.currentTask.processedSatellites = pointCloudItems.length;
            this.logger.log(`🎉 卫星点云生成完成！耗时: ${generationDuration}ms, 卫星数量: ${pointCloudItems.length}`);
            return {
                success: true,
                taskId,
                totalSatellites: pointCloudItems.length,
                generationDuration,
                outputPath: '/tiles/satellites.json',
                message: `成功生成 ${pointCloudItems.length} 颗卫星的点云数据`
            };
        }
        catch (error) {
            if (this.currentTask) {
                this.currentTask.status = 'failed';
                this.currentTask.endTime = new Date();
                this.currentTask.errorMessage = error.message;
            }
            this.logger.error(`❌ 卫星点云生成失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    getCurrentTaskStatus() {
        return this.currentTask;
    }
    async getMetadata() {
        try {
            if (!fs.existsSync(this.metadataFilePath)) {
                return null;
            }
            const metadataContent = await fs.promises.readFile(this.metadataFilePath, 'utf-8');
            return JSON.parse(metadataContent);
        }
        catch (error) {
            this.logger.error('读取元数据文件失败', error);
            return null;
        }
    }
    async getSatellitePointCloudData() {
        try {
            if (!fs.existsSync(this.satellitesFilePath)) {
                return null;
            }
            const satelliteContent = await fs.promises.readFile(this.satellitesFilePath, 'utf-8');
            return JSON.parse(satelliteContent);
        }
        catch (error) {
            this.logger.error('读取卫星点云文件失败', error);
            return null;
        }
    }
    ensureOutputDirectory() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
            this.logger.log(`📁 创建输出目录: ${this.outputDir}`);
        }
    }
    convertTLEDataToSatellites(tleData) {
        return tleData.map(tle => {
            const tleLines = tle.tle_raw.split('\n');
            return {
                satId: tle.norad_id.toString(),
                name: tle.satellite_name,
                line1: tleLines[1] || '',
                line2: tleLines[2] || ''
            };
        }).filter(sat => sat.line1 && sat.line2);
    }
    generatePointCloudItems(positions, tleData, config) {
        const tleMap = new Map();
        tleData.forEach(tle => {
            tleMap.set(tle.norad_id.toString(), tle);
        });
        return positions.map(pos => {
            var _a, _b, _c, _d, _e;
            const tle = tleMap.get(pos.satId);
            if (!tle)
                return null;
            const orbitClass = this.extractOrbitClass(pos);
            const properties = {
                noradId: parseInt(pos.satId),
                cosparId: tle.cospar_id,
                orbitClass,
                inclination: (_a = pos.orbital) === null || _a === void 0 ? void 0 : _a.inclination,
                period: (_b = pos.orbital) === null || _b === void 0 ? void 0 : _b.period,
                apogee: (_c = pos.orbital) === null || _c === void 0 ? void 0 : _c.apogee,
                perigee: (_d = pos.orbital) === null || _d === void 0 ? void 0 : _d.perigee,
                eccentricity: (_e = pos.orbital) === null || _e === void 0 ? void 0 : _e.eccentricity
            };
            const color = config.colorByOrbitClass
                ? this.orbitClassColors[orbitClass] || this.orbitClassColors['Unknown']
                : { r: 255, g: 255, b: 255 };
            const cartesianPos = {
                x: pos.position.x,
                y: pos.position.y,
                z: pos.position.z
            };
            const geographicPos = {
                longitude: pos.longitude,
                latitude: pos.latitude,
                altitude: pos.altitude
            };
            return {
                id: pos.satId,
                name: tle.satellite_name,
                position: cartesianPos,
                geographic: geographicPos,
                properties,
                color
            };
        }).filter(item => item !== null);
    }
    extractOrbitClass(position) {
        const altitude = position.altitude || 0;
        if (altitude < 2000) {
            return 'LEO';
        }
        else if (altitude < 35786) {
            return 'MEO';
        }
        else if (altitude >= 35786 && altitude <= 35790) {
            return 'GEO';
        }
        else {
            return 'HEO';
        }
    }
    generateStatistics(items) {
        const orbitClassDistribution = {};
        const altitudes = [];
        items.forEach(item => {
            const orbitClass = item.properties.orbitClass;
            orbitClassDistribution[orbitClass] = (orbitClassDistribution[orbitClass] || 0) + 1;
            altitudes.push(item.geographic.altitude * 1000);
        });
        const altitudeRange = {
            min: Math.min(...altitudes),
            max: Math.max(...altitudes),
            average: altitudes.reduce((sum, alt) => sum + alt, 0) / altitudes.length
        };
        return {
            orbitClassDistribution,
            altitudeRange
        };
    }
    async savePointCloudData(data) {
        const jsonContent = JSON.stringify(data, null, 2);
        await fs.promises.writeFile(this.satellitesFilePath, jsonContent, 'utf-8');
        this.logger.log(`💾 点云数据已保存到: ${this.satellitesFilePath}`);
    }
    async updateMetadata(data, startTime) {
        const now = new Date();
        const nextScheduled = new Date(now.getTime() + 4 * 60 * 60 * 1000);
        const metadata = {
            lastGenerated: data.generatedAt,
            nextScheduled: nextScheduled.toISOString(),
            totalSatellites: data.totalSatellites,
            generationDuration: Date.now() - startTime,
            status: 'idle',
            version: '1.0.0'
        };
        const metadataContent = JSON.stringify(metadata, null, 2);
        await fs.promises.writeFile(this.metadataFilePath, metadataContent, 'utf-8');
        this.logger.log(`📋 元数据已更新: ${this.metadataFilePath}`);
    }
};
__decorate([
    (0, schedule_1.Cron)('0 */4 * * *', {
        name: 'generateSatellitePointCloud',
        timeZone: 'UTC'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SatelliteTilesService.prototype, "scheduledGeneratePointCloud", null);
SatelliteTilesService = SatelliteTilesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [elasticsearch_orbit_service_1.ElasticsearchOrbitService,
        OrbitCalculator_1.OrbitCalculator,
        file_push_service_1.FilePushService])
], SatelliteTilesService);
exports.SatelliteTilesService = SatelliteTilesService;
//# sourceMappingURL=satellite-tiles.service.js.map