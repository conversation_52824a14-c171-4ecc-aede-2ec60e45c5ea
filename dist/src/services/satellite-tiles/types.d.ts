export interface ICartesianPosition {
    x: number;
    y: number;
    z: number;
}
export interface IGeographicPosition {
    longitude: number;
    latitude: number;
    altitude: number;
}
export interface IRGBColor {
    r: number;
    g: number;
    b: number;
}
export interface ISatelliteProperties {
    noradId: number;
    cosparId?: string;
    orbitClass: string;
    inclination?: number;
    period?: number;
    apogee?: number;
    perigee?: number;
    eccentricity?: number;
}
export interface ISatellitePointCloudItem {
    id: string;
    name: string;
    position: ICartesianPosition;
    geographic: IGeographicPosition;
    properties: ISatelliteProperties;
    color: IRGBColor;
}
export interface ISatellitePointCloud {
    generatedAt: string;
    totalSatellites: number;
    calculationTime: string;
    satellites: ISatellitePointCloudItem[];
    statistics: {
        orbitClassDistribution: Record<string, number>;
        altitudeRange: {
            min: number;
            max: number;
            average: number;
        };
    };
}
export interface IPointCloudMetadata {
    lastGenerated: string;
    nextScheduled: string;
    totalSatellites: number;
    generationDuration: number;
    status: 'idle' | 'generating' | 'error';
    errorMessage?: string;
    version: string;
}
export interface IOrbitClassColorMap {
    [orbitClass: string]: IRGBColor;
}
export interface IPointCloudGenerationConfig {
    outputPath: string;
    colorByOrbitClass: boolean;
    includeStatistics: boolean;
    coordinateSystem: 'cartesian' | 'geographic';
    compressionLevel: number;
}
export interface ISatelliteTLEData {
    norad_id: number;
    satellite_name: string;
    cospar_id?: string;
    tle_raw: string;
    epoch: string;
    time: string;
}
export interface IPointCloudGenerationTask {
    id: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    startTime: Date;
    endTime?: Date;
    progress: number;
    totalSatellites: number;
    processedSatellites: number;
    errorMessage?: string;
}
