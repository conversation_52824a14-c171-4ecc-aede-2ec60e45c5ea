export declare class SatelliteBasicInfoDto {
    satId: string;
    name: string;
    line1: string;
    line2: string;
}
export declare class ConjunctionAnalysisRequestDto {
    backgroundSatellites: SatelliteBasicInfoDto[];
    targetSatellites: SatelliteBasicInfoDto[];
    startTime: string;
    endTime: string;
    threshold: number;
    timeStep?: number;
    batchSize?: number;
}
export declare class RTNVectorDto {
    r: number;
    t: number;
    n: number;
}
export declare class ClosestApproachDto {
    time: Date;
    distance: number;
    rtn: RTNVectorDto;
}
export declare class SatelliteInfoDto {
    satId: string;
    name: string;
}
export declare class ConjunctionEventDto {
    conjunctionId: string;
    satellite1: SatelliteInfoDto;
    satellite2: SatelliteInfoDto;
    threshold: number;
    startTime: Date;
    endTime: Date;
    duration: number;
    closestApproach: ClosestApproachDto;
}
export declare class ConjunctionAnalysisResponseDto {
    conjunctions: ConjunctionEventDto[];
}
