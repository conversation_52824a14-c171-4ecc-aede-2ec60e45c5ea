"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConjunctionAnalysisResponseDto = exports.ConjunctionEventDto = exports.SatelliteInfoDto = exports.ClosestApproachDto = exports.RTNVectorDto = exports.ConjunctionAnalysisRequestDto = exports.SatelliteBasicInfoDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
class SatelliteBasicInfoDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星编号',
        example: '25544',
        required: true
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SatelliteBasicInfoDto.prototype, "satId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星名称',
        example: 'ISS (ZARYA)',
        required: true
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SatelliteBasicInfoDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'TLE数据第一行',
        example: '1 25544U 98067A   24054.88088078  .00014558  00000+0  26729-3 0  9990',
        required: true
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SatelliteBasicInfoDto.prototype, "line1", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'TLE数据第二行',
        example: '2 25544  51.6416 150.2506 0004789  89.9089  32.8809 15.49512781434577',
        required: true
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SatelliteBasicInfoDto.prototype, "line2", void 0);
exports.SatelliteBasicInfoDto = SatelliteBasicInfoDto;
class ConjunctionAnalysisRequestDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '背景目标卫星集合',
        type: [SatelliteBasicInfoDto],
        required: true,
        example: [
            {
                satId: '48274',
                name: 'STARLINK-3432',
                line1: '1 48274U 21041AF  24054.86885282  .00002571  00000+0  16843-3 0  9994',
                line2: '2 48274  53.0559  30.0121 0001038  89.7222 270.3898 15.06396635146374'
            },
            {
                satId: '48275',
                name: 'STARLINK-3433',
                line1: '1 48275U 21041AG  24054.85716922  .00002438  00000+0  16016-3 0  9990',
                line2: '2 48275  53.0558  30.0167 0001067  91.5344 268.5778 15.06396475146373'
            }
        ]
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => SatelliteBasicInfoDto),
    __metadata("design:type", Array)
], ConjunctionAnalysisRequestDto.prototype, "backgroundSatellites", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '关注目标卫星集合',
        type: [SatelliteBasicInfoDto],
        required: true,
        example: [
            {
                satId: '48276',
                name: 'STARLINK-3434',
                line1: '1 48276U 21041AH  24054.85132335  .00002443  00000+0  16052-3 0  9991',
                line2: '2 48276  53.0559  30.0189 0001073  91.8765 268.2357 15.06396512146372'
            },
            {
                satId: '48277',
                name: 'STARLINK-3435',
                line1: '1 48277U 21041AJ  24054.84547754  .00002453  00000+0  16115-3 0  9996',
                line2: '2 48277  53.0558  30.0198 0001078  92.1234 267.9888 15.06396498146371'
            }
        ]
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => SatelliteBasicInfoDto),
    __metadata("design:type", Array)
], ConjunctionAnalysisRequestDto.prototype, "targetSatellites", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '开始时间(UTC)',
        example: '2024-02-23T20:00:00Z',
        required: true
    }),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], ConjunctionAnalysisRequestDto.prototype, "startTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '结束时间(UTC)',
        example: '2024-02-24T04:00:00Z',
        required: true
    }),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], ConjunctionAnalysisRequestDto.prototype, "endTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '距离门限(千米)',
        example: 100,
        required: true,
        minimum: 0
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], ConjunctionAnalysisRequestDto.prototype, "threshold", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '时间步长(秒)',
        example: 30,
        required: false,
        minimum: 1,
        maximum: 3600,
        default: 60
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(3600),
    __metadata("design:type", Number)
], ConjunctionAnalysisRequestDto.prototype, "timeStep", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '每批处理的卫星数量',
        example: 100,
        required: false,
        minimum: 1,
        maximum: 1000,
        default: 100
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(1000),
    __metadata("design:type", Number)
], ConjunctionAnalysisRequestDto.prototype, "batchSize", void 0);
exports.ConjunctionAnalysisRequestDto = ConjunctionAnalysisRequestDto;
class RTNVectorDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '径向分量(度)',
        example: 45.67
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RTNVectorDto.prototype, "r", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '切向分量(度)',
        example: -23.45
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RTNVectorDto.prototype, "t", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '法向分量(度)',
        example: 12.34
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RTNVectorDto.prototype, "n", void 0);
exports.RTNVectorDto = RTNVectorDto;
class ClosestApproachDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最近距离时间(UTC)',
        example: '2024-02-24T02:30:00Z'
    }),
    __metadata("design:type", Date)
], ClosestApproachDto.prototype, "time", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最近距离(千米)',
        example: 85.6
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ClosestApproachDto.prototype, "distance", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'RTN方向',
        type: RTNVectorDto
    }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => RTNVectorDto),
    __metadata("design:type", RTNVectorDto)
], ClosestApproachDto.prototype, "rtn", void 0);
exports.ClosestApproachDto = ClosestApproachDto;
class SatelliteInfoDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星编号',
        example: '25544'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SatelliteInfoDto.prototype, "satId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星名称',
        example: 'ISS (ZARYA)'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SatelliteInfoDto.prototype, "name", void 0);
exports.SatelliteInfoDto = SatelliteInfoDto;
class ConjunctionEventDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '交汇编号',
        example: '20240223001',
        required: true
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ConjunctionEventDto.prototype, "conjunctionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星1信息',
        type: SatelliteInfoDto
    }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => SatelliteInfoDto),
    __metadata("design:type", SatelliteInfoDto)
], ConjunctionEventDto.prototype, "satellite1", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '卫星2信息',
        type: SatelliteInfoDto
    }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => SatelliteInfoDto),
    __metadata("design:type", SatelliteInfoDto)
], ConjunctionEventDto.prototype, "satellite2", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '交汇门限(千米)',
        example: 100
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ConjunctionEventDto.prototype, "threshold", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '开始时间(UTC)',
        example: '2024-02-24T02:30:00Z'
    }),
    __metadata("design:type", Date)
], ConjunctionEventDto.prototype, "startTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '结束时间(UTC)',
        example: '2024-02-24T02:40:00Z'
    }),
    __metadata("design:type", Date)
], ConjunctionEventDto.prototype, "endTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '交汇时长(秒)',
        example: 600
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ConjunctionEventDto.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '最近距离信息',
        type: ClosestApproachDto
    }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => ClosestApproachDto),
    __metadata("design:type", ClosestApproachDto)
], ConjunctionEventDto.prototype, "closestApproach", void 0);
exports.ConjunctionEventDto = ConjunctionEventDto;
class ConjunctionAnalysisResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '交汇事件列表',
        type: [ConjunctionEventDto],
        example: [
            {
                conjunctionId: '20240223001',
                satellite1: {
                    satId: '48274',
                    name: 'STARLINK-3432'
                },
                satellite2: {
                    satId: '48276',
                    name: 'STARLINK-3434'
                },
                threshold: 100,
                startTime: '2024-02-23T21:15:00Z',
                endTime: '2024-02-23T21:18:30Z',
                duration: 210,
                closestApproach: {
                    time: '2024-02-23T21:16:45Z',
                    distance: 82.3,
                    rtn: {
                        r: 35.67,
                        t: -28.45,
                        n: 15.34
                    }
                }
            },
            {
                conjunctionId: '20240223002',
                satellite1: {
                    satId: '48275',
                    name: 'STARLINK-3433'
                },
                satellite2: {
                    satId: '48277',
                    name: 'STARLINK-3435'
                },
                threshold: 100,
                startTime: '2024-02-23T22:45:00Z',
                endTime: '2024-02-23T22:48:00Z',
                duration: 180,
                closestApproach: {
                    time: '2024-02-23T22:46:30Z',
                    distance: 88.9,
                    rtn: {
                        r: -42.56,
                        t: 32.18,
                        n: 18.73
                    }
                }
            }
        ]
    }),
    __metadata("design:type", Array)
], ConjunctionAnalysisResponseDto.prototype, "conjunctions", void 0);
exports.ConjunctionAnalysisResponseDto = ConjunctionAnalysisResponseDto;
//# sourceMappingURL=conjunction-analysis.dto.js.map