export interface ISatelliteBasicInfo {
    satId: string;
    name: string;
    line1: string;
    line2: string;
}
export interface IRTNVector {
    r: number;
    t: number;
    n: number;
}
export interface IConjunctionEvent {
    conjunctionId: string;
    satellite1: {
        satId: string;
        name: string;
    };
    satellite2: {
        satId: string;
        name: string;
    };
    threshold: number;
    startTime: Date;
    endTime: Date;
    duration: number;
    closestApproach: {
        time: Date;
        distance: number;
        rtn: IRTNVector;
    };
}
export interface IConjunctionAnalysisParams {
    backgroundSatellites: ISatelliteBasicInfo[];
    targetSatellites: ISatelliteBasicInfo[];
    startTime: Date;
    endTime: Date;
    threshold: number;
    timeStep?: number;
    batchSize?: number;
}
