{"version": 3, "file": "conjunction-analysis.service.js", "sourceRoot": "", "sources": ["../../../../src/services/conjunction-analysis/conjunction-analysis.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,yEAAsE;AAK/D,IAAM,0BAA0B,GAAhC,MAAM,0BAA0B;IAIrC,YAA6B,eAAgC;QAAhC,oBAAe,GAAf,eAAe,CAAiB;QAH5C,oBAAe,GAAG,EAAE,CAAC;QACrB,qBAAgB,GAAG,GAAG,CAAC;IAEwB,CAAC;IAKzD,YAAY,CAClB,EAAuC,EACvC,EAAuC;QAEvC,OAAO;YACL,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;YAC5B,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;YAC5B,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;SAC7B,CAAC;IACJ,CAAC;IAKO,eAAe,CAAC,CAAsC;QAC5D,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACtD,CAAC;IAKO,UAAU,CAChB,EAAuC,EACvC,EAAuC;QAEvC,OAAO,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACjD,CAAC;IASO,YAAY,CAClB,IAAyC,EACzC,IAAyC,EACzC,IAAyC;QAGzC,MAAM,WAAW,GAAG;YAClB,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;YAClB,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;YAClB,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;SACnB,CAAC;QAGF,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACxC,MAAM,KAAK,GAAG;YACZ,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI;YAChB,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI;YAChB,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI;SACjB,CAAC;QAGF,MAAM,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACxC,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACrC,MAAM,KAAK,GAAG;YACZ,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI;YACb,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI;YACb,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI;SACd,CAAC;QAGF,MAAM,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAG1C,MAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAC/C,MAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QAG9C,OAAO;YACL,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE;YACpE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE;YAC/D,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE;SACrE,CAAC;IACJ,CAAC;IAQO,qBAAqB,CAAC,IAAU,EAAE,KAAa;QACrD,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACpE,OAAO,GAAG,SAAS,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;IAC5D,CAAC;IAKO,KAAK,CAAC,sBAAsB,CAClC,IAAyB,EACzB,IAAyB,EACzB,SAAe,EACf,OAAa,EACb,SAAiB,EACjB,QAAgB,EAChB,KAAa;QAEb,IAAI,eAAe,GAAG,KAAK,CAAC;QAC5B,IAAI,gBAAgB,GAAgB,IAAI,CAAC;QACzC,IAAI,WAAW,GAAG,QAAQ,CAAC;QAC3B,IAAI,eAAe,GAAgB,IAAI,CAAC;QACxC,IAAI,cAAc,GAAsB,IAAI,CAAC;QAC7C,IAAI,eAAe,GAAQ,IAAI,CAAC;QAChC,IAAI,eAAe,GAAQ,IAAI,CAAC;QAGhC,MAAM,YAAY,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC;QACtE,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,CAAC;QAEjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE;YAC/B,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC,CAAC;YAGxE,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACrC,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,IAAI,EAAE,WAAW,CAAC;gBACzD,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,IAAI,EAAE,WAAW,CAAC;aAC1D,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI;gBAAE,SAAS;YAG7B,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC7C,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC7C,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;YAGxD,IAAI,QAAQ,GAAG,WAAW,EAAE;gBAC1B,WAAW,GAAG,QAAQ,CAAC;gBACvB,eAAe,GAAG,WAAW,CAAC;gBAC9B,eAAe,GAAG,IAAI,CAAC;gBACvB,eAAe,GAAG,IAAI,CAAC;gBACvB,cAAc,GAAG,IAAI,CAAC,YAAY,CAChC,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,QAAQ,CACd,CAAC;aACH;YAGD,IAAI,QAAQ,IAAI,SAAS,EAAE;gBACzB,IAAI,CAAC,eAAe,EAAE;oBACpB,eAAe,GAAG,IAAI,CAAC;oBACvB,gBAAgB,GAAG,WAAW,CAAC;iBAChC;aACF;iBAAM,IAAI,eAAe,EAAE;gBAE1B,OAAO;oBACL,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,gBAAiB,EAAE,KAAK,CAAC;oBACnE,UAAU,EAAE;wBACV,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;qBAChB;oBACD,UAAU,EAAE;wBACV,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;qBAChB;oBACD,SAAS;oBACT,SAAS,EAAE,gBAAiB;oBAC5B,OAAO,EAAE,WAAW;oBACpB,QAAQ,EAAE,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,gBAAiB,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI;oBACtE,eAAe,EAAE;wBACf,IAAI,EAAE,eAAgB;wBACtB,QAAQ,EAAE,WAAW;wBACrB,GAAG,EAAE,cAAe;qBACrB;iBACF,CAAC;aACH;SACF;QAGD,IAAI,eAAe,EAAE;YACnB,OAAO;gBACL,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,gBAAiB,EAAE,KAAK,CAAC;gBACnE,UAAU,EAAE;oBACV,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;iBAChB;gBACD,UAAU,EAAE;oBACV,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;iBAChB;gBACD,SAAS;gBACT,SAAS,EAAE,gBAAiB;gBAC5B,OAAO;gBACP,QAAQ,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,gBAAiB,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI;gBAClE,eAAe,EAAE;oBACf,IAAI,EAAE,eAAgB;oBACtB,QAAQ,EAAE,WAAW;oBACrB,GAAG,EAAE,cAAe;iBACrB;aACF,CAAC;SACH;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAKO,KAAK,CAAC,qBAAqB,CACjC,KAAwD,EACxD,SAAe,EACf,OAAa,EACb,SAAiB,EACjB,QAAgB,EAChB,QAA6B;QAE7B,MAAM,YAAY,GAAwB,EAAE,CAAC;QAG7C,MAAM,mBAAmB,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CACrD,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,CACnG,CAAC;QAEF,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QAGvD,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACvB,IAAI,MAAM,EAAE;gBAEV,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,SAAS,EAAE,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;gBACxF,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAC3B;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,YAAY,CAAC;IACtB,CAAC;IAKM,KAAK,CAAC,mBAAmB,CAAC,MAAkC;QACjE,MAAM,EACJ,oBAAoB,EACpB,gBAAgB,EAChB,SAAS,EACT,OAAO,EACP,SAAS,EACT,QAAQ,GAAG,IAAI,CAAC,eAAe,EAC/B,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAClC,GAAG,MAAM,CAAC;QAEX,MAAM,eAAe,GAAwB,EAAE,CAAC;QAChD,MAAM,QAAQ,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;QAGhC,MAAM,cAAc,GAAsD,EAAE,CAAC;QAC7E,KAAK,MAAM,KAAK,IAAI,oBAAoB,EAAE;YACxC,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE;gBACrC,cAAc,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;aACtC;SACF;QAGD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE;YACzD,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;YACrD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,qBAAqB,CACnD,KAAK,EACL,SAAS,EACT,OAAO,EACP,SAAS,EACT,QAAQ,EACR,QAAQ,CACT,CAAC;YAEF,eAAe,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;SACvC;QAGD,OAAO,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;IACvF,CAAC;CACF,CAAA;AA/RY,0BAA0B;IADtC,IAAA,mBAAU,GAAE;qCAKmC,iCAAe;GAJlD,0BAA0B,CA+RtC;AA/RY,gEAA0B"}