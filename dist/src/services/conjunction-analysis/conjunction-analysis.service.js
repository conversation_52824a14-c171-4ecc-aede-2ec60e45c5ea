"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConjunctionAnalysisService = void 0;
const common_1 = require("@nestjs/common");
const OrbitCalculator_1 = require("../orbit-calculator/OrbitCalculator");
let ConjunctionAnalysisService = class ConjunctionAnalysisService {
    constructor(orbitCalculator) {
        this.orbitCalculator = orbitCalculator;
        this.defaultTimeStep = 60;
        this.defaultBatchSize = 100;
    }
    crossProduct(v1, v2) {
        return {
            x: v1.y * v2.z - v1.z * v2.y,
            y: v1.z * v2.x - v1.x * v2.z,
            z: v1.x * v2.y - v1.y * v2.x
        };
    }
    vectorMagnitude(v) {
        return Math.sqrt(v.x * v.x + v.y * v.y + v.z * v.z);
    }
    dotProduct(v1, v2) {
        return v1.x * v2.x + v1.y * v2.y + v1.z * v2.z;
    }
    calculateRTN(pos1, vel1, pos2) {
        const relativePos = {
            x: pos2.x - pos1.x,
            y: pos2.y - pos1.y,
            z: pos2.z - pos1.z
        };
        const rMag = this.vectorMagnitude(pos1);
        const rUnit = {
            x: pos1.x / rMag,
            y: pos1.y / rMag,
            z: pos1.z / rMag
        };
        const h = this.crossProduct(pos1, vel1);
        const hMag = this.vectorMagnitude(h);
        const nUnit = {
            x: h.x / hMag,
            y: h.y / hMag,
            z: h.z / hMag
        };
        const t = this.crossProduct(nUnit, rUnit);
        const r = this.dotProduct(relativePos, rUnit);
        const t_proj = this.dotProduct(relativePos, t);
        const n = this.dotProduct(relativePos, nUnit);
        return {
            r: Math.atan2(r, Math.sqrt(t_proj * t_proj + n * n)) * 180 / Math.PI,
            t: Math.atan2(t_proj, Math.sqrt(r * r + n * n)) * 180 / Math.PI,
            n: Math.atan2(n, Math.sqrt(r * r + t_proj * t_proj)) * 180 / Math.PI
        };
    }
    generateConjunctionId(time, index) {
        const timestamp = time.toISOString().slice(0, 10).replace(/-/g, '');
        return `${timestamp}${index.toString().padStart(3, '0')}`;
    }
    async analyzePairConjunction(sat1, sat2, startTime, endTime, threshold, timeStep, index) {
        let isInConjunction = false;
        let conjunctionStart = null;
        let minDistance = Infinity;
        let minDistanceTime = null;
        let minDistanceRTN = null;
        let minDistancePos1 = null;
        let minDistancePos2 = null;
        const totalSeconds = (endTime.getTime() - startTime.getTime()) / 1000;
        const steps = Math.ceil(totalSeconds / timeStep);
        for (let i = 0; i <= steps; i++) {
            const currentTime = new Date(startTime.getTime() + i * timeStep * 1000);
            const [pos1, pos2] = await Promise.all([
                this.orbitCalculator.calculatePosition(sat1, currentTime),
                this.orbitCalculator.calculatePosition(sat2, currentTime)
            ]);
            if (!pos1 || !pos2)
                continue;
            const dx = pos2.position.x - pos1.position.x;
            const dy = pos2.position.y - pos1.position.y;
            const dz = pos2.position.z - pos1.position.z;
            const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
            if (distance < minDistance) {
                minDistance = distance;
                minDistanceTime = currentTime;
                minDistancePos1 = pos1;
                minDistancePos2 = pos2;
                minDistanceRTN = this.calculateRTN(pos1.position, pos1.velocity, pos2.position);
            }
            if (distance <= threshold) {
                if (!isInConjunction) {
                    isInConjunction = true;
                    conjunctionStart = currentTime;
                }
            }
            else if (isInConjunction) {
                return {
                    conjunctionId: this.generateConjunctionId(conjunctionStart, index),
                    satellite1: {
                        satId: sat1.satId,
                        name: sat1.name
                    },
                    satellite2: {
                        satId: sat2.satId,
                        name: sat2.name
                    },
                    threshold,
                    startTime: conjunctionStart,
                    endTime: currentTime,
                    duration: (currentTime.getTime() - conjunctionStart.getTime()) / 1000,
                    closestApproach: {
                        time: minDistanceTime,
                        distance: minDistance,
                        rtn: minDistanceRTN
                    }
                };
            }
        }
        if (isInConjunction) {
            return {
                conjunctionId: this.generateConjunctionId(conjunctionStart, index),
                satellite1: {
                    satId: sat1.satId,
                    name: sat1.name
                },
                satellite2: {
                    satId: sat2.satId,
                    name: sat2.name
                },
                threshold,
                startTime: conjunctionStart,
                endTime,
                duration: (endTime.getTime() - conjunctionStart.getTime()) / 1000,
                closestApproach: {
                    time: minDistanceTime,
                    distance: minDistance,
                    rtn: minDistanceRTN
                }
            };
        }
        return null;
    }
    async analyzeSatelliteBatch(pairs, startTime, endTime, threshold, timeStep, indexRef) {
        const conjunctions = [];
        const conjunctionPromises = pairs.map(([sat1, sat2]) => this.analyzePairConjunction(sat1, sat2, startTime, endTime, threshold, timeStep, indexRef.current));
        const results = await Promise.all(conjunctionPromises);
        results.forEach(result => {
            if (result) {
                result.conjunctionId = this.generateConjunctionId(result.startTime, indexRef.current++);
                conjunctions.push(result);
            }
        });
        return conjunctions;
    }
    async analyzeConjunctions(params) {
        const { backgroundSatellites, targetSatellites, startTime, endTime, threshold, timeStep = this.defaultTimeStep, batchSize = this.defaultBatchSize } = params;
        const allConjunctions = [];
        const indexRef = { current: 1 };
        const satellitePairs = [];
        for (const bgSat of backgroundSatellites) {
            for (const tgtSat of targetSatellites) {
                satellitePairs.push([bgSat, tgtSat]);
            }
        }
        for (let i = 0; i < satellitePairs.length; i += batchSize) {
            const batch = satellitePairs.slice(i, i + batchSize);
            const batchResults = await this.analyzeSatelliteBatch(batch, startTime, endTime, threshold, timeStep, indexRef);
            allConjunctions.push(...batchResults);
        }
        return allConjunctions.sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
    }
};
ConjunctionAnalysisService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [OrbitCalculator_1.OrbitCalculator])
], ConjunctionAnalysisService);
exports.ConjunctionAnalysisService = ConjunctionAnalysisService;
//# sourceMappingURL=conjunction-analysis.service.js.map