import { OrbitCalculator } from '../orbit-calculator/OrbitCalculator';
import { IConjunctionEvent, IConjunctionAnalysisParams } from './types';
export declare class ConjunctionAnalysisService {
    private readonly orbitCalculator;
    private readonly defaultTimeStep;
    private readonly defaultBatchSize;
    constructor(orbitCalculator: OrbitCalculator);
    private crossProduct;
    private vectorMagnitude;
    private dotProduct;
    private calculateRTN;
    private generateConjunctionId;
    private analyzePairConjunction;
    private analyzeSatelliteBatch;
    analyzeConjunctions(params: IConjunctionAnalysisParams): Promise<IConjunctionEvent[]>;
}
