"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AggregationTaskService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AggregationTaskService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const aggregation_task_entity_1 = require("../entities/aggregation-task.entity");
let AggregationTaskService = AggregationTaskService_1 = class AggregationTaskService {
    constructor(taskRepository) {
        this.taskRepository = taskRepository;
        this.logger = new common_1.Logger(AggregationTaskService_1.name);
    }
    async createTask(options) {
        this.logger.debug(`创建聚合任务: ${JSON.stringify(options)}`);
        const task = new aggregation_task_entity_1.AggregationTask();
        task.task_type = options.task_type;
        task.status = 'pending';
        task.start_time = new Date();
        task.parameters = options.parameters;
        task.progress = 0;
        return this.taskRepository.save(task);
    }
    async startTask(taskId) {
        const task = await this.taskRepository.findOne({ where: { id: taskId } });
        if (!task) {
            throw new Error(`未找到ID为${taskId}的任务`);
        }
        task.status = 'running';
        task.start_time = new Date();
        return this.taskRepository.save(task);
    }
    async updateTaskProgress(taskId, progress, processedRecords) {
        const task = await this.taskRepository.findOne({ where: { id: taskId } });
        if (!task) {
            throw new Error(`未找到ID为${taskId}的任务`);
        }
        task.progress = progress;
        task.processed_records = processedRecords;
        return this.taskRepository.save(task);
    }
    async completeTask(taskId, aggregatedRecords) {
        const task = await this.taskRepository.findOne({ where: { id: taskId } });
        if (!task) {
            throw new Error(`未找到ID为${taskId}的任务`);
        }
        task.status = 'completed';
        task.end_time = new Date();
        task.aggregated_records = aggregatedRecords;
        task.progress = 100;
        return this.taskRepository.save(task);
    }
    async failTask(taskId, errorMessage) {
        const task = await this.taskRepository.findOne({ where: { id: taskId } });
        if (!task) {
            throw new Error(`未找到ID为${taskId}的任务`);
        }
        task.status = 'failed';
        task.end_time = new Date();
        task.error_message = errorMessage;
        return this.taskRepository.save(task);
    }
    async getTasks(page = 1, limit = 10) {
        const [tasks, total] = await this.taskRepository.findAndCount({
            order: { createdAt: 'DESC' },
            skip: (page - 1) * limit,
            take: limit,
        });
        return { tasks, total };
    }
    async getTaskById(taskId) {
        const task = await this.taskRepository.findOne({ where: { id: taskId } });
        if (!task) {
            throw new Error(`未找到ID为${taskId}的任务`);
        }
        return task;
    }
    async getRunningTasks() {
        try {
            this.logger.debug('获取正在运行的聚合任务');
            const tasks = await this.taskRepository.find({
                where: { status: 'running' },
                order: { start_time: 'DESC' },
            });
            this.logger.debug(`成功获取正在运行的任务，共 ${tasks.length} 个`);
            return tasks;
        }
        catch (error) {
            this.logger.error(`获取正在运行的任务失败: ${error.message}`, error.stack);
            return [];
        }
    }
    async getLatestTask() {
        try {
            this.logger.debug('获取最新聚合任务');
            const tasks = await this.taskRepository.find({
                order: {
                    createdAt: 'DESC'
                },
                take: 1
            });
            const task = tasks.length > 0 ? tasks[0] : null;
            if (task) {
                this.logger.debug(`成功获取最新任务, ID: ${task.id}, 类型: ${task.task_type}, 状态: ${task.status}`);
            }
            else {
                this.logger.debug('未找到任何聚合任务');
            }
            return task;
        }
        catch (error) {
            this.logger.error(`获取最新任务失败: ${error.message}`, error.stack);
            return null;
        }
    }
};
AggregationTaskService = AggregationTaskService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(aggregation_task_entity_1.AggregationTask)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], AggregationTaskService);
exports.AggregationTaskService = AggregationTaskService;
//# sourceMappingURL=aggregation-task.service.js.map