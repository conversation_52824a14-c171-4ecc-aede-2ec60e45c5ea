"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const constellation_service_1 = require("./constellation.service");
const satellite_entity_1 = require("../entities/satellite.entity");
const constellation_with_tle_dto_1 = require("../dto/constellation-with-tle.dto");
describe('ConstellationService', () => {
    let service;
    let satelliteRepo;
    const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([
            { constellation: 'Starlink', id: 1 },
            { constellation: 'Starlink', id: 2 },
            { constellation: 'OneWeb', id: 3 },
            { constellation: 'Starlink', id: 4 },
            { constellation: null, id: 5 },
            { constellation: 'OneWeb', id: 6 },
        ]),
    };
    const mockSatelliteRepo = {
        createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                constellation_service_1.ConstellationService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(satellite_entity_1.Satellite),
                    useValue: mockSatelliteRepo,
                },
            ],
        }).compile();
        service = module.get(constellation_service_1.ConstellationService);
        satelliteRepo = module.get((0, typeorm_1.getRepositoryToken)(satellite_entity_1.Satellite));
    });
    it('should be defined', () => {
        expect(service).toBeDefined();
    });
    describe('getConstellationsWithTle', () => {
        it('should return constellations with TLE information', async () => {
            const result = await service.getConstellationsWithTle();
            expect(result).toBeInstanceOf(constellation_with_tle_dto_1.ConstellationsWithTleResponseDto);
            expect(result.total).toBe(2);
            expect(result.constellations).toHaveLength(2);
            expect(result.constellations[0].name).toBe('Starlink');
            expect(result.constellations[0].satelliteCount).toBe(3);
            expect(result.constellations[1].name).toBe('OneWeb');
            expect(result.constellations[1].satelliteCount).toBe(2);
            expect(satelliteRepo.createQueryBuilder).toHaveBeenCalled();
            expect(mockQueryBuilder.select).toHaveBeenCalledWith('satellite.constellation');
            expect(mockQueryBuilder.where).toHaveBeenCalledWith('satellite.constellation IS NOT NULL');
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(expect.stringContaining('sources'));
        });
        it('should handle errors during query', async () => {
            mockQueryBuilder.getMany.mockRejectedValueOnce(new Error('Database error'));
            await expect(service.getConstellationsWithTle()).rejects.toThrow('查询具有TLE轨道信息的卫星星座失败: Database error');
        });
    });
});
//# sourceMappingURL=constellation.service.spec.js.map