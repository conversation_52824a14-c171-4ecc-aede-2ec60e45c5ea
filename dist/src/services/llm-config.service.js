"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var LLMConfigService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LLMConfigService = void 0;
const common_1 = require("@nestjs/common");
const llm_config_dto_1 = require("../dto/llm-config.dto");
const translation_service_1 = require("../elasticsearch/services/translation.service");
const llm_config_1 = require("../../config/llm.config");
let LLMConfigService = LLMConfigService_1 = class LLMConfigService {
    constructor(translationService) {
        this.translationService = translationService;
        this.logger = new common_1.Logger(LLMConfigService_1.name);
        this.configCache = new Map();
        this.stats = {
            translation: {
                totalRequests: 0,
                successfulRequests: 0,
                failedRequests: 0,
                totalResponseTime: 0,
                lastUsed: new Date().toISOString()
            },
            themeExtraction: {
                totalRequests: 0,
                successfulRequests: 0,
                failedRequests: 0,
                totalResponseTime: 0,
                lastUsed: new Date().toISOString()
            }
        };
        this.logger.log('LLM配置管理服务初始化完成');
        this.initializeStats();
    }
    initializeStats() {
        try {
            const translationStats = this.translationService.getAPIStats();
            this.stats.translation = {
                totalRequests: translationStats.callCount,
                successfulRequests: translationStats.callCount,
                failedRequests: 0,
                totalResponseTime: translationStats.totalTime,
                lastUsed: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.warn('无法获取翻译服务统计信息，使用默认值');
        }
    }
    async getConfig(configType) {
        try {
            let config;
            if (configType === llm_config_dto_1.ConfigType.TRANSLATION) {
                config = this.translationService.getTranslationConfig();
            }
            else {
                config = this.translationService.getThemeExtractionConfig();
            }
            const maskedApiKey = this.maskApiKey(config.apiKey);
            const response = {
                configType,
                provider: config.provider,
                model: config.model,
                baseURL: config.baseURL || '',
                apiKey: maskedApiKey,
                systemPrompt: config.systemPrompt || '',
                maxTokens: config.maxTokens || 4000,
                temperature: config.temperature || 0.1,
                timeout: config.timeout || 30000,
                maxRetries: config.maxRetries || 2,
                retryDelay: config.retryDelay || 1000,
                maxConcurrentRequests: config.maxConcurrentRequests || 3,
                lastUpdated: this.configCache.get(`${configType}_lastUpdated`) || new Date().toISOString()
            };
            return response;
        }
        catch (error) {
            this.logger.error(`获取${configType}配置失败:`, error);
            throw new common_1.InternalServerErrorException(`获取配置失败: ${error.message}`);
        }
    }
    async getAllConfigs() {
        const configs = [];
        try {
            const translationConfig = await this.getConfig(llm_config_dto_1.ConfigType.TRANSLATION);
            const themeExtractionConfig = await this.getConfig(llm_config_dto_1.ConfigType.THEME_EXTRACTION);
            configs.push(translationConfig, themeExtractionConfig);
            return configs;
        }
        catch (error) {
            this.logger.error('获取所有配置失败:', error);
            throw new common_1.InternalServerErrorException(`获取所有配置失败: ${error.message}`);
        }
    }
    async updateConfig(updateDto) {
        try {
            const { configType } = updateDto, updateData = __rest(updateDto, ["configType"]);
            const cleanUpdateData = Object.fromEntries(Object.entries(updateData).filter(([_, value]) => value !== undefined));
            this.logger.log(`开始更新${configType}配置:`, cleanUpdateData);
            if (configType === llm_config_dto_1.ConfigType.TRANSLATION) {
                this.translationService.updateTranslationConfig(cleanUpdateData);
            }
            else {
                this.translationService.updateThemeExtractionConfig(cleanUpdateData);
            }
            this.configCache.set(`${configType}_lastUpdated`, new Date().toISOString());
            this.logger.log(`${configType}配置更新成功`);
            return await this.getConfig(configType);
        }
        catch (error) {
            this.logger.error(`更新${updateDto.configType}配置失败:`, error);
            throw new common_1.BadRequestException(`更新配置失败: ${error.message}`);
        }
    }
    async testConnection(testDto) {
        const { configType, testText = 'Hello, world!' } = testDto;
        const startTime = Date.now();
        try {
            this.logger.log(`开始测试${configType}配置连接`);
            let result;
            if (configType === llm_config_dto_1.ConfigType.TRANSLATION) {
                result = await this.translationService.translateText(testText);
                this.updateStats(configType, true, Date.now() - startTime);
            }
            else {
                result = await this.translationService.extractThemes('测试标题', testText);
                this.updateStats(configType, true, Date.now() - startTime);
            }
            const responseTime = Date.now() - startTime;
            return {
                success: true,
                responseTime,
                result,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            this.logger.error(`测试${configType}配置连接失败:`, error);
            this.updateStats(configType, false, responseTime);
            return {
                success: false,
                responseTime,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
    async resetConfig(resetDto) {
        const { configType, confirm = false } = resetDto;
        if (!confirm) {
            throw new common_1.BadRequestException('需要确认才能重置配置');
        }
        try {
            this.logger.log(`开始重置${configType}配置到默认值`);
            let defaultConfig;
            if (configType === llm_config_dto_1.ConfigType.TRANSLATION) {
                defaultConfig = (0, llm_config_1.getTranslationConfig)('default');
                this.translationService.updateTranslationConfig(defaultConfig);
            }
            else {
                defaultConfig = (0, llm_config_1.getThemeExtractionConfig)('default');
                this.translationService.updateThemeExtractionConfig(defaultConfig);
            }
            this.configCache.set(`${configType}_lastUpdated`, new Date().toISOString());
            this.logger.log(`${configType}配置重置成功`);
            return await this.getConfig(configType);
        }
        catch (error) {
            this.logger.error(`重置${configType}配置失败:`, error);
            throw new common_1.BadRequestException(`重置配置失败: ${error.message}`);
        }
    }
    async getStats() {
        try {
            const translationApiStats = this.translationService.getAPIStats();
            const translationFailureStats = this.translationService.getFailureStats();
            return {
                translation: {
                    totalRequests: translationApiStats.callCount,
                    successfulRequests: translationApiStats.callCount - translationFailureStats.failures.other.count,
                    failedRequests: translationFailureStats.failures.other.count,
                    averageResponseTime: translationApiStats.callCount > 0
                        ? translationApiStats.totalTime / translationApiStats.callCount
                        : 0,
                    lastUsed: this.stats.translation.lastUsed
                },
                themeExtraction: {
                    totalRequests: this.stats.themeExtraction.totalRequests,
                    successfulRequests: this.stats.themeExtraction.successfulRequests,
                    failedRequests: this.stats.themeExtraction.failedRequests,
                    averageResponseTime: this.stats.themeExtraction.totalRequests > 0
                        ? this.stats.themeExtraction.totalResponseTime / this.stats.themeExtraction.totalRequests
                        : 0,
                    lastUsed: this.stats.themeExtraction.lastUsed
                },
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error('获取统计信息失败:', error);
            throw new common_1.InternalServerErrorException(`获取统计信息失败: ${error.message}`);
        }
    }
    maskApiKey(apiKey) {
        if (!apiKey || apiKey.length < 8) {
            return '****';
        }
        const prefix = apiKey.substring(0, 4);
        const suffix = apiKey.substring(apiKey.length - 4);
        const middle = '*'.repeat(Math.max(4, apiKey.length - 8));
        return `${prefix}${middle}${suffix}`;
    }
    updateStats(configType, success, responseTime) {
        const statsKey = configType === llm_config_dto_1.ConfigType.TRANSLATION ? 'translation' : 'themeExtraction';
        this.stats[statsKey].totalRequests++;
        this.stats[statsKey].totalResponseTime += responseTime;
        this.stats[statsKey].lastUsed = new Date().toISOString();
        if (success) {
            this.stats[statsKey].successfulRequests++;
        }
        else {
            this.stats[statsKey].failedRequests++;
        }
    }
    async clearStats() {
        this.stats = {
            translation: {
                totalRequests: 0,
                successfulRequests: 0,
                failedRequests: 0,
                totalResponseTime: 0,
                lastUsed: new Date().toISOString()
            },
            themeExtraction: {
                totalRequests: 0,
                successfulRequests: 0,
                failedRequests: 0,
                totalResponseTime: 0,
                lastUsed: new Date().toISOString()
            }
        };
        this.translationService.resetFailureStats();
        this.logger.log('统计信息已清除');
    }
};
LLMConfigService = LLMConfigService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [translation_service_1.TranslationService])
], LLMConfigService);
exports.LLMConfigService = LLMConfigService;
//# sourceMappingURL=llm-config.service.js.map