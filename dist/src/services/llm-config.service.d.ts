import { UpdateLLMConfigDto, LLMConfigResponseDto, ConfigType, TestConnectionDto, TestConnectionResponseDto, ResetConfigDto, ConfigStatsDto } from '../dto/llm-config.dto';
import { TranslationService } from '../elasticsearch/services/translation.service';
export declare class LLMConfigService {
    private readonly translationService;
    private readonly logger;
    private configCache;
    private stats;
    constructor(translationService: TranslationService);
    private initializeStats;
    getConfig(configType: ConfigType): Promise<LLMConfigResponseDto>;
    getAllConfigs(): Promise<LLMConfigResponseDto[]>;
    updateConfig(updateDto: UpdateLLMConfigDto): Promise<LLMConfigResponseDto>;
    testConnection(testDto: TestConnectionDto): Promise<TestConnectionResponseDto>;
    resetConfig(resetDto: ResetConfigDto): Promise<LLMConfigResponseDto>;
    getStats(): Promise<ConfigStatsDto>;
    private maskApiKey;
    private updateStats;
    clearStats(): Promise<void>;
}
