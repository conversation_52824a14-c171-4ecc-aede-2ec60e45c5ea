"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ConstellationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConstellationService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const satellite_entity_1 = require("../entities/satellite.entity");
const constellation_with_tle_dto_1 = require("../dto/constellation-with-tle.dto");
let ConstellationService = ConstellationService_1 = class ConstellationService {
    constructor(satelliteRepository) {
        this.satelliteRepository = satelliteRepository;
        this.logger = new common_1.Logger(ConstellationService_1.name);
    }
    async getConstellationsWithTle() {
        try {
            this.logger.log('查询具有TLE轨道信息的卫星星座');
            const query = `
        SELECT s.* 
        FROM satellites s 
        WHERE s.orbit_info IS NOT NULL 
          AND s.orbit_info::text != '[]'
          AND (
            -- 检查orbit_info中是否包含orbital_tle来源
            s.orbit_info::text ILIKE '%orbital_tle%'
            -- 或使用JSON路径匹配sources数组中的orbital_tle
            OR jsonb_path_exists(s.orbit_info, '$[*].sources[*] ? (@ == "orbital_tle")')
          )
      `;
            this.logger.debug(`执行查询SQL: ${query}`);
            const satellites = await this.satelliteRepository.query(query);
            this.logger.debug(`找到${satellites.length}个具有TLE轨道信息的卫星记录`);
            const totalCount = await this.satelliteRepository.count();
            this.logger.debug(`数据库中卫星总数: ${totalCount}`);
            if (satellites.length > 0) {
                for (let i = 0; i < Math.min(5, satellites.length); i++) {
                    this.logger.debug(`第${i + 1}条记录ID: ${satellites[i].id}`);
                    if (satellites[i].satellite_name) {
                        this.logger.debug(`卫星名称: ${JSON.stringify(satellites[i].satellite_name)}`);
                    }
                    this.logger.debug(`星座信息: ${JSON.stringify(satellites[i].constellation)}`);
                }
            }
            const constellationNames = [
                'Starlink',
                'OneWeb',
                'Planet Dove',
                'Planet Flock',
                'Planet',
                'Iridium',
                'Globalstar',
                'GaoFen',
                'Galileo',
                'BeiDou',
                'Compass',
                'GOES',
                'O3b',
                'SES',
                'Eutelsat',
                'Intelsat',
                'Inmarsat',
                'SkySat',
                'Orbcomm',
                'GLONASS',
                'GPS',
                'NavIC',
                'IRNSS',
                'Sentinel',
                'Landsat',
                'Meteosat',
                'FengYun',
                'Himawari',
                'Kuiper',
                'Jilin'
            ];
            const constellationCount = new Map();
            let processedCount = 0;
            for (const satellite of satellites) {
                try {
                    let constellationFound = false;
                    if (satellite.constellation) {
                        let constellationArray;
                        if (typeof satellite.constellation === 'string') {
                            try {
                                constellationArray = JSON.parse(satellite.constellation);
                            }
                            catch (e) {
                                this.logger.warn(`无法解析星座信息(${satellite.id}): ${e.message}`);
                            }
                        }
                        else if (Array.isArray(satellite.constellation)) {
                            constellationArray = satellite.constellation;
                        }
                        else if (typeof satellite.constellation === 'object' && satellite.constellation !== null) {
                            constellationArray = [satellite.constellation];
                        }
                        if (Array.isArray(constellationArray) && constellationArray.length > 0) {
                            for (const item of constellationArray) {
                                if (item && typeof item === 'object' && 'value' in item && item.value) {
                                    const constellationName = item.value.trim();
                                    if (constellationName) {
                                        const count = constellationCount.get(constellationName) || 0;
                                        constellationCount.set(constellationName, count + 1);
                                        constellationFound = true;
                                    }
                                }
                            }
                        }
                    }
                    const getAllSatelliteNames = (field) => {
                        const names = [];
                        if (!field)
                            return names;
                        if (typeof field === 'string') {
                            try {
                                const parsed = JSON.parse(field);
                                if (Array.isArray(parsed)) {
                                    parsed.forEach(item => {
                                        if (item && item.value)
                                            names.push(item.value.toString());
                                    });
                                }
                            }
                            catch (e) {
                                names.push(field);
                            }
                        }
                        else if (Array.isArray(field)) {
                            field.forEach(item => {
                                if (item && item.value)
                                    names.push(item.value.toString());
                            });
                        }
                        else if (typeof field === 'object' && field !== null) {
                            if (field.value)
                                names.push(field.value.toString());
                        }
                        return names;
                    };
                    const constellationMapping = {
                        'Jilin': 'GaoFen',
                        'Planet Flock': 'Planet Dove',
                        'SkySat': 'Planet SkySat',
                        'Compass': 'BeiDou',
                        'BeiDou Compass': 'BeiDou',
                    };
                    if (!constellationFound) {
                        const allNames = [
                            ...getAllSatelliteNames(satellite.satellite_name),
                            ...getAllSatelliteNames(satellite.alternative_name)
                        ];
                        if (allNames.length > 0) {
                            this.logger.debug(`卫星 ${satellite.id} 所有名称: ${allNames.join(', ')}`);
                            for (const satelliteName of allNames) {
                                if (!satelliteName)
                                    continue;
                                for (const name of constellationNames) {
                                    if (satelliteName.toLowerCase().includes(name.toLowerCase())) {
                                        const mappedName = constellationMapping[name] || name;
                                        const count = constellationCount.get(mappedName) || 0;
                                        constellationCount.set(mappedName, count + 1);
                                        constellationFound = true;
                                        if ((name === 'GaoFen' || name === 'Jilin') && processedCount < 5) {
                                            this.logger.debug(`GaoFen星座卫星: ID=${satellite.id}, 名称=${satelliteName}, 匹配=${name}`);
                                        }
                                        break;
                                    }
                                }
                                if (constellationFound)
                                    break;
                            }
                        }
                    }
                    if (constellationFound) {
                        processedCount++;
                    }
                }
                catch (error) {
                    this.logger.warn(`处理卫星${satellite.id}的星座信息时出错: ${error.message}`);
                }
            }
            this.logger.debug(`成功处理了${processedCount}条星座信息`);
            this.logger.debug(`找到了${constellationCount.size}个不同的星座`);
            const constellationNamesForDisplay = Array.from(constellationCount.keys()).join(', ');
            this.logger.debug(`找到的星座: ${constellationNamesForDisplay}`);
            const constellations = [];
            constellationCount.forEach((count, name) => {
                if (name !== 'BeiDou Compass' && name !== 'Compass') {
                    constellations.push({
                        name,
                        satelliteCount: count,
                    });
                }
            });
            constellations.sort((a, b) => b.satelliteCount - a.satelliteCount);
            const response = new constellation_with_tle_dto_1.ConstellationsWithTleResponseDto();
            response.total = constellations.length;
            response.constellations = constellations;
            return response;
        }
        catch (error) {
            this.logger.error(`查询具有TLE轨道信息的卫星星座列表失败: ${error.message}`, error.stack);
            throw new Error(`查询具有TLE轨道信息的卫星星座失败: ${error.message}`);
        }
    }
};
ConstellationService = ConstellationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(satellite_entity_1.Satellite)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ConstellationService);
exports.ConstellationService = ConstellationService;
//# sourceMappingURL=constellation.service.js.map