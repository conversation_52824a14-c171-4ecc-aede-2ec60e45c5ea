import { Repository } from 'typeorm';
import { Satellite } from '../entities/satellite.entity';
import { ElasticsearchSatelliteService } from '../elasticsearch/services/elasticsearch.satellite.service';
import { SatelliteQueryDto } from '../elasticsearch/dto/satellite-query.dto';
import { AggregationTaskService } from './aggregation-task.service';
interface ExtendedSatelliteQueryDto extends SatelliteQueryDto {
    sort_by?: string;
    sort_dir?: 'asc' | 'desc';
}
export declare class SatelliteService {
    private satelliteRepository;
    private elasticsearchSatelliteService;
    private aggregationTaskService;
    private readonly logger;
    constructor(satelliteRepository: Repository<Satellite>, elasticsearchSatelliteService: ElasticsearchSatelliteService, aggregationTaskService: AggregationTaskService);
    searchSatelliteInfoLocal(query: ExtendedSatelliteQueryDto): Promise<{
        total: number;
        page: number;
        limit: number;
        hits: Satellite[];
    }>;
    syncAllSatellitesFromES(): Promise<void>;
    private syncSatelliteBatch;
    syncSatelliteByName(satelliteName: string): Promise<void>;
    private findSatelliteByKey;
    private generateDocumentKey;
    getSatelliteNamesLocal(): Promise<string[]>;
    getSatelliteStatusesLocal(): Promise<Array<{
        en: string;
        zh: string;
    }>>;
    aggregateSatelliteData(options?: {
        keyword?: string;
        limit?: number;
        saveToDatabase?: boolean;
    }): Promise<{
        success: boolean;
        totalAggregated: number;
        message: string;
    }>;
    private canMergeWithGroup;
    private findSatelliteByNoradId;
    private findSatelliteByCosparId;
    private findSatelliteByName;
    getSatelliteById(id: number): Promise<Satellite>;
    clearSatelliteData(): Promise<{
        success: boolean;
        message: string;
    }>;
    testAggregationLogic(): Promise<{
        success: boolean;
        message: string;
    }>;
    testCustomAggregation(testDataFile: string): Promise<any>;
    incrementalAggregateSatelliteData(options?: {
        saveToDatabase?: boolean;
    }): Promise<{
        success: boolean;
        totalNewAggregated: number;
        message: string;
    }>;
    getSatelliteOrbitClassesLocal(): Promise<any[]>;
    getSatelliteConstellationsLocal(): Promise<Array<{
        en: string;
        zh: string;
    }>>;
    getSatelliteOrbitTypesLocal(): Promise<Array<{
        en: string;
        zh: string;
    }>>;
    updateSatelliteConstellationInfo(): Promise<{
        success: boolean;
        message: string;
        updated: number;
    }>;
    private updateConstellationFromSatelliteName;
    private updateConstellationFromESData;
    updateSatelliteOrbitInfoFromTLE(): Promise<{
        success: boolean;
        message: string;
        updated: number;
    }>;
    private validateTLEData;
    private needsOrbitInfoUpdate;
    private mergeOrbitInfo;
    private extractValues;
    private mapTLEToOrbitInfo;
    private generateOrbitOverview;
    private determineOrbitType;
    private determineOrbitClass;
    updateSatelliteUsersFromES(): Promise<{
        success: boolean;
        message: string;
        updated: number;
    }>;
}
export {};
