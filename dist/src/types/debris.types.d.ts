export interface SourcedValue {
    value: any;
    sources: string[];
}
export interface OrbitInfo {
    semi_major_axis: number;
    eccentricity: number;
    inclination: number;
    ra_of_asc_node: number;
    arg_of_pericenter: number;
    mean_anomaly: number;
    epoch: string;
}
export interface AttributesDetail {
    mass?: number;
    cross_section?: number;
    length?: number;
    height?: number;
    depth?: number;
    shape?: string;
}
export interface SpaceTrackDebrisDocument {
    debris_num?: string;
    cospar_id?: string;
    norad_id?: number;
    name?: string;
    country?: string;
    launch_date?: string;
    launch_site?: string;
    rcs_size?: string;
    decay?: string;
    first_epoch?: string;
    object_class?: string;
    mission?: string;
    on_orbit_cat_debris?: string;
    cat_debris?: string;
    orbit_info?: OrbitInfo;
    attributes_detail?: AttributesDetail;
}
export interface DiscosDebrisDocument extends SpaceTrackDebrisDocument {
    parent_object?: string;
    fragmentation_source?: string;
    fragmentation_date?: string;
}
export interface DebrisQueryDto {
    page?: number;
    limit?: number;
    cospar_id?: string;
    norad_id?: number;
    name?: string;
    country?: string;
    launch_date?: string;
    launch_site?: string;
    rcs_size?: string;
    decay?: string;
    first_epoch?: string;
    object_class?: string;
    mission?: string;
}
export interface AggregatedDebrisInfo {
    debris_num: SourcedValue[];
    cospar_id: SourcedValue[];
    norad_id: SourcedValue[];
    name: SourcedValue[];
    country: SourcedValue[];
    launch_date: SourcedValue[];
    launch_site: SourcedValue[];
    rcs_size: SourcedValue[];
    decay: SourcedValue[];
    first_epoch: SourcedValue[];
    object_class: SourcedValue[];
    mission: SourcedValue[];
    on_orbit_cat_debris: SourcedValue[];
    cat_debris: SourcedValue[];
    orbit_info?: OrbitInfo;
    attributes_detail?: AttributesDetail;
    parent_object: SourcedValue[];
    fragmentation_source: SourcedValue[];
    fragmentation_date: SourcedValue[];
}
export interface DebrisSearchResponse {
    total: number;
    page: number;
    limit: number;
    hits: AggregatedDebrisInfo[];
}
