"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const app_module_1 = require("./app.module");
const global_exception_filter_1 = require("./global-exception.filter");
const winston = require("winston");
const nest_winston_1 = require("nest-winston");
const winston_utilities_1 = require("nest-winston/dist/winston.utilities");
const fs = require("fs");
const path = require("path");
const news_dto_1 = require("./elasticsearch/dto/news.dto");
const llm_config_dto_1 = require("./dto/llm-config.dto");
async function bootstrap() {
    try {
        const logDir = path.join(__dirname, '..', 'logs');
        if (!fs.existsSync(logDir)) {
            fs.mkdirSync(logDir, { recursive: true });
        }
        const logger = nest_winston_1.WinstonModule.createLogger({
            transports: [
                new winston.transports.File({
                    filename: path.join(logDir, 'error.log'),
                    level: 'error',
                    format: winston.format.combine(winston.format.timestamp(), winston.format.json())
                }),
                new winston.transports.File({
                    filename: path.join(logDir, 'combined.log'),
                    format: winston.format.combine(winston.format.timestamp(), winston.format.json())
                }),
                new winston.transports.Console({
                    format: winston.format.combine(winston.format.timestamp(), winston.format.ms(), winston_utilities_1.utilities.format.nestLike('SpaceData', {
                        prettyPrint: true,
                        colors: true
                    }))
                })
            ]
        });
        const app = await core_1.NestFactory.create(app_module_1.AppModule, {
            logger
        });
        app.enableCors({
            origin: true,
            methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
            allowedHeaders: ['Content-Type', 'Accept', 'Authorization'],
            exposedHeaders: ['Content-Range', 'X-Content-Range'],
            credentials: true,
            preflightContinue: false,
            optionsSuccessStatus: 204
        });
        app.use(require('body-parser').json({ limit: '50mb' }));
        app.use(require('body-parser').urlencoded({ limit: '50mb', extended: true }));
        const express = require('express');
        const publicPath = path.join(__dirname, '..', 'public');
        if (!fs.existsSync(publicPath)) {
            fs.mkdirSync(publicPath, { recursive: true });
        }
        app.use('/public', express.static(publicPath));
        app.useGlobalPipes(new common_1.ValidationPipe({
            whitelist: true,
            forbidNonWhitelisted: false,
            transform: true,
            transformOptions: {
                enableImplicitConversion: true,
            },
            validationError: {
                target: false,
                value: true,
            },
            stopAtFirstError: false,
            enableDebugMessages: true,
            exceptionFactory: (errors) => {
                const messages = errors.map(error => {
                    if (error.children && error.children.length > 0) {
                        return error.children.map(child => Object.values(child.constraints || {})).flat();
                    }
                    return Object.values(error.constraints || {});
                }).flat();
                return new common_1.BadRequestException(messages);
            },
        }));
        app.useGlobalFilters(new global_exception_filter_1.GlobalExceptionFilter());
        const config = new swagger_1.DocumentBuilder()
            .setTitle('太空大数据平台API')
            .setDescription(`
# 太空大数据平台后端API文档

## 功能模块
- 认证：用户注册、登录等认证相关功能
- 星座信息：星座数据查询、列表获取等
- 碎片信息：空间碎片数据查询、碎片事件检索等
- 卫星信息：卫星数据查询等

## 认证说明
大部分API需要JWT认证，请在请求头中添加：
\`\`\`
Authorization: Bearer your_jwt_token
\`\`\`

## 数据格式
- 请求/响应数据格式：application/json
- 时间格式：ISO 8601 (YYYY-MM-DDTHH:mm:ss.sssZ)
- 距离单位：千米(km)
- 速度单位：千米/秒(km/s)
- 角度单位：度(degree)

## 错误处理
所有错误响应格式统一为：
\`\`\`json
{
  "statusCode": number,
  "message": string | string[],
  "error": string
}
\`\`\`

## 使用限制
- 免费用户：每日100次API调用
- 标准用户：每日1000次API调用
- 企业用户：无限制
      `)
            .setVersion('1.0')
            .addTag('认证', '用户注册、登录等认证相关接口')
            .addTag('星座信息', '星座信息查询相关接口')
            .addTag('碎片信息', '碎片信息查询相关接口')
            .addTag('卫星信息', '卫星信息查询相关接口')
            .addTag('本地卫星信息', '本地数据库中卫星信息查询相关接口')
            .addTag('聚合任务管理', '卫星数据聚合任务监控与管理相关接口')
            .addTag('频率数据', '频率信息查询相关接口')
            .addTag('轨道数据', '轨道计算与分析相关接口')
            .addTag('漏洞信息', '漏洞信息查询相关接口')
            .addTag('新闻分析', '新闻数据分析与翻译相关接口')
            .addTag('卫星3D Tiles点云', '卫星3D点云数据生成与管理相关接口')
            .addTag('大模型配置', '大模型API token、提示词、模型名称等配置管理接口')
            .addBearerAuth({
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
            name: 'JWT',
            description: '输入JWT token',
            in: 'header'
        }, 'JWT-auth')
            .build();
        const options = {
            include: [],
            deepScanRoutes: true,
            ignoreGlobalPrefix: false,
            includeNotTagged: false,
            extraModels: [
                news_dto_1.TranslateNewsDto,
                llm_config_dto_1.UpdateLLMConfigDto,
                llm_config_dto_1.LLMConfigResponseDto,
                llm_config_dto_1.TestConnectionDto,
                llm_config_dto_1.TestConnectionResponseDto,
                llm_config_dto_1.ResetConfigDto,
                llm_config_dto_1.ConfigStatsDto
            ],
            operationIdFactory: (controllerKey, methodKey) => methodKey,
        };
        const document = swagger_1.SwaggerModule.createDocument(app, config, options);
        const sanitizeDocument = (doc) => {
            if (doc.components && doc.components.securitySchemes) {
                const securitySchemes = Object.assign({}, doc.components.securitySchemes);
                const processedDoc = JSON.parse(JSON.stringify(doc, (key, value) => {
                    if (key !== '' && value === doc) {
                        return undefined;
                    }
                    return value;
                }));
                if (processedDoc.components) {
                    processedDoc.components.securitySchemes = securitySchemes;
                }
                return processedDoc;
            }
            return JSON.parse(JSON.stringify(doc, (key, value) => {
                if (key !== '' && value === doc) {
                    return undefined;
                }
                return value;
            }));
        };
        const sanitizedDocument = sanitizeDocument(document);
        swagger_1.SwaggerModule.setup('api-docs', app, sanitizedDocument, {
            swaggerOptions: {
                persistAuthorization: true,
                docExpansion: 'none',
                filter: true,
                showRequestDuration: true,
                syntaxHighlight: {
                    activate: true,
                    theme: 'monokai'
                },
                defaultModelsExpandDepth: 1,
                defaultModelExpandDepth: 1,
                defaultModelRendering: 'model',
                displayRequestDuration: true,
                tagsSorter: 'alpha',
                operationsSorter: 'alpha',
                tryItOutEnabled: true,
                requestSnippetsEnabled: true,
                deepLinking: true,
                layout: "StandaloneLayout",
                validatorUrl: "",
                withCredentials: true,
                displayOperationId: false,
                showExtensions: true,
                showCommonExtensions: true
            },
            customSiteTitle: '太空大数据平台 API 文档',
            customfavIcon: 'https://example.com/favicon.ico',
            customCss: '.swagger-ui .topbar { display: none }',
            explorer: true
        });
        const server = await app.listen(3001);
        const serverTimeout = parseInt(process.env.SERVER_TIMEOUT || '300000', 10);
        const keepAliveTimeout = parseInt(process.env.SERVER_KEEP_ALIVE_TIMEOUT || '305000', 10);
        const headersTimeout = parseInt(process.env.SERVER_HEADERS_TIMEOUT || '306000', 10);
        server.setTimeout(serverTimeout);
        server.keepAliveTimeout = keepAliveTimeout;
        server.headersTimeout = headersTimeout;
        console.log(`Server timeout configured: ${serverTimeout}ms`);
        console.log(`Keep-alive timeout: ${keepAliveTimeout}ms`);
        console.log(`Headers timeout: ${headersTimeout}ms`);
        console.log('Application is running on: http://localhost:3001');
        console.log('API documentation is available at: http://localhost:3001/api-docs');
    }
    catch (error) {
        console.error('Error during application bootstrap:', error);
        process.exit(1);
    }
}
bootstrap();
//# sourceMappingURL=main.js.map