"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AggregationTaskModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const aggregation_task_entity_1 = require("../entities/aggregation-task.entity");
const aggregation_task_service_1 = require("../services/aggregation-task.service");
const aggregation_task_controller_1 = require("../controllers/aggregation-task.controller");
let AggregationTaskModule = class AggregationTaskModule {
};
AggregationTaskModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([aggregation_task_entity_1.AggregationTask]),
        ],
        controllers: [aggregation_task_controller_1.AggregationTaskController],
        providers: [aggregation_task_service_1.AggregationTaskService],
        exports: [aggregation_task_service_1.AggregationTaskService],
    })
], AggregationTaskModule);
exports.AggregationTaskModule = AggregationTaskModule;
//# sourceMappingURL=aggregation-task.module.js.map