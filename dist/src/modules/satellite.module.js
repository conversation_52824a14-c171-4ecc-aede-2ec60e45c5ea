"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SatelliteModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const satellite_entity_1 = require("../entities/satellite.entity");
const satellite_service_1 = require("../services/satellite.service");
const satellite_controller_1 = require("../controllers/satellite.controller");
const elasticsearch_module_1 = require("../elasticsearch/elasticsearch.module");
const aggregation_task_module_1 = require("./aggregation-task.module");
const constellation_controller_1 = require("../controllers/constellation.controller");
const constellation_service_1 = require("../services/constellation.service");
let SatelliteModule = class SatelliteModule {
};
SatelliteModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([satellite_entity_1.Satellite]),
            elasticsearch_module_1.ESModule,
            aggregation_task_module_1.AggregationTaskModule,
        ],
        controllers: [satellite_controller_1.SatelliteController, constellation_controller_1.ConstellationController],
        providers: [satellite_service_1.SatelliteService, constellation_service_1.ConstellationService],
        exports: [satellite_service_1.SatelliteService, constellation_service_1.ConstellationService],
    })
], SatelliteModule);
exports.SatelliteModule = SatelliteModule;
//# sourceMappingURL=satellite.module.js.map