"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LLMConfigModule = void 0;
const common_1 = require("@nestjs/common");
const llm_config_controller_1 = require("../controllers/llm-config.controller");
const llm_config_service_1 = require("../services/llm-config.service");
const elasticsearch_module_1 = require("../elasticsearch/elasticsearch.module");
let LLMConfigModule = class LLMConfigModule {
};
LLMConfigModule = __decorate([
    (0, common_1.Module)({
        imports: [elasticsearch_module_1.ESModule],
        controllers: [llm_config_controller_1.LLMConfigController],
        providers: [llm_config_service_1.LLMConfigService],
        exports: [llm_config_service_1.LLMConfigService],
    })
], LLMConfigModule);
exports.LLMConfigModule = LLMConfigModule;
//# sourceMappingURL=llm-config.module.js.map