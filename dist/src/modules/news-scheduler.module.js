"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NewsSchedulerModule = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const news_scheduler_service_1 = require("../services/news-scheduler/news-scheduler.service");
const news_scheduler_controller_1 = require("../controllers/news-scheduler.controller");
const elasticsearch_module_1 = require("../elasticsearch/elasticsearch.module");
let NewsSchedulerModule = class NewsSchedulerModule {
};
NewsSchedulerModule = __decorate([
    (0, common_1.Module)({
        imports: [
            schedule_1.ScheduleModule.forRoot(),
            elasticsearch_module_1.ESModule,
        ],
        controllers: [news_scheduler_controller_1.NewsSchedulerController],
        providers: [news_scheduler_service_1.NewsSchedulerService],
        exports: [news_scheduler_service_1.NewsSchedulerService],
    })
], NewsSchedulerModule);
exports.NewsSchedulerModule = NewsSchedulerModule;
//# sourceMappingURL=news-scheduler.module.js.map