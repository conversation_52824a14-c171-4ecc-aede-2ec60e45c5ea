"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SatelliteSchedulerModule = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const satellite_scheduler_service_1 = require("../services/satellite-scheduler/satellite-scheduler.service");
const satellite_scheduler_controller_1 = require("../controllers/satellite-scheduler.controller");
const satellite_module_1 = require("./satellite.module");
let SatelliteSchedulerModule = class SatelliteSchedulerModule {
};
SatelliteSchedulerModule = __decorate([
    (0, common_1.Module)({
        imports: [
            schedule_1.ScheduleModule.forRoot(),
            satellite_module_1.SatelliteModule,
        ],
        controllers: [satellite_scheduler_controller_1.SatelliteSchedulerController],
        providers: [satellite_scheduler_service_1.SatelliteSchedulerService],
        exports: [satellite_scheduler_service_1.SatelliteSchedulerService],
    })
], SatelliteSchedulerModule);
exports.SatelliteSchedulerModule = SatelliteSchedulerModule;
//# sourceMappingURL=satellite-scheduler.module.js.map