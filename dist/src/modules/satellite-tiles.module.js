"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SatelliteTilesModule = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const satellite_tiles_service_1 = require("../services/satellite-tiles/satellite-tiles.service");
const file_push_service_1 = require("../services/satellite-tiles/file-push.service");
const satellite_tiles_controller_1 = require("../controllers/satellite-tiles.controller");
const elasticsearch_module_1 = require("../elasticsearch/elasticsearch.module");
const orbit_analysis_module_1 = require("./orbit-analysis.module");
let SatelliteTilesModule = class SatelliteTilesModule {
};
SatelliteTilesModule = __decorate([
    (0, common_1.Module)({
        imports: [
            schedule_1.ScheduleModule.forRoot(),
            elasticsearch_module_1.ESModule,
            orbit_analysis_module_1.OrbitAnalysisModule,
        ],
        controllers: [satellite_tiles_controller_1.SatelliteTilesController],
        providers: [satellite_tiles_service_1.SatelliteTilesService, file_push_service_1.FilePushService],
        exports: [satellite_tiles_service_1.SatelliteTilesService, file_push_service_1.FilePushService],
    })
], SatelliteTilesModule);
exports.SatelliteTilesModule = SatelliteTilesModule;
//# sourceMappingURL=satellite-tiles.module.js.map