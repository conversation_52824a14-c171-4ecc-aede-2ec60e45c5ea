"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrbitAnalysisModule = void 0;
const common_1 = require("@nestjs/common");
const OrbitCalculator_1 = require("../services/orbit-calculator/OrbitCalculator");
const pass_analysis_service_1 = require("../services/pass-analysis/pass-analysis.service");
const conjunction_analysis_service_1 = require("../services/conjunction-analysis/conjunction-analysis.service");
const pass_analysis_controller_1 = require("../controllers/pass-analysis.controller");
const conjunction_analysis_controller_1 = require("../controllers/conjunction-analysis.controller");
const orbit_calculator_controller_1 = require("../controllers/orbit-calculator.controller");
let OrbitAnalysisModule = class OrbitAnalysisModule {
};
OrbitAnalysisModule = __decorate([
    (0, common_1.Module)({
        imports: [],
        controllers: [
            orbit_calculator_controller_1.OrbitCalculatorController,
            pass_analysis_controller_1.PassAnalysisController,
            conjunction_analysis_controller_1.ConjunctionAnalysisController
        ],
        providers: [
            {
                provide: OrbitCalculator_1.OrbitCalculator,
                useFactory: () => new OrbitCalculator_1.OrbitCalculator({
                    wgs84: {
                        earthRadius: 6378.137,
                        mu: 398600.4418,
                        j2: 0.00108262998905,
                        j3: -0.00000253215306,
                        j4: -0.00000161098761
                    }
                })
            },
            pass_analysis_service_1.PassAnalysisService,
            conjunction_analysis_service_1.ConjunctionAnalysisService
        ],
        exports: [
            OrbitCalculator_1.OrbitCalculator,
            pass_analysis_service_1.PassAnalysisService,
            conjunction_analysis_service_1.ConjunctionAnalysisService
        ]
    })
], OrbitAnalysisModule);
exports.OrbitAnalysisModule = OrbitAnalysisModule;
//# sourceMappingURL=orbit-analysis.module.js.map