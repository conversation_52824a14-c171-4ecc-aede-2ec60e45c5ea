"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminOrOwnerGuard = exports.AdminGuard = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("../../entities/user.entity");
const user_role_enum_1 = require("../enums/user-role.enum");
const user_approval_status_enum_1 = require("../enums/user-approval-status.enum");
let AdminGuard = class AdminGuard {
    constructor(userRepository) {
        this.userRepository = userRepository;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user || !user.id) {
            throw new common_1.ForbiddenException('未登录用户无法访问此资源');
        }
        if (user.role !== user_role_enum_1.UserRole.ADMIN) {
            throw new common_1.ForbiddenException('只有管理员才能执行此操作');
        }
        if (user.approvalStatus !== user_approval_status_enum_1.UserApprovalStatus.APPROVED) {
            throw new common_1.ForbiddenException('管理员账户未通过审批');
        }
        if (!user.isActive) {
            throw new common_1.ForbiddenException('管理员账户已被禁用');
        }
        request.adminUser = user;
        return true;
    }
};
AdminGuard = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], AdminGuard);
exports.AdminGuard = AdminGuard;
let AdminOrOwnerGuard = class AdminOrOwnerGuard {
    canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        const targetUserId = parseInt(request.params.id);
        if (!user) {
            throw new common_1.ForbiddenException('未找到用户信息');
        }
        if (user.role === user_role_enum_1.UserRole.ADMIN) {
            return true;
        }
        if (user.id === targetUserId) {
            return true;
        }
        throw new common_1.ForbiddenException('您只能访问自己的资源');
    }
};
AdminOrOwnerGuard = __decorate([
    (0, common_1.Injectable)()
], AdminOrOwnerGuard);
exports.AdminOrOwnerGuard = AdminOrOwnerGuard;
//# sourceMappingURL=admin.guard.js.map