"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const auth_controller_1 = require("./auth.controller");
const auth_service_1 = require("./auth.service");
const user_role_enum_1 = require("./enums/user-role.enum");
describe('AuthController', () => {
    let controller;
    let authService;
    const mockUser = {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        role: user_role_enum_1.UserRole.FREE,
        apiCallsToday: 0,
        downloadsToday: 0,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const mockAuthService = {
        register: jest.fn(),
        login: jest.fn(),
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            controllers: [auth_controller_1.AuthController],
            providers: [
                {
                    provide: auth_service_1.AuthService,
                    useValue: mockAuthService,
                },
            ],
        }).compile();
        controller = module.get(auth_controller_1.AuthController);
        authService = module.get(auth_service_1.AuthService);
    });
    describe('register', () => {
        it('should register a new user', async () => {
            const registerDto = {
                username: 'testuser',
                password: 'password123',
                email: '<EMAIL>',
            };
            mockAuthService.register.mockResolvedValue(mockUser);
            const result = await controller.register(registerDto);
            expect(result).toEqual(mockUser);
        });
    });
    describe('login', () => {
        it('should login a user', async () => {
            const loginDto = {
                username: 'testuser',
                password: 'password123',
            };
            const mockLoginResponse = {
                access_token: 'mock.jwt.token',
                user: mockUser,
            };
            mockAuthService.login.mockResolvedValue(mockLoginResponse);
            const result = await controller.login(loginDto);
            expect(result).toEqual(mockLoginResponse);
        });
    });
});
//# sourceMappingURL=auth.controller.spec.js.map