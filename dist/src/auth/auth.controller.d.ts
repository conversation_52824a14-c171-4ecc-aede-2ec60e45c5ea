import { AuthService } from './auth.service';
import { RegisterDto, LoginDto } from './dto/auth.dto';
import { SetUserRoleDto, UserPermissionResponseDto, GetUsersQueryDto, UsersListResponseDto } from './dto/permission.dto';
import { UserApprovalDto, PendingUsersQueryDto, PendingUsersResponseDto, ApprovalResultResponseDto } from './dto/user-approval.dto';
import { User } from '../entities/user.entity';
import { DeleteUserResponseDto } from './dto/user-delete.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    register(registerDto: RegisterDto): Promise<Omit<User, 'password'>>;
    login(loginDto: LoginDto): Promise<{
        access_token: string;
        user: Omit<User, 'password'>;
    }>;
    setUserRole(userId: number, setUserRoleDto: SetUserRoleDto): Promise<UserPermissionResponseDto>;
    getUserPermissions(userId: number, currentUser: User): Promise<UserPermissionResponseDto>;
    getUsers(queryDto: GetUsersQueryDto): Promise<UsersListResponseDto>;
    getProfile(currentUser: User): Promise<UserPermissionResponseDto>;
    getPendingUsers(queryDto: PendingUsersQueryDto): Promise<PendingUsersResponseDto>;
    approveUser(userId: number, approvalDto: UserApprovalDto, currentUser: User): Promise<ApprovalResultResponseDto>;
    getUserApprovalHistory(queryDto: GetUsersQueryDto): Promise<UsersListResponseDto>;
    deleteUser(userId: number): Promise<DeleteUserResponseDto>;
    changePassword(currentUser: User, changePasswordDto: ChangePasswordDto): Promise<{
        message: string;
    }>;
}
