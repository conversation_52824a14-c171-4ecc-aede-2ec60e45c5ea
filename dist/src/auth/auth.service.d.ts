import { Repository, DataSource } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { User } from '../entities/user.entity';
import { RegisterDto, LoginDto } from './dto/auth.dto';
import { SetUserRoleDto, UserPermissionResponseDto, GetUsersQueryDto, UsersListResponseDto } from './dto/permission.dto';
import { UserApprovalDto, PendingUsersQueryDto, PendingUsersResponseDto, ApprovalResultResponseDto } from './dto/user-approval.dto';
import { DeleteUserResponseDto } from './dto/user-delete.dto';
import { UserService } from '../modules/user/user.service';
import { ChangePasswordDto } from './dto/change-password.dto';
export declare class AuthService {
    private readonly userRepository;
    private readonly jwtService;
    private readonly dataSource;
    private readonly userService;
    constructor(userRepository: Repository<User>, jwtService: JwtService, dataSource: DataSource, userService: UserService);
    register(registerDto: RegisterDto): Promise<Omit<User, 'password'>>;
    login(loginDto: LoginDto): Promise<{
        access_token: string;
        user: Omit<User, 'password'>;
    }>;
    setUserRole(userId: number, setUserRoleDto: SetUserRoleDto): Promise<UserPermissionResponseDto>;
    getUserById(userId: number): Promise<UserPermissionResponseDto>;
    getUserPermissions(userId: number): Promise<UserPermissionResponseDto>;
    getUsers(queryDto: GetUsersQueryDto): Promise<UsersListResponseDto>;
    isAdmin(userId: number): Promise<boolean>;
    getPendingUsers(queryDto: PendingUsersQueryDto): Promise<PendingUsersResponseDto>;
    approveUser(userId: number, approvalDto: UserApprovalDto, adminId: number): Promise<ApprovalResultResponseDto>;
    getUserApprovalHistory(queryDto: GetUsersQueryDto): Promise<UsersListResponseDto>;
    deleteUser(userId: number): Promise<DeleteUserResponseDto>;
    changePassword(userId: number, changePasswordDto: ChangePasswordDto): Promise<{
        message: string;
    }>;
}
