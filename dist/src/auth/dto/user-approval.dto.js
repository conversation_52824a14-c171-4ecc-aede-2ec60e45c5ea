"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApprovalResultResponseDto = exports.PendingUsersResponseDto = exports.UserApprovalResponseDto = exports.PendingUsersQueryDto = exports.UserApprovalDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const user_approval_status_enum_1 = require("../enums/user-approval-status.enum");
const user_role_enum_1 = require("../enums/user-role.enum");
class UserApprovalDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '审批操作类型',
        enum: ['approve', 'reject'],
        example: 'approve'
    }),
    (0, class_validator_1.IsEnum)(['approve', 'reject'], { message: '审批操作只能是 approve 或 reject' }),
    (0, class_validator_1.IsNotEmpty)({ message: '审批操作不能为空' }),
    __metadata("design:type", String)
], UserApprovalDto.prototype, "action", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '拒绝原因（拒绝时必填）',
        example: '申请信息不完整，请重新提交完整资料'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '拒绝原因必须是字符串' }),
    __metadata("design:type", String)
], UserApprovalDto.prototype, "rejectionReason", void 0);
exports.UserApprovalDto = UserApprovalDto;
class PendingUsersQueryDto {
    constructor() {
        this.page = 1;
        this.limit = 10;
    }
}
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '页码',
        minimum: 1,
        default: 1,
        example: 1
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)({ message: '页码必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '页码必须大于0' }),
    __metadata("design:type", Number)
], PendingUsersQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '每页数量',
        minimum: 1,
        maximum: 100,
        default: 10,
        example: 10
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)({ message: '每页数量必须是整数' }),
    (0, class_validator_1.Min)(1, { message: '每页数量必须大于0' }),
    (0, class_validator_1.Max)(100, { message: '每页数量不能超过100' }),
    __metadata("design:type", Number)
], PendingUsersQueryDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '用户名筛选（模糊搜索）',
        example: 'john'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '用户名必须是字符串' }),
    __metadata("design:type", String)
], PendingUsersQueryDto.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '邮箱筛选（模糊搜索）',
        example: '<EMAIL>'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '邮箱必须是字符串' }),
    __metadata("design:type", String)
], PendingUsersQueryDto.prototype, "email", void 0);
exports.PendingUsersQueryDto = PendingUsersQueryDto;
class UserApprovalResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: 1 }),
    __metadata("design:type", Number)
], UserApprovalResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户名', example: 'johnsmith' }),
    __metadata("design:type", String)
], UserApprovalResponseDto.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邮箱', example: '<EMAIL>' }),
    __metadata("design:type", String)
], UserApprovalResponseDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '用户角色',
        enum: user_role_enum_1.UserRole,
        example: user_role_enum_1.UserRole.FREE
    }),
    __metadata("design:type", String)
], UserApprovalResponseDto.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '审批状态',
        enum: user_approval_status_enum_1.UserApprovalStatus,
        example: user_approval_status_enum_1.UserApprovalStatus.PENDING
    }),
    __metadata("design:type", String)
], UserApprovalResponseDto.prototype, "approvalStatus", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '审批操作时间（包括批准和拒绝）',
        example: '2024-01-15T10:30:00Z'
    }),
    __metadata("design:type", Date)
], UserApprovalResponseDto.prototype, "approvedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '执行审批操作的管理员用户ID（包括批准和拒绝）',
        example: 1
    }),
    __metadata("design:type", Number)
], UserApprovalResponseDto.prototype, "approvedBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '拒绝原因',
        example: '申请信息不完整'
    }),
    __metadata("design:type", String)
], UserApprovalResponseDto.prototype, "rejectionReason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '注册时间',
        example: '2024-01-15T10:00:00Z'
    }),
    __metadata("design:type", Date)
], UserApprovalResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否激活',
        example: true
    }),
    __metadata("design:type", Boolean)
], UserApprovalResponseDto.prototype, "isActive", void 0);
exports.UserApprovalResponseDto = UserApprovalResponseDto;
class PendingUsersResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '用户列表',
        type: [UserApprovalResponseDto]
    }),
    __metadata("design:type", Array)
], PendingUsersResponseDto.prototype, "users", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总数量', example: 25 }),
    __metadata("design:type", Number)
], PendingUsersResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前页码', example: 1 }),
    __metadata("design:type", Number)
], PendingUsersResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量', example: 10 }),
    __metadata("design:type", Number)
], PendingUsersResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总页数', example: 3 }),
    __metadata("design:type", Number)
], PendingUsersResponseDto.prototype, "totalPages", void 0);
exports.PendingUsersResponseDto = PendingUsersResponseDto;
class ApprovalResultResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否成功', example: true }),
    __metadata("design:type", Boolean)
], ApprovalResultResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '操作信息', example: '用户审批成功' }),
    __metadata("design:type", String)
], ApprovalResultResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '用户信息',
        type: UserApprovalResponseDto
    }),
    __metadata("design:type", UserApprovalResponseDto)
], ApprovalResultResponseDto.prototype, "user", void 0);
exports.ApprovalResultResponseDto = ApprovalResultResponseDto;
//# sourceMappingURL=user-approval.dto.js.map