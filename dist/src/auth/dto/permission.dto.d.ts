import { UserRole } from '../enums/user-role.enum';
export declare class SetUserRoleDto {
    role: UserRole;
}
export declare class UserPermissionResponseDto {
    id: number;
    username: string;
    email: string;
    role: UserRole;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export declare class GetUsersQueryDto {
    page?: number;
    limit?: number;
    role?: UserRole;
    username?: string;
}
export declare class UsersListResponseDto {
    total: number;
    page: number;
    limit: number;
    users: UserPermissionResponseDto[];
}
