"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IsStrongPassword = void 0;
const class_validator_1 = require("class-validator");
const password_validator_1 = require("../../utils/password-validator");
function IsStrongPassword(validationOptions) {
    return function (object, propertyName) {
        (0, class_validator_1.registerDecorator)({
            name: 'isStrongPassword',
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            validator: {
                validate(value, args) {
                    if (typeof value !== 'string')
                        return false;
                    const result = password_validator_1.PasswordValidator.validate(value);
                    if (!result.isValid) {
                        if (validationOptions) {
                            validationOptions.message = result.errors.join(', ');
                        }
                        return false;
                    }
                    return true;
                }
            }
        });
    };
}
exports.IsStrongPassword = IsStrongPassword;
//# sourceMappingURL=password.validator.js.map