"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersListResponseDto = exports.GetUsersQueryDto = exports.UserPermissionResponseDto = exports.SetUserRoleDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const user_role_enum_1 = require("../enums/user-role.enum");
class SetUserRoleDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '用户角色',
        enum: user_role_enum_1.UserRole,
        example: user_role_enum_1.UserRole.PREMIUM,
        enumName: 'UserRole'
    }),
    (0, class_validator_1.IsEnum)(user_role_enum_1.UserRole, { message: '请选择有效的用户角色' }),
    __metadata("design:type", String)
], SetUserRoleDto.prototype, "role", void 0);
exports.SetUserRoleDto = SetUserRoleDto;
class UserPermissionResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: 1 }),
    __metadata("design:type", Number)
], UserPermissionResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户名', example: 'testuser' }),
    __metadata("design:type", String)
], UserPermissionResponseDto.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邮箱', example: '<EMAIL>' }),
    __metadata("design:type", String)
], UserPermissionResponseDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '用户角色',
        enum: user_role_enum_1.UserRole,
        example: user_role_enum_1.UserRole.PREMIUM
    }),
    __metadata("design:type", String)
], UserPermissionResponseDto.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否激活', example: true }),
    __metadata("design:type", Boolean)
], UserPermissionResponseDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间', example: '2024-02-03T12:34:56.789Z' }),
    __metadata("design:type", Date)
], UserPermissionResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间', example: '2024-02-03T12:34:56.789Z' }),
    __metadata("design:type", Date)
], UserPermissionResponseDto.prototype, "updatedAt", void 0);
exports.UserPermissionResponseDto = UserPermissionResponseDto;
class GetUsersQueryDto {
    constructor() {
        this.page = 1;
        this.limit = 10;
    }
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '页码',
        example: 1,
        required: false,
        default: 1
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '页码必须是数字' }),
    __metadata("design:type", Number)
], GetUsersQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '每页数量',
        example: 10,
        required: false,
        default: 10
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '每页数量必须是数字' }),
    __metadata("design:type", Number)
], GetUsersQueryDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '角色筛选',
        enum: user_role_enum_1.UserRole,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(user_role_enum_1.UserRole, { message: '请选择有效的用户角色' }),
    __metadata("design:type", String)
], GetUsersQueryDto.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '用户名搜索',
        example: 'test',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '用户名必须是字符串' }),
    __metadata("design:type", String)
], GetUsersQueryDto.prototype, "username", void 0);
exports.GetUsersQueryDto = GetUsersQueryDto;
class UsersListResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总数量', example: 100 }),
    __metadata("design:type", Number)
], UsersListResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前页码', example: 1 }),
    __metadata("design:type", Number)
], UsersListResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量', example: 10 }),
    __metadata("design:type", Number)
], UsersListResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '用户列表',
        type: [UserPermissionResponseDto]
    }),
    __metadata("design:type", Array)
], UsersListResponseDto.prototype, "users", void 0);
exports.UsersListResponseDto = UsersListResponseDto;
//# sourceMappingURL=permission.dto.js.map