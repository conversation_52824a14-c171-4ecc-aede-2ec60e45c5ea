import { UserApprovalStatus } from '../enums/user-approval-status.enum';
import { UserRole } from '../enums/user-role.enum';
export declare class UserApprovalDto {
    action: 'approve' | 'reject';
    rejectionReason?: string;
}
export declare class PendingUsersQueryDto {
    page?: number;
    limit?: number;
    username?: string;
    email?: string;
}
export declare class UserApprovalResponseDto {
    id: number;
    username: string;
    email: string;
    role: UserRole;
    approvalStatus: UserApprovalStatus;
    approvedAt?: Date;
    approvedBy?: number;
    rejectionReason?: string;
    createdAt: Date;
    isActive: boolean;
}
export declare class PendingUsersResponseDto {
    users: UserApprovalResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
export declare class ApprovalResultResponseDto {
    success: boolean;
    message: string;
    user: UserApprovalResponseDto;
}
