"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChangePasswordDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const password_validator_1 = require("./password.validator");
class ChangePasswordDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '当前密码',
        example: 'OldPass123!@#',
    }),
    (0, class_validator_1.IsString)({ message: '当前密码必须是字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: '当前密码不能为空' }),
    __metadata("design:type", String)
], ChangePasswordDto.prototype, "currentPassword", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '新密码',
        example: 'NewPass123!@#',
        minLength: 8,
        maxLength: 20,
    }),
    (0, class_validator_1.IsString)({ message: '新密码必须是字符串' }),
    (0, class_validator_1.IsNotEmpty)({ message: '新密码不能为空' }),
    (0, password_validator_1.IsStrongPassword)({
        message: '新密码必须包含大小写字母、数字和特殊字符，长度在8-20个字符之间'
    }),
    __metadata("design:type", String)
], ChangePasswordDto.prototype, "newPassword", void 0);
exports.ChangePasswordDto = ChangePasswordDto;
//# sourceMappingURL=change-password.dto.js.map