"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteUserResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class DeleteUserResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '操作是否成功',
        example: true
    }),
    __metadata("design:type", Boolean)
], DeleteUserResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '操作结果信息',
        example: '用户删除成功'
    }),
    __metadata("design:type", String)
], DeleteUserResponseDto.prototype, "message", void 0);
exports.DeleteUserResponseDto = DeleteUserResponseDto;
//# sourceMappingURL=user-delete.dto.js.map