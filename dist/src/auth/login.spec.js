"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const request = require("supertest");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const auth_module_1 = require("./auth.module");
const user_entity_1 = require("../entities/user.entity");
const user_role_enum_1 = require("./enums/user-role.enum");
const database_config_1 = require("../config/database.config");
describe('用户认证测试', () => {
    let app;
    let authToken;
    beforeAll(async () => {
        const moduleFixture = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                    load: [database_config_1.default],
                }),
                typeorm_1.TypeOrmModule.forRootAsync({
                    imports: [config_1.ConfigModule],
                    useFactory: (configService) => ({
                        type: 'postgres',
                        host: configService.get('DB_HOST', 'localhost'),
                        port: configService.get('DB_PORT', 5432),
                        username: configService.get('DB_USERNAME', 'postgres'),
                        password: configService.get('DB_PASSWORD', 'postgres'),
                        database: configService.get('DB_DATABASE', 'spacedata_test'),
                        entities: [user_entity_1.User],
                        synchronize: true,
                    }),
                    inject: [config_1.ConfigService],
                }),
                auth_module_1.AuthModule,
            ],
        }).compile();
        app = moduleFixture.createNestApplication();
        await app.init();
    });
    afterAll(async () => {
        await app.close();
    });
    describe('用户注册和登录流程', () => {
        const testUser = {
            username: 'testuser',
            email: '<EMAIL>',
            password: 'Password123!',
            role: user_role_enum_1.UserRole.FREE
        };
        beforeAll(async () => {
            await request(app.getHttpServer())
                .post('/auth/register')
                .send(testUser)
                .expect(201);
            const response = await request(app.getHttpServer())
                .post('/auth/login')
                .send({
                username: testUser.username,
                password: testUser.password,
            });
            authToken = response.body.data.token;
        });
        describe('POST /auth/login', () => {
            it('使用正确的用户名和密码应该成功登录', async () => {
                const response = await request(app.getHttpServer())
                    .post('/auth/login')
                    .send({
                    username: testUser.username,
                    password: testUser.password,
                })
                    .expect(200);
                expect(response.body).toEqual({
                    success: true,
                    data: expect.objectContaining({
                        token: expect.any(String),
                        role: user_role_enum_1.UserRole.FREE,
                    }),
                    error: null,
                    timestamp: expect.any(String),
                });
            });
            it('使用错误的密码应该登录失败', async () => {
                const response = await request(app.getHttpServer())
                    .post('/auth/login')
                    .send({
                    username: testUser.username,
                    password: 'wrongpassword',
                })
                    .expect(401);
                expect(response.body).toEqual({
                    success: false,
                    data: null,
                    error: '用户名或密码错误',
                    timestamp: expect.any(String),
                });
            });
            it('使用不存在的用户名应该登录失败', async () => {
                const response = await request(app.getHttpServer())
                    .post('/auth/login')
                    .send({
                    username: 'nonexistentuser',
                    password: testUser.password,
                })
                    .expect(401);
                expect(response.body).toEqual({
                    success: false,
                    data: null,
                    error: '用户名或密码错误',
                    timestamp: expect.any(String),
                });
            });
            it('使用空的用户名或密码应该返回验证错误', async () => {
                const response = await request(app.getHttpServer())
                    .post('/auth/login')
                    .send({
                    username: '',
                    password: '',
                })
                    .expect(400);
                expect(response.body).toEqual({
                    success: false,
                    data: null,
                    error: expect.any(String),
                    timestamp: expect.any(String),
                });
            });
            it('密码长度不足应该返回验证错误', async () => {
                const response = await request(app.getHttpServer())
                    .post('/auth/login')
                    .send({
                    username: testUser.username,
                    password: '123',
                })
                    .expect(400);
                expect(response.body).toEqual({
                    success: false,
                    data: null,
                    error: expect.any(String),
                    timestamp: expect.any(String),
                });
            });
        });
        describe('登录后的token验证', () => {
            it('使用有效token应该能访问受保护的资源', async () => {
                await request(app.getHttpServer())
                    .get('/auth/profile')
                    .set('Authorization', `Bearer ${authToken}`)
                    .expect(200);
            });
            it('使用无效token应该被拒绝访问', async () => {
                await request(app.getHttpServer())
                    .get('/auth/profile')
                    .set('Authorization', 'Bearer invalid-token')
                    .expect(401);
            });
            it('不提供token应该被拒绝访问', async () => {
                await request(app.getHttpServer())
                    .get('/auth/profile')
                    .expect(401);
            });
        });
        describe('权限验证', () => {
            it('免费用户应该有API调用次数限制', async () => {
                const response = await request(app.getHttpServer())
                    .get('/auth/limits')
                    .set('Authorization', `Bearer ${authToken}`)
                    .expect(200);
                expect(response.body.data).toEqual({
                    apiCallsLimit: 100,
                    downloadsLimit: 3,
                    downloadSizeLimit: 10 * 1024 * 1024,
                });
            });
        });
    });
});
//# sourceMappingURL=login.spec.js.map