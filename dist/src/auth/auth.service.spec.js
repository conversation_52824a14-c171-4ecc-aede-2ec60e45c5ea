"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const jwt_1 = require("@nestjs/jwt");
const common_1 = require("@nestjs/common");
const bcrypt = require("bcrypt");
const auth_service_1 = require("./auth.service");
const user_entity_1 = require("../entities/user.entity");
const user_role_enum_1 = require("./enums/user-role.enum");
describe('AuthService', () => {
    let service;
    let jwtService;
    let mockUserRepository;
    beforeEach(async () => {
        mockUserRepository = {
            findOne: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                auth_service_1.AuthService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(user_entity_1.User),
                    useValue: mockUserRepository,
                },
                {
                    provide: jwt_1.JwtService,
                    useValue: {
                        sign: jest.fn(() => 'test-token'),
                    },
                },
            ],
        }).compile();
        service = module.get(auth_service_1.AuthService);
        jwtService = module.get(jwt_1.JwtService);
    });
    describe('register', () => {
        const validRegisterDto = {
            username: 'testuser',
            password: 'password123',
            email: '<EMAIL>',
        };
        it('should successfully register a new user', async () => {
            mockUserRepository.findOne
                .mockResolvedValueOnce(null)
                .mockResolvedValueOnce(null);
            const mockUser = Object.assign(Object.assign({ id: 1 }, validRegisterDto), { password: 'hashedPassword', role: user_role_enum_1.UserRole.FREE, createdAt: new Date(), updatedAt: new Date() });
            mockUserRepository.create.mockReturnValue(mockUser);
            mockUserRepository.save.mockResolvedValue(mockUser);
            const result = await service.register(validRegisterDto);
            expect(result).toHaveProperty('id');
            expect(result).toHaveProperty('username', validRegisterDto.username);
            expect(result).toHaveProperty('email', validRegisterDto.email);
            expect(result).not.toHaveProperty('password');
            expect(mockUserRepository.findOne).toHaveBeenCalledTimes(2);
        });
        it('should throw BadRequestException if email is not provided', async () => {
            const dtoWithoutEmail = {
                username: 'testuser',
                password: 'password123',
            };
            await expect(service.register(dtoWithoutEmail)).rejects.toThrow(new common_1.BadRequestException('电子邮箱是必填项'));
        });
        it('should throw ConflictException with USERNAME_EXISTS code if username exists', async () => {
            mockUserRepository.findOne
                .mockResolvedValueOnce(Object.assign({ id: 1 }, validRegisterDto))
                .mockResolvedValueOnce(null);
            const error = await service.register(validRegisterDto).catch(e => e);
            expect(error).toBeInstanceOf(common_1.ConflictException);
            expect(error.message).toBe('用户名已存在');
            expect(error.response.error).toBe('USERNAME_EXISTS');
        });
        it('should throw ConflictException with EMAIL_EXISTS code if email exists', async () => {
            mockUserRepository.findOne
                .mockResolvedValueOnce(null)
                .mockResolvedValueOnce({ id: 2, username: 'other', email: validRegisterDto.email });
            const error = await service.register(validRegisterDto).catch(e => e);
            expect(error).toBeInstanceOf(common_1.ConflictException);
            expect(error.message).toBe('邮箱已被使用');
            expect(error.response.error).toBe('EMAIL_EXISTS');
        });
    });
    describe('login', () => {
        const validLoginDto = {
            username: 'testuser',
            password: 'password123',
        };
        it('should successfully login a user', async () => {
            const hashedPassword = await bcrypt.hash(validLoginDto.password, 10);
            const mockUser = {
                id: 1,
                username: validLoginDto.username,
                password: hashedPassword,
                role: user_role_enum_1.UserRole.FREE,
                email: '<EMAIL>',
            };
            mockUserRepository.findOne.mockResolvedValue(mockUser);
            const result = await service.login(validLoginDto);
            expect(result).toHaveProperty('access_token', 'test-token');
            expect(result).toHaveProperty('user');
            expect(result.user).not.toHaveProperty('password');
            expect(result.user).toHaveProperty('email', mockUser.email);
        });
        it('should throw UnauthorizedException with INVALID_CREDENTIALS code if user not found', async () => {
            mockUserRepository.findOne.mockResolvedValue(null);
            const error = await service.login(validLoginDto).catch(e => e);
            expect(error).toBeInstanceOf(common_1.UnauthorizedException);
            expect(error.message).toBe('用户名或密码错误');
            expect(error.response.error).toBe('INVALID_CREDENTIALS');
        });
        it('should throw UnauthorizedException with INVALID_CREDENTIALS code if password is incorrect', async () => {
            const hashedPassword = await bcrypt.hash('differentpassword', 10);
            const mockUser = {
                id: 1,
                username: validLoginDto.username,
                password: hashedPassword,
                role: user_role_enum_1.UserRole.FREE,
            };
            mockUserRepository.findOne.mockResolvedValue(mockUser);
            const error = await service.login(validLoginDto).catch(e => e);
            expect(error).toBeInstanceOf(common_1.UnauthorizedException);
            expect(error.message).toBe('用户名或密码错误');
            expect(error.response.error).toBe('INVALID_CREDENTIALS');
        });
    });
});
//# sourceMappingURL=auth.service.spec.js.map