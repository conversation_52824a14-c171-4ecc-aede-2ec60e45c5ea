"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const jwt_1 = require("@nestjs/jwt");
const bcrypt = require("bcrypt");
const user_entity_1 = require("../entities/user.entity");
const user_role_enum_1 = require("./enums/user-role.enum");
const user_approval_status_enum_1 = require("./enums/user-approval-status.enum");
const user_service_1 = require("../modules/user/user.service");
let AuthService = class AuthService {
    constructor(userRepository, jwtService, dataSource, userService) {
        this.userRepository = userRepository;
        this.jwtService = jwtService;
        this.dataSource = dataSource;
        this.userService = userService;
    }
    async register(registerDto) {
        const { username, password, email } = registerDto;
        if (!email) {
            throw new common_1.BadRequestException('电子邮箱是必填项');
        }
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            throw new common_1.BadRequestException('请输入有效的电子邮箱地址');
        }
        return await this.dataSource.transaction(async (transactionalEntityManager) => {
            const [existingUserByUsername, existingUserByEmail] = await Promise.all([
                transactionalEntityManager.findOne(user_entity_1.User, {
                    where: { username },
                    withDeleted: true,
                }),
                transactionalEntityManager.findOne(user_entity_1.User, {
                    where: { email },
                    withDeleted: true,
                })
            ]);
            if (existingUserByUsername) {
                throw new common_1.ConflictException('用户名已存在');
            }
            if (existingUserByEmail) {
                throw new common_1.ConflictException('邮箱已被使用');
            }
            const hashedPassword = await bcrypt.hash(password, 10);
            const user = this.userRepository.create({
                username,
                password: hashedPassword,
                email,
                approvalStatus: user_approval_status_enum_1.UserApprovalStatus.PENDING,
            });
            const savedUser = await transactionalEntityManager.save(user_entity_1.User, user);
            const { password: _ } = savedUser, result = __rest(savedUser, ["password"]);
            return result;
        }).catch(error => {
            var _a, _b;
            if (error.code === '23505') {
                if ((_a = error.detail) === null || _a === void 0 ? void 0 : _a.includes('email')) {
                    throw new common_1.ConflictException('邮箱已被使用');
                }
                if ((_b = error.detail) === null || _b === void 0 ? void 0 : _b.includes('username')) {
                    throw new common_1.ConflictException('用户名已存在');
                }
            }
            throw error;
        });
    }
    async login(loginDto) {
        const { username, password } = loginDto;
        const user = await this.userRepository.findOne({ where: { username } });
        if (!user) {
            throw new common_1.UnauthorizedException('用户名或密码错误');
        }
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
            throw new common_1.UnauthorizedException('用户名或密码错误');
        }
        if (user.approvalStatus === user_approval_status_enum_1.UserApprovalStatus.PENDING) {
            throw new common_1.UnauthorizedException('您的账户正在等待管理员审批，请耐心等待审批完成后再次尝试登录');
        }
        if (user.approvalStatus === user_approval_status_enum_1.UserApprovalStatus.REJECTED) {
            const rejectionReason = user.rejectionReason || '未提供拒绝原因';
            throw new common_1.UnauthorizedException(`您的账户申请已被拒绝，原因：${rejectionReason}。如有疑问，请联系管理员`);
        }
        if (!user.isActive) {
            throw new common_1.UnauthorizedException('您的账户已被禁用，请联系管理员');
        }
        const payload = { username: user.username, sub: user.id };
        const access_token = this.jwtService.sign(payload);
        const { password: _ } = user, result = __rest(user, ["password"]);
        return {
            access_token,
            user: result,
        };
    }
    async setUserRole(userId, setUserRoleDto) {
        const { role } = setUserRoleDto;
        const user = await this.userRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        user.role = role;
        await this.userRepository.save(user);
        const { password: _ } = user, userInfo = __rest(user, ["password"]);
        return userInfo;
    }
    async getUserById(userId) {
        const user = await this.userRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        const { password: _ } = user, userInfo = __rest(user, ["password"]);
        return userInfo;
    }
    async getUserPermissions(userId) {
        return this.getUserById(userId);
    }
    async getUsers(queryDto) {
        const { page = 1, limit = 10, role, username } = queryDto;
        const where = {};
        if (role) {
            where.role = role;
        }
        if (username) {
            where.username = (0, typeorm_2.Like)(`%${username}%`);
        }
        const [users, total] = await this.userRepository.findAndCount({
            where,
            skip: (page - 1) * limit,
            take: limit,
            order: { createdAt: 'DESC' },
        });
        const usersWithoutPassword = users.map(user => {
            const { password: _ } = user, userInfo = __rest(user, ["password"]);
            return userInfo;
        });
        return {
            total,
            page,
            limit,
            users: usersWithoutPassword,
        };
    }
    async isAdmin(userId) {
        const user = await this.userRepository.findOne({ where: { id: userId } });
        return (user === null || user === void 0 ? void 0 : user.role) === user_role_enum_1.UserRole.ADMIN || false;
    }
    async getPendingUsers(queryDto) {
        const { page = 1, limit = 10, username, email } = queryDto;
        const where = { approvalStatus: user_approval_status_enum_1.UserApprovalStatus.PENDING };
        if (username) {
            where.username = (0, typeorm_2.Like)(`%${username}%`);
        }
        if (email) {
            where.email = (0, typeorm_2.Like)(`%${email}%`);
        }
        const [users, total] = await this.userRepository.findAndCount({
            where,
            skip: (page - 1) * limit,
            take: limit,
            order: { createdAt: 'ASC' },
        });
        const usersResponse = users.map(user => {
            const { password: _ } = user, userInfo = __rest(user, ["password"]);
            return userInfo;
        });
        const totalPages = Math.ceil(total / limit);
        return {
            users: usersResponse,
            total,
            page,
            limit,
            totalPages,
        };
    }
    async approveUser(userId, approvalDto, adminId) {
        const { action, rejectionReason } = approvalDto;
        if (action === 'reject' && !rejectionReason) {
            throw new common_1.BadRequestException('拒绝用户申请时必须提供拒绝原因');
        }
        return await this.dataSource.transaction(async (transactionalEntityManager) => {
            console.log('开始事务，审批用户:', userId, '操作:', action);
            const user = await transactionalEntityManager.findOne(user_entity_1.User, { where: { id: userId } });
            if (!user) {
                throw new common_1.NotFoundException('用户不存在');
            }
            if (user.approvalStatus !== user_approval_status_enum_1.UserApprovalStatus.PENDING) {
                throw new common_1.BadRequestException(`用户当前状态为 ${user.approvalStatus}，无法重复审批`);
            }
            if (action === 'approve') {
                user.approvalStatus = user_approval_status_enum_1.UserApprovalStatus.APPROVED;
                user.approvedAt = new Date();
                user.approvedBy = adminId;
                user.rejectionReason = undefined;
            }
            else {
                user.approvalStatus = user_approval_status_enum_1.UserApprovalStatus.REJECTED;
                user.rejectionReason = rejectionReason;
                user.approvedAt = new Date();
                user.approvedBy = adminId;
                console.log('设置拒绝审批信息:', {
                    userId: user.id,
                    approvedAt: user.approvedAt,
                    approvedBy: user.approvedBy,
                    rejectionReason: user.rejectionReason
                });
            }
            console.log('开始执行SQL更新...');
            if (action === 'reject') {
                const result = await transactionalEntityManager.query(`
          UPDATE users 
          SET approval_status = $1, 
              approved_at = $2, 
              approved_by = $3, 
              rejection_reason = $4,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $5
        `, [
                    user_approval_status_enum_1.UserApprovalStatus.REJECTED,
                    new Date(),
                    adminId,
                    rejectionReason,
                    userId
                ]);
                console.log('拒绝SQL更新结果:', result);
            }
            else {
                const result = await transactionalEntityManager.query(`
          UPDATE users 
          SET approval_status = $1, 
              approved_at = $2, 
              approved_by = $3, 
              rejection_reason = NULL,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $4
        `, [
                    user_approval_status_enum_1.UserApprovalStatus.APPROVED,
                    new Date(),
                    adminId,
                    userId
                ]);
                console.log('批准SQL更新结果:', result);
            }
            const updatedUserResult = await transactionalEntityManager.query(`
        SELECT id, username, email, role, approval_status, approved_at, approved_by, rejection_reason, 
               api_calls_today, downloads_today, last_api_reset, avatar_url, is_active, 
               created_at, updated_at, deleted_at
        FROM users WHERE id = $1
      `, [userId]);
            if (!updatedUserResult || updatedUserResult.length === 0) {
                throw new common_1.NotFoundException('更新后用户不存在');
            }
            const updatedUser = updatedUserResult[0];
            console.log('保存后的用户信息:', {
                id: updatedUser.id,
                approvedAt: updatedUser.approved_at,
                approvedBy: updatedUser.approved_by,
                rejectionReason: updatedUser.rejection_reason
            });
            const userInfo = {
                id: updatedUser.id,
                username: updatedUser.username,
                email: updatedUser.email,
                role: updatedUser.role,
                approvalStatus: updatedUser.approval_status,
                approvedAt: updatedUser.approved_at,
                approvedBy: updatedUser.approved_by,
                rejectionReason: updatedUser.rejection_reason,
                apiCallsToday: updatedUser.api_calls_today,
                downloadsToday: updatedUser.downloads_today,
                lastApiReset: updatedUser.last_api_reset,
                avatarUrl: updatedUser.avatar_url,
                isActive: updatedUser.is_active,
                createdAt: updatedUser.created_at,
                updatedAt: updatedUser.updated_at,
                deletedAt: updatedUser.deleted_at
            };
            const message = action === 'approve' ? '用户审批通过' : '用户申请已拒绝';
            return {
                success: true,
                message,
                user: userInfo,
            };
        });
    }
    async getUserApprovalHistory(queryDto) {
        const { page = 1, limit = 10, role, username } = queryDto;
        const where = {};
        if (role) {
            where.role = role;
        }
        if (username) {
            where.username = (0, typeorm_2.Like)(`%${username}%`);
        }
        const [users, total] = await this.userRepository.findAndCount({
            where,
            skip: (page - 1) * limit,
            take: limit,
            order: { createdAt: 'DESC' },
        });
        const usersWithoutPassword = users.map(user => {
            const { password: _ } = user, userInfo = __rest(user, ["password"]);
            return userInfo;
        });
        return {
            total,
            page,
            limit,
            users: usersWithoutPassword,
        };
    }
    async deleteUser(userId) {
        const user = await this.userRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        if (user.role === 'admin') {
            throw new common_1.ForbiddenException('不能删除管理员用户');
        }
        await this.userRepository.softDelete(userId);
        return {
            success: true,
            message: '用户删除成功'
        };
    }
    async changePassword(userId, changePasswordDto) {
        const user = await this.userService.findOne(userId);
        if (!user) {
            throw new common_1.UnauthorizedException('用户不存在');
        }
        const isPasswordValid = await bcrypt.compare(changePasswordDto.currentPassword, user.password);
        if (!isPasswordValid) {
            throw new common_1.UnauthorizedException('当前密码错误');
        }
        if (changePasswordDto.currentPassword === changePasswordDto.newPassword) {
            throw new common_1.BadRequestException('新密码不能与当前密码相同');
        }
        const salt = await bcrypt.genSalt();
        const hashedPassword = await bcrypt.hash(changePasswordDto.newPassword, salt);
        await this.userService.update(userId, { password: hashedPassword });
        return { message: '密码修改成功' };
    }
};
AuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        jwt_1.JwtService,
        typeorm_2.DataSource,
        user_service_1.UserService])
], AuthService);
exports.AuthService = AuthService;
//# sourceMappingURL=auth.service.js.map