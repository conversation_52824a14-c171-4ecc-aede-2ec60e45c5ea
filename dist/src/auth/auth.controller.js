"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const auth_service_1 = require("./auth.service");
const auth_dto_1 = require("./dto/auth.dto");
const permission_dto_1 = require("./dto/permission.dto");
const user_approval_dto_1 = require("./dto/user-approval.dto");
const user_entity_1 = require("../entities/user.entity");
const public_decorator_1 = require("./decorators/public.decorator");
const current_user_decorator_1 = require("./decorators/current-user.decorator");
const jwt_auth_guard_1 = require("./guards/jwt-auth.guard");
const admin_guard_1 = require("./guards/admin.guard");
const user_delete_dto_1 = require("./dto/user-delete.dto");
const change_password_dto_1 = require("./dto/change-password.dto");
let AuthController = class AuthController {
    constructor(authService) {
        this.authService = authService;
    }
    async register(registerDto) {
        return this.authService.register(registerDto);
    }
    async login(loginDto) {
        return this.authService.login(loginDto);
    }
    async setUserRole(userId, setUserRoleDto) {
        return this.authService.setUserRole(userId, setUserRoleDto);
    }
    async getUserPermissions(userId, currentUser) {
        return this.authService.getUserPermissions(userId);
    }
    async getUsers(queryDto) {
        return this.authService.getUsers(queryDto);
    }
    async getProfile(currentUser) {
        const { password: _ } = currentUser, userInfo = __rest(currentUser, ["password"]);
        return userInfo;
    }
    async getPendingUsers(queryDto) {
        return this.authService.getPendingUsers(queryDto);
    }
    async approveUser(userId, approvalDto, currentUser) {
        return this.authService.approveUser(userId, approvalDto, currentUser.id);
    }
    async getUserApprovalHistory(queryDto) {
        return this.authService.getUserApprovalHistory(queryDto);
    }
    async deleteUser(userId) {
        return this.authService.deleteUser(userId);
    }
    async changePassword(currentUser, changePasswordDto) {
        return this.authService.changePassword(currentUser.id, changePasswordDto);
    }
};
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Post)('register'),
    (0, swagger_1.ApiOperation)({ summary: '用户注册', description: '创建新用户账号，注册成功后需要等待管理员审批才能登录' }),
    (0, swagger_1.ApiBody)({ type: auth_dto_1.RegisterDto }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '用户注册成功，等待管理员审批',
        schema: {
            example: {
                username: "testuser",
                email: "<EMAIL>",
                avatarUrl: null,
                id: 1,
                role: "free",
                approvalStatus: "pending",
                apiCallsToday: 0,
                downloadsToday: 0,
                lastApiReset: "2024-02-03T12:34:56.789Z",
                isActive: true,
                createdAt: "2024-02-03T12:34:56.789Z",
                updatedAt: "2024-02-03T12:34:56.789Z"
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数验证失败',
        schema: {
            example: {
                statusCode: 400,
                message: ["用户名长度不能小于4个字符", "密码长度不能小于6个字符", "请输入有效的电子邮箱地址"],
                error: "Bad Request"
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: '用户名或邮箱已存在',
        schema: {
            example: {
                statusCode: 409,
                message: "用户名已存在",
                error: "Conflict"
            }
        }
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_dto_1.RegisterDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "register", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Post)('login'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiTags)('认证'),
    (0, swagger_1.ApiOperation)({ summary: '用户登录', description: '使用用户名和密码登录' }),
    (0, swagger_1.ApiBody)({
        type: auth_dto_1.LoginDto,
        schema: {
            example: {
                username: "testuser",
                password: "password123"
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '登录成功',
        schema: {
            example: {
                access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                user: {
                    id: 1,
                    username: "testuser",
                    email: "<EMAIL>",
                    role: "free",
                    apiCallsToday: 0,
                    downloadsToday: 0,
                    lastApiReset: "2024-02-03T12:34:56.789Z",
                    avatarUrl: null,
                    isActive: true,
                    createdAt: "2024-02-03T12:34:56.789Z",
                    updatedAt: "2024-02-03T12:34:56.789Z"
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '用户名或密码错误，或账户未通过审批',
        schema: {
            examples: {
                wrongCredentials: {
                    summary: '用户名或密码错误',
                    value: {
                        statusCode: 401,
                        message: "用户名或密码错误",
                        error: "Unauthorized"
                    }
                },
                pendingApproval: {
                    summary: '账户等待审批',
                    value: {
                        statusCode: 401,
                        message: "您的账户正在等待管理员审批，请耐心等待审批完成后再次尝试登录",
                        error: "Unauthorized"
                    }
                },
                rejected: {
                    summary: '账户申请被拒绝',
                    value: {
                        statusCode: 401,
                        message: "您的账户申请已被拒绝，原因：申请信息不完整。如有疑问，请联系管理员",
                        error: "Unauthorized"
                    }
                },
                disabled: {
                    summary: '账户已被禁用',
                    value: {
                        statusCode: 401,
                        message: "您的账户已被禁用，请联系管理员",
                        error: "Unauthorized"
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数验证失败',
        schema: {
            example: {
                statusCode: 400,
                message: ["用户名不能为空", "密码不能为空"],
                error: "Bad Request"
            }
        }
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_dto_1.LoginDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "login", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_guard_1.AdminGuard),
    (0, common_1.Put)('users/:id/role'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({
        summary: '设置用户角色',
        description: '管理员设置指定用户的权限角色'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: '用户ID',
        type: 'number',
        example: 1
    }),
    (0, swagger_1.ApiBody)({ type: permission_dto_1.SetUserRoleDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '用户角色设置成功',
        type: permission_dto_1.UserPermissionResponseDto,
        schema: {
            example: {
                id: 1,
                username: "testuser",
                email: "<EMAIL>",
                role: "premium",
                isActive: true,
                createdAt: "2024-02-03T12:34:56.789Z",
                updatedAt: "2024-02-03T12:34:56.789Z"
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '未认证',
        schema: {
            example: {
                statusCode: 401,
                message: "Unauthorized",
                error: "Unauthorized"
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: '权限不足',
        schema: {
            example: {
                statusCode: 403,
                message: "只有管理员才能执行此操作",
                error: "Forbidden"
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '用户不存在',
        schema: {
            example: {
                statusCode: 404,
                message: "用户不存在",
                error: "Not Found"
            }
        }
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, permission_dto_1.SetUserRoleDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "setUserRole", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_guard_1.AdminOrOwnerGuard),
    (0, common_1.Get)('users/:id/permissions'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({
        summary: '获取用户权限信息',
        description: '获取指定用户的权限信息（管理员可查看任何用户，普通用户只能查看自己）'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: '用户ID',
        type: 'number',
        example: 1
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取用户权限信息成功',
        type: permission_dto_1.UserPermissionResponseDto,
        schema: {
            example: {
                id: 1,
                username: "testuser",
                email: "<EMAIL>",
                role: "premium",
                isActive: true,
                createdAt: "2024-02-03T12:34:56.789Z",
                updatedAt: "2024-02-03T12:34:56.789Z"
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '未认证',
        schema: {
            example: {
                statusCode: 401,
                message: "Unauthorized",
                error: "Unauthorized"
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: '权限不足',
        schema: {
            example: {
                statusCode: 403,
                message: "您只能访问自己的资源",
                error: "Forbidden"
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '用户不存在',
        schema: {
            example: {
                statusCode: 404,
                message: "用户不存在",
                error: "Not Found"
            }
        }
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, user_entity_1.User]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "getUserPermissions", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_guard_1.AdminGuard),
    (0, common_1.Get)('users'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({
        summary: '获取用户列表',
        description: '管理员获取系统中的用户列表，支持分页和筛选'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        description: '页码',
        required: false,
        type: 'number',
        example: 1
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        description: '每页数量',
        required: false,
        type: 'number',
        example: 10
    }),
    (0, swagger_1.ApiQuery)({
        name: 'role',
        description: '角色筛选',
        required: false,
        enum: ['admin', 'free', 'premium', 'enterprise', 'government'],
        example: 'premium'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'username',
        description: '用户名搜索',
        required: false,
        type: 'string',
        example: 'test'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取用户列表成功',
        type: permission_dto_1.UsersListResponseDto,
        schema: {
            example: {
                total: 100,
                page: 1,
                limit: 10,
                users: [
                    {
                        id: 1,
                        username: "testuser1",
                        email: "<EMAIL>",
                        role: "premium",
                        isActive: true,
                        createdAt: "2024-02-03T12:34:56.789Z",
                        updatedAt: "2024-02-03T12:34:56.789Z"
                    },
                    {
                        id: 2,
                        username: "testuser2",
                        email: "<EMAIL>",
                        role: "free",
                        isActive: true,
                        createdAt: "2024-02-03T12:34:56.789Z",
                        updatedAt: "2024-02-03T12:34:56.789Z"
                    }
                ]
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '未认证',
        schema: {
            example: {
                statusCode: 401,
                message: "Unauthorized",
                error: "Unauthorized"
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: '权限不足',
        schema: {
            example: {
                statusCode: 403,
                message: "只有管理员才能执行此操作",
                error: "Forbidden"
            }
        }
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [permission_dto_1.GetUsersQueryDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "getUsers", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('profile'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({
        summary: '获取当前用户信息',
        description: '获取当前登录用户的基本信息和权限'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取用户信息成功',
        type: permission_dto_1.UserPermissionResponseDto,
        schema: {
            example: {
                id: 1,
                username: "testuser",
                email: "<EMAIL>",
                role: "premium",
                isActive: true,
                createdAt: "2024-02-03T12:34:56.789Z",
                updatedAt: "2024-02-03T12:34:56.789Z"
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '未认证',
        schema: {
            example: {
                statusCode: 401,
                message: "Unauthorized",
                error: "Unauthorized"
            }
        }
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "getProfile", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_guard_1.AdminGuard),
    (0, common_1.Get)('admin/pending-users'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({
        summary: '获取待审批用户列表',
        description: '管理员获取系统中等待审批的用户列表'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        description: '页码',
        required: false,
        type: 'number',
        example: 1
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        description: '每页数量',
        required: false,
        type: 'number',
        example: 10
    }),
    (0, swagger_1.ApiQuery)({
        name: 'username',
        description: '用户名搜索',
        required: false,
        type: 'string',
        example: 'john'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'email',
        description: '邮箱搜索',
        required: false,
        type: 'string',
        example: '<EMAIL>'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取待审批用户列表成功',
        type: user_approval_dto_1.PendingUsersResponseDto,
        schema: {
            example: {
                users: [
                    {
                        id: 1,
                        username: "newuser1",
                        email: "<EMAIL>",
                        role: "free",
                        approvalStatus: "pending",
                        createdAt: "2024-01-15T10:00:00Z",
                        isActive: true
                    }
                ],
                total: 1,
                page: 1,
                limit: 10,
                totalPages: 1
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '未认证'
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: '只有管理员才能执行此操作'
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_approval_dto_1.PendingUsersQueryDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "getPendingUsers", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_guard_1.AdminGuard),
    (0, common_1.Put)('admin/users/:id/approval'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({
        summary: '审批用户申请',
        description: '管理员批准或拒绝用户注册申请'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: '待审批用户ID',
        type: 'number',
        example: 1
    }),
    (0, swagger_1.ApiBody)({
        type: user_approval_dto_1.UserApprovalDto,
        examples: {
            approve: {
                summary: '批准用户',
                value: {
                    action: 'approve'
                }
            },
            reject: {
                summary: '拒绝用户',
                value: {
                    action: 'reject',
                    rejectionReason: '申请信息不完整，请重新提交完整资料'
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '用户审批成功',
        type: user_approval_dto_1.ApprovalResultResponseDto,
        schema: {
            example: {
                success: true,
                message: '用户审批通过',
                user: {
                    id: 1,
                    username: "newuser1",
                    email: "<EMAIL>",
                    role: "free",
                    approvalStatus: "approved",
                    approvedAt: "2024-01-15T10:30:00Z",
                    approvedBy: 1,
                    createdAt: "2024-01-15T10:00:00Z",
                    isActive: true
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数错误',
        schema: {
            example: {
                statusCode: 400,
                message: '拒绝用户申请时必须提供拒绝原因',
                error: 'Bad Request'
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '未认证'
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: '只有管理员才能执行此操作'
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '用户不存在'
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, user_approval_dto_1.UserApprovalDto,
        user_entity_1.User]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "approveUser", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_guard_1.AdminGuard),
    (0, common_1.Get)('admin/user-approval-history'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({
        summary: '获取用户审批历史',
        description: '管理员查看所有用户的审批历史记录'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        description: '页码',
        required: false,
        type: 'number',
        example: 1
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        description: '每页数量',
        required: false,
        type: 'number',
        example: 10
    }),
    (0, swagger_1.ApiQuery)({
        name: 'role',
        description: '角色筛选',
        required: false,
        enum: ['admin', 'free', 'premium', 'enterprise', 'government'],
        example: 'free'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'username',
        description: '用户名搜索',
        required: false,
        type: 'string',
        example: 'user'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取审批历史成功',
        type: permission_dto_1.UsersListResponseDto
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '未认证'
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: '只有管理员才能执行此操作'
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [permission_dto_1.GetUsersQueryDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "getUserApprovalHistory", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_guard_1.AdminGuard),
    (0, common_1.Delete)('admin/users/:id/delete'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiTags)('认证'),
    (0, swagger_1.ApiOperation)({
        summary: '删除用户',
        description: '管理员删除指定用户（不能删除管理员用户）'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: '要删除的用户ID',
        type: 'number',
        example: 1
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '用户删除成功',
        type: user_delete_dto_1.DeleteUserResponseDto
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '未认证',
        schema: {
            example: {
                statusCode: 401,
                message: "Unauthorized",
                error: "Unauthorized"
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: '权限不足或尝试删除管理员',
        schema: {
            examples: {
                noPermission: {
                    summary: '权限不足',
                    value: {
                        statusCode: 403,
                        message: "只有管理员才能执行此操作",
                        error: "Forbidden"
                    }
                },
                deleteAdmin: {
                    summary: '尝试删除管理员',
                    value: {
                        statusCode: 403,
                        message: "不能删除管理员用户",
                        error: "Forbidden"
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '用户不存在',
        schema: {
            example: {
                statusCode: 404,
                message: "用户不存在",
                error: "Not Found"
            }
        }
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "deleteUser", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('change-password'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({
        summary: '修改密码',
        description: '用户修改自己的密码'
    }),
    (0, swagger_1.ApiBody)({ type: change_password_dto_1.ChangePasswordDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '密码修改成功',
        schema: {
            example: {
                message: '密码修改成功'
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '当前密码错误',
        schema: {
            example: {
                statusCode: 401,
                message: '当前密码错误',
                error: 'Unauthorized'
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '新密码不能与当前密码相同',
        schema: {
            example: {
                statusCode: 400,
                message: '新密码不能与当前密码相同',
                error: 'Bad Request'
            }
        }
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User,
        change_password_dto_1.ChangePasswordDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "changePassword", null);
AuthController = __decorate([
    (0, swagger_1.ApiTags)('认证'),
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [auth_service_1.AuthService])
], AuthController);
exports.AuthController = AuthController;
//# sourceMappingURL=auth.controller.js.map