"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PasswordValidator = void 0;
class PasswordValidator {
    static validate(password) {
        const errors = [];
        if (password.length < 8 || password.length > 20) {
            errors.push('密码长度必须在8-20个字符之间');
        }
        if (!/[A-Z]/.test(password)) {
            errors.push('密码必须包含至少一个大写字母');
        }
        if (!/[a-z]/.test(password)) {
            errors.push('密码必须包含至少一个小写字母');
        }
        if (!/[0-9]/.test(password)) {
            errors.push('密码必须包含至少一个数字');
        }
        if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
            errors.push('密码必须包含至少一个特殊字符(!@#$%^&*(),.?":{}|<>)');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
}
exports.PasswordValidator = PasswordValidator;
//# sourceMappingURL=password-validator.js.map