{"version": 3, "file": "test-simple.js", "sourceRoot": "", "sources": ["../../src/test-simple.ts"], "names": [], "mappings": "AAIA,SAAS,iBAAiB,CAAC,UAAkB;IAE3C,MAAM,iBAAiB,GAAkD;QACvE,qBAAqB,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,qBAAqB,EAAE;QAChE,iBAAiB,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,iBAAiB,EAAE;QACxD,oDAAoD,EAAE,EAAE,EAAE,EAAE,iBAAiB,EAAE,EAAE,EAAE,oDAAoD,EAAE;QACzI,4CAA4C,EAAE,EAAE,EAAE,EAAE,eAAe,EAAE,EAAE,EAAE,4CAA4C,EAAE;QACvH,wDAAwD,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE,EAAE,EAAE,wDAAwD,EAAE;QAC7I,wCAAwC,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,wCAAwC,EAAE;QAC3G,wCAAwC,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE,EAAE,EAAE,wCAAwC,EAAE;QAC9G,6CAA6C,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE,EAAE,EAAE,6CAA6C,EAAE;QACxH,4CAA4C,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,4CAA4C,EAAE;QACnH,gCAAgC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,gCAAgC,EAAE;QACzF,kBAAkB,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,kBAAkB,EAAE;QAC1D,qBAAqB,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,qBAAqB,EAAE;QAChE,2BAA2B,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,2BAA2B,EAAE;QAC7E,6BAA6B,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE,EAAE,EAAE,6BAA6B,EAAE;KACzF,CAAC;IAGF,MAAM,cAAc,GAAiE;QACnF,EAAE,OAAO,EAAE,sBAAsB,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,qBAAqB,EAAE,EAAE;QAC3F,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE;QACjE,EAAE,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,eAAe,EAAE,EAAE;QAChF,EAAE,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,kBAAkB,EAAE,EAAE;QACrF,EAAE,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,kBAAkB,EAAE,EAAE;QAC1F,EAAE,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,kBAAkB,EAAE,EAAE;QACxF,EAAE,OAAO,EAAE,sBAAsB,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,qBAAqB,EAAE,EAAE;QAC3F,EAAE,OAAO,EAAE,uCAAuC,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,6BAA6B,EAAE,EAAE;QACrH,EAAE,OAAO,EAAE,8BAA8B,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE,EAAE,EAAE,6BAA6B,EAAE,EAAE;KACpH,CAAC;IAGF,IAAI,iBAAiB,CAAC,UAAU,CAAC,EAAE;QACjC,OAAO,iBAAiB,CAAC,UAAU,CAAC,CAAC;KACtC;IAGD,KAAK,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,cAAc,EAAE;QACrD,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YAE5B,OAAO;gBACL,EAAE,EAAE,WAAW,CAAC,EAAE;gBAClB,EAAE,EAAE,UAAU;aACf,CAAC;SACH;KACF;IAGD,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAG9C,MAAM,SAAS,GAAG,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAErD,IAAI,SAAS,IAAI,CAAC,SAAS,EAAE;QAE3B,IAAI,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACrC,OAAO;gBACL,EAAE,EAAE,KAAK,UAAU,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;gBAC1D,EAAE,EAAE,UAAU;aACf,CAAC;SACH;aAEI,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACtC,OAAO;gBACL,EAAE,EAAE,KAAK,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;gBACrD,EAAE,EAAE,UAAU;aACf,CAAC;SACH;aACI,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACtC,OAAO;gBACL,EAAE,EAAE,KAAK,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;gBACrD,EAAE,EAAE,UAAU;aACf,CAAC;SACH;aACI,IAAI,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,OAAO;gBACL,EAAE,EAAE,KAAK,UAAU,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;gBACnD,EAAE,EAAE,UAAU;aACf,CAAC;SACH;aACI,IAAI,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACrC,OAAO;gBACL,EAAE,EAAE,KAAK,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;gBACpD,EAAE,EAAE,UAAU;aACf,CAAC;SACH;aACI,IAAI,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACnC,OAAO;gBACL,EAAE,EAAE,KAAK,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;gBAClD,EAAE,EAAE,UAAU;aACf,CAAC;SACH;aACI,IAAI,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACnC,OAAO;gBACL,EAAE,EAAE,KAAK,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;gBAClD,EAAE,EAAE,UAAU;aACf,CAAC;SACH;aACI,IAAI,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACrC,OAAO;gBACL,EAAE,EAAE,KAAK,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;gBACpD,EAAE,EAAE,UAAU;aACf,CAAC;SACH;aACI,IAAI,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,OAAO;gBACL,EAAE,EAAE,MAAM,UAAU,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;gBACpD,EAAE,EAAE,UAAU;aACf,CAAC;SACH;aACI,IAAI,qBAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YAC/C,OAAO;gBACL,EAAE,EAAE,KAAK,UAAU,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;gBAC9D,EAAE,EAAE,UAAU;aACf,CAAC;SACH;aACI,IAAI,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACrC,OAAO;gBACL,EAAE,EAAE,KAAK,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE;gBACpD,EAAE,EAAE,UAAU;aACf,CAAC;SACH;aACI;YAEH,OAAO,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC;SAC3C;KACF;SAAM,IAAI,SAAS,IAAI,CAAC,SAAS,EAAE;QAElC,OAAO,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC;KAC3C;SAAM,IAAI,SAAS,IAAI,SAAS,EAAE;QAEjC,OAAO,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC;KAC3C;SAAM;QAEL,OAAO,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC;KAC3C;AACH,CAAC;AAGD,MAAM,eAAe,GAAG;IACtB,qBAAqB;IACrB,iBAAiB;IACjB,oDAAoD;IACpD,4CAA4C;IAC5C,wDAAwD;IACxD,wCAAwC;IACxC,wCAAwC;IACxC,6CAA6C;IAC7C,4CAA4C;IAC5C,gCAAgC;IAChC,kBAAkB;IAClB,qBAAqB;IACrB,2BAA2B;IAC3B,6BAA6B;CAC9B,CAAC;AAGF,MAAM,OAAO,GAAG,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;IAC/C,MAAM,WAAW,GAAG,iBAAiB,CAAC,UAAU,CAAC,CAAC;IAClD,OAAO;QACL,QAAQ,EAAE,UAAU;QACpB,EAAE,EAAE,WAAW,CAAC,EAAE;QAClB,EAAE,EAAE,WAAW,CAAC,EAAE;KACnB,CAAC;AACJ,CAAC,CAAC,CAAC;AAGH,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AACvB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC"}