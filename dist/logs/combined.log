{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T10:04:18.153Z"}
{"context":"TranslationService","level":"info","message":"加载翻译配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-07-03T10:04:18.175Z"}
{"context":"TranslationService","level":"info","message":"加载主题提取配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-07-03T10:04:18.175Z"}
{"context":"TranslationService","level":"info","message":"翻译服务已初始化 - 翻译模型: qwen-turbo, 主题提取模型: qwen-turbo","timestamp":"2025-07-03T10:04:18.177Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-03T10:04:18.179Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T10:04:18.179Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-03T10:04:18.179Z"}
{"context":"LLMConfigService","level":"info","message":"LLM配置管理服务初始化完成","timestamp":"2025-07-03T10:04:18.179Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T10:04:18.180Z"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized","timestamp":"2025-07-03T10:04:18.181Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T10:04:18.181Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T10:04:18.181Z"}
{"context":"InstanceLoader","level":"info","message":"LLMConfigModule dependencies initialized","timestamp":"2025-07-03T10:04:18.181Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-03T10:04:18.182Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-03T10:04:18.182Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-03T10:04:18.182Z"}
{"context":"InstanceLoader","level":"info","message":"ElasticsearchModule dependencies initialized","timestamp":"2025-07-03T10:04:18.184Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T10:04:18.184Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"TLE查询配置加载完成: 时间窗口=30分钟, 最大记录数=50000","timestamp":"2025-07-03T10:04:18.186Z"}
{"context":"InstanceLoader","level":"info","message":"OrbitAnalysisModule dependencies initialized","timestamp":"2025-07-03T10:04:18.187Z"}
{"context":"NewsSchedulerService","level":"info","message":"新闻定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 5,12,18 * * *\",\"timezone\":\"Asia/Shanghai\"}","timestamp":"2025-07-03T10:04:18.188Z"}
{"context":"InstanceLoader","level":"info","message":"ESModule dependencies initialized","timestamp":"2025-07-03T10:04:18.189Z"}
{"context":"InstanceLoader","level":"info","message":"NewsSchedulerModule dependencies initialized","timestamp":"2025-07-03T10:04:18.189Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteTilesModule dependencies initialized","timestamp":"2025-07-03T10:04:18.189Z"}
{"context":"ElasticsearchRocketService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-03T10:04:18.223Z"}
{"context":"ElasticsearchConstellationService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-03T10:04:18.224Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-03T10:04:18.224Z"}
{"context":"ElasticsearchSatelliteService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-03T10:04:18.224Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized","timestamp":"2025-07-03T10:04:18.226Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-03T10:04:18.227Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-03T10:04:18.227Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-03T10:04:18.227Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-03T10:04:18.227Z"}
{"context":"InstanceLoader","level":"info","message":"UserModule dependencies initialized","timestamp":"2025-07-03T10:04:18.227Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T10:04:18.228Z"}
{"context":"SatelliteSchedulerService","level":"info","message":"卫星数据定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 3 * * *\",\"timezone\":\"Asia/Shanghai\",\"saveToDatabase\":true}","timestamp":"2025-07-03T10:04:18.228Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T10:04:18.229Z"}
{"context":"InstanceLoader","level":"info","message":"AggregationTaskModule dependencies initialized","timestamp":"2025-07-03T10:04:18.229Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-07-03T10:04:18.230Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteModule dependencies initialized","timestamp":"2025-07-03T10:04:18.230Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteSchedulerModule dependencies initialized","timestamp":"2025-07-03T10:04:18.230Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteAggregationController {/local/satellite-aggregation}:","timestamp":"2025-07-03T10:04:18.303Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/status, GET} route","timestamp":"2025-07-03T10:04:18.305Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/running, GET} route","timestamp":"2025-07-03T10:04:18.305Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/auth}:","timestamp":"2025-07-03T10:04:18.305Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/register, POST} route","timestamp":"2025-07-03T10:04:18.305Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/login, POST} route","timestamp":"2025-07-03T10:04:18.305Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/role, PUT} route","timestamp":"2025-07-03T10:04:18.306Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/permissions, GET} route","timestamp":"2025-07-03T10:04:18.306Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users, GET} route","timestamp":"2025-07-03T10:04:18.306Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/profile, GET} route","timestamp":"2025-07-03T10:04:18.306Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/pending-users, GET} route","timestamp":"2025-07-03T10:04:18.306Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/approval, PUT} route","timestamp":"2025-07-03T10:04:18.306Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/user-approval-history, GET} route","timestamp":"2025-07-03T10:04:18.306Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/delete, DELETE} route","timestamp":"2025-07-03T10:04:18.306Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/change-password, PUT} route","timestamp":"2025-07-03T10:04:18.307Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisEventController {/api/debris-events}:","timestamp":"2025-07-03T10:04:18.307Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/debris-events/search, POST} route","timestamp":"2025-07-03T10:04:18.307Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchConstellationController {/constellation}:","timestamp":"2025-07-03T10:04:18.307Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/search, POST} route","timestamp":"2025-07-03T10:04:18.307Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/names, GET} route","timestamp":"2025-07-03T10:04:18.307Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/organizations, GET} route","timestamp":"2025-07-03T10:04:18.307Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/purposes, GET} route","timestamp":"2025-07-03T10:04:18.307Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchCorrelationController {/correlation}:","timestamp":"2025-07-03T10:04:18.307Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/debris-to-event, POST} route","timestamp":"2025-07-03T10:04:18.307Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/event-to-debris, POST} route","timestamp":"2025-07-03T10:04:18.307Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisController {/debris}:","timestamp":"2025-07-03T10:04:18.307Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/search, POST} route","timestamp":"2025-07-03T10:04:18.308Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/names, GET} route","timestamp":"2025-07-03T10:04:18.308Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/object-classes, GET} route","timestamp":"2025-07-03T10:04:18.308Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/missions, GET} route","timestamp":"2025-07-03T10:04:18.308Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/countries, GET} route","timestamp":"2025-07-03T10:04:18.308Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/debug-query-dto, GET} route","timestamp":"2025-07-03T10:04:18.308Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchSatelliteController {/satellite}:","timestamp":"2025-07-03T10:04:18.308Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/search, POST} route","timestamp":"2025-07-03T10:04:18.308Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/names, GET} route","timestamp":"2025-07-03T10:04:18.308Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/statuses, GET} route","timestamp":"2025-07-03T10:04:18.308Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/test-direct-query, POST} route","timestamp":"2025-07-03T10:04:18.308Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchFreqController {/freq}:","timestamp":"2025-07-03T10:04:18.309Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/freq/search, POST} route","timestamp":"2025-07-03T10:04:18.309Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchOrbitController {/orbit}:","timestamp":"2025-07-03T10:04:18.309Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/search, POST} route","timestamp":"2025-07-03T10:04:18.309Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle, POST} route","timestamp":"2025-07-03T10:04:18.310Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle/all, POST} route","timestamp":"2025-07-03T10:04:18.310Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, GET} route","timestamp":"2025-07-03T10:04:18.310Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, PUT} route","timestamp":"2025-07-03T10:04:18.310Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLoopholeController {/loophole}:","timestamp":"2025-07-03T10:04:18.310Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/loophole/search, POST} route","timestamp":"2025-07-03T10:04:18.310Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchNewsController {/api/es/news}:","timestamp":"2025-07-03T10:04:18.310Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/indices, GET} route","timestamp":"2025-07-03T10:04:18.310Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translation-status, GET} route","timestamp":"2025-07-03T10:04:18.310Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translate, POST} route","timestamp":"2025-07-03T10:04:18.314Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/api-stats, GET} route","timestamp":"2025-07-03T10:04:18.314Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failure-stats, GET} route","timestamp":"2025-07-03T10:04:18.314Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/reset-failure-stats, POST} route","timestamp":"2025-07-03T10:04:18.315Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failed-translations, GET} route","timestamp":"2025-07-03T10:04:18.315Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/retry-failed, POST} route","timestamp":"2025-07-03T10:04:18.315Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/extract-themes, POST} route","timestamp":"2025-07-03T10:04:18.315Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/list, POST} route","timestamp":"2025-07-03T10:04:18.315Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/hot-themes, GET} route","timestamp":"2025-07-03T10:04:18.315Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLaunchController {/api/es/launch}:","timestamp":"2025-07-03T10:04:18.315Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/search, POST} route","timestamp":"2025-07-03T10:04:18.315Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/rocket-names, GET} route","timestamp":"2025-07-03T10:04:18.315Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/site-names, GET} route","timestamp":"2025-07-03T10:04:18.315Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/providers, GET} route","timestamp":"2025-07-03T10:04:18.315Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/cospar, POST} route","timestamp":"2025-07-03T10:04:18.315Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-names, GET} route","timestamp":"2025-07-03T10:04:18.315Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/all-launch-sites, GET} route","timestamp":"2025-07-03T10:04:18.315Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-info, GET} route","timestamp":"2025-07-03T10:04:18.315Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/service-providers/search, POST} route","timestamp":"2025-07-03T10:04:18.316Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchRocketController {/api/es/rocket}:","timestamp":"2025-07-03T10:04:18.316Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/rocket/search, POST} route","timestamp":"2025-07-03T10:04:18.316Z"}
{"context":"RoutesResolver","level":"info","message":"OrbitCalculatorController {/api/orbit-calculator}:","timestamp":"2025-07-03T10:04:18.316Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/orbit-calculator/calculate-positions, POST} route","timestamp":"2025-07-03T10:04:18.316Z"}
{"context":"RoutesResolver","level":"info","message":"PassAnalysisController {/api/pass-analysis}:","timestamp":"2025-07-03T10:04:18.316Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/pass-analysis/pass-analysis, POST} route","timestamp":"2025-07-03T10:04:18.316Z"}
{"context":"RoutesResolver","level":"info","message":"ConjunctionAnalysisController {/api/conjunction-analysis}:","timestamp":"2025-07-03T10:04:18.316Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/conjunction-analysis/conjunction-analysis, POST} route","timestamp":"2025-07-03T10:04:18.316Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteController {/local/satellite}:","timestamp":"2025-07-03T10:04:18.316Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/search, POST} route","timestamp":"2025-07-03T10:04:18.316Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/names, GET} route","timestamp":"2025-07-03T10:04:18.316Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/statuses, GET} route","timestamp":"2025-07-03T10:04:18.316Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync, POST} route","timestamp":"2025-07-03T10:04:18.316Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync-all, POST} route","timestamp":"2025-07-03T10:04:18.316Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate, POST} route","timestamp":"2025-07-03T10:04:18.316Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/clear, POST} route","timestamp":"2025-07-03T10:04:18.316Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-classes-local, GET} route","timestamp":"2025-07-03T10:04:18.317Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-types-local, GET} route","timestamp":"2025-07-03T10:04:18.317Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/:id, GET} route","timestamp":"2025-07-03T10:04:18.317Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite, GET} route","timestamp":"2025-07-03T10:04:18.317Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-aggregation, POST} route","timestamp":"2025-07-03T10:04:18.317Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-custom-aggregation, POST} route","timestamp":"2025-07-03T10:04:18.317Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate-all, POST} route","timestamp":"2025-07-03T10:04:18.317Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/incremental-aggregate, POST} route","timestamp":"2025-07-03T10:04:18.317Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-orbit-info, POST} route","timestamp":"2025-07-03T10:04:18.317Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-users, POST} route","timestamp":"2025-07-03T10:04:18.317Z"}
{"context":"RoutesResolver","level":"info","message":"ConstellationController {/local/constellation}:","timestamp":"2025-07-03T10:04:18.317Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation, GET} route","timestamp":"2025-07-03T10:04:18.317Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/update, POST} route","timestamp":"2025-07-03T10:04:18.317Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/with-tle, GET} route","timestamp":"2025-07-03T10:04:18.317Z"}
{"context":"RoutesResolver","level":"info","message":"AggregationTaskController {/tasks/aggregation}:","timestamp":"2025-07-03T10:04:18.317Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, GET} route","timestamp":"2025-07-03T10:04:18.317Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/latest, GET} route","timestamp":"2025-07-03T10:04:18.318Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/running, GET} route","timestamp":"2025-07-03T10:04:18.318Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id, GET} route","timestamp":"2025-07-03T10:04:18.318Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, POST} route","timestamp":"2025-07-03T10:04:18.318Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/start, POST} route","timestamp":"2025-07-03T10:04:18.318Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/update-progress, POST} route","timestamp":"2025-07-03T10:04:18.318Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/complete, POST} route","timestamp":"2025-07-03T10:04:18.318Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/fail, POST} route","timestamp":"2025-07-03T10:04:18.318Z"}
{"context":"RoutesResolver","level":"info","message":"DatabaseController {/api/v1/database}:","timestamp":"2025-07-03T10:04:18.318Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/query, POST} route","timestamp":"2025-07-03T10:04:18.318Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/filter-satellites, POST} route","timestamp":"2025-07-03T10:04:18.318Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-users, GET} route","timestamp":"2025-07-03T10:04:18.318Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-purposes, GET} route","timestamp":"2025-07-03T10:04:18.318Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-contractors, GET} route","timestamp":"2025-07-03T10:04:18.318Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-sites, GET} route","timestamp":"2025-07-03T10:04:18.318Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-vehicles, GET} route","timestamp":"2025-07-03T10:04:18.318Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteTilesController {/tiles}:","timestamp":"2025-07-03T10:04:18.318Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/generate, POST} route","timestamp":"2025-07-03T10:04:18.319Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/status, GET} route","timestamp":"2025-07-03T10:04:18.319Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/satellites, GET} route","timestamp":"2025-07-03T10:04:18.319Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/metadata, GET} route","timestamp":"2025-07-03T10:04:18.319Z"}
{"context":"RoutesResolver","level":"info","message":"NewsSchedulerController {/api/news-scheduler}:","timestamp":"2025-07-03T10:04:18.319Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/status, GET} route","timestamp":"2025-07-03T10:04:18.319Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/trigger, POST} route","timestamp":"2025-07-03T10:04:18.319Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/config, POST} route","timestamp":"2025-07-03T10:04:18.320Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/stop, POST} route","timestamp":"2025-07-03T10:04:18.320Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/cron-expressions, GET} route","timestamp":"2025-07-03T10:04:18.320Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteSchedulerController {/api/satellite-scheduler}:","timestamp":"2025-07-03T10:04:18.321Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/status, GET} route","timestamp":"2025-07-03T10:04:18.321Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/trigger, POST} route","timestamp":"2025-07-03T10:04:18.321Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/config, POST} route","timestamp":"2025-07-03T10:04:18.321Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/stop, POST} route","timestamp":"2025-07-03T10:04:18.321Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/cron-expressions, GET} route","timestamp":"2025-07-03T10:04:18.321Z"}
{"context":"RoutesResolver","level":"info","message":"LLMConfigController {/llm-config}:","timestamp":"2025-07-03T10:04:18.321Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config, GET} route","timestamp":"2025-07-03T10:04:18.321Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/:configType, GET} route","timestamp":"2025-07-03T10:04:18.322Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config, PUT} route","timestamp":"2025-07-03T10:04:18.322Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/test, POST} route","timestamp":"2025-07-03T10:04:18.322Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/reset, POST} route","timestamp":"2025-07-03T10:04:18.322Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/stats/summary, GET} route","timestamp":"2025-07-03T10:04:18.322Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/stats, DELETE} route","timestamp":"2025-07-03T10:04:18.322Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/export/all, GET} route","timestamp":"2025-07-03T10:04:18.322Z"}
{"context":"DatabaseService","level":"info","message":"正在检查并启用pg_trgm扩展...","timestamp":"2025-07-03T10:04:18.329Z"}
{"context":"ElasticsearchLaunchService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-03T10:04:18.331Z"}
{"context":"ElasticsearchBaseService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-03T10:04:18.332Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-03T10:04:18.332Z"}
{"context":"ElasticsearchDebrisEventService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-03T10:04:18.332Z"}
{"context":"DatabaseService","level":"info","message":"pg_trgm扩展已启用","timestamp":"2025-07-03T10:04:18.337Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-07-03T10:04:18.349Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"开始轨道信息查询...","timestamp":"2025-07-03T10:04:24.189Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"开始轨道信息查询...","timestamp":"2025-07-03T10:04:24.249Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"开始轨道信息查询...","timestamp":"2025-07-03T10:04:24.269Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"开始轨道信息查询...","timestamp":"2025-07-03T10:04:24.289Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"开始轨道信息查询...","timestamp":"2025-07-03T10:04:24.321Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"开始轨道信息查询...","timestamp":"2025-07-03T10:10:15.215Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-07-03T10:10:15.333Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"找到12个新闻索引","timestamp":"2025-07-03T10:10:17.763Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"开始轨道信息查询...","timestamp":"2025-07-03T10:10:37.013Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"开始轨道信息查询...","timestamp":"2025-07-03T10:10:59.441Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"开始轨道信息查询...","timestamp":"2025-07-03T10:10:59.463Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-03T10:20:47.902Z"}
{"context":"TranslationService","level":"info","message":"加载翻译配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-07-03T10:20:47.921Z"}
{"context":"TranslationService","level":"info","message":"加载主题提取配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-07-03T10:20:47.921Z"}
{"context":"TranslationService","level":"info","message":"翻译服务已初始化 - 翻译模型: qwen-turbo, 主题提取模型: qwen-turbo","timestamp":"2025-07-03T10:20:47.923Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-03T10:20:47.923Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-03T10:20:47.924Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-03T10:20:47.924Z"}
{"context":"LLMConfigService","level":"info","message":"LLM配置管理服务初始化完成","timestamp":"2025-07-03T10:20:47.924Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-03T10:20:47.924Z"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized","timestamp":"2025-07-03T10:20:47.924Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T10:20:47.925Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-03T10:20:47.925Z"}
{"context":"InstanceLoader","level":"info","message":"LLMConfigModule dependencies initialized","timestamp":"2025-07-03T10:20:47.925Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-03T10:20:47.925Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-03T10:20:47.925Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-03T10:20:47.925Z"}
{"context":"InstanceLoader","level":"info","message":"ElasticsearchModule dependencies initialized","timestamp":"2025-07-03T10:20:47.927Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-03T10:20:47.928Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"TLE查询配置加载完成: 时间窗口=30分钟, 最大记录数=50000","timestamp":"2025-07-03T10:20:47.930Z"}
{"context":"InstanceLoader","level":"info","message":"OrbitAnalysisModule dependencies initialized","timestamp":"2025-07-03T10:20:47.931Z"}
{"context":"NewsSchedulerService","level":"info","message":"新闻定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 5,12,18 * * *\",\"timezone\":\"Asia/Shanghai\"}","timestamp":"2025-07-03T10:20:47.931Z"}
{"context":"InstanceLoader","level":"info","message":"ESModule dependencies initialized","timestamp":"2025-07-03T10:20:47.932Z"}
{"context":"InstanceLoader","level":"info","message":"NewsSchedulerModule dependencies initialized","timestamp":"2025-07-03T10:20:47.932Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteTilesModule dependencies initialized","timestamp":"2025-07-03T10:20:47.932Z"}
{"context":"ElasticsearchConstellationService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-03T10:20:47.959Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-03T10:20:47.959Z"}
{"context":"ElasticsearchBaseService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-03T10:20:47.959Z"}
{"context":"ElasticsearchRocketService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-03T10:20:47.959Z"}
{"context":"ElasticsearchLaunchService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-03T10:20:47.959Z"}
{"context":"ElasticsearchDebrisEventService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-03T10:20:47.959Z"}
{"context":"ElasticsearchSatelliteService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-03T10:20:47.960Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-03T10:20:47.960Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized","timestamp":"2025-07-03T10:20:47.985Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-03T10:20:47.985Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-03T10:20:47.985Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-03T10:20:47.985Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-03T10:20:47.985Z"}
{"context":"InstanceLoader","level":"info","message":"UserModule dependencies initialized","timestamp":"2025-07-03T10:20:47.986Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-03T10:20:47.986Z"}
{"context":"SatelliteSchedulerService","level":"info","message":"卫星数据定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 3 * * *\",\"timezone\":\"Asia/Shanghai\",\"saveToDatabase\":true}","timestamp":"2025-07-03T10:20:47.986Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-03T10:20:47.987Z"}
{"context":"InstanceLoader","level":"info","message":"AggregationTaskModule dependencies initialized","timestamp":"2025-07-03T10:20:47.987Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-07-03T10:20:47.987Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteModule dependencies initialized","timestamp":"2025-07-03T10:20:47.987Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteSchedulerModule dependencies initialized","timestamp":"2025-07-03T10:20:47.987Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteAggregationController {/local/satellite-aggregation}:","timestamp":"2025-07-03T10:20:48.059Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/status, GET} route","timestamp":"2025-07-03T10:20:48.061Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/running, GET} route","timestamp":"2025-07-03T10:20:48.061Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/auth}:","timestamp":"2025-07-03T10:20:48.061Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/register, POST} route","timestamp":"2025-07-03T10:20:48.061Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/login, POST} route","timestamp":"2025-07-03T10:20:48.061Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/role, PUT} route","timestamp":"2025-07-03T10:20:48.062Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/permissions, GET} route","timestamp":"2025-07-03T10:20:48.062Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users, GET} route","timestamp":"2025-07-03T10:20:48.062Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/profile, GET} route","timestamp":"2025-07-03T10:20:48.062Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/pending-users, GET} route","timestamp":"2025-07-03T10:20:48.062Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/approval, PUT} route","timestamp":"2025-07-03T10:20:48.063Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/user-approval-history, GET} route","timestamp":"2025-07-03T10:20:48.063Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/delete, DELETE} route","timestamp":"2025-07-03T10:20:48.063Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/change-password, PUT} route","timestamp":"2025-07-03T10:20:48.063Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisEventController {/api/debris-events}:","timestamp":"2025-07-03T10:20:48.063Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/debris-events/search, POST} route","timestamp":"2025-07-03T10:20:48.063Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchConstellationController {/constellation}:","timestamp":"2025-07-03T10:20:48.063Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/search, POST} route","timestamp":"2025-07-03T10:20:48.064Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/names, GET} route","timestamp":"2025-07-03T10:20:48.064Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/organizations, GET} route","timestamp":"2025-07-03T10:20:48.064Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/purposes, GET} route","timestamp":"2025-07-03T10:20:48.064Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchCorrelationController {/correlation}:","timestamp":"2025-07-03T10:20:48.064Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/debris-to-event, POST} route","timestamp":"2025-07-03T10:20:48.064Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/event-to-debris, POST} route","timestamp":"2025-07-03T10:20:48.064Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisController {/debris}:","timestamp":"2025-07-03T10:20:48.064Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/search, POST} route","timestamp":"2025-07-03T10:20:48.064Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/names, GET} route","timestamp":"2025-07-03T10:20:48.065Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/object-classes, GET} route","timestamp":"2025-07-03T10:20:48.065Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/missions, GET} route","timestamp":"2025-07-03T10:20:48.065Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/countries, GET} route","timestamp":"2025-07-03T10:20:48.065Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/debug-query-dto, GET} route","timestamp":"2025-07-03T10:20:48.065Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchSatelliteController {/satellite}:","timestamp":"2025-07-03T10:20:48.065Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/search, POST} route","timestamp":"2025-07-03T10:20:48.065Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/names, GET} route","timestamp":"2025-07-03T10:20:48.066Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/statuses, GET} route","timestamp":"2025-07-03T10:20:48.066Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/test-direct-query, POST} route","timestamp":"2025-07-03T10:20:48.066Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchFreqController {/freq}:","timestamp":"2025-07-03T10:20:48.066Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/freq/search, POST} route","timestamp":"2025-07-03T10:20:48.066Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchOrbitController {/orbit}:","timestamp":"2025-07-03T10:20:48.066Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/search, POST} route","timestamp":"2025-07-03T10:20:48.066Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle, POST} route","timestamp":"2025-07-03T10:20:48.067Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle/all, POST} route","timestamp":"2025-07-03T10:20:48.067Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, GET} route","timestamp":"2025-07-03T10:20:48.067Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, PUT} route","timestamp":"2025-07-03T10:20:48.067Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLoopholeController {/loophole}:","timestamp":"2025-07-03T10:20:48.067Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/loophole/search, POST} route","timestamp":"2025-07-03T10:20:48.067Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchNewsController {/api/es/news}:","timestamp":"2025-07-03T10:20:48.067Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/indices, GET} route","timestamp":"2025-07-03T10:20:48.067Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translation-status, GET} route","timestamp":"2025-07-03T10:20:48.067Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translate, POST} route","timestamp":"2025-07-03T10:20:48.067Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/api-stats, GET} route","timestamp":"2025-07-03T10:20:48.068Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failure-stats, GET} route","timestamp":"2025-07-03T10:20:48.068Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/reset-failure-stats, POST} route","timestamp":"2025-07-03T10:20:48.068Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failed-translations, GET} route","timestamp":"2025-07-03T10:20:48.068Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/retry-failed, POST} route","timestamp":"2025-07-03T10:20:48.068Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/extract-themes, POST} route","timestamp":"2025-07-03T10:20:48.068Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/list, POST} route","timestamp":"2025-07-03T10:20:48.068Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/hot-themes, GET} route","timestamp":"2025-07-03T10:20:48.068Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLaunchController {/api/es/launch}:","timestamp":"2025-07-03T10:20:48.068Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/search, POST} route","timestamp":"2025-07-03T10:20:48.068Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/rocket-names, GET} route","timestamp":"2025-07-03T10:20:48.068Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/site-names, GET} route","timestamp":"2025-07-03T10:20:48.068Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/providers, GET} route","timestamp":"2025-07-03T10:20:48.069Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/cospar, POST} route","timestamp":"2025-07-03T10:20:48.069Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-names, GET} route","timestamp":"2025-07-03T10:20:48.069Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/all-launch-sites, GET} route","timestamp":"2025-07-03T10:20:48.069Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-info, GET} route","timestamp":"2025-07-03T10:20:48.069Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/service-providers/search, POST} route","timestamp":"2025-07-03T10:20:48.069Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchRocketController {/api/es/rocket}:","timestamp":"2025-07-03T10:20:48.069Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/rocket/search, POST} route","timestamp":"2025-07-03T10:20:48.069Z"}
{"context":"RoutesResolver","level":"info","message":"OrbitCalculatorController {/api/orbit-calculator}:","timestamp":"2025-07-03T10:20:48.069Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/orbit-calculator/calculate-positions, POST} route","timestamp":"2025-07-03T10:20:48.069Z"}
{"context":"RoutesResolver","level":"info","message":"PassAnalysisController {/api/pass-analysis}:","timestamp":"2025-07-03T10:20:48.069Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/pass-analysis/pass-analysis, POST} route","timestamp":"2025-07-03T10:20:48.070Z"}
{"context":"RoutesResolver","level":"info","message":"ConjunctionAnalysisController {/api/conjunction-analysis}:","timestamp":"2025-07-03T10:20:48.070Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/conjunction-analysis/conjunction-analysis, POST} route","timestamp":"2025-07-03T10:20:48.070Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteController {/local/satellite}:","timestamp":"2025-07-03T10:20:48.071Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/search, POST} route","timestamp":"2025-07-03T10:20:48.071Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/names, GET} route","timestamp":"2025-07-03T10:20:48.071Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/statuses, GET} route","timestamp":"2025-07-03T10:20:48.071Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync, POST} route","timestamp":"2025-07-03T10:20:48.071Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync-all, POST} route","timestamp":"2025-07-03T10:20:48.071Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate, POST} route","timestamp":"2025-07-03T10:20:48.071Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/clear, POST} route","timestamp":"2025-07-03T10:20:48.071Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-classes-local, GET} route","timestamp":"2025-07-03T10:20:48.071Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-types-local, GET} route","timestamp":"2025-07-03T10:20:48.072Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/:id, GET} route","timestamp":"2025-07-03T10:20:48.072Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite, GET} route","timestamp":"2025-07-03T10:20:48.072Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-aggregation, POST} route","timestamp":"2025-07-03T10:20:48.072Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-custom-aggregation, POST} route","timestamp":"2025-07-03T10:20:48.072Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate-all, POST} route","timestamp":"2025-07-03T10:20:48.072Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/incremental-aggregate, POST} route","timestamp":"2025-07-03T10:20:48.072Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-orbit-info, POST} route","timestamp":"2025-07-03T10:20:48.072Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-users, POST} route","timestamp":"2025-07-03T10:20:48.072Z"}
{"context":"RoutesResolver","level":"info","message":"ConstellationController {/local/constellation}:","timestamp":"2025-07-03T10:20:48.072Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation, GET} route","timestamp":"2025-07-03T10:20:48.072Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/update, POST} route","timestamp":"2025-07-03T10:20:48.073Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/with-tle, GET} route","timestamp":"2025-07-03T10:20:48.073Z"}
{"context":"RoutesResolver","level":"info","message":"AggregationTaskController {/tasks/aggregation}:","timestamp":"2025-07-03T10:20:48.073Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, GET} route","timestamp":"2025-07-03T10:20:48.073Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/latest, GET} route","timestamp":"2025-07-03T10:20:48.073Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/running, GET} route","timestamp":"2025-07-03T10:20:48.073Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id, GET} route","timestamp":"2025-07-03T10:20:48.073Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, POST} route","timestamp":"2025-07-03T10:20:48.073Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/start, POST} route","timestamp":"2025-07-03T10:20:48.073Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/update-progress, POST} route","timestamp":"2025-07-03T10:20:48.073Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/complete, POST} route","timestamp":"2025-07-03T10:20:48.073Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/fail, POST} route","timestamp":"2025-07-03T10:20:48.073Z"}
{"context":"RoutesResolver","level":"info","message":"DatabaseController {/api/v1/database}:","timestamp":"2025-07-03T10:20:48.074Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/query, POST} route","timestamp":"2025-07-03T10:20:48.074Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/filter-satellites, POST} route","timestamp":"2025-07-03T10:20:48.074Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-users, GET} route","timestamp":"2025-07-03T10:20:48.074Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-purposes, GET} route","timestamp":"2025-07-03T10:20:48.075Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-contractors, GET} route","timestamp":"2025-07-03T10:20:48.075Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-sites, GET} route","timestamp":"2025-07-03T10:20:48.075Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-vehicles, GET} route","timestamp":"2025-07-03T10:20:48.075Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteTilesController {/tiles}:","timestamp":"2025-07-03T10:20:48.076Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/generate, POST} route","timestamp":"2025-07-03T10:20:48.076Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/status, GET} route","timestamp":"2025-07-03T10:20:48.076Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/satellites, GET} route","timestamp":"2025-07-03T10:20:48.076Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/metadata, GET} route","timestamp":"2025-07-03T10:20:48.076Z"}
{"context":"RoutesResolver","level":"info","message":"NewsSchedulerController {/api/news-scheduler}:","timestamp":"2025-07-03T10:20:48.076Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/status, GET} route","timestamp":"2025-07-03T10:20:48.077Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/trigger, POST} route","timestamp":"2025-07-03T10:20:48.077Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/config, POST} route","timestamp":"2025-07-03T10:20:48.077Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/stop, POST} route","timestamp":"2025-07-03T10:20:48.077Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/cron-expressions, GET} route","timestamp":"2025-07-03T10:20:48.077Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteSchedulerController {/api/satellite-scheduler}:","timestamp":"2025-07-03T10:20:48.077Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/status, GET} route","timestamp":"2025-07-03T10:20:48.077Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/trigger, POST} route","timestamp":"2025-07-03T10:20:48.077Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/config, POST} route","timestamp":"2025-07-03T10:20:48.077Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/stop, POST} route","timestamp":"2025-07-03T10:20:48.077Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/cron-expressions, GET} route","timestamp":"2025-07-03T10:20:48.077Z"}
{"context":"RoutesResolver","level":"info","message":"LLMConfigController {/llm-config}:","timestamp":"2025-07-03T10:20:48.077Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config, GET} route","timestamp":"2025-07-03T10:20:48.078Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/:configType, GET} route","timestamp":"2025-07-03T10:20:48.078Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config, PUT} route","timestamp":"2025-07-03T10:20:48.078Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/test, POST} route","timestamp":"2025-07-03T10:20:48.078Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/reset, POST} route","timestamp":"2025-07-03T10:20:48.078Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/stats/summary, GET} route","timestamp":"2025-07-03T10:20:48.078Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/stats, DELETE} route","timestamp":"2025-07-03T10:20:48.078Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/export/all, GET} route","timestamp":"2025-07-03T10:20:48.078Z"}
{"context":"DatabaseService","level":"info","message":"正在检查并启用pg_trgm扩展...","timestamp":"2025-07-03T10:20:48.083Z"}
