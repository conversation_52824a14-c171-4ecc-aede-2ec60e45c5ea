{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-06T05:51:00.152Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:00.178Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-06T05:51:00.179Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:00.179Z"}
{"context":"InstanceLoader","level":"info","message":"HttpModule dependencies initialized","timestamp":"2025-07-06T05:51:00.179Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-06T05:51:00.180Z"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized","timestamp":"2025-07-06T05:51:00.180Z"}
{"context":"TranslationService","level":"info","message":"加载翻译配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-07-06T05:51:00.180Z"}
{"context":"TranslationService","level":"info","message":"加载主题提取配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-07-06T05:51:00.181Z"}
{"context":"TranslationService","level":"info","message":"翻译服务已初始化 - 翻译模型: qwen-turbo, 主题提取模型: qwen-turbo","timestamp":"2025-07-06T05:51:00.182Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-06T05:51:00.183Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-06T05:51:00.183Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-06T05:51:00.183Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-06T05:51:00.183Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-06T05:51:00.183Z"}
{"context":"LLMConfigService","level":"info","message":"LLM配置管理服务初始化完成","timestamp":"2025-07-06T05:51:00.183Z"}
{"context":"InstanceLoader","level":"info","message":"ElasticsearchModule dependencies initialized","timestamp":"2025-07-06T05:51:00.190Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-06T05:51:00.190Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"TLE查询配置加载完成: 时间窗口=30分钟, 最大记录数=50000","timestamp":"2025-07-06T05:51:00.196Z"}
{"context":"InstanceLoader","level":"info","message":"OrbitAnalysisModule dependencies initialized","timestamp":"2025-07-06T05:51:00.199Z"}
{"context":"InstanceLoader","level":"info","message":"LLMConfigModule dependencies initialized","timestamp":"2025-07-06T05:51:00.199Z"}
{"context":"NewsSchedulerService","level":"info","message":"新闻定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 5,12,18 * * *\",\"timezone\":\"Asia/Shanghai\"}","timestamp":"2025-07-06T05:51:00.200Z"}
{"context":"InstanceLoader","level":"info","message":"ESModule dependencies initialized","timestamp":"2025-07-06T05:51:00.201Z"}
{"context":"InstanceLoader","level":"info","message":"NewsSchedulerModule dependencies initialized","timestamp":"2025-07-06T05:51:00.201Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteTilesModule dependencies initialized","timestamp":"2025-07-06T05:51:00.201Z"}
{"context":"ElasticsearchSatelliteService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:00.239Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:00.239Z"}
{"context":"ElasticsearchLaunchService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:00.239Z"}
{"context":"ElasticsearchConstellationService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:00.239Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:00.239Z"}
{"context":"ElasticsearchRocketService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:00.240Z"}
{"context":"ElasticsearchDebrisEventService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:00.240Z"}
{"context":"ElasticsearchBaseService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:00.240Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized","timestamp":"2025-07-06T05:51:00.275Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:00.275Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:00.275Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:00.276Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:00.276Z"}
{"context":"InstanceLoader","level":"info","message":"UserModule dependencies initialized","timestamp":"2025-07-06T05:51:00.276Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-06T05:51:00.277Z"}
{"context":"SatelliteSchedulerService","level":"info","message":"卫星数据定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 3 * * *\",\"timezone\":\"Asia/Shanghai\",\"saveToDatabase\":true}","timestamp":"2025-07-06T05:51:00.277Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-06T05:51:00.278Z"}
{"context":"InstanceLoader","level":"info","message":"AggregationTaskModule dependencies initialized","timestamp":"2025-07-06T05:51:00.278Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-07-06T05:51:00.278Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteModule dependencies initialized","timestamp":"2025-07-06T05:51:00.278Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteSchedulerModule dependencies initialized","timestamp":"2025-07-06T05:51:00.278Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteAggregationController {/local/satellite-aggregation}:","timestamp":"2025-07-06T05:51:00.363Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/status, GET} route","timestamp":"2025-07-06T05:51:00.364Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/running, GET} route","timestamp":"2025-07-06T05:51:00.364Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/auth}:","timestamp":"2025-07-06T05:51:00.364Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/register, POST} route","timestamp":"2025-07-06T05:51:00.365Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/login, POST} route","timestamp":"2025-07-06T05:51:00.365Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/role, PUT} route","timestamp":"2025-07-06T05:51:00.365Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/permissions, GET} route","timestamp":"2025-07-06T05:51:00.365Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users, GET} route","timestamp":"2025-07-06T05:51:00.365Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/profile, GET} route","timestamp":"2025-07-06T05:51:00.365Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/pending-users, GET} route","timestamp":"2025-07-06T05:51:00.365Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/approval, PUT} route","timestamp":"2025-07-06T05:51:00.366Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/user-approval-history, GET} route","timestamp":"2025-07-06T05:51:00.366Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/delete, DELETE} route","timestamp":"2025-07-06T05:51:00.366Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/change-password, PUT} route","timestamp":"2025-07-06T05:51:00.366Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisEventController {/api/debris-events}:","timestamp":"2025-07-06T05:51:00.366Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/debris-events/search, POST} route","timestamp":"2025-07-06T05:51:00.366Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchConstellationController {/constellation}:","timestamp":"2025-07-06T05:51:00.366Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/search, POST} route","timestamp":"2025-07-06T05:51:00.366Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/names, GET} route","timestamp":"2025-07-06T05:51:00.367Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/organizations, GET} route","timestamp":"2025-07-06T05:51:00.367Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/purposes, GET} route","timestamp":"2025-07-06T05:51:00.367Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchCorrelationController {/correlation}:","timestamp":"2025-07-06T05:51:00.367Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/debris-to-event, POST} route","timestamp":"2025-07-06T05:51:00.367Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/event-to-debris, POST} route","timestamp":"2025-07-06T05:51:00.367Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisController {/debris}:","timestamp":"2025-07-06T05:51:00.367Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/search, POST} route","timestamp":"2025-07-06T05:51:00.367Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/names, GET} route","timestamp":"2025-07-06T05:51:00.367Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/object-classes, GET} route","timestamp":"2025-07-06T05:51:00.367Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/missions, GET} route","timestamp":"2025-07-06T05:51:00.367Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/countries, GET} route","timestamp":"2025-07-06T05:51:00.367Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/debug-query-dto, GET} route","timestamp":"2025-07-06T05:51:00.367Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchSatelliteController {/satellite}:","timestamp":"2025-07-06T05:51:00.367Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/search, POST} route","timestamp":"2025-07-06T05:51:00.367Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/names, GET} route","timestamp":"2025-07-06T05:51:00.368Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/statuses, GET} route","timestamp":"2025-07-06T05:51:00.368Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/test-direct-query, POST} route","timestamp":"2025-07-06T05:51:00.368Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchFreqController {/freq}:","timestamp":"2025-07-06T05:51:00.368Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/freq/search, POST} route","timestamp":"2025-07-06T05:51:00.368Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchOrbitController {/orbit}:","timestamp":"2025-07-06T05:51:00.368Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/search, POST} route","timestamp":"2025-07-06T05:51:00.368Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle, POST} route","timestamp":"2025-07-06T05:51:00.368Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle/all, POST} route","timestamp":"2025-07-06T05:51:00.368Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, GET} route","timestamp":"2025-07-06T05:51:00.368Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, PUT} route","timestamp":"2025-07-06T05:51:00.368Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLoopholeController {/loophole}:","timestamp":"2025-07-06T05:51:00.369Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/loophole/search, POST} route","timestamp":"2025-07-06T05:51:00.369Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchNewsController {/api/es/news}:","timestamp":"2025-07-06T05:51:00.369Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/indices, GET} route","timestamp":"2025-07-06T05:51:00.369Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translation-status, GET} route","timestamp":"2025-07-06T05:51:00.369Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translate, POST} route","timestamp":"2025-07-06T05:51:00.369Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/api-stats, GET} route","timestamp":"2025-07-06T05:51:00.375Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failure-stats, GET} route","timestamp":"2025-07-06T05:51:00.375Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/reset-failure-stats, POST} route","timestamp":"2025-07-06T05:51:00.375Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failed-translations, GET} route","timestamp":"2025-07-06T05:51:00.375Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/retry-failed, POST} route","timestamp":"2025-07-06T05:51:00.376Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/extract-themes, POST} route","timestamp":"2025-07-06T05:51:00.376Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/list, POST} route","timestamp":"2025-07-06T05:51:00.376Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/hot-themes, GET} route","timestamp":"2025-07-06T05:51:00.376Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLaunchController {/api/es/launch}:","timestamp":"2025-07-06T05:51:00.376Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/search, POST} route","timestamp":"2025-07-06T05:51:00.376Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/rocket-names, GET} route","timestamp":"2025-07-06T05:51:00.376Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/site-names, GET} route","timestamp":"2025-07-06T05:51:00.376Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/providers, GET} route","timestamp":"2025-07-06T05:51:00.376Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/cospar, POST} route","timestamp":"2025-07-06T05:51:00.376Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-names, GET} route","timestamp":"2025-07-06T05:51:00.376Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/all-launch-sites, GET} route","timestamp":"2025-07-06T05:51:00.377Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-info, GET} route","timestamp":"2025-07-06T05:51:00.377Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/service-providers/search, POST} route","timestamp":"2025-07-06T05:51:00.377Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchRocketController {/api/es/rocket}:","timestamp":"2025-07-06T05:51:00.377Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/rocket/search, POST} route","timestamp":"2025-07-06T05:51:00.377Z"}
{"context":"RoutesResolver","level":"info","message":"OrbitCalculatorController {/api/orbit-calculator}:","timestamp":"2025-07-06T05:51:00.377Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/orbit-calculator/calculate-positions, POST} route","timestamp":"2025-07-06T05:51:00.377Z"}
{"context":"RoutesResolver","level":"info","message":"PassAnalysisController {/api/pass-analysis}:","timestamp":"2025-07-06T05:51:00.377Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/pass-analysis/pass-analysis, POST} route","timestamp":"2025-07-06T05:51:00.377Z"}
{"context":"RoutesResolver","level":"info","message":"ConjunctionAnalysisController {/api/conjunction-analysis}:","timestamp":"2025-07-06T05:51:00.377Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/conjunction-analysis/conjunction-analysis, POST} route","timestamp":"2025-07-06T05:51:00.377Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteController {/local/satellite}:","timestamp":"2025-07-06T05:51:00.377Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/search, POST} route","timestamp":"2025-07-06T05:51:00.378Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/names, GET} route","timestamp":"2025-07-06T05:51:00.378Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/statuses, GET} route","timestamp":"2025-07-06T05:51:00.378Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync, POST} route","timestamp":"2025-07-06T05:51:00.378Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync-all, POST} route","timestamp":"2025-07-06T05:51:00.378Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate, POST} route","timestamp":"2025-07-06T05:51:00.378Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/clear, POST} route","timestamp":"2025-07-06T05:51:00.378Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-classes-local, GET} route","timestamp":"2025-07-06T05:51:00.378Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-types-local, GET} route","timestamp":"2025-07-06T05:51:00.378Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/:id, GET} route","timestamp":"2025-07-06T05:51:00.378Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite, GET} route","timestamp":"2025-07-06T05:51:00.379Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-aggregation, POST} route","timestamp":"2025-07-06T05:51:00.379Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-custom-aggregation, POST} route","timestamp":"2025-07-06T05:51:00.379Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate-all, POST} route","timestamp":"2025-07-06T05:51:00.379Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/incremental-aggregate, POST} route","timestamp":"2025-07-06T05:51:00.379Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-orbit-info, POST} route","timestamp":"2025-07-06T05:51:00.379Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-users, POST} route","timestamp":"2025-07-06T05:51:00.379Z"}
{"context":"RoutesResolver","level":"info","message":"ConstellationController {/local/constellation}:","timestamp":"2025-07-06T05:51:00.379Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation, GET} route","timestamp":"2025-07-06T05:51:00.379Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/update, POST} route","timestamp":"2025-07-06T05:51:00.381Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/with-tle, GET} route","timestamp":"2025-07-06T05:51:00.381Z"}
{"context":"RoutesResolver","level":"info","message":"AggregationTaskController {/tasks/aggregation}:","timestamp":"2025-07-06T05:51:00.381Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, GET} route","timestamp":"2025-07-06T05:51:00.381Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/latest, GET} route","timestamp":"2025-07-06T05:51:00.381Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/running, GET} route","timestamp":"2025-07-06T05:51:00.381Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id, GET} route","timestamp":"2025-07-06T05:51:00.381Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, POST} route","timestamp":"2025-07-06T05:51:00.381Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/start, POST} route","timestamp":"2025-07-06T05:51:00.381Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/update-progress, POST} route","timestamp":"2025-07-06T05:51:00.383Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/complete, POST} route","timestamp":"2025-07-06T05:51:00.383Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/fail, POST} route","timestamp":"2025-07-06T05:51:00.383Z"}
{"context":"RoutesResolver","level":"info","message":"DatabaseController {/api/v1/database}:","timestamp":"2025-07-06T05:51:00.383Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/query, POST} route","timestamp":"2025-07-06T05:51:00.383Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/filter-satellites, POST} route","timestamp":"2025-07-06T05:51:00.383Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-users, GET} route","timestamp":"2025-07-06T05:51:00.383Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-purposes, GET} route","timestamp":"2025-07-06T05:51:00.383Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-contractors, GET} route","timestamp":"2025-07-06T05:51:00.384Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-sites, GET} route","timestamp":"2025-07-06T05:51:00.384Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-vehicles, GET} route","timestamp":"2025-07-06T05:51:00.384Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteTilesController {/tiles}:","timestamp":"2025-07-06T05:51:00.384Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/generate, POST} route","timestamp":"2025-07-06T05:51:00.384Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/status, GET} route","timestamp":"2025-07-06T05:51:00.384Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/satellites, GET} route","timestamp":"2025-07-06T05:51:00.384Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/metadata, GET} route","timestamp":"2025-07-06T05:51:00.384Z"}
{"context":"RoutesResolver","level":"info","message":"NewsSchedulerController {/api/news-scheduler}:","timestamp":"2025-07-06T05:51:00.384Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/status, GET} route","timestamp":"2025-07-06T05:51:00.384Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/trigger, POST} route","timestamp":"2025-07-06T05:51:00.384Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/config, POST} route","timestamp":"2025-07-06T05:51:00.384Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/stop, POST} route","timestamp":"2025-07-06T05:51:00.384Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/cron-expressions, GET} route","timestamp":"2025-07-06T05:51:00.384Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteSchedulerController {/api/satellite-scheduler}:","timestamp":"2025-07-06T05:51:00.385Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/status, GET} route","timestamp":"2025-07-06T05:51:00.385Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/trigger, POST} route","timestamp":"2025-07-06T05:51:00.385Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/config, POST} route","timestamp":"2025-07-06T05:51:00.385Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/stop, POST} route","timestamp":"2025-07-06T05:51:00.385Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/cron-expressions, GET} route","timestamp":"2025-07-06T05:51:00.385Z"}
{"context":"RoutesResolver","level":"info","message":"LLMConfigController {/llm-config}:","timestamp":"2025-07-06T05:51:00.385Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config, GET} route","timestamp":"2025-07-06T05:51:00.385Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/:configType, GET} route","timestamp":"2025-07-06T05:51:00.385Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config, PUT} route","timestamp":"2025-07-06T05:51:00.385Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/test, POST} route","timestamp":"2025-07-06T05:51:00.385Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/reset, POST} route","timestamp":"2025-07-06T05:51:00.385Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/stats/summary, GET} route","timestamp":"2025-07-06T05:51:00.385Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/stats, DELETE} route","timestamp":"2025-07-06T05:51:00.385Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/export/all, GET} route","timestamp":"2025-07-06T05:51:00.385Z"}
{"context":"DatabaseService","level":"info","message":"正在检查并启用pg_trgm扩展...","timestamp":"2025-07-06T05:51:00.390Z"}
{"context":"DatabaseService","level":"info","message":"pg_trgm扩展已启用","timestamp":"2025-07-06T05:51:00.430Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-07-06T05:51:00.443Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-06T05:51:13.668Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:13.696Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-06T05:51:13.696Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:13.697Z"}
{"context":"InstanceLoader","level":"info","message":"HttpModule dependencies initialized","timestamp":"2025-07-06T05:51:13.697Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-06T05:51:13.698Z"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized","timestamp":"2025-07-06T05:51:13.698Z"}
{"context":"TranslationService","level":"info","message":"加载翻译配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-07-06T05:51:13.698Z"}
{"context":"TranslationService","level":"info","message":"加载主题提取配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-07-06T05:51:13.698Z"}
{"context":"TranslationService","level":"info","message":"翻译服务已初始化 - 翻译模型: qwen-turbo, 主题提取模型: qwen-turbo","timestamp":"2025-07-06T05:51:13.699Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-06T05:51:13.699Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-06T05:51:13.699Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-06T05:51:13.700Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-06T05:51:13.700Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-06T05:51:13.700Z"}
{"context":"LLMConfigService","level":"info","message":"LLM配置管理服务初始化完成","timestamp":"2025-07-06T05:51:13.700Z"}
{"context":"InstanceLoader","level":"info","message":"ElasticsearchModule dependencies initialized","timestamp":"2025-07-06T05:51:13.702Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-06T05:51:13.702Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"TLE查询配置加载完成: 时间窗口=30分钟, 最大记录数=50000","timestamp":"2025-07-06T05:51:13.712Z"}
{"context":"InstanceLoader","level":"info","message":"OrbitAnalysisModule dependencies initialized","timestamp":"2025-07-06T05:51:13.714Z"}
{"context":"InstanceLoader","level":"info","message":"LLMConfigModule dependencies initialized","timestamp":"2025-07-06T05:51:13.714Z"}
{"context":"NewsSchedulerService","level":"info","message":"新闻定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 5,12,18 * * *\",\"timezone\":\"Asia/Shanghai\"}","timestamp":"2025-07-06T05:51:13.715Z"}
{"context":"InstanceLoader","level":"info","message":"ESModule dependencies initialized","timestamp":"2025-07-06T05:51:13.716Z"}
{"context":"InstanceLoader","level":"info","message":"NewsSchedulerModule dependencies initialized","timestamp":"2025-07-06T05:51:13.716Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteTilesModule dependencies initialized","timestamp":"2025-07-06T05:51:13.716Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized","timestamp":"2025-07-06T05:51:13.742Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:13.742Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:13.742Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:13.742Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:13.742Z"}
{"context":"InstanceLoader","level":"info","message":"UserModule dependencies initialized","timestamp":"2025-07-06T05:51:13.742Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-06T05:51:13.743Z"}
{"context":"SatelliteSchedulerService","level":"info","message":"卫星数据定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 3 * * *\",\"timezone\":\"Asia/Shanghai\",\"saveToDatabase\":true}","timestamp":"2025-07-06T05:51:13.743Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-06T05:51:13.743Z"}
{"context":"InstanceLoader","level":"info","message":"AggregationTaskModule dependencies initialized","timestamp":"2025-07-06T05:51:13.743Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-07-06T05:51:13.743Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteModule dependencies initialized","timestamp":"2025-07-06T05:51:13.743Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteSchedulerModule dependencies initialized","timestamp":"2025-07-06T05:51:13.743Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteAggregationController {/local/satellite-aggregation}:","timestamp":"2025-07-06T05:51:13.817Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/status, GET} route","timestamp":"2025-07-06T05:51:13.819Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/running, GET} route","timestamp":"2025-07-06T05:51:13.819Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/auth}:","timestamp":"2025-07-06T05:51:13.819Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/register, POST} route","timestamp":"2025-07-06T05:51:13.819Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/login, POST} route","timestamp":"2025-07-06T05:51:13.819Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/role, PUT} route","timestamp":"2025-07-06T05:51:13.819Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/permissions, GET} route","timestamp":"2025-07-06T05:51:13.819Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users, GET} route","timestamp":"2025-07-06T05:51:13.820Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/profile, GET} route","timestamp":"2025-07-06T05:51:13.820Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/pending-users, GET} route","timestamp":"2025-07-06T05:51:13.820Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/approval, PUT} route","timestamp":"2025-07-06T05:51:13.820Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/user-approval-history, GET} route","timestamp":"2025-07-06T05:51:13.820Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/delete, DELETE} route","timestamp":"2025-07-06T05:51:13.820Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/change-password, PUT} route","timestamp":"2025-07-06T05:51:13.820Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisEventController {/api/debris-events}:","timestamp":"2025-07-06T05:51:13.820Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/debris-events/search, POST} route","timestamp":"2025-07-06T05:51:13.820Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchConstellationController {/constellation}:","timestamp":"2025-07-06T05:51:13.820Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/search, POST} route","timestamp":"2025-07-06T05:51:13.820Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/names, GET} route","timestamp":"2025-07-06T05:51:13.821Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/organizations, GET} route","timestamp":"2025-07-06T05:51:13.821Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/purposes, GET} route","timestamp":"2025-07-06T05:51:13.821Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchCorrelationController {/correlation}:","timestamp":"2025-07-06T05:51:13.821Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/debris-to-event, POST} route","timestamp":"2025-07-06T05:51:13.821Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/event-to-debris, POST} route","timestamp":"2025-07-06T05:51:13.821Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisController {/debris}:","timestamp":"2025-07-06T05:51:13.821Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/search, POST} route","timestamp":"2025-07-06T05:51:13.821Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/names, GET} route","timestamp":"2025-07-06T05:51:13.821Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/object-classes, GET} route","timestamp":"2025-07-06T05:51:13.821Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/missions, GET} route","timestamp":"2025-07-06T05:51:13.821Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/countries, GET} route","timestamp":"2025-07-06T05:51:13.821Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/debug-query-dto, GET} route","timestamp":"2025-07-06T05:51:13.821Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchSatelliteController {/satellite}:","timestamp":"2025-07-06T05:51:13.821Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/search, POST} route","timestamp":"2025-07-06T05:51:13.821Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/names, GET} route","timestamp":"2025-07-06T05:51:13.822Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/statuses, GET} route","timestamp":"2025-07-06T05:51:13.822Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/test-direct-query, POST} route","timestamp":"2025-07-06T05:51:13.822Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchFreqController {/freq}:","timestamp":"2025-07-06T05:51:13.822Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/freq/search, POST} route","timestamp":"2025-07-06T05:51:13.822Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchOrbitController {/orbit}:","timestamp":"2025-07-06T05:51:13.822Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/search, POST} route","timestamp":"2025-07-06T05:51:13.822Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle, POST} route","timestamp":"2025-07-06T05:51:13.822Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle/all, POST} route","timestamp":"2025-07-06T05:51:13.822Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, GET} route","timestamp":"2025-07-06T05:51:13.822Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, PUT} route","timestamp":"2025-07-06T05:51:13.822Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLoopholeController {/loophole}:","timestamp":"2025-07-06T05:51:13.822Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/loophole/search, POST} route","timestamp":"2025-07-06T05:51:13.822Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchNewsController {/api/es/news}:","timestamp":"2025-07-06T05:51:13.823Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/indices, GET} route","timestamp":"2025-07-06T05:51:13.823Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translation-status, GET} route","timestamp":"2025-07-06T05:51:13.823Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translate, POST} route","timestamp":"2025-07-06T05:51:13.823Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/api-stats, GET} route","timestamp":"2025-07-06T05:51:13.823Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failure-stats, GET} route","timestamp":"2025-07-06T05:51:13.823Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/reset-failure-stats, POST} route","timestamp":"2025-07-06T05:51:13.825Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failed-translations, GET} route","timestamp":"2025-07-06T05:51:13.825Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/retry-failed, POST} route","timestamp":"2025-07-06T05:51:13.825Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/extract-themes, POST} route","timestamp":"2025-07-06T05:51:13.825Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/list, POST} route","timestamp":"2025-07-06T05:51:13.825Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/hot-themes, GET} route","timestamp":"2025-07-06T05:51:13.825Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLaunchController {/api/es/launch}:","timestamp":"2025-07-06T05:51:13.825Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/search, POST} route","timestamp":"2025-07-06T05:51:13.825Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/rocket-names, GET} route","timestamp":"2025-07-06T05:51:13.825Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/site-names, GET} route","timestamp":"2025-07-06T05:51:13.826Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/providers, GET} route","timestamp":"2025-07-06T05:51:13.826Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/cospar, POST} route","timestamp":"2025-07-06T05:51:13.826Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-names, GET} route","timestamp":"2025-07-06T05:51:13.826Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/all-launch-sites, GET} route","timestamp":"2025-07-06T05:51:13.826Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-info, GET} route","timestamp":"2025-07-06T05:51:13.826Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/service-providers/search, POST} route","timestamp":"2025-07-06T05:51:13.826Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchRocketController {/api/es/rocket}:","timestamp":"2025-07-06T05:51:13.826Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/rocket/search, POST} route","timestamp":"2025-07-06T05:51:13.826Z"}
{"context":"RoutesResolver","level":"info","message":"OrbitCalculatorController {/api/orbit-calculator}:","timestamp":"2025-07-06T05:51:13.826Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/orbit-calculator/calculate-positions, POST} route","timestamp":"2025-07-06T05:51:13.826Z"}
{"context":"RoutesResolver","level":"info","message":"PassAnalysisController {/api/pass-analysis}:","timestamp":"2025-07-06T05:51:13.826Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/pass-analysis/pass-analysis, POST} route","timestamp":"2025-07-06T05:51:13.826Z"}
{"context":"RoutesResolver","level":"info","message":"ConjunctionAnalysisController {/api/conjunction-analysis}:","timestamp":"2025-07-06T05:51:13.826Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/conjunction-analysis/conjunction-analysis, POST} route","timestamp":"2025-07-06T05:51:13.826Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteController {/local/satellite}:","timestamp":"2025-07-06T05:51:13.826Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/search, POST} route","timestamp":"2025-07-06T05:51:13.826Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/names, GET} route","timestamp":"2025-07-06T05:51:13.827Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/statuses, GET} route","timestamp":"2025-07-06T05:51:13.827Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync, POST} route","timestamp":"2025-07-06T05:51:13.827Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync-all, POST} route","timestamp":"2025-07-06T05:51:13.827Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate, POST} route","timestamp":"2025-07-06T05:51:13.827Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/clear, POST} route","timestamp":"2025-07-06T05:51:13.827Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-classes-local, GET} route","timestamp":"2025-07-06T05:51:13.827Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-types-local, GET} route","timestamp":"2025-07-06T05:51:13.827Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/:id, GET} route","timestamp":"2025-07-06T05:51:13.827Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite, GET} route","timestamp":"2025-07-06T05:51:13.827Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-aggregation, POST} route","timestamp":"2025-07-06T05:51:13.827Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-custom-aggregation, POST} route","timestamp":"2025-07-06T05:51:13.828Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate-all, POST} route","timestamp":"2025-07-06T05:51:13.828Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/incremental-aggregate, POST} route","timestamp":"2025-07-06T05:51:13.828Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-orbit-info, POST} route","timestamp":"2025-07-06T05:51:13.828Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-users, POST} route","timestamp":"2025-07-06T05:51:13.828Z"}
{"context":"RoutesResolver","level":"info","message":"ConstellationController {/local/constellation}:","timestamp":"2025-07-06T05:51:13.828Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation, GET} route","timestamp":"2025-07-06T05:51:13.828Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/update, POST} route","timestamp":"2025-07-06T05:51:13.828Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/with-tle, GET} route","timestamp":"2025-07-06T05:51:13.828Z"}
{"context":"RoutesResolver","level":"info","message":"AggregationTaskController {/tasks/aggregation}:","timestamp":"2025-07-06T05:51:13.828Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, GET} route","timestamp":"2025-07-06T05:51:13.828Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/latest, GET} route","timestamp":"2025-07-06T05:51:13.828Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/running, GET} route","timestamp":"2025-07-06T05:51:13.828Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id, GET} route","timestamp":"2025-07-06T05:51:13.829Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, POST} route","timestamp":"2025-07-06T05:51:13.829Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/start, POST} route","timestamp":"2025-07-06T05:51:13.829Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/update-progress, POST} route","timestamp":"2025-07-06T05:51:13.829Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/complete, POST} route","timestamp":"2025-07-06T05:51:13.829Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/fail, POST} route","timestamp":"2025-07-06T05:51:13.829Z"}
{"context":"RoutesResolver","level":"info","message":"DatabaseController {/api/v1/database}:","timestamp":"2025-07-06T05:51:13.829Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/query, POST} route","timestamp":"2025-07-06T05:51:13.829Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/filter-satellites, POST} route","timestamp":"2025-07-06T05:51:13.829Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-users, GET} route","timestamp":"2025-07-06T05:51:13.829Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-purposes, GET} route","timestamp":"2025-07-06T05:51:13.829Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-contractors, GET} route","timestamp":"2025-07-06T05:51:13.830Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-sites, GET} route","timestamp":"2025-07-06T05:51:13.830Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-vehicles, GET} route","timestamp":"2025-07-06T05:51:13.830Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteTilesController {/tiles}:","timestamp":"2025-07-06T05:51:13.830Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/generate, POST} route","timestamp":"2025-07-06T05:51:13.831Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/status, GET} route","timestamp":"2025-07-06T05:51:13.831Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/satellites, GET} route","timestamp":"2025-07-06T05:51:13.831Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/metadata, GET} route","timestamp":"2025-07-06T05:51:13.831Z"}
{"context":"RoutesResolver","level":"info","message":"NewsSchedulerController {/api/news-scheduler}:","timestamp":"2025-07-06T05:51:13.831Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/status, GET} route","timestamp":"2025-07-06T05:51:13.831Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/trigger, POST} route","timestamp":"2025-07-06T05:51:13.831Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/config, POST} route","timestamp":"2025-07-06T05:51:13.831Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/stop, POST} route","timestamp":"2025-07-06T05:51:13.831Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/cron-expressions, GET} route","timestamp":"2025-07-06T05:51:13.831Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteSchedulerController {/api/satellite-scheduler}:","timestamp":"2025-07-06T05:51:13.831Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/status, GET} route","timestamp":"2025-07-06T05:51:13.831Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/trigger, POST} route","timestamp":"2025-07-06T05:51:13.832Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/config, POST} route","timestamp":"2025-07-06T05:51:13.832Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/stop, POST} route","timestamp":"2025-07-06T05:51:13.832Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/cron-expressions, GET} route","timestamp":"2025-07-06T05:51:13.832Z"}
{"context":"RoutesResolver","level":"info","message":"LLMConfigController {/llm-config}:","timestamp":"2025-07-06T05:51:13.832Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config, GET} route","timestamp":"2025-07-06T05:51:13.832Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/:configType, GET} route","timestamp":"2025-07-06T05:51:13.832Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config, PUT} route","timestamp":"2025-07-06T05:51:13.832Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/test, POST} route","timestamp":"2025-07-06T05:51:13.832Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/reset, POST} route","timestamp":"2025-07-06T05:51:13.832Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/stats/summary, GET} route","timestamp":"2025-07-06T05:51:13.832Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/stats, DELETE} route","timestamp":"2025-07-06T05:51:13.832Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/export/all, GET} route","timestamp":"2025-07-06T05:51:13.832Z"}
{"context":"DatabaseService","level":"info","message":"正在检查并启用pg_trgm扩展...","timestamp":"2025-07-06T05:51:13.837Z"}
{"context":"DatabaseService","level":"info","message":"pg_trgm扩展已启用","timestamp":"2025-07-06T05:51:13.842Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-07-06T05:51:13.859Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:13.860Z"}
{"context":"ElasticsearchRocketService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:13.861Z"}
{"context":"ElasticsearchDebrisEventService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:13.861Z"}
{"context":"ElasticsearchSatelliteService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:13.861Z"}
{"context":"ElasticsearchLaunchService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:13.861Z"}
{"context":"ElasticsearchConstellationService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:13.861Z"}
{"context":"ElasticsearchBaseService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:13.861Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:13.862Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-06T05:51:29.420Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:29.437Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-06T05:51:29.437Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:29.438Z"}
{"context":"InstanceLoader","level":"info","message":"HttpModule dependencies initialized","timestamp":"2025-07-06T05:51:29.438Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-06T05:51:29.439Z"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized","timestamp":"2025-07-06T05:51:29.439Z"}
{"context":"TranslationService","level":"info","message":"加载翻译配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-07-06T05:51:29.439Z"}
{"context":"TranslationService","level":"info","message":"加载主题提取配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-07-06T05:51:29.440Z"}
{"context":"TranslationService","level":"info","message":"翻译服务已初始化 - 翻译模型: qwen-turbo, 主题提取模型: qwen-turbo","timestamp":"2025-07-06T05:51:29.441Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-06T05:51:29.441Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-06T05:51:29.442Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-06T05:51:29.442Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-06T05:51:29.442Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-06T05:51:29.443Z"}
{"context":"LLMConfigService","level":"info","message":"LLM配置管理服务初始化完成","timestamp":"2025-07-06T05:51:29.443Z"}
{"context":"InstanceLoader","level":"info","message":"ElasticsearchModule dependencies initialized","timestamp":"2025-07-06T05:51:29.446Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-06T05:51:29.446Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"TLE查询配置加载完成: 时间窗口=30分钟, 最大记录数=50000","timestamp":"2025-07-06T05:51:29.450Z"}
{"context":"InstanceLoader","level":"info","message":"OrbitAnalysisModule dependencies initialized","timestamp":"2025-07-06T05:51:29.451Z"}
{"context":"InstanceLoader","level":"info","message":"LLMConfigModule dependencies initialized","timestamp":"2025-07-06T05:51:29.451Z"}
{"context":"NewsSchedulerService","level":"info","message":"新闻定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 5,12,18 * * *\",\"timezone\":\"Asia/Shanghai\"}","timestamp":"2025-07-06T05:51:29.452Z"}
{"context":"InstanceLoader","level":"info","message":"ESModule dependencies initialized","timestamp":"2025-07-06T05:51:29.453Z"}
{"context":"InstanceLoader","level":"info","message":"NewsSchedulerModule dependencies initialized","timestamp":"2025-07-06T05:51:29.453Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteTilesModule dependencies initialized","timestamp":"2025-07-06T05:51:29.453Z"}
{"context":"ElasticsearchBaseService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:29.499Z"}
{"context":"ElasticsearchLaunchService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:29.499Z"}
{"context":"ElasticsearchDebrisEventService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:29.499Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:29.500Z"}
{"context":"ElasticsearchSatelliteService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:29.500Z"}
{"context":"ElasticsearchRocketService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:29.500Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:29.500Z"}
{"context":"ElasticsearchConstellationService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:29.500Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized","timestamp":"2025-07-06T05:51:29.501Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:29.501Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:29.501Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:29.501Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:29.501Z"}
{"context":"InstanceLoader","level":"info","message":"UserModule dependencies initialized","timestamp":"2025-07-06T05:51:29.502Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-06T05:51:29.502Z"}
{"context":"SatelliteSchedulerService","level":"info","message":"卫星数据定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 3 * * *\",\"timezone\":\"Asia/Shanghai\",\"saveToDatabase\":true}","timestamp":"2025-07-06T05:51:29.502Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-06T05:51:29.503Z"}
{"context":"InstanceLoader","level":"info","message":"AggregationTaskModule dependencies initialized","timestamp":"2025-07-06T05:51:29.503Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-07-06T05:51:29.503Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteModule dependencies initialized","timestamp":"2025-07-06T05:51:29.503Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteSchedulerModule dependencies initialized","timestamp":"2025-07-06T05:51:29.504Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteAggregationController {/local/satellite-aggregation}:","timestamp":"2025-07-06T05:51:29.897Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/status, GET} route","timestamp":"2025-07-06T05:51:29.898Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/running, GET} route","timestamp":"2025-07-06T05:51:29.899Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/auth}:","timestamp":"2025-07-06T05:51:29.899Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/register, POST} route","timestamp":"2025-07-06T05:51:29.899Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/login, POST} route","timestamp":"2025-07-06T05:51:29.899Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/role, PUT} route","timestamp":"2025-07-06T05:51:29.899Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/permissions, GET} route","timestamp":"2025-07-06T05:51:29.900Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users, GET} route","timestamp":"2025-07-06T05:51:29.900Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/profile, GET} route","timestamp":"2025-07-06T05:51:29.900Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/pending-users, GET} route","timestamp":"2025-07-06T05:51:29.900Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/approval, PUT} route","timestamp":"2025-07-06T05:51:29.900Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/user-approval-history, GET} route","timestamp":"2025-07-06T05:51:29.901Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/delete, DELETE} route","timestamp":"2025-07-06T05:51:29.901Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/change-password, PUT} route","timestamp":"2025-07-06T05:51:29.901Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisEventController {/api/debris-events}:","timestamp":"2025-07-06T05:51:29.901Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/debris-events/search, POST} route","timestamp":"2025-07-06T05:51:29.901Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchConstellationController {/constellation}:","timestamp":"2025-07-06T05:51:29.901Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/search, POST} route","timestamp":"2025-07-06T05:51:29.901Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/names, GET} route","timestamp":"2025-07-06T05:51:29.901Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/organizations, GET} route","timestamp":"2025-07-06T05:51:29.902Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/purposes, GET} route","timestamp":"2025-07-06T05:51:29.902Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchCorrelationController {/correlation}:","timestamp":"2025-07-06T05:51:29.902Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/debris-to-event, POST} route","timestamp":"2025-07-06T05:51:29.902Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/event-to-debris, POST} route","timestamp":"2025-07-06T05:51:29.902Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisController {/debris}:","timestamp":"2025-07-06T05:51:29.902Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/search, POST} route","timestamp":"2025-07-06T05:51:29.906Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/names, GET} route","timestamp":"2025-07-06T05:51:29.906Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/object-classes, GET} route","timestamp":"2025-07-06T05:51:29.906Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/missions, GET} route","timestamp":"2025-07-06T05:51:29.906Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/countries, GET} route","timestamp":"2025-07-06T05:51:29.906Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/debug-query-dto, GET} route","timestamp":"2025-07-06T05:51:29.907Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchSatelliteController {/satellite}:","timestamp":"2025-07-06T05:51:29.907Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/search, POST} route","timestamp":"2025-07-06T05:51:29.907Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/names, GET} route","timestamp":"2025-07-06T05:51:29.908Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/statuses, GET} route","timestamp":"2025-07-06T05:51:29.909Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/test-direct-query, POST} route","timestamp":"2025-07-06T05:51:29.909Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchFreqController {/freq}:","timestamp":"2025-07-06T05:51:29.909Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/freq/search, POST} route","timestamp":"2025-07-06T05:51:29.910Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchOrbitController {/orbit}:","timestamp":"2025-07-06T05:51:29.910Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/search, POST} route","timestamp":"2025-07-06T05:51:29.912Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle, POST} route","timestamp":"2025-07-06T05:51:29.912Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle/all, POST} route","timestamp":"2025-07-06T05:51:29.912Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, GET} route","timestamp":"2025-07-06T05:51:29.913Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, PUT} route","timestamp":"2025-07-06T05:51:29.913Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLoopholeController {/loophole}:","timestamp":"2025-07-06T05:51:29.915Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/loophole/search, POST} route","timestamp":"2025-07-06T05:51:29.915Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchNewsController {/api/es/news}:","timestamp":"2025-07-06T05:51:29.915Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/indices, GET} route","timestamp":"2025-07-06T05:51:29.915Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translation-status, GET} route","timestamp":"2025-07-06T05:51:29.915Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translate, POST} route","timestamp":"2025-07-06T05:51:29.915Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/api-stats, GET} route","timestamp":"2025-07-06T05:51:29.915Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failure-stats, GET} route","timestamp":"2025-07-06T05:51:29.916Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/reset-failure-stats, POST} route","timestamp":"2025-07-06T05:51:29.916Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failed-translations, GET} route","timestamp":"2025-07-06T05:51:29.916Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/retry-failed, POST} route","timestamp":"2025-07-06T05:51:29.916Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/extract-themes, POST} route","timestamp":"2025-07-06T05:51:29.916Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/list, POST} route","timestamp":"2025-07-06T05:51:29.917Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/hot-themes, GET} route","timestamp":"2025-07-06T05:51:29.917Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLaunchController {/api/es/launch}:","timestamp":"2025-07-06T05:51:29.917Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/search, POST} route","timestamp":"2025-07-06T05:51:29.917Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/rocket-names, GET} route","timestamp":"2025-07-06T05:51:29.917Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/site-names, GET} route","timestamp":"2025-07-06T05:51:29.917Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/providers, GET} route","timestamp":"2025-07-06T05:51:29.917Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/cospar, POST} route","timestamp":"2025-07-06T05:51:29.917Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-names, GET} route","timestamp":"2025-07-06T05:51:29.917Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/all-launch-sites, GET} route","timestamp":"2025-07-06T05:51:29.918Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-info, GET} route","timestamp":"2025-07-06T05:51:29.918Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/service-providers/search, POST} route","timestamp":"2025-07-06T05:51:29.918Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchRocketController {/api/es/rocket}:","timestamp":"2025-07-06T05:51:29.918Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/rocket/search, POST} route","timestamp":"2025-07-06T05:51:29.918Z"}
{"context":"RoutesResolver","level":"info","message":"OrbitCalculatorController {/api/orbit-calculator}:","timestamp":"2025-07-06T05:51:29.918Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/orbit-calculator/calculate-positions, POST} route","timestamp":"2025-07-06T05:51:29.918Z"}
{"context":"RoutesResolver","level":"info","message":"PassAnalysisController {/api/pass-analysis}:","timestamp":"2025-07-06T05:51:29.918Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/pass-analysis/pass-analysis, POST} route","timestamp":"2025-07-06T05:51:29.918Z"}
{"context":"RoutesResolver","level":"info","message":"ConjunctionAnalysisController {/api/conjunction-analysis}:","timestamp":"2025-07-06T05:51:29.918Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/conjunction-analysis/conjunction-analysis, POST} route","timestamp":"2025-07-06T05:51:29.918Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteController {/local/satellite}:","timestamp":"2025-07-06T05:51:29.918Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/search, POST} route","timestamp":"2025-07-06T05:51:29.918Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/names, GET} route","timestamp":"2025-07-06T05:51:29.918Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/statuses, GET} route","timestamp":"2025-07-06T05:51:29.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync, POST} route","timestamp":"2025-07-06T05:51:29.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync-all, POST} route","timestamp":"2025-07-06T05:51:29.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate, POST} route","timestamp":"2025-07-06T05:51:29.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/clear, POST} route","timestamp":"2025-07-06T05:51:29.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-classes-local, GET} route","timestamp":"2025-07-06T05:51:29.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-types-local, GET} route","timestamp":"2025-07-06T05:51:29.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/:id, GET} route","timestamp":"2025-07-06T05:51:29.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite, GET} route","timestamp":"2025-07-06T05:51:29.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-aggregation, POST} route","timestamp":"2025-07-06T05:51:29.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-custom-aggregation, POST} route","timestamp":"2025-07-06T05:51:29.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate-all, POST} route","timestamp":"2025-07-06T05:51:29.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/incremental-aggregate, POST} route","timestamp":"2025-07-06T05:51:29.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-orbit-info, POST} route","timestamp":"2025-07-06T05:51:29.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-users, POST} route","timestamp":"2025-07-06T05:51:29.920Z"}
{"context":"RoutesResolver","level":"info","message":"ConstellationController {/local/constellation}:","timestamp":"2025-07-06T05:51:29.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation, GET} route","timestamp":"2025-07-06T05:51:29.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/update, POST} route","timestamp":"2025-07-06T05:51:29.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/with-tle, GET} route","timestamp":"2025-07-06T05:51:29.920Z"}
{"context":"RoutesResolver","level":"info","message":"AggregationTaskController {/tasks/aggregation}:","timestamp":"2025-07-06T05:51:29.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, GET} route","timestamp":"2025-07-06T05:51:29.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/latest, GET} route","timestamp":"2025-07-06T05:51:29.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/running, GET} route","timestamp":"2025-07-06T05:51:29.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id, GET} route","timestamp":"2025-07-06T05:51:29.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, POST} route","timestamp":"2025-07-06T05:51:29.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/start, POST} route","timestamp":"2025-07-06T05:51:29.921Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/update-progress, POST} route","timestamp":"2025-07-06T05:51:29.921Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/complete, POST} route","timestamp":"2025-07-06T05:51:29.921Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/fail, POST} route","timestamp":"2025-07-06T05:51:29.921Z"}
{"context":"RoutesResolver","level":"info","message":"DatabaseController {/api/v1/database}:","timestamp":"2025-07-06T05:51:29.921Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/query, POST} route","timestamp":"2025-07-06T05:51:29.922Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/filter-satellites, POST} route","timestamp":"2025-07-06T05:51:29.922Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-users, GET} route","timestamp":"2025-07-06T05:51:29.923Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-purposes, GET} route","timestamp":"2025-07-06T05:51:29.923Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-contractors, GET} route","timestamp":"2025-07-06T05:51:29.923Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-sites, GET} route","timestamp":"2025-07-06T05:51:29.923Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-vehicles, GET} route","timestamp":"2025-07-06T05:51:29.923Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteTilesController {/tiles}:","timestamp":"2025-07-06T05:51:29.923Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/generate, POST} route","timestamp":"2025-07-06T05:51:29.923Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/status, GET} route","timestamp":"2025-07-06T05:51:29.924Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/satellites, GET} route","timestamp":"2025-07-06T05:51:29.924Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/metadata, GET} route","timestamp":"2025-07-06T05:51:29.924Z"}
{"context":"RoutesResolver","level":"info","message":"NewsSchedulerController {/api/news-scheduler}:","timestamp":"2025-07-06T05:51:29.924Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/status, GET} route","timestamp":"2025-07-06T05:51:29.924Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/trigger, POST} route","timestamp":"2025-07-06T05:51:29.924Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/config, POST} route","timestamp":"2025-07-06T05:51:29.924Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/stop, POST} route","timestamp":"2025-07-06T05:51:29.924Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/cron-expressions, GET} route","timestamp":"2025-07-06T05:51:29.925Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteSchedulerController {/api/satellite-scheduler}:","timestamp":"2025-07-06T05:51:29.925Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/status, GET} route","timestamp":"2025-07-06T05:51:29.925Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/trigger, POST} route","timestamp":"2025-07-06T05:51:29.925Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/config, POST} route","timestamp":"2025-07-06T05:51:29.925Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/stop, POST} route","timestamp":"2025-07-06T05:51:29.926Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/cron-expressions, GET} route","timestamp":"2025-07-06T05:51:29.926Z"}
{"context":"RoutesResolver","level":"info","message":"LLMConfigController {/llm-config}:","timestamp":"2025-07-06T05:51:29.926Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config, GET} route","timestamp":"2025-07-06T05:51:29.926Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/:configType, GET} route","timestamp":"2025-07-06T05:51:29.926Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config, PUT} route","timestamp":"2025-07-06T05:51:29.927Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/test, POST} route","timestamp":"2025-07-06T05:51:29.927Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/reset, POST} route","timestamp":"2025-07-06T05:51:29.927Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/stats/summary, GET} route","timestamp":"2025-07-06T05:51:29.927Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/stats, DELETE} route","timestamp":"2025-07-06T05:51:29.927Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/export/all, GET} route","timestamp":"2025-07-06T05:51:29.928Z"}
{"context":"DatabaseService","level":"info","message":"正在检查并启用pg_trgm扩展...","timestamp":"2025-07-06T05:51:29.933Z"}
{"context":"DatabaseService","level":"info","message":"pg_trgm扩展已启用","timestamp":"2025-07-06T05:51:29.938Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-07-06T05:51:29.951Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-06T05:51:43.017Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:43.033Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-06T05:51:43.034Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:43.034Z"}
{"context":"InstanceLoader","level":"info","message":"HttpModule dependencies initialized","timestamp":"2025-07-06T05:51:43.035Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-06T05:51:43.035Z"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized","timestamp":"2025-07-06T05:51:43.035Z"}
{"context":"TranslationService","level":"info","message":"加载翻译配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-07-06T05:51:43.036Z"}
{"context":"TranslationService","level":"info","message":"加载主题提取配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-07-06T05:51:43.036Z"}
{"context":"TranslationService","level":"info","message":"翻译服务已初始化 - 翻译模型: qwen-turbo, 主题提取模型: qwen-turbo","timestamp":"2025-07-06T05:51:43.037Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-06T05:51:43.037Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-06T05:51:43.037Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-06T05:51:43.037Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-06T05:51:43.038Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-06T05:51:43.038Z"}
{"context":"LLMConfigService","level":"info","message":"LLM配置管理服务初始化完成","timestamp":"2025-07-06T05:51:43.038Z"}
{"context":"InstanceLoader","level":"info","message":"ElasticsearchModule dependencies initialized","timestamp":"2025-07-06T05:51:43.040Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-06T05:51:43.040Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"TLE查询配置加载完成: 时间窗口=30分钟, 最大记录数=50000","timestamp":"2025-07-06T05:51:43.045Z"}
{"context":"InstanceLoader","level":"info","message":"OrbitAnalysisModule dependencies initialized","timestamp":"2025-07-06T05:51:43.047Z"}
{"context":"InstanceLoader","level":"info","message":"LLMConfigModule dependencies initialized","timestamp":"2025-07-06T05:51:43.048Z"}
{"context":"NewsSchedulerService","level":"info","message":"新闻定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 5,12,18 * * *\",\"timezone\":\"Asia/Shanghai\"}","timestamp":"2025-07-06T05:51:43.049Z"}
{"context":"InstanceLoader","level":"info","message":"ESModule dependencies initialized","timestamp":"2025-07-06T05:51:43.050Z"}
{"context":"InstanceLoader","level":"info","message":"NewsSchedulerModule dependencies initialized","timestamp":"2025-07-06T05:51:43.050Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteTilesModule dependencies initialized","timestamp":"2025-07-06T05:51:43.050Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized","timestamp":"2025-07-06T05:51:43.085Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:43.085Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:43.085Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:43.085Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:51:43.085Z"}
{"context":"InstanceLoader","level":"info","message":"UserModule dependencies initialized","timestamp":"2025-07-06T05:51:43.086Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-06T05:51:43.086Z"}
{"context":"SatelliteSchedulerService","level":"info","message":"卫星数据定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 3 * * *\",\"timezone\":\"Asia/Shanghai\",\"saveToDatabase\":true}","timestamp":"2025-07-06T05:51:43.086Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-06T05:51:43.086Z"}
{"context":"InstanceLoader","level":"info","message":"AggregationTaskModule dependencies initialized","timestamp":"2025-07-06T05:51:43.086Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-07-06T05:51:43.087Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteModule dependencies initialized","timestamp":"2025-07-06T05:51:43.087Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteSchedulerModule dependencies initialized","timestamp":"2025-07-06T05:51:43.087Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteAggregationController {/local/satellite-aggregation}:","timestamp":"2025-07-06T05:51:43.159Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/status, GET} route","timestamp":"2025-07-06T05:51:43.161Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/running, GET} route","timestamp":"2025-07-06T05:51:43.162Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/auth}:","timestamp":"2025-07-06T05:51:43.162Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/register, POST} route","timestamp":"2025-07-06T05:51:43.162Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/login, POST} route","timestamp":"2025-07-06T05:51:43.163Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/role, PUT} route","timestamp":"2025-07-06T05:51:43.163Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/permissions, GET} route","timestamp":"2025-07-06T05:51:43.163Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users, GET} route","timestamp":"2025-07-06T05:51:43.163Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/profile, GET} route","timestamp":"2025-07-06T05:51:43.163Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/pending-users, GET} route","timestamp":"2025-07-06T05:51:43.164Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/approval, PUT} route","timestamp":"2025-07-06T05:51:43.164Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/user-approval-history, GET} route","timestamp":"2025-07-06T05:51:43.164Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/delete, DELETE} route","timestamp":"2025-07-06T05:51:43.165Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/change-password, PUT} route","timestamp":"2025-07-06T05:51:43.165Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisEventController {/api/debris-events}:","timestamp":"2025-07-06T05:51:43.165Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/debris-events/search, POST} route","timestamp":"2025-07-06T05:51:43.165Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchConstellationController {/constellation}:","timestamp":"2025-07-06T05:51:43.165Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/search, POST} route","timestamp":"2025-07-06T05:51:43.165Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/names, GET} route","timestamp":"2025-07-06T05:51:43.166Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/organizations, GET} route","timestamp":"2025-07-06T05:51:43.166Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/purposes, GET} route","timestamp":"2025-07-06T05:51:43.166Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchCorrelationController {/correlation}:","timestamp":"2025-07-06T05:51:43.166Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/debris-to-event, POST} route","timestamp":"2025-07-06T05:51:43.166Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/event-to-debris, POST} route","timestamp":"2025-07-06T05:51:43.166Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisController {/debris}:","timestamp":"2025-07-06T05:51:43.166Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/search, POST} route","timestamp":"2025-07-06T05:51:43.166Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/names, GET} route","timestamp":"2025-07-06T05:51:43.166Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/object-classes, GET} route","timestamp":"2025-07-06T05:51:43.166Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/missions, GET} route","timestamp":"2025-07-06T05:51:43.166Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/countries, GET} route","timestamp":"2025-07-06T05:51:43.166Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/debug-query-dto, GET} route","timestamp":"2025-07-06T05:51:43.166Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchSatelliteController {/satellite}:","timestamp":"2025-07-06T05:51:43.166Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/search, POST} route","timestamp":"2025-07-06T05:51:43.171Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/names, GET} route","timestamp":"2025-07-06T05:51:43.171Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/statuses, GET} route","timestamp":"2025-07-06T05:51:43.171Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/test-direct-query, POST} route","timestamp":"2025-07-06T05:51:43.172Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchFreqController {/freq}:","timestamp":"2025-07-06T05:51:43.172Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/freq/search, POST} route","timestamp":"2025-07-06T05:51:43.172Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchOrbitController {/orbit}:","timestamp":"2025-07-06T05:51:43.172Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/search, POST} route","timestamp":"2025-07-06T05:51:43.172Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle, POST} route","timestamp":"2025-07-06T05:51:43.172Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle/all, POST} route","timestamp":"2025-07-06T05:51:43.172Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, GET} route","timestamp":"2025-07-06T05:51:43.172Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, PUT} route","timestamp":"2025-07-06T05:51:43.172Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLoopholeController {/loophole}:","timestamp":"2025-07-06T05:51:43.172Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/loophole/search, POST} route","timestamp":"2025-07-06T05:51:43.172Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchNewsController {/api/es/news}:","timestamp":"2025-07-06T05:51:43.172Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/indices, GET} route","timestamp":"2025-07-06T05:51:43.172Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translation-status, GET} route","timestamp":"2025-07-06T05:51:43.172Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translate, POST} route","timestamp":"2025-07-06T05:51:43.172Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/api-stats, GET} route","timestamp":"2025-07-06T05:51:43.172Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failure-stats, GET} route","timestamp":"2025-07-06T05:51:43.173Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/reset-failure-stats, POST} route","timestamp":"2025-07-06T05:51:43.173Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failed-translations, GET} route","timestamp":"2025-07-06T05:51:43.173Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/retry-failed, POST} route","timestamp":"2025-07-06T05:51:43.173Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/extract-themes, POST} route","timestamp":"2025-07-06T05:51:43.173Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/list, POST} route","timestamp":"2025-07-06T05:51:43.173Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/hot-themes, GET} route","timestamp":"2025-07-06T05:51:43.173Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLaunchController {/api/es/launch}:","timestamp":"2025-07-06T05:51:43.173Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/search, POST} route","timestamp":"2025-07-06T05:51:43.173Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/rocket-names, GET} route","timestamp":"2025-07-06T05:51:43.173Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/site-names, GET} route","timestamp":"2025-07-06T05:51:43.173Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/providers, GET} route","timestamp":"2025-07-06T05:51:43.173Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/cospar, POST} route","timestamp":"2025-07-06T05:51:43.173Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-names, GET} route","timestamp":"2025-07-06T05:51:43.173Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/all-launch-sites, GET} route","timestamp":"2025-07-06T05:51:43.173Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-info, GET} route","timestamp":"2025-07-06T05:51:43.173Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/service-providers/search, POST} route","timestamp":"2025-07-06T05:51:43.174Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchRocketController {/api/es/rocket}:","timestamp":"2025-07-06T05:51:43.174Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/rocket/search, POST} route","timestamp":"2025-07-06T05:51:43.174Z"}
{"context":"RoutesResolver","level":"info","message":"OrbitCalculatorController {/api/orbit-calculator}:","timestamp":"2025-07-06T05:51:43.174Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/orbit-calculator/calculate-positions, POST} route","timestamp":"2025-07-06T05:51:43.174Z"}
{"context":"RoutesResolver","level":"info","message":"PassAnalysisController {/api/pass-analysis}:","timestamp":"2025-07-06T05:51:43.174Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/pass-analysis/pass-analysis, POST} route","timestamp":"2025-07-06T05:51:43.174Z"}
{"context":"RoutesResolver","level":"info","message":"ConjunctionAnalysisController {/api/conjunction-analysis}:","timestamp":"2025-07-06T05:51:43.174Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/conjunction-analysis/conjunction-analysis, POST} route","timestamp":"2025-07-06T05:51:43.174Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteController {/local/satellite}:","timestamp":"2025-07-06T05:51:43.174Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/search, POST} route","timestamp":"2025-07-06T05:51:43.174Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/names, GET} route","timestamp":"2025-07-06T05:51:43.174Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/statuses, GET} route","timestamp":"2025-07-06T05:51:43.174Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync, POST} route","timestamp":"2025-07-06T05:51:43.174Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync-all, POST} route","timestamp":"2025-07-06T05:51:43.174Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate, POST} route","timestamp":"2025-07-06T05:51:43.174Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/clear, POST} route","timestamp":"2025-07-06T05:51:43.174Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-classes-local, GET} route","timestamp":"2025-07-06T05:51:43.175Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-types-local, GET} route","timestamp":"2025-07-06T05:51:43.175Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/:id, GET} route","timestamp":"2025-07-06T05:51:43.175Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite, GET} route","timestamp":"2025-07-06T05:51:43.175Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-aggregation, POST} route","timestamp":"2025-07-06T05:51:43.175Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-custom-aggregation, POST} route","timestamp":"2025-07-06T05:51:43.175Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate-all, POST} route","timestamp":"2025-07-06T05:51:43.175Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/incremental-aggregate, POST} route","timestamp":"2025-07-06T05:51:43.175Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-orbit-info, POST} route","timestamp":"2025-07-06T05:51:43.175Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-users, POST} route","timestamp":"2025-07-06T05:51:43.175Z"}
{"context":"RoutesResolver","level":"info","message":"ConstellationController {/local/constellation}:","timestamp":"2025-07-06T05:51:43.175Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation, GET} route","timestamp":"2025-07-06T05:51:43.175Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/update, POST} route","timestamp":"2025-07-06T05:51:43.175Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/with-tle, GET} route","timestamp":"2025-07-06T05:51:43.175Z"}
{"context":"RoutesResolver","level":"info","message":"AggregationTaskController {/tasks/aggregation}:","timestamp":"2025-07-06T05:51:43.175Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, GET} route","timestamp":"2025-07-06T05:51:43.175Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/latest, GET} route","timestamp":"2025-07-06T05:51:43.176Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/running, GET} route","timestamp":"2025-07-06T05:51:43.176Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id, GET} route","timestamp":"2025-07-06T05:51:43.176Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, POST} route","timestamp":"2025-07-06T05:51:43.176Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/start, POST} route","timestamp":"2025-07-06T05:51:43.176Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/update-progress, POST} route","timestamp":"2025-07-06T05:51:43.176Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/complete, POST} route","timestamp":"2025-07-06T05:51:43.176Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/fail, POST} route","timestamp":"2025-07-06T05:51:43.176Z"}
{"context":"RoutesResolver","level":"info","message":"DatabaseController {/api/v1/database}:","timestamp":"2025-07-06T05:51:43.176Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/query, POST} route","timestamp":"2025-07-06T05:51:43.176Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/filter-satellites, POST} route","timestamp":"2025-07-06T05:51:43.177Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-users, GET} route","timestamp":"2025-07-06T05:51:43.177Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-purposes, GET} route","timestamp":"2025-07-06T05:51:43.177Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-contractors, GET} route","timestamp":"2025-07-06T05:51:43.177Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-sites, GET} route","timestamp":"2025-07-06T05:51:43.177Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-vehicles, GET} route","timestamp":"2025-07-06T05:51:43.177Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteTilesController {/tiles}:","timestamp":"2025-07-06T05:51:43.177Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/generate, POST} route","timestamp":"2025-07-06T05:51:43.177Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/status, GET} route","timestamp":"2025-07-06T05:51:43.178Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/satellites, GET} route","timestamp":"2025-07-06T05:51:43.178Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/metadata, GET} route","timestamp":"2025-07-06T05:51:43.178Z"}
{"context":"RoutesResolver","level":"info","message":"NewsSchedulerController {/api/news-scheduler}:","timestamp":"2025-07-06T05:51:43.178Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/status, GET} route","timestamp":"2025-07-06T05:51:43.178Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/trigger, POST} route","timestamp":"2025-07-06T05:51:43.178Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/config, POST} route","timestamp":"2025-07-06T05:51:43.178Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/stop, POST} route","timestamp":"2025-07-06T05:51:43.178Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/cron-expressions, GET} route","timestamp":"2025-07-06T05:51:43.178Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteSchedulerController {/api/satellite-scheduler}:","timestamp":"2025-07-06T05:51:43.178Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/status, GET} route","timestamp":"2025-07-06T05:51:43.179Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/trigger, POST} route","timestamp":"2025-07-06T05:51:43.179Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/config, POST} route","timestamp":"2025-07-06T05:51:43.179Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/stop, POST} route","timestamp":"2025-07-06T05:51:43.179Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/cron-expressions, GET} route","timestamp":"2025-07-06T05:51:43.179Z"}
{"context":"RoutesResolver","level":"info","message":"LLMConfigController {/llm-config}:","timestamp":"2025-07-06T05:51:43.179Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config, GET} route","timestamp":"2025-07-06T05:51:43.179Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/:configType, GET} route","timestamp":"2025-07-06T05:51:43.179Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config, PUT} route","timestamp":"2025-07-06T05:51:43.179Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/test, POST} route","timestamp":"2025-07-06T05:51:43.179Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/reset, POST} route","timestamp":"2025-07-06T05:51:43.179Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/stats/summary, GET} route","timestamp":"2025-07-06T05:51:43.179Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/stats, DELETE} route","timestamp":"2025-07-06T05:51:43.179Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/export/all, GET} route","timestamp":"2025-07-06T05:51:43.180Z"}
{"context":"DatabaseService","level":"info","message":"正在检查并启用pg_trgm扩展...","timestamp":"2025-07-06T05:51:43.184Z"}
{"context":"DatabaseService","level":"info","message":"pg_trgm扩展已启用","timestamp":"2025-07-06T05:51:43.186Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-07-06T05:51:43.194Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:43.195Z"}
{"context":"ElasticsearchRocketService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:43.195Z"}
{"context":"ElasticsearchSatelliteService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:43.195Z"}
{"context":"ElasticsearchBaseService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:43.196Z"}
{"context":"ElasticsearchConstellationService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:43.196Z"}
{"context":"ElasticsearchDebrisEventService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:43.196Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:43.196Z"}
{"context":"ElasticsearchLaunchService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:51:49.296Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-06T05:52:10.181Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:52:10.202Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-06T05:52:10.202Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:52:10.202Z"}
{"context":"InstanceLoader","level":"info","message":"HttpModule dependencies initialized","timestamp":"2025-07-06T05:52:10.202Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-06T05:52:10.203Z"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized","timestamp":"2025-07-06T05:52:10.203Z"}
{"context":"TranslationService","level":"info","message":"加载翻译配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-07-06T05:52:10.203Z"}
{"context":"TranslationService","level":"info","message":"加载主题提取配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-07-06T05:52:10.203Z"}
{"context":"TranslationService","level":"info","message":"翻译服务已初始化 - 翻译模型: qwen-turbo, 主题提取模型: qwen-turbo","timestamp":"2025-07-06T05:52:10.205Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-06T05:52:10.205Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-06T05:52:10.205Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-06T05:52:10.206Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-06T05:52:10.206Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-06T05:52:10.206Z"}
{"context":"LLMConfigService","level":"info","message":"LLM配置管理服务初始化完成","timestamp":"2025-07-06T05:52:10.206Z"}
{"context":"InstanceLoader","level":"info","message":"ElasticsearchModule dependencies initialized","timestamp":"2025-07-06T05:52:10.209Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-06T05:52:10.209Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"TLE查询配置加载完成: 时间窗口=30分钟, 最大记录数=50000","timestamp":"2025-07-06T05:52:10.259Z"}
{"context":"InstanceLoader","level":"info","message":"OrbitAnalysisModule dependencies initialized","timestamp":"2025-07-06T05:52:10.261Z"}
{"context":"InstanceLoader","level":"info","message":"LLMConfigModule dependencies initialized","timestamp":"2025-07-06T05:52:10.261Z"}
{"context":"NewsSchedulerService","level":"info","message":"新闻定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 5,12,18 * * *\",\"timezone\":\"Asia/Shanghai\"}","timestamp":"2025-07-06T05:52:10.262Z"}
{"context":"InstanceLoader","level":"info","message":"ESModule dependencies initialized","timestamp":"2025-07-06T05:52:10.262Z"}
{"context":"InstanceLoader","level":"info","message":"NewsSchedulerModule dependencies initialized","timestamp":"2025-07-06T05:52:10.262Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteTilesModule dependencies initialized","timestamp":"2025-07-06T05:52:10.262Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized","timestamp":"2025-07-06T05:52:10.289Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:52:10.290Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:52:10.290Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:52:10.290Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:52:10.290Z"}
{"context":"InstanceLoader","level":"info","message":"UserModule dependencies initialized","timestamp":"2025-07-06T05:52:10.290Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-06T05:52:10.290Z"}
{"context":"SatelliteSchedulerService","level":"info","message":"卫星数据定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 3 * * *\",\"timezone\":\"Asia/Shanghai\",\"saveToDatabase\":true}","timestamp":"2025-07-06T05:52:10.291Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-06T05:52:10.291Z"}
{"context":"InstanceLoader","level":"info","message":"AggregationTaskModule dependencies initialized","timestamp":"2025-07-06T05:52:10.291Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-07-06T05:52:10.291Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteModule dependencies initialized","timestamp":"2025-07-06T05:52:10.291Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteSchedulerModule dependencies initialized","timestamp":"2025-07-06T05:52:10.291Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteAggregationController {/local/satellite-aggregation}:","timestamp":"2025-07-06T05:52:10.372Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/status, GET} route","timestamp":"2025-07-06T05:52:10.373Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/running, GET} route","timestamp":"2025-07-06T05:52:10.373Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/auth}:","timestamp":"2025-07-06T05:52:10.374Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/register, POST} route","timestamp":"2025-07-06T05:52:10.374Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/login, POST} route","timestamp":"2025-07-06T05:52:10.374Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/role, PUT} route","timestamp":"2025-07-06T05:52:10.374Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/permissions, GET} route","timestamp":"2025-07-06T05:52:10.375Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users, GET} route","timestamp":"2025-07-06T05:52:10.375Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/profile, GET} route","timestamp":"2025-07-06T05:52:10.375Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/pending-users, GET} route","timestamp":"2025-07-06T05:52:10.375Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/approval, PUT} route","timestamp":"2025-07-06T05:52:10.375Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/user-approval-history, GET} route","timestamp":"2025-07-06T05:52:10.376Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/delete, DELETE} route","timestamp":"2025-07-06T05:52:10.376Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/change-password, PUT} route","timestamp":"2025-07-06T05:52:10.376Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisEventController {/api/debris-events}:","timestamp":"2025-07-06T05:52:10.376Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/debris-events/search, POST} route","timestamp":"2025-07-06T05:52:10.376Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchConstellationController {/constellation}:","timestamp":"2025-07-06T05:52:10.376Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/search, POST} route","timestamp":"2025-07-06T05:52:10.376Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/names, GET} route","timestamp":"2025-07-06T05:52:10.376Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/organizations, GET} route","timestamp":"2025-07-06T05:52:10.376Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/purposes, GET} route","timestamp":"2025-07-06T05:52:10.376Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchCorrelationController {/correlation}:","timestamp":"2025-07-06T05:52:10.376Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/debris-to-event, POST} route","timestamp":"2025-07-06T05:52:10.377Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/event-to-debris, POST} route","timestamp":"2025-07-06T05:52:10.377Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisController {/debris}:","timestamp":"2025-07-06T05:52:10.377Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/search, POST} route","timestamp":"2025-07-06T05:52:10.377Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/names, GET} route","timestamp":"2025-07-06T05:52:10.377Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/object-classes, GET} route","timestamp":"2025-07-06T05:52:10.377Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/missions, GET} route","timestamp":"2025-07-06T05:52:10.377Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/countries, GET} route","timestamp":"2025-07-06T05:52:10.378Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/debug-query-dto, GET} route","timestamp":"2025-07-06T05:52:10.378Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchSatelliteController {/satellite}:","timestamp":"2025-07-06T05:52:10.378Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/search, POST} route","timestamp":"2025-07-06T05:52:10.378Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/names, GET} route","timestamp":"2025-07-06T05:52:10.378Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/statuses, GET} route","timestamp":"2025-07-06T05:52:10.378Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/test-direct-query, POST} route","timestamp":"2025-07-06T05:52:10.378Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchFreqController {/freq}:","timestamp":"2025-07-06T05:52:10.378Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/freq/search, POST} route","timestamp":"2025-07-06T05:52:10.379Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchOrbitController {/orbit}:","timestamp":"2025-07-06T05:52:10.379Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/search, POST} route","timestamp":"2025-07-06T05:52:10.379Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle, POST} route","timestamp":"2025-07-06T05:52:10.379Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle/all, POST} route","timestamp":"2025-07-06T05:52:10.379Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, GET} route","timestamp":"2025-07-06T05:52:10.379Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, PUT} route","timestamp":"2025-07-06T05:52:10.379Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLoopholeController {/loophole}:","timestamp":"2025-07-06T05:52:10.379Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/loophole/search, POST} route","timestamp":"2025-07-06T05:52:10.379Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchNewsController {/api/es/news}:","timestamp":"2025-07-06T05:52:10.379Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/indices, GET} route","timestamp":"2025-07-06T05:52:10.379Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translation-status, GET} route","timestamp":"2025-07-06T05:52:10.379Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translate, POST} route","timestamp":"2025-07-06T05:52:10.380Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/api-stats, GET} route","timestamp":"2025-07-06T05:52:10.380Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failure-stats, GET} route","timestamp":"2025-07-06T05:52:10.380Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/reset-failure-stats, POST} route","timestamp":"2025-07-06T05:52:10.380Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failed-translations, GET} route","timestamp":"2025-07-06T05:52:10.380Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/retry-failed, POST} route","timestamp":"2025-07-06T05:52:10.380Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/extract-themes, POST} route","timestamp":"2025-07-06T05:52:10.380Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/list, POST} route","timestamp":"2025-07-06T05:52:10.380Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/hot-themes, GET} route","timestamp":"2025-07-06T05:52:10.381Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLaunchController {/api/es/launch}:","timestamp":"2025-07-06T05:52:10.381Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/search, POST} route","timestamp":"2025-07-06T05:52:10.381Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/rocket-names, GET} route","timestamp":"2025-07-06T05:52:10.381Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/site-names, GET} route","timestamp":"2025-07-06T05:52:10.381Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/providers, GET} route","timestamp":"2025-07-06T05:52:10.381Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/cospar, POST} route","timestamp":"2025-07-06T05:52:10.381Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-names, GET} route","timestamp":"2025-07-06T05:52:10.381Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/all-launch-sites, GET} route","timestamp":"2025-07-06T05:52:10.381Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-info, GET} route","timestamp":"2025-07-06T05:52:10.382Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/service-providers/search, POST} route","timestamp":"2025-07-06T05:52:10.382Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchRocketController {/api/es/rocket}:","timestamp":"2025-07-06T05:52:10.382Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/rocket/search, POST} route","timestamp":"2025-07-06T05:52:10.382Z"}
{"context":"RoutesResolver","level":"info","message":"OrbitCalculatorController {/api/orbit-calculator}:","timestamp":"2025-07-06T05:52:10.382Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/orbit-calculator/calculate-positions, POST} route","timestamp":"2025-07-06T05:52:10.383Z"}
{"context":"RoutesResolver","level":"info","message":"PassAnalysisController {/api/pass-analysis}:","timestamp":"2025-07-06T05:52:10.383Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/pass-analysis/pass-analysis, POST} route","timestamp":"2025-07-06T05:52:10.383Z"}
{"context":"RoutesResolver","level":"info","message":"ConjunctionAnalysisController {/api/conjunction-analysis}:","timestamp":"2025-07-06T05:52:10.383Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/conjunction-analysis/conjunction-analysis, POST} route","timestamp":"2025-07-06T05:52:10.383Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteController {/local/satellite}:","timestamp":"2025-07-06T05:52:10.383Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/search, POST} route","timestamp":"2025-07-06T05:52:10.383Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/names, GET} route","timestamp":"2025-07-06T05:52:10.383Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/statuses, GET} route","timestamp":"2025-07-06T05:52:10.383Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync, POST} route","timestamp":"2025-07-06T05:52:10.383Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync-all, POST} route","timestamp":"2025-07-06T05:52:10.383Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate, POST} route","timestamp":"2025-07-06T05:52:10.383Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/clear, POST} route","timestamp":"2025-07-06T05:52:10.384Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-classes-local, GET} route","timestamp":"2025-07-06T05:52:10.384Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-types-local, GET} route","timestamp":"2025-07-06T05:52:10.384Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/:id, GET} route","timestamp":"2025-07-06T05:52:10.384Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite, GET} route","timestamp":"2025-07-06T05:52:10.384Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-aggregation, POST} route","timestamp":"2025-07-06T05:52:10.384Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-custom-aggregation, POST} route","timestamp":"2025-07-06T05:52:10.384Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate-all, POST} route","timestamp":"2025-07-06T05:52:10.384Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/incremental-aggregate, POST} route","timestamp":"2025-07-06T05:52:10.384Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-orbit-info, POST} route","timestamp":"2025-07-06T05:52:10.384Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-users, POST} route","timestamp":"2025-07-06T05:52:10.385Z"}
{"context":"RoutesResolver","level":"info","message":"ConstellationController {/local/constellation}:","timestamp":"2025-07-06T05:52:10.385Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation, GET} route","timestamp":"2025-07-06T05:52:10.385Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/update, POST} route","timestamp":"2025-07-06T05:52:10.385Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/with-tle, GET} route","timestamp":"2025-07-06T05:52:10.385Z"}
{"context":"RoutesResolver","level":"info","message":"AggregationTaskController {/tasks/aggregation}:","timestamp":"2025-07-06T05:52:10.385Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, GET} route","timestamp":"2025-07-06T05:52:10.386Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/latest, GET} route","timestamp":"2025-07-06T05:52:10.386Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/running, GET} route","timestamp":"2025-07-06T05:52:10.386Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id, GET} route","timestamp":"2025-07-06T05:52:10.386Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, POST} route","timestamp":"2025-07-06T05:52:10.386Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/start, POST} route","timestamp":"2025-07-06T05:52:10.386Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/update-progress, POST} route","timestamp":"2025-07-06T05:52:10.386Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/complete, POST} route","timestamp":"2025-07-06T05:52:10.387Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/fail, POST} route","timestamp":"2025-07-06T05:52:10.387Z"}
{"context":"RoutesResolver","level":"info","message":"DatabaseController {/api/v1/database}:","timestamp":"2025-07-06T05:52:10.387Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/query, POST} route","timestamp":"2025-07-06T05:52:10.388Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/filter-satellites, POST} route","timestamp":"2025-07-06T05:52:10.388Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-users, GET} route","timestamp":"2025-07-06T05:52:10.388Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-purposes, GET} route","timestamp":"2025-07-06T05:52:10.388Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-contractors, GET} route","timestamp":"2025-07-06T05:52:10.397Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-sites, GET} route","timestamp":"2025-07-06T05:52:10.398Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-vehicles, GET} route","timestamp":"2025-07-06T05:52:10.398Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteTilesController {/tiles}:","timestamp":"2025-07-06T05:52:10.398Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/generate, POST} route","timestamp":"2025-07-06T05:52:10.398Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/status, GET} route","timestamp":"2025-07-06T05:52:10.398Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/satellites, GET} route","timestamp":"2025-07-06T05:52:10.398Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/metadata, GET} route","timestamp":"2025-07-06T05:52:10.398Z"}
{"context":"RoutesResolver","level":"info","message":"NewsSchedulerController {/api/news-scheduler}:","timestamp":"2025-07-06T05:52:10.398Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/status, GET} route","timestamp":"2025-07-06T05:52:10.399Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/trigger, POST} route","timestamp":"2025-07-06T05:52:10.399Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/config, POST} route","timestamp":"2025-07-06T05:52:10.399Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/stop, POST} route","timestamp":"2025-07-06T05:52:10.399Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/cron-expressions, GET} route","timestamp":"2025-07-06T05:52:10.399Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteSchedulerController {/api/satellite-scheduler}:","timestamp":"2025-07-06T05:52:10.399Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/status, GET} route","timestamp":"2025-07-06T05:52:10.399Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/trigger, POST} route","timestamp":"2025-07-06T05:52:10.399Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/config, POST} route","timestamp":"2025-07-06T05:52:10.399Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/stop, POST} route","timestamp":"2025-07-06T05:52:10.399Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/cron-expressions, GET} route","timestamp":"2025-07-06T05:52:10.399Z"}
{"context":"RoutesResolver","level":"info","message":"LLMConfigController {/llm-config}:","timestamp":"2025-07-06T05:52:10.399Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config, GET} route","timestamp":"2025-07-06T05:52:10.399Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/:configType, GET} route","timestamp":"2025-07-06T05:52:10.399Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config, PUT} route","timestamp":"2025-07-06T05:52:10.400Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/test, POST} route","timestamp":"2025-07-06T05:52:10.400Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/reset, POST} route","timestamp":"2025-07-06T05:52:10.400Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/stats/summary, GET} route","timestamp":"2025-07-06T05:52:10.400Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/stats, DELETE} route","timestamp":"2025-07-06T05:52:10.400Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/export/all, GET} route","timestamp":"2025-07-06T05:52:10.400Z"}
{"context":"DatabaseService","level":"info","message":"正在检查并启用pg_trgm扩展...","timestamp":"2025-07-06T05:52:10.405Z"}
{"context":"DatabaseService","level":"info","message":"pg_trgm扩展已启用","timestamp":"2025-07-06T05:52:10.407Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-07-06T05:52:10.418Z"}
{"context":"ElasticsearchSatelliteService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:52:10.419Z"}
{"context":"ElasticsearchLaunchService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:52:10.419Z"}
{"context":"ElasticsearchConstellationService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:52:10.419Z"}
{"context":"ElasticsearchRocketService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:52:10.419Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:52:10.419Z"}
{"context":"ElasticsearchBaseService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:52:10.419Z"}
{"context":"ElasticsearchDebrisEventService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:52:10.419Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:52:10.419Z"}
{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-06T05:52:24.016Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:52:24.035Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-06T05:52:24.036Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:52:24.036Z"}
{"context":"InstanceLoader","level":"info","message":"HttpModule dependencies initialized","timestamp":"2025-07-06T05:52:24.037Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-06T05:52:24.037Z"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized","timestamp":"2025-07-06T05:52:24.037Z"}
{"context":"TranslationService","level":"info","message":"加载翻译配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-07-06T05:52:24.038Z"}
{"context":"TranslationService","level":"info","message":"加载主题提取配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-07-06T05:52:24.039Z"}
{"context":"TranslationService","level":"info","message":"翻译服务已初始化 - 翻译模型: qwen-turbo, 主题提取模型: qwen-turbo","timestamp":"2025-07-06T05:52:24.040Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-06T05:52:24.041Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-06T05:52:24.042Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-06T05:52:24.044Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-06T05:52:24.044Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-06T05:52:24.044Z"}
{"context":"LLMConfigService","level":"info","message":"LLM配置管理服务初始化完成","timestamp":"2025-07-06T05:52:24.045Z"}
{"context":"InstanceLoader","level":"info","message":"ElasticsearchModule dependencies initialized","timestamp":"2025-07-06T05:52:24.053Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-06T05:52:24.053Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"TLE查询配置加载完成: 时间窗口=30分钟, 最大记录数=50000","timestamp":"2025-07-06T05:52:24.059Z"}
{"context":"InstanceLoader","level":"info","message":"OrbitAnalysisModule dependencies initialized","timestamp":"2025-07-06T05:52:24.060Z"}
{"context":"InstanceLoader","level":"info","message":"LLMConfigModule dependencies initialized","timestamp":"2025-07-06T05:52:24.061Z"}
{"context":"NewsSchedulerService","level":"info","message":"新闻定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 5,12,18 * * *\",\"timezone\":\"Asia/Shanghai\"}","timestamp":"2025-07-06T05:52:24.062Z"}
{"context":"InstanceLoader","level":"info","message":"ESModule dependencies initialized","timestamp":"2025-07-06T05:52:24.063Z"}
{"context":"InstanceLoader","level":"info","message":"NewsSchedulerModule dependencies initialized","timestamp":"2025-07-06T05:52:24.063Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteTilesModule dependencies initialized","timestamp":"2025-07-06T05:52:24.063Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized","timestamp":"2025-07-06T05:52:24.097Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:52:24.097Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:52:24.097Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:52:24.097Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-06T05:52:24.097Z"}
{"context":"InstanceLoader","level":"info","message":"UserModule dependencies initialized","timestamp":"2025-07-06T05:52:24.097Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-06T05:52:24.098Z"}
{"context":"SatelliteSchedulerService","level":"info","message":"卫星数据定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 3 * * *\",\"timezone\":\"Asia/Shanghai\",\"saveToDatabase\":true}","timestamp":"2025-07-06T05:52:24.098Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-06T05:52:24.098Z"}
{"context":"InstanceLoader","level":"info","message":"AggregationTaskModule dependencies initialized","timestamp":"2025-07-06T05:52:24.099Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-07-06T05:52:24.099Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteModule dependencies initialized","timestamp":"2025-07-06T05:52:24.099Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteSchedulerModule dependencies initialized","timestamp":"2025-07-06T05:52:24.099Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteAggregationController {/local/satellite-aggregation}:","timestamp":"2025-07-06T05:52:24.176Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/status, GET} route","timestamp":"2025-07-06T05:52:24.178Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/running, GET} route","timestamp":"2025-07-06T05:52:24.178Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/auth}:","timestamp":"2025-07-06T05:52:24.178Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/register, POST} route","timestamp":"2025-07-06T05:52:24.178Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/login, POST} route","timestamp":"2025-07-06T05:52:24.178Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/role, PUT} route","timestamp":"2025-07-06T05:52:24.178Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/permissions, GET} route","timestamp":"2025-07-06T05:52:24.179Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users, GET} route","timestamp":"2025-07-06T05:52:24.179Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/profile, GET} route","timestamp":"2025-07-06T05:52:24.179Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/pending-users, GET} route","timestamp":"2025-07-06T05:52:24.179Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/approval, PUT} route","timestamp":"2025-07-06T05:52:24.179Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/user-approval-history, GET} route","timestamp":"2025-07-06T05:52:24.179Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/delete, DELETE} route","timestamp":"2025-07-06T05:52:24.179Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/change-password, PUT} route","timestamp":"2025-07-06T05:52:24.180Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisEventController {/api/debris-events}:","timestamp":"2025-07-06T05:52:24.180Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/debris-events/search, POST} route","timestamp":"2025-07-06T05:52:24.180Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchConstellationController {/constellation}:","timestamp":"2025-07-06T05:52:24.180Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/search, POST} route","timestamp":"2025-07-06T05:52:24.180Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/names, GET} route","timestamp":"2025-07-06T05:52:24.180Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/organizations, GET} route","timestamp":"2025-07-06T05:52:24.180Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/purposes, GET} route","timestamp":"2025-07-06T05:52:24.180Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchCorrelationController {/correlation}:","timestamp":"2025-07-06T05:52:24.180Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/debris-to-event, POST} route","timestamp":"2025-07-06T05:52:24.180Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/event-to-debris, POST} route","timestamp":"2025-07-06T05:52:24.180Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisController {/debris}:","timestamp":"2025-07-06T05:52:24.180Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/search, POST} route","timestamp":"2025-07-06T05:52:24.180Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/names, GET} route","timestamp":"2025-07-06T05:52:24.181Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/object-classes, GET} route","timestamp":"2025-07-06T05:52:24.181Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/missions, GET} route","timestamp":"2025-07-06T05:52:24.181Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/countries, GET} route","timestamp":"2025-07-06T05:52:24.181Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/debug-query-dto, GET} route","timestamp":"2025-07-06T05:52:24.181Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchSatelliteController {/satellite}:","timestamp":"2025-07-06T05:52:24.181Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/search, POST} route","timestamp":"2025-07-06T05:52:24.181Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/names, GET} route","timestamp":"2025-07-06T05:52:24.181Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/statuses, GET} route","timestamp":"2025-07-06T05:52:24.181Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/test-direct-query, POST} route","timestamp":"2025-07-06T05:52:24.181Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchFreqController {/freq}:","timestamp":"2025-07-06T05:52:24.181Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/freq/search, POST} route","timestamp":"2025-07-06T05:52:24.181Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchOrbitController {/orbit}:","timestamp":"2025-07-06T05:52:24.181Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/search, POST} route","timestamp":"2025-07-06T05:52:24.181Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle, POST} route","timestamp":"2025-07-06T05:52:24.182Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle/all, POST} route","timestamp":"2025-07-06T05:52:24.182Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, GET} route","timestamp":"2025-07-06T05:52:24.182Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, PUT} route","timestamp":"2025-07-06T05:52:24.182Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLoopholeController {/loophole}:","timestamp":"2025-07-06T05:52:24.182Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/loophole/search, POST} route","timestamp":"2025-07-06T05:52:24.182Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchNewsController {/api/es/news}:","timestamp":"2025-07-06T05:52:24.182Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/indices, GET} route","timestamp":"2025-07-06T05:52:24.182Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translation-status, GET} route","timestamp":"2025-07-06T05:52:24.183Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translate, POST} route","timestamp":"2025-07-06T05:52:24.183Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/api-stats, GET} route","timestamp":"2025-07-06T05:52:24.183Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failure-stats, GET} route","timestamp":"2025-07-06T05:52:24.183Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/reset-failure-stats, POST} route","timestamp":"2025-07-06T05:52:24.183Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failed-translations, GET} route","timestamp":"2025-07-06T05:52:24.183Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/retry-failed, POST} route","timestamp":"2025-07-06T05:52:24.184Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/extract-themes, POST} route","timestamp":"2025-07-06T05:52:24.184Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/list, POST} route","timestamp":"2025-07-06T05:52:24.184Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/hot-themes, GET} route","timestamp":"2025-07-06T05:52:24.184Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLaunchController {/api/es/launch}:","timestamp":"2025-07-06T05:52:24.184Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/search, POST} route","timestamp":"2025-07-06T05:52:24.184Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/rocket-names, GET} route","timestamp":"2025-07-06T05:52:24.184Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/site-names, GET} route","timestamp":"2025-07-06T05:52:24.184Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/providers, GET} route","timestamp":"2025-07-06T05:52:24.184Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/cospar, POST} route","timestamp":"2025-07-06T05:52:24.185Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-names, GET} route","timestamp":"2025-07-06T05:52:24.185Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/all-launch-sites, GET} route","timestamp":"2025-07-06T05:52:24.185Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-info, GET} route","timestamp":"2025-07-06T05:52:24.185Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/service-providers/search, POST} route","timestamp":"2025-07-06T05:52:24.185Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchRocketController {/api/es/rocket}:","timestamp":"2025-07-06T05:52:24.185Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/rocket/search, POST} route","timestamp":"2025-07-06T05:52:24.185Z"}
{"context":"RoutesResolver","level":"info","message":"OrbitCalculatorController {/api/orbit-calculator}:","timestamp":"2025-07-06T05:52:24.185Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/orbit-calculator/calculate-positions, POST} route","timestamp":"2025-07-06T05:52:24.185Z"}
{"context":"RoutesResolver","level":"info","message":"PassAnalysisController {/api/pass-analysis}:","timestamp":"2025-07-06T05:52:24.185Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/pass-analysis/pass-analysis, POST} route","timestamp":"2025-07-06T05:52:24.185Z"}
{"context":"RoutesResolver","level":"info","message":"ConjunctionAnalysisController {/api/conjunction-analysis}:","timestamp":"2025-07-06T05:52:24.185Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/conjunction-analysis/conjunction-analysis, POST} route","timestamp":"2025-07-06T05:52:24.185Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteController {/local/satellite}:","timestamp":"2025-07-06T05:52:24.186Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/search, POST} route","timestamp":"2025-07-06T05:52:24.186Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/names, GET} route","timestamp":"2025-07-06T05:52:24.186Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/statuses, GET} route","timestamp":"2025-07-06T05:52:24.186Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync, POST} route","timestamp":"2025-07-06T05:52:24.186Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync-all, POST} route","timestamp":"2025-07-06T05:52:24.186Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate, POST} route","timestamp":"2025-07-06T05:52:24.186Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/clear, POST} route","timestamp":"2025-07-06T05:52:24.186Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-classes-local, GET} route","timestamp":"2025-07-06T05:52:24.186Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-types-local, GET} route","timestamp":"2025-07-06T05:52:24.186Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/:id, GET} route","timestamp":"2025-07-06T05:52:24.186Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite, GET} route","timestamp":"2025-07-06T05:52:24.186Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-aggregation, POST} route","timestamp":"2025-07-06T05:52:24.187Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-custom-aggregation, POST} route","timestamp":"2025-07-06T05:52:24.187Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate-all, POST} route","timestamp":"2025-07-06T05:52:24.187Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/incremental-aggregate, POST} route","timestamp":"2025-07-06T05:52:24.187Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-orbit-info, POST} route","timestamp":"2025-07-06T05:52:24.187Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-users, POST} route","timestamp":"2025-07-06T05:52:24.187Z"}
{"context":"RoutesResolver","level":"info","message":"ConstellationController {/local/constellation}:","timestamp":"2025-07-06T05:52:24.191Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation, GET} route","timestamp":"2025-07-06T05:52:24.191Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/update, POST} route","timestamp":"2025-07-06T05:52:24.191Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/with-tle, GET} route","timestamp":"2025-07-06T05:52:24.191Z"}
{"context":"RoutesResolver","level":"info","message":"AggregationTaskController {/tasks/aggregation}:","timestamp":"2025-07-06T05:52:24.191Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, GET} route","timestamp":"2025-07-06T05:52:24.191Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/latest, GET} route","timestamp":"2025-07-06T05:52:24.191Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/running, GET} route","timestamp":"2025-07-06T05:52:24.191Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id, GET} route","timestamp":"2025-07-06T05:52:24.191Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, POST} route","timestamp":"2025-07-06T05:52:24.192Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/start, POST} route","timestamp":"2025-07-06T05:52:24.192Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/update-progress, POST} route","timestamp":"2025-07-06T05:52:24.192Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/complete, POST} route","timestamp":"2025-07-06T05:52:24.192Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/fail, POST} route","timestamp":"2025-07-06T05:52:24.192Z"}
{"context":"RoutesResolver","level":"info","message":"DatabaseController {/api/v1/database}:","timestamp":"2025-07-06T05:52:24.192Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/query, POST} route","timestamp":"2025-07-06T05:52:24.192Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/filter-satellites, POST} route","timestamp":"2025-07-06T05:52:24.192Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-users, GET} route","timestamp":"2025-07-06T05:52:24.192Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-purposes, GET} route","timestamp":"2025-07-06T05:52:24.192Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-contractors, GET} route","timestamp":"2025-07-06T05:52:24.192Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-sites, GET} route","timestamp":"2025-07-06T05:52:24.192Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-vehicles, GET} route","timestamp":"2025-07-06T05:52:24.193Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteTilesController {/tiles}:","timestamp":"2025-07-06T05:52:24.193Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/generate, POST} route","timestamp":"2025-07-06T05:52:24.193Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/status, GET} route","timestamp":"2025-07-06T05:52:24.193Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/satellites, GET} route","timestamp":"2025-07-06T05:52:24.193Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/metadata, GET} route","timestamp":"2025-07-06T05:52:24.193Z"}
{"context":"RoutesResolver","level":"info","message":"NewsSchedulerController {/api/news-scheduler}:","timestamp":"2025-07-06T05:52:24.193Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/status, GET} route","timestamp":"2025-07-06T05:52:24.193Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/trigger, POST} route","timestamp":"2025-07-06T05:52:24.193Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/config, POST} route","timestamp":"2025-07-06T05:52:24.193Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/stop, POST} route","timestamp":"2025-07-06T05:52:24.193Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/cron-expressions, GET} route","timestamp":"2025-07-06T05:52:24.194Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteSchedulerController {/api/satellite-scheduler}:","timestamp":"2025-07-06T05:52:24.194Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/status, GET} route","timestamp":"2025-07-06T05:52:24.194Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/trigger, POST} route","timestamp":"2025-07-06T05:52:24.194Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/config, POST} route","timestamp":"2025-07-06T05:52:24.194Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/stop, POST} route","timestamp":"2025-07-06T05:52:24.194Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/cron-expressions, GET} route","timestamp":"2025-07-06T05:52:24.194Z"}
{"context":"RoutesResolver","level":"info","message":"LLMConfigController {/llm-config}:","timestamp":"2025-07-06T05:52:24.194Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config, GET} route","timestamp":"2025-07-06T05:52:24.194Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/:configType, GET} route","timestamp":"2025-07-06T05:52:24.194Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config, PUT} route","timestamp":"2025-07-06T05:52:24.194Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/test, POST} route","timestamp":"2025-07-06T05:52:24.194Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/reset, POST} route","timestamp":"2025-07-06T05:52:24.194Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/stats/summary, GET} route","timestamp":"2025-07-06T05:52:24.194Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/stats, DELETE} route","timestamp":"2025-07-06T05:52:24.194Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/export/all, GET} route","timestamp":"2025-07-06T05:52:24.195Z"}
{"context":"DatabaseService","level":"info","message":"正在检查并启用pg_trgm扩展...","timestamp":"2025-07-06T05:52:24.201Z"}
{"context":"DatabaseService","level":"info","message":"pg_trgm扩展已启用","timestamp":"2025-07-06T05:52:24.204Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-07-06T05:52:24.219Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:52:24.223Z"}
{"context":"ElasticsearchRocketService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:52:24.224Z"}
{"context":"ElasticsearchSatelliteService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:52:24.224Z"}
{"context":"ElasticsearchLaunchService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:52:24.224Z"}
{"context":"ElasticsearchBaseService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:52:24.225Z"}
{"context":"ElasticsearchConstellationService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:52:24.225Z"}
{"context":"ElasticsearchDebrisEventService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:52:24.225Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-06T05:52:24.225Z"}
