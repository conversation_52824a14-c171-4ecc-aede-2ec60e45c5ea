"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PREDEFINED_CRON_EXPRESSIONS = exports.validateNewsSchedulerConfig = exports.getNewsSchedulerConfig = exports.DEFAULT_NEWS_SCHEDULER_CONFIG = exports.TaskStatus = void 0;
const news_types_1 = require("../src/elasticsearch/types/news.types");
var TaskStatus;
(function (TaskStatus) {
    TaskStatus["IDLE"] = "idle";
    TaskStatus["RUNNING"] = "running";
    TaskStatus["COMPLETED"] = "completed";
    TaskStatus["FAILED"] = "failed";
})(TaskStatus = exports.TaskStatus || (exports.TaskStatus = {}));
exports.DEFAULT_NEWS_SCHEDULER_CONFIG = {
    enabled: true,
    cronExpression: '0 5,12,18 * * *',
    timezone: 'Asia/Shanghai',
    translationConfig: {
        batchSize: 20,
        maxDocs: 0,
        forceReprocess: false,
        llmMode: news_types_1.LLMMode.DEFAULT
    },
    themeExtractionConfig: {
        batchSize: 15,
        maxDocs: 0,
        forceReprocess: false,
        llmMode: news_types_1.LLMMode.DEFAULT
    },
    maxRetries: 3,
    retryDelay: 60000
};
function getNewsSchedulerConfig() {
    return {
        enabled: process.env.NEWS_SCHEDULER_ENABLED !== 'false',
        cronExpression: process.env.NEWS_SCHEDULER_CRON || exports.DEFAULT_NEWS_SCHEDULER_CONFIG.cronExpression,
        timezone: process.env.NEWS_SCHEDULER_TIMEZONE || exports.DEFAULT_NEWS_SCHEDULER_CONFIG.timezone,
        translationConfig: {
            batchSize: parseInt(process.env.NEWS_TRANSLATION_BATCH_SIZE || '20'),
            maxDocs: parseInt(process.env.NEWS_TRANSLATION_MAX_DOCS || '0'),
            forceReprocess: process.env.NEWS_TRANSLATION_FORCE_REPROCESS === 'true',
            llmMode: process.env.NEWS_TRANSLATION_LLM_MODE || news_types_1.LLMMode.DEFAULT,
            customModel: process.env.NEWS_TRANSLATION_CUSTOM_MODEL
        },
        themeExtractionConfig: {
            batchSize: parseInt(process.env.NEWS_THEME_BATCH_SIZE || '15'),
            maxDocs: parseInt(process.env.NEWS_THEME_MAX_DOCS || '0'),
            forceReprocess: process.env.NEWS_THEME_FORCE_REPROCESS === 'true',
            llmMode: process.env.NEWS_THEME_LLM_MODE || news_types_1.LLMMode.DEFAULT,
            customModel: process.env.NEWS_THEME_CUSTOM_MODEL
        },
        maxRetries: parseInt(process.env.NEWS_SCHEDULER_MAX_RETRIES || '3'),
        retryDelay: parseInt(process.env.NEWS_SCHEDULER_RETRY_DELAY || '60000')
    };
}
exports.getNewsSchedulerConfig = getNewsSchedulerConfig;
function validateNewsSchedulerConfig(config) {
    if (!config.cronExpression) {
        throw new Error('cron表达式不能为空');
    }
    if (config.maxRetries < 0) {
        throw new Error('最大重试次数不能小于0');
    }
    if (config.retryDelay <= 0) {
        throw new Error('重试延迟时间必须大于0');
    }
    if (config.translationConfig.batchSize <= 0) {
        throw new Error('翻译批次大小必须大于0');
    }
    if (config.themeExtractionConfig.batchSize <= 0) {
        throw new Error('主题提取批次大小必须大于0');
    }
}
exports.validateNewsSchedulerConfig = validateNewsSchedulerConfig;
exports.PREDEFINED_CRON_EXPRESSIONS = {
    DAILY_5_12_18: '0 5,12,18 * * *',
    DAILY_6_14_22: '0 6,14,22 * * *',
    DAILY_8_16: '0 8,16 * * *',
    DAILY_MIDNIGHT: '0 0 * * *',
    HOURLY: '0 * * * *',
    EVERY_2_HOURS: '0 */2 * * *',
    EVERY_4_HOURS: '0 */4 * * *',
    EVERY_6_HOURS: '0 */6 * * *',
    WEEKDAYS_9_17: '0 9,17 * * 1-5',
    WEEKDAYS_NOON: '0 12 * * 1-5',
};
//# sourceMappingURL=news-scheduler.config.js.map