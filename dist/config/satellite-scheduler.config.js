"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SATELLITE_PREDEFINED_CRON_EXPRESSIONS = exports.validateSatelliteSchedulerConfig = exports.getSatelliteSchedulerConfig = exports.DEFAULT_SATELLITE_SCHEDULER_CONFIG = exports.SatelliteTaskStatus = void 0;
var SatelliteTaskStatus;
(function (SatelliteTaskStatus) {
    SatelliteTaskStatus["IDLE"] = "idle";
    SatelliteTaskStatus["RUNNING"] = "running";
    SatelliteTaskStatus["COMPLETED"] = "completed";
    SatelliteTaskStatus["FAILED"] = "failed";
})(SatelliteTaskStatus = exports.SatelliteTaskStatus || (exports.SatelliteTaskStatus = {}));
exports.DEFAULT_SATELLITE_SCHEDULER_CONFIG = {
    enabled: true,
    cronExpression: '0 3 * * *',
    timezone: 'Asia/Shanghai',
    maxRetries: 3,
    retryDelay: 300000,
    saveToDatabase: true
};
function getSatelliteSchedulerConfig() {
    return {
        enabled: process.env.SATELLITE_SCHEDULER_ENABLED !== 'false',
        cronExpression: process.env.SATELLITE_SCHEDULER_CRON || exports.DEFAULT_SATELLITE_SCHEDULER_CONFIG.cronExpression,
        timezone: process.env.SATELLITE_SCHEDULER_TIMEZONE || exports.DEFAULT_SATELLITE_SCHEDULER_CONFIG.timezone,
        maxRetries: parseInt(process.env.SATELLITE_SCHEDULER_MAX_RETRIES || '3'),
        retryDelay: parseInt(process.env.SATELLITE_SCHEDULER_RETRY_DELAY || '300000'),
        saveToDatabase: process.env.SATELLITE_SCHEDULER_SAVE_TO_DB !== 'false'
    };
}
exports.getSatelliteSchedulerConfig = getSatelliteSchedulerConfig;
function validateSatelliteSchedulerConfig(config) {
    if (!config.cronExpression) {
        throw new Error('cron表达式不能为空');
    }
    if (config.maxRetries < 0) {
        throw new Error('最大重试次数不能小于0');
    }
    if (config.retryDelay <= 0) {
        throw new Error('重试延迟时间必须大于0');
    }
}
exports.validateSatelliteSchedulerConfig = validateSatelliteSchedulerConfig;
exports.SATELLITE_PREDEFINED_CRON_EXPRESSIONS = {
    DAILY_3AM: '0 3 * * *',
    DAILY_2AM: '0 2 * * *',
    DAILY_4AM: '0 4 * * *',
    DAILY_MIDNIGHT: '0 0 * * *',
    DAILY_6AM: '0 6 * * *',
    EVERY_12_HOURS: '0 3,15 * * *',
    WEEKLY_SUNDAY_3AM: '0 3 * * 0',
    WEEKLY_MONDAY_3AM: '0 3 * * 1',
    HOURLY: '0 * * * *',
    EVERY_30_MIN: '*/30 * * * *',
};
//# sourceMappingURL=satellite-scheduler.config.js.map