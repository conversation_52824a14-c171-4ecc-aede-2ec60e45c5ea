export interface TleQueryConfig {
    timeWindowMinutes: number;
    maxRecords: number;
    timeout: number;
    scrollSize: number;
    scrollTimeout: string;
}
declare const DEFAULT_TLE_QUERY_CONFIG: TleQueryConfig;
export declare function getTleQueryConfig(): TleQueryConfig;
export declare function validateTleQueryConfig(config: TleQueryConfig): void;
export declare function mergeTleQueryConfig(baseConfig: TleQueryConfig, overrides: Partial<TleQueryConfig>): TleQueryConfig;
export { DEFAULT_TLE_QUERY_CONFIG };
