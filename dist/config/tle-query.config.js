"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_TLE_QUERY_CONFIG = exports.mergeTleQueryConfig = exports.validateTleQueryConfig = exports.getTleQueryConfig = void 0;
const DEFAULT_TLE_QUERY_CONFIG = {
    timeWindowMinutes: 30,
    maxRecords: 50000,
    timeout: 300000,
    scrollSize: 10000,
    scrollTimeout: '10m'
};
exports.DEFAULT_TLE_QUERY_CONFIG = DEFAULT_TLE_QUERY_CONFIG;
function getTleQueryConfig() {
    return {
        timeWindowMinutes: parseInt(process.env.TLE_QUERY_TIME_WINDOW_MINUTES || '30', 10),
        maxRecords: parseInt(process.env.TLE_QUERY_MAX_RECORDS || '50000', 10),
        timeout: parseInt(process.env.TLE_QUERY_TIMEOUT || '300000', 10),
        scrollSize: parseInt(process.env.TLE_QUERY_SCROLL_SIZE || '10000', 10),
        scrollTimeout: process.env.TLE_QUERY_SCROLL_TIMEOUT || '10m'
    };
}
exports.getTleQueryConfig = getTleQueryConfig;
function validateTleQueryConfig(config) {
    if (config.timeWindowMinutes <= 0 || config.timeWindowMinutes > 1440) {
        throw new Error(`时间窗口必须在1-1440分钟之间，当前值: ${config.timeWindowMinutes}`);
    }
    if (config.maxRecords <= 0 || config.maxRecords > 100000) {
        throw new Error(`最大记录数必须在1-100000之间，当前值: ${config.maxRecords}`);
    }
    if (config.timeout <= 0 || config.timeout > 600000) {
        throw new Error(`超时时间必须在1-600000毫秒之间（1毫秒到10分钟），当前值: ${config.timeout}`);
    }
    if (config.scrollSize <= 0 || config.scrollSize > 10000) {
        throw new Error(`滚动查询批次大小必须在1-10000之间，当前值: ${config.scrollSize}`);
    }
}
exports.validateTleQueryConfig = validateTleQueryConfig;
function mergeTleQueryConfig(baseConfig, overrides) {
    return Object.assign(Object.assign({}, baseConfig), overrides);
}
exports.mergeTleQueryConfig = mergeTleQueryConfig;
//# sourceMappingURL=tle-query.config.js.map