import { LLMMode, TranslationStatistics } from '../src/elasticsearch/types/news.types';
export interface NewsSchedulerConfig {
    enabled: boolean;
    cronExpression: string;
    timezone: string;
    translationConfig: TaskConfig;
    themeExtractionConfig: TaskConfig;
    maxRetries: number;
    retryDelay: number;
}
export interface TaskConfig {
    batchSize: number;
    maxDocs: number;
    forceReprocess: boolean;
    specificIndexes?: string[];
    llmMode?: LLMMode;
    customModel?: string;
}
export declare enum TaskStatus {
    IDLE = "idle",
    RUNNING = "running",
    COMPLETED = "completed",
    FAILED = "failed"
}
export interface TaskExecutionResult {
    taskType: 'translation' | 'theme_extraction';
    status: TaskStatus;
    startTime: Date;
    endTime?: Date;
    duration?: number;
    statistics?: TranslationStatistics;
    error?: string;
}
export declare const DEFAULT_NEWS_SCHEDULER_CONFIG: NewsSchedulerConfig;
export declare function getNewsSchedulerConfig(): NewsSchedulerConfig;
export declare function validateNewsSchedulerConfig(config: NewsSchedulerConfig): void;
export declare const PREDEFINED_CRON_EXPRESSIONS: {
    DAILY_5_12_18: string;
    DAILY_6_14_22: string;
    DAILY_8_16: string;
    DAILY_MIDNIGHT: string;
    HOURLY: string;
    EVERY_2_HOURS: string;
    EVERY_4_HOURS: string;
    EVERY_6_HOURS: string;
    WEEKDAYS_9_17: string;
    WEEKDAYS_NOON: string;
};
