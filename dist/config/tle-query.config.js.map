{"version": 3, "file": "tle-query.config.js", "sourceRoot": "", "sources": ["../../config/tle-query.config.ts"], "names": [], "mappings": ";;;AAmBA,MAAM,wBAAwB,GAAmB;IAC/C,iBAAiB,EAAE,EAAE;IACrB,UAAU,EAAE,KAAK;IACjB,OAAO,EAAE,MAAM;IACf,UAAU,EAAE,KAAK;IACjB,aAAa,EAAE,KAAK;CACrB,CAAC;AAqDO,4DAAwB;AA/CjC,SAAgB,iBAAiB;IAC/B,OAAO;QACL,iBAAiB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,IAAI,EAAE,EAAE,CAAC;QAClF,UAAU,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,OAAO,EAAE,EAAE,CAAC;QACtE,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,QAAQ,EAAE,EAAE,CAAC;QAChE,UAAU,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,OAAO,EAAE,EAAE,CAAC;QACtE,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI,KAAK;KAC7D,CAAC;AACJ,CAAC;AARD,8CAQC;AAMD,SAAgB,sBAAsB,CAAC,MAAsB;IAC3D,IAAI,MAAM,CAAC,iBAAiB,IAAI,CAAC,IAAI,MAAM,CAAC,iBAAiB,GAAG,IAAI,EAAE;QACpE,MAAM,IAAI,KAAK,CAAC,0BAA0B,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC;KACvE;IAED,IAAI,MAAM,CAAC,UAAU,IAAI,CAAC,IAAI,MAAM,CAAC,UAAU,GAAG,MAAM,EAAE;QACxD,MAAM,IAAI,KAAK,CAAC,2BAA2B,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;KACjE;IAED,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,GAAG,MAAM,EAAE;QAClD,MAAM,IAAI,KAAK,CAAC,sCAAsC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;KACzE;IAED,IAAI,MAAM,CAAC,UAAU,IAAI,CAAC,IAAI,MAAM,CAAC,UAAU,GAAG,KAAK,EAAE;QACvD,MAAM,IAAI,KAAK,CAAC,6BAA6B,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;KACnE;AACH,CAAC;AAhBD,wDAgBC;AAOD,SAAgB,mBAAmB,CACjC,UAA0B,EAC1B,SAAkC;IAElC,uCACK,UAAU,GACV,SAAS,EACZ;AACJ,CAAC;AARD,kDAQC"}