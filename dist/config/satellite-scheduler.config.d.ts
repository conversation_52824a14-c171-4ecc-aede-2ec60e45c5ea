export interface SatelliteSchedulerConfig {
    enabled: boolean;
    cronExpression: string;
    timezone: string;
    maxRetries: number;
    retryDelay: number;
    saveToDatabase: boolean;
}
export declare enum SatelliteTaskStatus {
    IDLE = "idle",
    RUNNING = "running",
    COMPLETED = "completed",
    FAILED = "failed"
}
export interface SatelliteTaskExecutionResult {
    taskType: 'satellite_incremental_aggregate';
    status: SatelliteTaskStatus;
    startTime: Date;
    endTime?: Date;
    duration?: number;
    totalNewAggregated?: number;
    error?: string;
}
export declare const DEFAULT_SATELLITE_SCHEDULER_CONFIG: SatelliteSchedulerConfig;
export declare function getSatelliteSchedulerConfig(): SatelliteSchedulerConfig;
export declare function validateSatelliteSchedulerConfig(config: SatelliteSchedulerConfig): void;
export declare const SATELLITE_PREDEFINED_CRON_EXPRESSIONS: {
    DAILY_3AM: string;
    DAILY_2AM: string;
    DAILY_4AM: string;
    DAILY_MIDNIGHT: string;
    DAILY_6AM: string;
    EVERY_12_HOURS: string;
    WEEKLY_SUNDAY_3AM: string;
    WEEKLY_MONDAY_3AM: string;
    HOURLY: string;
    EVERY_30_MIN: string;
};
