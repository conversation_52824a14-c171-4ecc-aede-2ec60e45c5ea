export declare enum LLMProvider {
    QWEN = "qwen",
    OPENAI = "openai",
    CLAUDE = "claude",
    GLM = "glm"
}
export interface LLMConfig {
    provider: LLMProvider;
    model: string;
    apiKey: string;
    baseURL?: string;
    maxTokens?: number;
    temperature?: number;
    timeout?: number;
    maxRetries?: number;
    retryDelay?: number;
    maxConcurrentRequests?: number;
}
export interface TranslationConfig extends LLMConfig {
    systemPrompt: string;
    maxTextLength: number;
    batchSize?: number;
}
export interface ThemeExtractionConfig extends LLMConfig {
    systemPrompt: string;
    maxContentLength: number;
    maxThemes?: number;
}
export declare const DEFAULT_TRANSLATION_CONFIG: TranslationConfig;
export declare const DEFAULT_THEME_EXTRACTION_CONFIG: ThemeExtractionConfig;
export declare const ALTERNATIVE_CONFIGS: {
    ULTRA_LONG_TRANSLATION: TranslationConfig;
    HIGH_QUALITY_TRANSLATION: TranslationConfig;
    FAST_TRANSLATION: TranslationConfig;
    ULTRA_LONG_THEME_EXTRACTION: ThemeExtractionConfig;
    HIGH_QUALITY_THEME_EXTRACTION: ThemeExtractionConfig;
    FAST_THEME_EXTRACTION: ThemeExtractionConfig;
};
export declare function getTranslationConfig(mode?: 'default' | 'high_quality' | 'fast' | 'ultra_long'): TranslationConfig;
export declare function getThemeExtractionConfig(mode?: 'default' | 'high_quality' | 'fast' | 'ultra_long'): ThemeExtractionConfig;
export declare function mergeConfig<T extends LLMConfig>(baseConfig: T, customConfig: Partial<T>): T;
export declare function validateLLMConfig(config: LLMConfig): void;
