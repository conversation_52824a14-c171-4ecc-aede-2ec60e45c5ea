export interface LaunchSite {
    code: string;
    englishName: string;
    chineseName: string;
    country?: string;
    category: 'USA' | 'China' | 'Russia' | 'Other' | 'Commercial' | 'TestSite';
    aliases?: string[];
}
export declare const launchSites: LaunchSite[];
export declare function getLaunchSiteByCode(code: string): LaunchSite | undefined;
export declare function getLaunchSitesByCategory(category: LaunchSite['category']): LaunchSite[];
export declare function getAllLaunchSiteCodes(): string[];
export declare function getLaunchSitesByCountry(country: string): LaunchSite[];
export declare function findLaunchSiteByName(name: string): LaunchSite | undefined;
export declare function searchLaunchSites(keyword: string): LaunchSite[];
export declare function getLaunchSiteChineseName(identifier: string): string;
