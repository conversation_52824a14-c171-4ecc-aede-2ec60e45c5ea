/**
 * 测试API层面的超长文档翻译处理
 */

const https = require('http');

function testUltraLongDocumentAPI() {
  console.log('🚀 测试API层面的超长文档翻译处理...');
  
  const postData = JSON.stringify({
    documentId: "bbfa7e8c6c4ff26acf0e27f7fff9ba97",
    specificIndexes: ["news_space"],
    forceRetranslate: true,
    autoExtractThemes: true
  });

  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/es/news/translate',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwic3ViIjozLCJpYXQiOjE3NTE0MjMxMjIsImV4cCI6MTc1MTUwOTUyMn0.enWpeiLQ5jDX61vvoffX-_1bgo3TVMVFCHkFbSQqEz4',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  const startTime = Date.now();
  console.log(`⏰ 开始时间: ${new Date().toISOString()}`);

  const req = https.request(options, (res) => {
    const elapsedTime = Date.now() - startTime;
    console.log(`📡 响应状态: ${res.statusCode}`);
    console.log(`⚡ 响应时间: ${elapsedTime}ms`);

    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      try {
        const result = JSON.parse(data);
        console.log('\n✅ API响应结果:');
        console.log(`- 总处理文档数: ${result.total || 0}`);
        console.log(`- 成功处理: ${result.success || 0}`);
        console.log(`- 失败处理: ${result.failed || 0}`);
        console.log(`- 跳过处理: ${result.skipped || 0}`);
        
        if (result.success === 1 && elapsedTime < 5000) {
          console.log('\n🎯 成功！超长文档已被快速处理（跳过翻译）');
        } else if (elapsedTime > 10000) {
          console.log('\n⚠️  响应时间过长，可能还在进行实际翻译');
        } else {
          console.log('\n✅ 处理完成');
        }
        
      } catch (error) {
        console.error('❌ 解析响应失败:', error.message);
        console.log('原始响应:', data);
      }
    });
  });

  req.on('error', (error) => {
    const elapsedTime = Date.now() - startTime;
    console.error(`❌ 请求失败 (${elapsedTime}ms):`, error.message);
  });

  req.on('timeout', () => {
    console.error('❌ 请求超时');
    req.abort();
  });

  // 设置30秒超时
  req.setTimeout(30000);

  req.write(postData);
  req.end();
}

// 运行测试
testUltraLongDocumentAPI(); 