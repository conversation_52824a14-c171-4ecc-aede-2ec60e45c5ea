/**
 * 测试超长文档翻译功能
 * 直接调用翻译服务验证改进的智能配置选择和分段策略
 */

const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('./dist/src/app.module');

async function testUltraLongTranslation() {
  console.log('🚀 初始化NestJS应用...');
  
  try {
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // 获取翻译服务
    const { ElasticsearchNewsService } = require('./dist/src/elasticsearch/services/elasticsearch.news.service');
    const newsService = app.get(ElasticsearchNewsService);
    
    console.log('✅ 翻译服务获取成功');
    
    // 测试文档ID
    const documentId = 'bbfa7e8c6c4ff26acf0e27f7fff9ba97';
    
    console.log(`📄 开始测试超长文档翻译: ${documentId}`);
    console.log('⏰ 开始时间:', new Date().toISOString());
    
    const startTime = Date.now();
    
    try {
      // 调用翻译服务
      const result = await newsService.translateNews({
        documentId: documentId,
        specificIndexes: ['news_space'],
        forceRetranslate: true,
        autoExtractThemes: true,
        llmMode: 'high_quality'
      });
      
      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;
      
      console.log('\n✅ 翻译任务完成!');
      console.log(`⏰ 总耗时: ${duration.toFixed(2)}秒`);
      console.log('📊 翻译结果统计:');
      console.log(`  - 总文档数: ${result.total}`);
      console.log(`  - 成功翻译: ${result.success}`);
      console.log(`  - 翻译失败: ${result.failed}`);
      console.log(`  - 跳过文档: ${result.skipped}`);
      
      if (result.themeExtraction) {
        console.log('🎯 主题提取统计:');
        console.log(`  - 成功提取: ${result.themeExtraction.success}`);
        console.log(`  - 提取失败: ${result.themeExtraction.failed}`);
      }
      
      // 获取API调用统计
      const apiStats = newsService.getAPIStats();
      console.log('\n📈 API调用统计:');
      console.log(`  - 总调用次数: ${apiStats.callCount}`);
      console.log(`  - 总耗时: ${apiStats.totalTime}ms`);
      console.log(`  - 平均耗时: ${apiStats.averageTime.toFixed(2)}ms`);
      console.log(`  - 缓存大小: ${apiStats.cacheSize}`);
      
      // 获取失败统计
      const failureStats = newsService.getFailureStats();
      console.log('\n📊 失败统计:');
      console.log(`  - 成功率: ${failureStats.successRate}%`);
      console.log(`  - 超时错误: ${failureStats.failures.timeout}`);
      console.log(`  - 内容过滤: ${failureStats.failures.contentFilter}`);
      console.log(`  - API限制: ${failureStats.failures.rateLimit}`);
      console.log(`  - 网络错误: ${failureStats.failures.networkError}`);
      
    } catch (error) {
      console.error('\n❌ 翻译失败:', error.message);
      console.error('错误详情:', error.stack);
      
      // 仍然显示统计信息
      const apiStats = newsService.getAPIStats();
      console.log('\n📈 API调用统计 (失败前):');
      console.log(`  - 总调用次数: ${apiStats.callCount}`);
      console.log(`  - 总耗时: ${apiStats.totalTime}ms`);
      
      const failureStats = newsService.getFailureStats();
      console.log('\n📊 失败统计:');
      console.log(`  - 总尝试次数: ${failureStats.totalAttempts}`);
      console.log(`  - 成功处理: ${failureStats.successfulProcessing}`);
      console.log(`  - 超时错误: ${failureStats.failures.timeout}`);
      console.log(`  - 内容过滤: ${failureStats.failures.contentFilter}`);
    }
    
    await app.close();
    
  } catch (error) {
    console.error('❌ 应用初始化失败:', error.message);
    console.error('错误详情:', error.stack);
  }
}

// 运行测试
testUltraLongTranslation()
  .then(() => {
    console.log('\n🎉 测试完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }); 