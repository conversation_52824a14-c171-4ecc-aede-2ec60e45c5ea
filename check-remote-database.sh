#!/bin/bash

echo "=== 手动检查远程服务器数据库状态 ==="
echo "请根据提示修改服务器地址和执行相应命令"
echo ""

# 设置变量（请修改为您的服务器地址）
SERVER_URL="http://your-server-ip:3000"  # 请替换为实际服务器地址
TOKEN_FILE="token.json"

echo "步骤1: 修改服务器地址"
echo "请编辑此脚本，将 SERVER_URL 改为您的实际服务器地址"
echo "当前设置: $SERVER_URL"
echo ""

echo "步骤2: 获取认证令牌"
echo "在远程服务器上运行以下命令获取管理员令牌："
echo "cd /path/to/your/project && ./get-admin-token.sh"
echo "或者使用API获取："
echo "curl -X POST \"$SERVER_URL/auth/login\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"username\":\"admin\",\"password\":\"your_password\"}'"
echo ""

echo "步骤3: 将令牌保存到 token.json 文件"
echo "格式: {\"token\":\"your_jwt_token_here\"}"
echo ""

# 检查token文件
if [[ -f "$TOKEN_FILE" ]]; then
    TOKEN=$(cat "$TOKEN_FILE" | jq -r '.token // .access_token // .')
    if [[ "$TOKEN" != "null" && -n "$TOKEN" ]]; then
        echo "✅ 找到认证令牌，开始检查数据库..."
        echo ""
        
        echo "=== 1. 检查数据库连接 ==="
        echo "执行命令:"
        echo "curl -X GET \"$SERVER_URL/api/database/health\" \\"
        echo "  -H \"Authorization: Bearer $TOKEN\""
        echo ""
        curl -s -X GET "$SERVER_URL/api/database/health" \
          -H "Authorization: Bearer $TOKEN" | jq '.'
        echo ""
        
        echo "=== 2. 检查 satellites 表是否存在 ==="
        echo "执行命令:"
        echo "curl -X GET \"$SERVER_URL/api/database/table-info?table_name=satellites\" \\"
        echo "  -H \"Authorization: Bearer $TOKEN\""
        echo ""
        curl -s -X GET "$SERVER_URL/api/database/table-info?table_name=satellites" \
          -H "Authorization: Bearer $TOKEN" | jq '.'
        echo ""
        
        echo "=== 3. 检查 satellites 表记录总数 ==="
        echo "执行SQL: SELECT COUNT(*) as total FROM satellites"
        curl -s -X POST "$SERVER_URL/api/database/query" \
          -H "Authorization: Bearer $TOKEN" \
          -H "Content-Type: application/json" \
          -d '{
            "query": "SELECT COUNT(*) as total FROM satellites",
            "params": []
          }' | jq '.'
        echo ""
        
        echo "=== 4. 检查表结构 ==="
        echo "执行SQL: SELECT column_name, data_type, is_nullable FROM information_schema.columns WHERE table_name = 'satellites'"
        curl -s -X POST "$SERVER_URL/api/database/query" \
          -H "Authorization: Bearer $TOKEN" \
          -H "Content-Type: application/json" \
          -d '{
            "query": "SELECT column_name, data_type, is_nullable FROM information_schema.columns WHERE table_name = '\''satellites'\'' ORDER BY ordinal_position",
            "params": []
          }' | jq '.'
        echo ""
        
        echo "=== 5. 查看前5条记录示例 ==="
        echo "执行SQL: SELECT id, satellite_name, constellation, orbit_info FROM satellites LIMIT 5"
        curl -s -X POST "$SERVER_URL/api/database/query" \
          -H "Authorization: Bearer $TOKEN" \
          -H "Content-Type: application/json" \
          -d '{
            "query": "SELECT id, satellite_name, constellation, orbit_info FROM satellites LIMIT 5",
            "params": []
          }' | jq '.'
        echo ""
        
        echo "=== 6. 检查具有 orbit_info 的记录数量 ==="
        echo "执行SQL: SELECT COUNT(*) as count_with_orbit_info FROM satellites WHERE orbit_info IS NOT NULL AND orbit_info != '[]'"
        curl -s -X POST "$SERVER_URL/api/database/query" \
          -H "Authorization: Bearer $TOKEN" \
          -H "Content-Type: application/json" \
          -d '{
            "query": "SELECT COUNT(*) as count_with_orbit_info FROM satellites WHERE orbit_info IS NOT NULL AND orbit_info != '\''[]'\''",
            "params": []
          }' | jq '.'
        echo ""
        
        echo "=== 7. 检查具有 TLE 轨道信息的记录数量 ==="
        echo "执行SQL: 检查 orbit_info 中包含 orbital_tle 的记录"
        curl -s -X POST "$SERVER_URL/api/database/query" \
          -H "Authorization: Bearer $TOKEN" \
          -H "Content-Type: application/json" \
          -d '{
            "query": "SELECT COUNT(*) as count_with_tle FROM satellites WHERE orbit_info IS NOT NULL AND orbit_info::text != '\''[]'\'' AND (orbit_info::text ILIKE '\''%orbital_tle%'\'' OR jsonb_path_exists(orbit_info, '\''$[*].sources[*] ? (@ == \"orbital_tle\")'\''))",
            "params": []
          }' | jq '.'
        echo ""
        
    else
        echo "❌ token.json 文件中没有有效的认证令牌"
    fi
else
    echo "❌ 未找到 token.json 文件"
fi

echo "=== 手动命令参考 ==="
echo ""
echo "如果上述自动检查失败，您可以手动执行以下命令："
echo ""
echo "1. 获取令牌:"
echo "TOKEN=\$(cat token.json | jq -r '.token')"
echo ""
echo "2. 检查数据库连接:"
echo "curl -X GET \"$SERVER_URL/api/database/health\" -H \"Authorization: Bearer \$TOKEN\""
echo ""
echo "3. 查询数据总数:"
echo "curl -X POST \"$SERVER_URL/api/database/query\" \\"
echo "  -H \"Authorization: Bearer \$TOKEN\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"query\":\"SELECT COUNT(*) FROM satellites\",\"params\":[]}'"
echo ""
echo "4. 查看表结构:"
echo "curl -X POST \"$SERVER_URL/api/database/query\" \\"
echo "  -H \"Authorization: Bearer \$TOKEN\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"query\":\"\\\\d satellites\",\"params\":[]}'"
echo "" 