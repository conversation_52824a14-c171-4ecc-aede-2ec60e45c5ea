#!/bin/bash

# 太空大数据平台后端Ubuntu部署脚本
# 适用于Ubuntu 18.04/20.04/22.04

set -e

echo "======================================"
echo "太空大数据平台后端 - Ubuntu部署脚本"
echo "======================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印函数
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_warning "检测到root用户运行，建议使用普通用户运行部署脚本"
        read -p "是否继续? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 检查Ubuntu版本
check_ubuntu_version() {
    if ! command -v lsb_release &> /dev/null; then
        print_error "无法检测Ubuntu版本，请确保运行在Ubuntu系统上"
        exit 1
    fi
    
    UBUNTU_VERSION=$(lsb_release -rs)
    print_status "检测到Ubuntu版本: $UBUNTU_VERSION"
}

# 更新系统包
update_system() {
    print_status "更新系统包..."
    
    # 修复可能存在的损坏源
    print_status "检查并修复软件源..."
    sudo apt update 2>/dev/null || {
        print_warning "检测到软件源问题，尝试修复..."
        # 移除有问题的Docker源（如果存在）
        sudo rm -f /etc/apt/sources.list.d/docker.list* 2>/dev/null || true
        # 重新更新
        sudo apt update
    }
    
    # 只升级安全更新（可选）
    print_status "升级系统包（可能需要几分钟）..."
    sudo apt upgrade -y || {
        print_warning "系统升级遇到问题，继续安装必要依赖..."
    }
}

# 安装基础依赖
install_dependencies() {
    print_status "安装基础依赖..."
    sudo apt install -y \
        curl \
        wget \
        git \
        build-essential \
        python3 \
        python3-pip \
        sqlite3 \
        ca-certificates \
        gnupg \
        lsb-release
}

# 安装PostgreSQL数据库
install_postgresql() {
    print_status "安装PostgreSQL数据库..."
    
    # 检查是否已安装PostgreSQL
    if command -v psql &> /dev/null; then
        print_status "PostgreSQL已安装，跳过安装步骤"
        return
    fi
    
    # 安装PostgreSQL
    sudo apt install -y postgresql postgresql-contrib
    
    # 启动并启用PostgreSQL服务
    sudo systemctl start postgresql
    sudo systemctl enable postgresql
    
    print_status "PostgreSQL安装完成"
}

# 配置PostgreSQL数据库
configure_postgresql() {
    print_status "配置PostgreSQL数据库..."
    
    # 创建数据库和用户
    sudo -u postgres psql -c "CREATE DATABASE spacedata;" 2>/dev/null || print_warning "数据库spacedata可能已存在"
    sudo -u postgres psql -c "CREATE USER spaceuser WITH PASSWORD 'spacepass123';" 2>/dev/null || print_warning "用户spaceuser可能已存在"
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE spacedata TO spaceuser;" 2>/dev/null || true
    sudo -u postgres psql -c "ALTER USER spaceuser CREATEDB;" 2>/dev/null || true
    
    print_status "PostgreSQL配置完成"
    print_status "数据库: spacedata"
    print_status "用户: spaceuser"
    print_status "密码: spacepass123"
}

# 安装Node.js 18.x
install_nodejs() {
    print_status "安装Node.js 18.x..."
    
    # 检查是否已安装Node.js
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node -v)
        print_status "检测到已安装Node.js版本: $NODE_VERSION"
        
        # 检查版本是否为18.x
        if [[ $NODE_VERSION == v18* ]]; then
            print_status "Node.js版本符合要求，跳过安装"
            return
        else
            print_warning "Node.js版本不符合要求，将安装18.x版本"
        fi
    fi
    
    # 安装Node.js 18.x
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
    
    # 验证安装
    NODE_VERSION=$(node -v)
    NPM_VERSION=$(npm -v)
    print_status "Node.js安装完成: $NODE_VERSION"
    print_status "NPM版本: $NPM_VERSION"
}

# 安装pm2进程管理器
install_pm2() {
    print_status "安装PM2进程管理器..."
    
    if command -v pm2 &> /dev/null; then
        print_status "PM2已安装，跳过"
        return
    fi
    
    sudo npm install -g pm2
    print_status "PM2安装完成"
}

# 安装项目依赖
install_project_dependencies() {
    print_status "安装项目依赖..."
    
    if [ ! -f "package.json" ]; then
        print_error "未找到package.json文件，请确保在项目根目录运行此脚本"
        exit 1
    fi
    
    # 清理node_modules和package-lock.json
    if [ -d "node_modules" ]; then
        print_status "清理旧的node_modules..."
        rm -rf node_modules
    fi
    
    if [ -f "package-lock.json" ]; then
        print_status "清理package-lock.json..."
        rm -f package-lock.json
    fi
    
    # 使用--legacy-peer-deps来解决依赖冲突
    print_status "安装依赖（使用--legacy-peer-deps解决版本冲突）..."
    npm install --legacy-peer-deps || {
        print_warning "标准安装失败，尝试使用--force选项..."
        npm install --force || {
            print_error "依赖安装失败，请检查package.json文件"
            exit 1
        }
    }
    
    print_status "项目依赖安装完成"
}

# 编译项目
build_project() {
    print_status "编译项目..."
    
    # 清理旧的编译文件
    if [ -d "dist" ]; then
        rm -rf dist
    fi
    
    npm run build
    print_status "项目编译完成"
}

# 创建环境变量文件模板（如果不存在）
create_env_template() {
    if [ ! -f ".env" ]; then
        print_status "创建环境变量文件模板..."
        cat > .env << EOF
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=spacedata

# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=24h

# 服务配置
PORT=3001
NODE_ENV=production

# Elasticsearch配置（如果使用）
ES_HOST=localhost
ES_PORT=9200

# Redis配置（如果使用）
REDIS_HOST=localhost
REDIS_PORT=6379

# 大模型API配置（如果使用）
OPENAI_API_KEY=your_openai_api_key
QIANWEN_API_KEY=your_qianwen_api_key

# 日志配置
LOG_LEVEL=info
LOG_DIR=./logs
EOF
        print_warning "已创建环境变量文件模板(.env)，请根据实际情况修改配置"
    else
        print_status "环境变量文件已存在，跳过创建"
    fi
}

# 创建启动脚本
create_start_script() {
    print_status "创建启动脚本..."
    
    cat > start.sh << 'EOF'
#!/bin/bash

# 太空大数据平台启动脚本

# 设置环境变量
export NODE_ENV=production

# 停止已运行的进程
pkill -f "dist/main" || true
sleep 2

# 启动应用
echo "启动太空大数据平台..."
npm run start:prod

echo "应用已启动"
echo "访问地址: http://localhost:3001"
echo "API文档: http://localhost:3001/api-docs"
EOF
    
    chmod +x start.sh
    print_status "启动脚本创建完成"
}

# 创建PM2配置文件
create_pm2_config() {
    print_status "创建PM2配置文件..."
    
    cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'spacedata-backend',
    script: 'dist/src/main.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    time: true
  }]
};
EOF
    
    print_status "PM2配置文件创建完成"
}

# 启动应用
start_application() {
    print_status "启动应用..."
    
    # 确保日志目录存在
    mkdir -p logs
    
    # 使用PM2启动
    pm2 start ecosystem.config.js
    pm2 save
    pm2 startup
    
    print_status "应用已启动"
    print_status "访问地址: http://localhost:3001"
    print_status "API文档: http://localhost:3001/api-docs"
    print_status "查看日志: pm2 logs spacedata-backend"
    print_status "停止应用: pm2 stop spacedata-backend"
    print_status "重启应用: pm2 restart spacedata-backend"
}

# 显示防火墙配置提示
show_firewall_tips() {
    print_warning "防火墙配置提示:"
    echo "如果需要外部访问，请开放3001端口:"
    echo "sudo ufw allow 3001"
    echo "或配置nginx反向代理"
}

# 主函数
main() {
    check_root
    check_ubuntu_version
    
    # 询问是否跳过系统升级
    print_warning "是否跳过系统升级以加快部署速度？"
    read -p "跳过升级? (y/n，默认n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "跳过系统升级..."
        sudo apt update || {
            print_warning "apt update失败，尝试修复软件源..."
            sudo rm -f /etc/apt/sources.list.d/docker.list* 2>/dev/null || true
            sudo apt update
        }
    else
        update_system
    fi
    
    install_dependencies
    
    # 询问是否安装PostgreSQL
    print_warning "是否安装PostgreSQL数据库？"
    read -p "安装PostgreSQL? (y/n，默认y): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Nn]$ ]]; then
        install_postgresql
        configure_postgresql
    else
        print_status "跳过PostgreSQL安装"
    fi
    
    install_nodejs
    install_pm2
    install_project_dependencies
    build_project
    create_env_template
    create_start_script
    create_pm2_config
    start_application
    show_firewall_tips
    
    echo ""
    print_status "======================================"
    print_status "部署完成!"
    print_status "======================================"
    print_status "应用地址: http://localhost:3001"
    print_status "API文档: http://localhost:3001/api-docs"
    print_status "======================================"
}

# 运行主函数
main "$@" 