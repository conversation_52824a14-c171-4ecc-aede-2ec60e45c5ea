/**
 * 脚本功能：
 * 1. 查询Elasticsearch中orbital_tle索引，提取以"starlink"开头且time字段晚于指定时间的卫星记录，
 *    并将satellite_name、norad_id、cospar_id写入 starlink_tle.txt。
 * 2. 调用后端API获取Starlink星座所有卫星信息，提取satellite_name、alternative_name、norad_id、cospar_id写入 starlink_satinfo.txt。
 * 3. 比较两个文件的 norad_id 与 cospar_id，找出 API 结果中在 TLE 文件中缺失的 ID，写入 diff_id.txt。
 *
 * 运行方式（项目根目录）：
 * npx ts-node scripts/extract-starlink.ts
 */

import { Client as ESClient } from '@elastic/elasticsearch';
import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';

interface TleRecord {
  satellite_name: string;
  norad_id: number | null;
  cospar_id: string | null;
}

interface SatInfoRecord {
  satellite_name: string;
  alternative_name: string;
  norad_id: number | null;
  cospar_id: string | null;
}

// 输出文件定义
const OUTPUT_DIR = '.';
const TLE_FILE = path.join(OUTPUT_DIR, 'starlink_tle.txt');
const SATINFO_FILE = path.join(OUTPUT_DIR, 'starlink_satinfo.txt');
const DIFF_FILE = path.join(OUTPUT_DIR, 'diff_id.txt');

// Elasticsearch 连接配置（可通过环境变量覆盖）
const ES_URL = process.env.ES_NODE || 'http://**************:9200';
const ES_USERNAME = process.env.ES_USERNAME || 'web_readonly';
const ES_PASSWORD = process.env.ES_PASSWORD || 'web@readonly4all';

// 检查是否跳过 ES 查询（用于测试）
const SKIP_ES = process.env.SKIP_ES === 'true';
// 检查是否跳过 API 查询（用于测试）
const SKIP_API = process.env.SKIP_API === 'true';

// 查询常量
const TIME_THRESHOLD = '2025-06-21T04:00:52.163901+00:00';
const INDEX_NAME = 'orbital_tle';

// API 配置
const API_URL = 'http://localhost:3001/api/v1/database/filter-satellites';
const TOKEN = process.env.API_TOKEN || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwic3ViIjozLCJpYXQiOjE3NTA1MDE5NzYsImV4cCI6MTc1MDU4ODM3Nn0.ltAVJ5xI7ut6Khe5ncsMpZzgNFjlpa3b_qYWE7mld2o';

/**
 * 从 Elasticsearch 获取 Starlink TLE 数据
 */
async function fetchTleData(): Promise<TleRecord[]> {
  console.log('🚀 开始从 Elasticsearch 提取 Starlink TLE 数据...');
  
  if (SKIP_ES) {
    console.log('⚠️  跳过 ES 查询（SKIP_ES=true）');
    return [];
  }

  console.log(`连接到 ES: ${ES_URL}`);
  const es = new ESClient({
    node: ES_URL,
    auth: { username: ES_USERNAME, password: ES_PASSWORD },
  });

  const results: TleRecord[] = [];

  let response = await es.search({
    index: INDEX_NAME,
    scroll: '2m',
    size: 1000,
    _source: ['satellite_name', 'norad_id', 'cospar_id'],
    body: {
      query: {
        wildcard: {
          satellite_name: {
            value: 'starlink*',
            case_insensitive: true,
          },
        },
      },
    },
  });

  // 滚动查询获取全部数据
  // eslint-disable-next-line no-constant-condition
  while (true) {
    const hits = (response as any).hits.hits;
    hits.forEach((hit: any) => {
      const { satellite_name, norad_id, cospar_id } = hit._source;
      results.push({
        satellite_name,
        norad_id: norad_id ?? null,
        cospar_id: cospar_id ?? null,
      });
    });

    if (hits.length === 0) break;

    const scrollId = (response as any)._scroll_id;
    response = await es.scroll({ scroll_id: scrollId, scroll: '2m' });
    if ((response as any).hits.hits.length === 0) break;
  }

  // 清理滚动上下文
  if ((response as any)._scroll_id) {
    await es.clearScroll({ scroll_id: (response as any)._scroll_id });
  }

  console.log(`✅ 共获取 ${results.length} 条 TLE 记录`);
  
  // 按 NORAD_ID 去重（保留最新的记录）
  const uniqueResults = new Map<number, TleRecord>();
  results.forEach((record) => {
    if (record.norad_id) {
      const key = Number(record.norad_id);
      // 如果已存在相同NORAD_ID，保留当前记录（假设后面的记录更新）
      uniqueResults.set(key, record);
    }
  });
  
  const deduplicatedResults = Array.from(uniqueResults.values());
  console.log(`🔄 去重后剩余 ${deduplicatedResults.length} 条唯一 TLE 记录`);
  return deduplicatedResults;
}

/**
 * 将 TLE 结果写入文件
 */
function writeTleFile(records: TleRecord[]): void {
  const lines = records.map(
    (r) => `${r.satellite_name || ''},${r.norad_id || ''},${r.cospar_id || ''}`,
  );
  fs.writeFileSync(TLE_FILE, lines.join('\n'), 'utf-8');
  console.log(`📄 已写入 ${TLE_FILE}`);
}

/**
 * 调用 API 获取 Starlink 卫星信息
 */
async function fetchSatInfo(): Promise<SatInfoRecord[]> {
  console.log('🚀 开始调用 API 获取 Starlink 卫星信息...');
  
  if (SKIP_API) {
    console.log('⚠️  跳过 API 查询（SKIP_API=true）');
    return [];
  }

  const results: SatInfoRecord[] = [];
  let page = 1;
  const limit = 100; // 调整为较小的值以避免请求过大

  // eslint-disable-next-line no-constant-condition
  while (true) {
    /* eslint-disable @typescript-eslint/no-explicit-any */
    const { data }: { data: any } = await axios.post(
      API_URL,
      { constellationName: 'Starlink', page, limit },
      {
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer ${TOKEN}`,
          'Content-Type': 'application/json',
        },
      },
    );

    if (!data?.success) throw new Error(`API 请求失败，page=${page}`);

    const pageRecords: SatInfoRecord[] = data.results.map((item: any) => ({
      satellite_name: (item.satellite_name ?? []).map((n: any) => n.value).join(';'),
      alternative_name: (item.alternative_name ?? []).map((n: any) => n.value).join(';'),
      norad_id: item.norad_id?.[0]?.value ?? null,
      cospar_id: item.cospar_id?.[0]?.value ?? null,
    }));

    results.push(...pageRecords);
    console.log(`📥 第 ${page} 页获取 ${pageRecords.length} 条记录，累计: ${results.length} 条`);

    const total = data.total ?? results.length;
    if (results.length >= total || pageRecords.length < limit) break;
    page += 1;
  }

  console.log(`✅ 共获取 ${results.length} 条卫星信息记录`);
  return results;
}

/**
 * 将卫星信息写入文件
 */
function writeSatInfoFile(records: SatInfoRecord[]): void {
  const lines = records.map(
    (r) => `${r.satellite_name}|${r.alternative_name}|${r.norad_id || ''}|${r.cospar_id || ''}`,
  );
  fs.writeFileSync(SATINFO_FILE, lines.join('\n'), 'utf-8');
  console.log(`📄 已写入 ${SATINFO_FILE}，共 ${records.length} 条记录`);
}

/**
 * 生成差异文件
 */
function generateDiff(tleRecords: TleRecord[], satInfoRecords: SatInfoRecord[]): void {
  const noradSet = new Set<number>();
  const cosparSet = new Set<string>();

  // 收集 TLE 文件中的 ID
  tleRecords.forEach((r) => {
    if (r.norad_id) noradSet.add(Number(r.norad_id));
    if (r.cospar_id) cosparSet.add(String(r.cospar_id));
  });

  // 查找缺失项
  const missingNorad = new Set<number>();
  const missingCospar = new Set<string>();

  satInfoRecords.forEach((r) => {
    if (r.norad_id && !noradSet.has(Number(r.norad_id))) missingNorad.add(Number(r.norad_id));
    if (r.cospar_id && !cosparSet.has(String(r.cospar_id))) missingCospar.add(String(r.cospar_id));
  });

  const lines: string[] = [];
  lines.push('缺失的 NORAD_ID:');
  missingNorad.forEach((id) => lines.push(String(id)));
  lines.push('\n缺失的 COSPAR_ID:');
  missingCospar.forEach((id) => lines.push(id));

  fs.writeFileSync(DIFF_FILE, lines.join('\n'), 'utf-8');
  console.log(`📄 已写入差异文件 ${DIFF_FILE}`);
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  try {
    const tleData = await fetchTleData();
    writeTleFile(tleData);

    const satInfoData = await fetchSatInfo();
    writeSatInfoFile(satInfoData);

    generateDiff(tleData, satInfoData);

    console.log('🎉 全部处理完成');
  } catch (error) {
    console.error('❌ 发生错误:', error);
    process.exit(1);
  }
}

main(); 