#!/bin/bash

# 应用部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 解析参数
ENV="production"
PORT="3001"
DEPLOY_METHOD="docker"
APP_DIR="/opt/spacedata"

while [[ $# -gt 0 ]]; do
    case $1 in
        --env)
            ENV="$2"
            shift 2
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --method)
            DEPLOY_METHOD="$2"
            shift 2
            ;;
        --app-dir)
            APP_DIR="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 检查部署方法
check_deploy_method() {
    case $DEPLOY_METHOD in
        docker)
            if ! command -v docker &> /dev/null; then
                log_error "Docker未安装"
                exit 1
            fi
            if ! command -v docker-compose &> /dev/null; then
                log_error "Docker Compose未安装"
                exit 1
            fi
            ;;
        pm2)
            if ! command -v pm2 &> /dev/null; then
                log_error "PM2未安装"
                exit 1
            fi
            ;;
        systemd)
            if ! systemctl --version &> /dev/null; then
                log_error "systemd不可用"
                exit 1
            fi
            ;;
        *)
            log_error "不支持的部署方法: $DEPLOY_METHOD"
            log_info "支持的方法: docker, pm2, systemd"
            exit 1
            ;;
    esac
}

# 准备部署目录
prepare_deploy_directory() {
    log_info "准备部署目录..."
    
    # 创建应用目录
    sudo mkdir -p $APP_DIR
    sudo chown $USER:$USER $APP_DIR
    
    # 备份现有部署
    if [[ -d "$APP_DIR/current" ]]; then
        BACKUP_DIR="$APP_DIR/backup-$(date +%Y%m%d-%H%M%S)"
        log_info "备份当前部署到: $BACKUP_DIR"
        mv "$APP_DIR/current" "$BACKUP_DIR"
    fi
    
    # 创建新的部署目录
    mkdir -p "$APP_DIR/current"
    
    log_success "部署目录准备完成"
}

# 复制应用文件
copy_application_files() {
    log_info "复制应用文件..."
    
    # 复制构建产物
    cp -r dist/* "$APP_DIR/current/"
    
    # 复制package.json
    cp package.json "$APP_DIR/current/"
    cp package-lock.json "$APP_DIR/current/" 2>/dev/null || true
    
    # 复制配置文件
    if [[ -d "config" ]]; then
        cp -r config "$APP_DIR/current/"
    fi
    
    # 复制Docker配置
    if [[ "$DEPLOY_METHOD" == "docker" ]]; then
        cp -r docker "$APP_DIR/current/"
    fi
    
    # 设置权限
    chown -R $USER:$USER "$APP_DIR/current"
    
    log_success "应用文件复制完成"
}

# Docker部署
deploy_with_docker() {
    log_info "使用Docker部署..."
    
    cd "$APP_DIR/current"
    
    # 停止现有容器
    docker-compose -f docker/compose/production.yml down || true
    
    # 拉取最新镜像
    docker-compose -f docker/compose/production.yml pull
    
    # 启动服务
    docker-compose -f docker/compose/production.yml up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    docker-compose -f docker/compose/production.yml ps
    
    log_success "Docker部署完成"
}

# PM2部署
deploy_with_pm2() {
    log_info "使用PM2部署..."
    
    cd "$APP_DIR/current"
    
    # 安装生产依赖
    npm ci --only=production
    
    # 停止现有进程
    pm2 stop spacedata-app || true
    pm2 delete spacedata-app || true
    
    # 创建PM2配置文件
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'spacedata-app',
    script: 'main.js',
    cwd: '$APP_DIR/current',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: '$ENV',
      PORT: '$PORT'
    },
    env_file: 'config/env/$ENV.env',
    log_file: '/var/log/spacedata/combined.log',
    out_file: '/var/log/spacedata/out.log',
    error_file: '/var/log/spacedata/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=4096',
    watch: false,
    ignore_watch: ['node_modules', 'logs'],
    max_restarts: 10,
    min_uptime: '10s',
    kill_timeout: 5000
  }]
};
EOF
    
    # 启动应用
    pm2 start ecosystem.config.js
    
    # 保存PM2配置
    pm2 save
    
    log_success "PM2部署完成"
}

# systemd部署
deploy_with_systemd() {
    log_info "使用systemd部署..."
    
    cd "$APP_DIR/current"
    
    # 安装生产依赖
    npm ci --only=production
    
    # 创建systemd服务文件
    sudo tee /etc/systemd/system/spacedata.service > /dev/null << EOF
[Unit]
Description=SpaceData Backend Service
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$APP_DIR/current
ExecStart=/usr/bin/node main.js
Restart=always
RestartSec=10
Environment=NODE_ENV=$ENV
Environment=PORT=$PORT
EnvironmentFile=$APP_DIR/current/config/env/$ENV.env
StandardOutput=journal
StandardError=journal
SyslogIdentifier=spacedata

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载systemd配置
    sudo systemctl daemon-reload
    
    # 停止现有服务
    sudo systemctl stop spacedata || true
    
    # 启动服务
    sudo systemctl start spacedata
    sudo systemctl enable spacedata
    
    log_success "systemd部署完成"
}

# 配置环境变量
configure_environment() {
    log_info "配置环境变量..."
    
    ENV_FILE="$APP_DIR/current/config/env/$ENV.env"
    
    if [[ ! -f "$ENV_FILE" ]]; then
        log_error "环境配置文件不存在: $ENV_FILE"
        exit 1
    fi
    
    # 验证必要的环境变量
    required_vars=(
        "DB_HOST"
        "DB_USERNAME"
        "DB_PASSWORD"
        "JWT_SECRET"
        "REDIS_HOST"
        "ES_NODE"
    )
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" "$ENV_FILE"; then
            log_warning "环境变量 $var 未配置"
        fi
    done
    
    log_success "环境变量配置检查完成"
}

# 数据库迁移
run_database_migration() {
    log_info "执行数据库迁移..."
    
    cd "$APP_DIR/current"
    
    # 等待数据库服务启动
    if [[ "$DEPLOY_METHOD" == "docker" ]]; then
        log_info "等待数据库服务启动..."
        sleep 60
    fi
    
    # 执行迁移
    case $DEPLOY_METHOD in
        docker)
            docker-compose -f docker/compose/production.yml exec spacedata-app npm run migration:run || true
            ;;
        pm2|systemd)
            npm run migration:run || true
            ;;
    esac
    
    log_success "数据库迁移完成"
}

# 主函数
main() {
    log_info "开始部署应用 (方法: $DEPLOY_METHOD, 环境: $ENV)..."
    
    check_deploy_method
    prepare_deploy_directory
    copy_application_files
    configure_environment
    
    case $DEPLOY_METHOD in
        docker)
            deploy_with_docker
            ;;
        pm2)
            deploy_with_pm2
            ;;
        systemd)
            deploy_with_systemd
            ;;
    esac
    
    run_database_migration
    
    log_success "应用部署完成!"
    log_info "部署信息:"
    log_info "  方法: $DEPLOY_METHOD"
    log_info "  环境: $ENV"
    log_info "  端口: $PORT"
    log_info "  目录: $APP_DIR/current"
}

# 执行主函数
main "$@"
