#!/bin/bash

# Nginx配置脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 解析参数
DOMAIN="localhost"
PORT="3001"
SKIP_SSL=false
SSL_EMAIL=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --domain)
            DOMAIN="$2"
            shift 2
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --skip-ssl)
            SKIP_SSL="$2"
            shift 2
            ;;
        --ssl-email)
            SSL_EMAIL="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 检查Nginx是否安装
check_nginx() {
    if ! command -v nginx &> /dev/null; then
        log_error "Nginx未安装"
        exit 1
    fi
    
    nginx_version=$(nginx -v 2>&1)
    log_success "Nginx已安装: $nginx_version"
}

# 备份现有配置
backup_existing_config() {
    log_info "备份现有Nginx配置..."
    
    BACKUP_DIR="/etc/nginx/backup-$(date +%Y%m%d-%H%M%S)"
    sudo mkdir -p "$BACKUP_DIR"
    
    if [[ -f "/etc/nginx/sites-available/spacedata" ]]; then
        sudo cp "/etc/nginx/sites-available/spacedata" "$BACKUP_DIR/"
        log_info "备份现有配置到: $BACKUP_DIR"
    fi
}

# 创建Nginx配置
create_nginx_config() {
    log_info "创建Nginx配置..."
    
    # 创建主配置文件
    sudo tee /etc/nginx/sites-available/spacedata > /dev/null << EOF
# SpaceData Backend Nginx Configuration

# 上游服务器配置
upstream spacedata_backend {
    server 127.0.0.1:$PORT;
    keepalive 32;
}

# HTTP服务器配置
server {
    listen 80;
    server_name $DOMAIN;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    
    # 日志配置
    access_log /var/log/nginx/spacedata_access.log;
    error_log /var/log/nginx/spacedata_error.log;
    
    # 客户端配置
    client_max_body_size 50M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 静态文件服务
    location /public/ {
        alias /opt/spacedata/current/public/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files \$uri \$uri/ =404;
    }
    
    # API代理
    location / {
        proxy_pass http://spacedata_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://spacedata_backend/health;
        access_log off;
    }
    
    # API文档
    location /api-docs {
        proxy_pass http://spacedata_backend/api-docs;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # 监控端点
    location /metrics {
        proxy_pass http://spacedata_backend/metrics;
        allow 127.0.0.1;
        deny all;
    }
}
EOF

    # 如果不跳过SSL，添加HTTPS配置
    if [[ "$SKIP_SSL" != "true" && "$DOMAIN" != "localhost" ]]; then
        sudo tee -a /etc/nginx/sites-available/spacedata > /dev/null << EOF

# HTTPS服务器配置
server {
    listen 443 ssl http2;
    server_name $DOMAIN;
    
    # SSL配置
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/$DOMAIN/chain.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    
    # 其他配置与HTTP相同
    include /etc/nginx/snippets/spacedata-common.conf;
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name $DOMAIN;
    return 301 https://\$server_name\$request_uri;
}
EOF
    fi
    
    log_success "Nginx配置创建完成"
}

# 创建通用配置片段
create_common_config() {
    log_info "创建通用配置片段..."
    
    sudo tee /etc/nginx/snippets/spacedata-common.conf > /dev/null << EOF
# SpaceData通用配置

# 日志配置
access_log /var/log/nginx/spacedata_access.log;
error_log /var/log/nginx/spacedata_error.log;

# 客户端配置
client_max_body_size 50M;
client_body_timeout 60s;
client_header_timeout 60s;

# Gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_proxied any;
gzip_comp_level 6;
gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/json
    application/javascript
    application/xml+rss
    application/atom+xml
    image/svg+xml;

# 静态文件服务
location /public/ {
    alias /opt/spacedata/current/public/;
    expires 1y;
    add_header Cache-Control "public, immutable";
    try_files \$uri \$uri/ =404;
}

# API代理
location / {
    proxy_pass http://spacedata_backend;
    proxy_http_version 1.1;
    proxy_set_header Upgrade \$http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host \$host;
    proxy_set_header X-Real-IP \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto \$scheme;
    proxy_cache_bypass \$http_upgrade;
    
    # 超时配置
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    
    # 缓冲配置
    proxy_buffering on;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;
    proxy_busy_buffers_size 8k;
}

# 健康检查
location /health {
    proxy_pass http://spacedata_backend/health;
    access_log off;
}

# API文档
location /api-docs {
    proxy_pass http://spacedata_backend/api-docs;
    proxy_set_header Host \$host;
    proxy_set_header X-Real-IP \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto \$scheme;
}

# 监控端点
location /metrics {
    proxy_pass http://spacedata_backend/metrics;
    allow 127.0.0.1;
    deny all;
}
EOF
    
    log_success "通用配置片段创建完成"
}

# 启用站点
enable_site() {
    log_info "启用站点..."
    
    # 禁用默认站点
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # 启用SpaceData站点
    sudo ln -sf /etc/nginx/sites-available/spacedata /etc/nginx/sites-enabled/
    
    log_success "站点启用完成"
}

# 测试配置
test_nginx_config() {
    log_info "测试Nginx配置..."
    
    if sudo nginx -t; then
        log_success "Nginx配置测试通过"
    else
        log_error "Nginx配置测试失败"
        exit 1
    fi
}

# 安装SSL证书
install_ssl_certificate() {
    if [[ "$SKIP_SSL" == "true" || "$DOMAIN" == "localhost" ]]; then
        log_warning "跳过SSL证书安装"
        return
    fi
    
    log_info "安装SSL证书..."
    
    # 安装Certbot
    if ! command -v certbot &> /dev/null; then
        sudo apt-get update
        sudo apt-get install -y certbot python3-certbot-nginx
    fi
    
    # 获取SSL证书
    if [[ -n "$SSL_EMAIL" ]]; then
        sudo certbot --nginx -d "$DOMAIN" --email "$SSL_EMAIL" --agree-tos --non-interactive
    else
        log_warning "未提供邮箱地址，跳过SSL证书自动获取"
        log_info "请手动运行: sudo certbot --nginx -d $DOMAIN"
    fi
    
    log_success "SSL证书安装完成"
}

# 重启Nginx
restart_nginx() {
    log_info "重启Nginx..."
    
    sudo systemctl restart nginx
    sudo systemctl enable nginx
    
    if sudo systemctl is-active --quiet nginx; then
        log_success "Nginx启动成功"
    else
        log_error "Nginx启动失败"
        sudo systemctl status nginx
        exit 1
    fi
}

# 主函数
main() {
    log_info "开始配置Nginx (域名: $DOMAIN, 端口: $PORT)..."
    
    check_nginx
    backup_existing_config
    create_common_config
    create_nginx_config
    enable_site
    test_nginx_config
    restart_nginx
    install_ssl_certificate
    
    log_success "Nginx配置完成!"
    log_info "配置信息:"
    log_info "  域名: $DOMAIN"
    log_info "  后端端口: $PORT"
    log_info "  SSL: $([ "$SKIP_SSL" == "true" ] && echo "禁用" || echo "启用")"
    log_info "  配置文件: /etc/nginx/sites-available/spacedata"
}

# 执行主函数
main "$@"
