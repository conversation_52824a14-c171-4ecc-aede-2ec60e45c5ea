#!/bin/bash

# 应用构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 解析参数
ENV="production"
SKIP_TESTS=false
SKIP_LINT=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --env)
            ENV="$2"
            shift 2
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --skip-lint)
            SKIP_LINT=true
            shift
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 检查Node.js和npm
check_nodejs() {
    log_info "检查Node.js环境..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm未安装"
        exit 1
    fi
    
    node_version=$(node --version)
    npm_version=$(npm --version)
    log_success "Node.js版本: $node_version"
    log_success "npm版本: $npm_version"
}

# 清理之前的构建
clean_build() {
    log_info "清理之前的构建..."
    
    if [[ -d "dist" ]]; then
        rm -rf dist
        log_success "删除dist目录"
    fi
    
    if [[ -d "node_modules" ]]; then
        log_info "清理node_modules..."
        rm -rf node_modules
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装依赖..."
    
    # 使用npm ci进行清洁安装
    npm ci
    
    log_success "依赖安装完成"
}

# 代码检查
lint_code() {
    if [[ "$SKIP_LINT" == true ]]; then
        log_warning "跳过代码检查"
        return
    fi
    
    log_info "执行代码检查..."
    
    # ESLint检查
    npm run lint
    
    # 格式化检查
    npm run format
    
    log_success "代码检查通过"
}

# 运行测试
run_tests() {
    if [[ "$SKIP_TESTS" == true ]]; then
        log_warning "跳过测试"
        return
    fi
    
    log_info "运行测试..."
    
    # 单元测试
    npm run test
    
    # 如果是生产环境，运行覆盖率测试
    if [[ "$ENV" == "production" ]]; then
        npm run test:cov
    fi
    
    log_success "测试通过"
}

# 构建应用
build_application() {
    log_info "构建应用..."
    
    # 设置环境变量
    export NODE_ENV=$ENV
    
    # 执行构建
    npm run build
    
    # 验证构建结果
    if [[ ! -d "dist" ]]; then
        log_error "构建失败：dist目录不存在"
        exit 1
    fi
    
    if [[ ! -f "dist/main.js" ]]; then
        log_error "构建失败：main.js文件不存在"
        exit 1
    fi
    
    log_success "应用构建完成"
}

# 优化构建产物
optimize_build() {
    log_info "优化构建产物..."
    
    # 复制必要的配置文件
    if [[ -d "config" ]]; then
        cp -r config dist/
        log_info "复制配置文件"
    fi
    
    # 复制公共资源
    if [[ -d "public" ]]; then
        cp -r public dist/
        log_info "复制公共资源"
    fi
    
    # 创建日志目录
    mkdir -p dist/logs
    
    # 生成版本信息
    cat > dist/version.json << EOF
{
  "version": "$(npm pkg get version | tr -d '"')",
  "buildTime": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "gitCommit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
  "gitBranch": "$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')",
  "nodeVersion": "$(node --version)",
  "environment": "$ENV"
}
EOF
    
    log_success "构建产物优化完成"
}

# 构建Docker镜像
build_docker_image() {
    log_info "构建Docker镜像..."
    
    # 检查Docker是否可用
    if ! command -v docker &> /dev/null; then
        log_warning "Docker未安装，跳过镜像构建"
        return
    fi
    
    # 构建镜像
    docker build -f docker/services/app.Dockerfile -t spacedata-app:latest .
    
    # 如果是生产环境，添加版本标签
    if [[ "$ENV" == "production" ]]; then
        VERSION=$(npm pkg get version | tr -d '"')
        docker tag spacedata-app:latest spacedata-app:$VERSION
        log_success "Docker镜像构建完成，标签: latest, $VERSION"
    else
        log_success "Docker镜像构建完成，标签: latest"
    fi
}

# 生成部署清单
generate_manifest() {
    log_info "生成部署清单..."
    
    cat > deployment-manifest.json << EOF
{
  "application": "spacedata-backend",
  "version": "$(npm pkg get version | tr -d '"')",
  "environment": "$ENV",
  "buildTime": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "gitCommit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
  "gitBranch": "$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')",
  "nodeVersion": "$(node --version)",
  "dependencies": {
    "production": $(npm list --prod --json 2>/dev/null | jq '.dependencies // {}'),
    "development": $(npm list --dev --json 2>/dev/null | jq '.dependencies // {}')
  },
  "buildConfig": {
    "skipTests": $SKIP_TESTS,
    "skipLint": $SKIP_LINT
  }
}
EOF
    
    log_success "部署清单生成完成"
}

# 主函数
main() {
    log_info "开始构建应用 (环境: $ENV)..."
    
    check_nodejs
    clean_build
    install_dependencies
    lint_code
    run_tests
    build_application
    optimize_build
    build_docker_image
    generate_manifest
    
    log_success "应用构建完成!"
    log_info "构建信息:"
    log_info "  环境: $ENV"
    log_info "  版本: $(npm pkg get version | tr -d '"')"
    log_info "  构建时间: $(date)"
    log_info "  构建产物: dist/"
}

# 执行主函数
main "$@"
