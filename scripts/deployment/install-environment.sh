#!/bin/bash

# 太空大数据平台 - 环境准备脚本
# 适用于 Ubuntu 20.04+ / CentOS 8+ / Debian 11+
# 作者: AI助手
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${PURPLE}[SUCCESS]${NC} $1"
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    elif type lsb_release >/dev/null 2>&1; then
        OS=$(lsb_release -si)
        VER=$(lsb_release -sr)
    elif [[ -f /etc/redhat-release ]]; then
        OS="CentOS"
        VER=$(rpm -q --qf "%{VERSION}" $(rpm -q --whatprovides redhat-release))
    else
        log_error "无法检测操作系统"
        exit 1
    fi
    
    log_info "检测到操作系统: $OS $VER"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warn "检测到root用户，建议使用普通用户执行此脚本"
        read -p "是否继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 更新系统包
update_system() {
    log_step "更新系统包..."
    
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        sudo apt update && sudo apt upgrade -y
        sudo apt install -y curl wget git vim htop unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release
    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
        sudo yum update -y
        sudo yum install -y curl wget git vim htop unzip yum-utils device-mapper-persistent-data lvm2
    else
        log_error "不支持的操作系统: $OS"
        exit 1
    fi
    
    log_success "系统包更新完成"
}

# 安装Node.js 18
install_nodejs() {
    log_step "安装Node.js 18..."
    
    # 检查是否已安装
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        log_info "Node.js已安装: $NODE_VERSION"
        if [[ "$NODE_VERSION" == v18* ]]; then
            log_success "Node.js版本符合要求"
            return
        else
            log_warn "Node.js版本不符合要求，将重新安装"
        fi
    fi
    
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        sudo apt-get install -y nodejs
    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
        curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
        sudo yum install -y nodejs
    fi
    
    # 验证安装
    if command -v node &> /dev/null && command -v npm &> /dev/null; then
        log_success "Node.js安装成功: $(node --version)"
        log_success "npm版本: $(npm --version)"
    else
        log_error "Node.js安装失败"
        exit 1
    fi
}

# 安装Docker
install_docker() {
    log_step "安装Docker..."
    
    # 检查是否已安装
    if command -v docker &> /dev/null; then
        log_info "Docker已安装: $(docker --version)"
        if sudo docker info &> /dev/null; then
            log_success "Docker运行正常"
            return
        fi
    fi
    
    # 卸载旧版本
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        sudo apt-get remove -y docker docker-engine docker.io containerd runc || true
        
        # 添加Docker官方GPG密钥
        sudo mkdir -p /etc/apt/keyrings
        curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
        
        # 添加Docker仓库
        echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
        
        # 安装Docker
        sudo apt-get update
        sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
        
    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
        sudo yum remove -y docker docker-client docker-client-latest docker-common docker-latest docker-latest-logrotate docker-logrotate docker-engine || true
        
        # 添加Docker仓库
        sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
        
        # 安装Docker
        sudo yum install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
    fi
    
    # 启动Docker服务
    sudo systemctl start docker
    sudo systemctl enable docker
    
    # 将当前用户添加到docker组
    sudo usermod -aG docker $USER
    
    # 验证安装
    if sudo docker run hello-world &> /dev/null; then
        log_success "Docker安装成功: $(docker --version)"
    else
        log_error "Docker安装失败"
        exit 1
    fi
}

# 安装Docker Compose (独立版本)
install_docker_compose() {
    log_step "安装Docker Compose..."
    
    # 检查是否已安装
    if command -v docker-compose &> /dev/null; then
        log_info "Docker Compose已安装: $(docker compose --version)"
        return
    fi
    
    # 获取最新版本
    DOCKER_COMPOSE_VERSION=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep 'tag_name' | cut -d\" -f4)
    
    # 下载并安装
    sudo curl -L "https://github.com/docker/compose/releases/download/${DOCKER_COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    
    # 创建软链接
    sudo ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    
    # 验证安装
    if command -v docker-compose &> /dev/null; then
        log_success "Docker Compose安装成功: $(docker compose --version)"
    else
        log_error "Docker Compose安装失败"
        exit 1
    fi
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙..."
    
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        # 安装ufw
        sudo apt install -y ufw
        
        # 配置规则
        sudo ufw --force reset
        sudo ufw default deny incoming
        sudo ufw default allow outgoing
        sudo ufw allow ssh
        sudo ufw allow 80/tcp
        sudo ufw allow 443/tcp
        sudo ufw allow 3000/tcp  # 应用端口
        
        # 启用防火墙
        sudo ufw --force enable
        
        log_success "UFW防火墙配置完成"
        
    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
        # 配置firewalld
        sudo systemctl start firewalld
        sudo systemctl enable firewalld
        
        sudo firewall-cmd --permanent --add-service=ssh
        sudo firewall-cmd --permanent --add-service=http
        sudo firewall-cmd --permanent --add-service=https
        sudo firewall-cmd --permanent --add-port=3000/tcp
        
        sudo firewall-cmd --reload
        
        log_success "Firewalld防火墙配置完成"
    fi
}

# 创建项目目录
create_project_directory() {
    log_step "创建项目目录..."
    
    PROJECT_DIR="/opt/spacedata"
    
    if [[ ! -d "$PROJECT_DIR" ]]; then
        sudo mkdir -p "$PROJECT_DIR"
        sudo chown $USER:$USER "$PROJECT_DIR"
        log_success "项目目录创建完成: $PROJECT_DIR"
    else
        log_info "项目目录已存在: $PROJECT_DIR"
    fi
}

# 配置系统优化
optimize_system() {
    log_step "优化系统配置..."
    
    # 增加文件描述符限制
    echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
    echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf
    
    # 优化内核参数
    cat << EOF | sudo tee -a /etc/sysctl.conf
# 太空大数据平台优化配置
vm.max_map_count=262144
net.core.somaxconn=65535
net.ipv4.tcp_max_syn_backlog=65535
net.core.netdev_max_backlog=5000
net.ipv4.tcp_fin_timeout=30
net.ipv4.tcp_keepalive_time=1200
net.ipv4.tcp_syncookies=1
net.ipv4.tcp_tw_reuse=1
net.ipv4.tcp_tw_recycle=0
net.ipv4.ip_local_port_range=10000 65000
net.core.rmem_default=262144
net.core.rmem_max=16777216
net.core.wmem_default=262144
net.core.wmem_max=16777216
EOF
    
    # 应用配置
    sudo sysctl -p
    
    log_success "系统优化配置完成"
}

# 安装监控工具
install_monitoring_tools() {
    log_step "安装监控工具..."
    
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        sudo apt install -y htop iotop nethogs ncdu tree
    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
        sudo yum install -y htop iotop nethogs ncdu tree
    fi
    
    log_success "监控工具安装完成"
}

# 主函数
main() {
    echo -e "${CYAN}"
    echo "=================================================="
    echo "    太空大数据平台 - 环境准备脚本"
    echo "=================================================="
    echo -e "${NC}"
    
    log_info "开始环境准备..."
    
    # 检查权限
    check_root
    
    # 检测操作系统
    detect_os
    
    # 更新系统
    update_system
    
    # 安装Node.js
    install_nodejs
    
    # 安装Docker
    install_docker
    
    # 安装Docker Compose
    install_docker_compose
    
    # 配置防火墙
    configure_firewall
    
    # 创建项目目录
    create_project_directory
    
    # 优化系统
    optimize_system
    
    # 安装监控工具
    install_monitoring_tools
    
    echo -e "${CYAN}"
    echo "=================================================="
    echo "           环境准备完成！"
    echo "=================================================="
    echo -e "${NC}"
    
    log_success "所有组件安装完成！"
    log_info "Node.js版本: $(node --version)"
    log_info "npm版本: $(npm --version)"
    log_info "Docker版本: $(docker --version)"
    log_info "Docker Compose版本: $(docker-compose --version)"
    
    echo -e "${YELLOW}"
    echo "注意事项："
    echo "1. 请重新登录或执行 'newgrp docker' 以使docker组权限生效"
    echo "2. 项目目录: /opt/spacedata"
    echo "3. 接下来可以运行一键部署脚本"
    echo -e "${NC}"
    
    log_info "环境准备脚本执行完成！"
}

# 执行主函数
main "$@" 