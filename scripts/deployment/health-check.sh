#!/bin/bash

# 健康检查脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 解析参数
DOMAIN="localhost"
PORT="3001"
TIMEOUT=10

while [[ $# -gt 0 ]]; do
    case $1 in
        --domain)
            DOMAIN="$2"
            shift 2
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# HTTP健康检查
check_http_endpoint() {
    local url=$1
    local description=$2
    local expected_status=${3:-200}
    
    log_info "检查 $description: $url"
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout $TIMEOUT "$url" 2>/dev/null || echo "000")
    
    if [[ "$response" == "$expected_status" ]]; then
        log_success "$description 健康检查通过 (状态码: $response)"
        return 0
    else
        log_error "$description 健康检查失败 (状态码: $response)"
        return 1
    fi
}

# 检查JSON响应
check_json_endpoint() {
    local url=$1
    local description=$2
    local expected_field=$3
    
    log_info "检查 $description: $url"
    
    local response=$(curl -s --connect-timeout $TIMEOUT "$url" 2>/dev/null || echo "{}")
    
    if echo "$response" | jq -e ".$expected_field" > /dev/null 2>&1; then
        log_success "$description 健康检查通过"
        return 0
    else
        log_error "$description 健康检查失败"
        log_error "响应: $response"
        return 1
    fi
}

# 检查数据库连接
check_database_connection() {
    local host=$1
    local port=$2
    local description=$3
    
    log_info "检查 $description 连接: $host:$port"
    
    if timeout $TIMEOUT bash -c "</dev/tcp/$host/$port" 2>/dev/null; then
        log_success "$description 连接正常"
        return 0
    else
        log_error "$description 连接失败"
        return 1
    fi
}

# 检查应用健康状态
check_application_health() {
    log_info "=== 应用健康检查 ==="
    
    local failed_checks=0
    
    # 检查主应用
    if ! check_http_endpoint "http://$DOMAIN:$PORT/health" "应用健康检查"; then
        ((failed_checks++))
    fi
    
    # 检查API文档
    if ! check_http_endpoint "http://$DOMAIN:$PORT/api-docs" "API文档"; then
        ((failed_checks++))
    fi
    
    # 检查API响应
    if ! check_json_endpoint "http://$DOMAIN:$PORT/api/v1/health" "API健康检查" "status"; then
        ((failed_checks++))
    fi
    
    return $failed_checks
}

# 检查Web服务
check_web_services() {
    log_info "=== Web服务检查 ==="
    
    local failed_checks=0
    
    # 检查Nginx
    if ! check_http_endpoint "http://$DOMAIN" "Nginx服务"; then
        ((failed_checks++))
    fi
    
    # 如果配置了HTTPS，检查SSL
    if [[ "$DOMAIN" != "localhost" ]]; then
        if ! check_http_endpoint "https://$DOMAIN" "HTTPS服务"; then
            ((failed_checks++))
        fi
    fi
    
    return $failed_checks
}

# 检查数据库服务
check_database_services() {
    log_info "=== 数据库服务检查 ==="
    
    local failed_checks=0
    
    # 检查PostgreSQL
    if ! check_database_connection "localhost" "5432" "PostgreSQL"; then
        ((failed_checks++))
    fi
    
    # 检查MongoDB
    if ! check_database_connection "localhost" "27017" "MongoDB"; then
        ((failed_checks++))
    fi
    
    # 检查Redis
    if ! check_database_connection "localhost" "6379" "Redis"; then
        ((failed_checks++))
    fi
    
    # 检查Elasticsearch
    if ! check_http_endpoint "http://localhost:9200/_cluster/health" "Elasticsearch集群健康"; then
        ((failed_checks++))
    fi
    
    # 检查RabbitMQ
    if ! check_database_connection "localhost" "5672" "RabbitMQ"; then
        ((failed_checks++))
    fi
    
    return $failed_checks
}

# 检查监控服务
check_monitoring_services() {
    log_info "=== 监控服务检查 ==="
    
    local failed_checks=0
    
    # 检查Node Exporter
    if ! check_http_endpoint "http://localhost:9100/metrics" "Node Exporter"; then
        ((failed_checks++))
    fi
    
    # 检查Prometheus
    if ! check_http_endpoint "http://localhost:9090/-/healthy" "Prometheus"; then
        ((failed_checks++))
    fi
    
    # 检查Grafana
    if ! check_http_endpoint "http://localhost:3000/api/health" "Grafana"; then
        ((failed_checks++))
    fi
    
    return $failed_checks
}

# 检查系统资源
check_system_resources() {
    log_info "=== 系统资源检查 ==="
    
    local failed_checks=0
    
    # 检查内存使用率
    local memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    log_info "内存使用率: ${memory_usage}%"
    if [[ $memory_usage -gt 90 ]]; then
        log_warning "内存使用率过高: ${memory_usage}%"
        ((failed_checks++))
    else
        log_success "内存使用率正常: ${memory_usage}%"
    fi
    
    # 检查CPU使用率
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
    log_info "CPU使用率: ${cpu_usage}%"
    if (( $(echo "$cpu_usage > 80" | bc -l) )); then
        log_warning "CPU使用率过高: ${cpu_usage}%"
        ((failed_checks++))
    else
        log_success "CPU使用率正常: ${cpu_usage}%"
    fi
    
    # 检查磁盘使用率
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    log_info "磁盘使用率: ${disk_usage}%"
    if [[ $disk_usage -gt 85 ]]; then
        log_warning "磁盘使用率过高: ${disk_usage}%"
        ((failed_checks++))
    else
        log_success "磁盘使用率正常: ${disk_usage}%"
    fi
    
    # 检查负载平均值
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local cpu_cores=$(nproc)
    log_info "系统负载: $load_avg (CPU核心数: $cpu_cores)"
    if (( $(echo "$load_avg > $cpu_cores * 2" | bc -l) )); then
        log_warning "系统负载过高: $load_avg"
        ((failed_checks++))
    else
        log_success "系统负载正常: $load_avg"
    fi
    
    return $failed_checks
}

# 检查日志错误
check_log_errors() {
    log_info "=== 日志错误检查 ==="
    
    local failed_checks=0
    
    # 检查应用日志中的错误
    if [[ -f "/var/log/spacedata/error.log" ]]; then
        local error_count=$(tail -n 100 /var/log/spacedata/error.log | grep -c "ERROR" || echo "0")
        if [[ $error_count -gt 0 ]]; then
            log_warning "发现 $error_count 个错误日志"
            tail -n 5 /var/log/spacedata/error.log | while read line; do
                log_warning "  $line"
            done
            ((failed_checks++))
        else
            log_success "应用日志无错误"
        fi
    fi
    
    # 检查Nginx错误日志
    if [[ -f "/var/log/nginx/spacedata_error.log" ]]; then
        local nginx_errors=$(tail -n 100 /var/log/nginx/spacedata_error.log | wc -l)
        if [[ $nginx_errors -gt 0 ]]; then
            log_warning "发现 $nginx_errors 个Nginx错误"
            ((failed_checks++))
        else
            log_success "Nginx日志无错误"
        fi
    fi
    
    return $failed_checks
}

# 生成健康检查报告
generate_health_report() {
    local total_failed=$1
    
    echo
    echo "=== 健康检查报告 ==="
    echo "检查时间: $(date)"
    echo "检查域名: $DOMAIN"
    echo "检查端口: $PORT"
    echo "超时时间: ${TIMEOUT}秒"
    echo
    
    if [[ $total_failed -eq 0 ]]; then
        log_success "所有健康检查通过！"
        echo "系统状态: 健康"
    else
        log_error "发现 $total_failed 个问题"
        echo "系统状态: 需要关注"
    fi
    
    echo
    echo "建议操作:"
    echo "1. 定期运行健康检查"
    echo "2. 监控系统资源使用情况"
    echo "3. 检查应用日志"
    echo "4. 确保备份策略正常运行"
}

# 主函数
main() {
    log_info "开始健康检查 (域名: $DOMAIN, 端口: $PORT)..."
    
    local total_failed=0
    
    # 执行各项检查
    check_application_health
    total_failed=$((total_failed + $?))
    
    check_web_services
    total_failed=$((total_failed + $?))
    
    check_database_services
    total_failed=$((total_failed + $?))
    
    check_monitoring_services
    total_failed=$((total_failed + $?))
    
    check_system_resources
    total_failed=$((total_failed + $?))
    
    check_log_errors
    total_failed=$((total_failed + $?))
    
    # 生成报告
    generate_health_report $total_failed
    
    # 返回结果
    if [[ $total_failed -eq 0 ]]; then
        exit 0
    else
        exit 1
    fi
}

# 执行主函数
main "$@"
