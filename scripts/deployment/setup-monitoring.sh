#!/bin/bash

# 监控配置脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建监控目录
create_monitoring_directories() {
    log_info "创建监控目录..."
    
    sudo mkdir -p /opt/spacedata/monitoring/{prometheus,grafana,alertmanager}
    sudo mkdir -p /var/lib/prometheus
    sudo mkdir -p /var/lib/grafana
    sudo chown -R $USER:$USER /opt/spacedata/monitoring
    
    log_success "监控目录创建完成"
}

# 配置Prometheus
setup_prometheus() {
    log_info "配置Prometheus..."
    
    # 创建Prometheus配置文件
    cat > /opt/spacedata/monitoring/prometheus/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # SpaceData应用监控
  - job_name: 'spacedata-app'
    static_configs:
      - targets: ['localhost:3001']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Node Exporter系统监控
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']

  # Nginx监控
  - job_name: 'nginx'
    static_configs:
      - targets: ['localhost:9113']

  # PostgreSQL监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['localhost:9187']

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['localhost:9121']

  # Elasticsearch监控
  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['localhost:9114']

  # MongoDB监控
  - job_name: 'mongodb'
    static_configs:
      - targets: ['localhost:9216']
EOF

    # 创建告警规则
    mkdir -p /opt/spacedata/monitoring/prometheus/rules
    cat > /opt/spacedata/monitoring/prometheus/rules/spacedata.yml << EOF
groups:
  - name: spacedata.rules
    rules:
      # 应用健康检查
      - alert: SpaceDataAppDown
        expr: up{job="spacedata-app"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "SpaceData应用服务不可用"
          description: "SpaceData应用服务已停止响应超过1分钟"

      # 高内存使用率
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用率过高"
          description: "内存使用率超过90%，当前值: {{ \$value }}"

      # 高CPU使用率
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "CPU使用率过高"
          description: "CPU使用率超过80%，当前值: {{ \$value }}%"

      # 磁盘空间不足
      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "磁盘空间不足"
          description: "磁盘可用空间少于10%，当前值: {{ \$value }}%"

      # 数据库连接失败
      - alert: DatabaseDown
        expr: up{job="postgres"} == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "数据库服务不可用"
          description: "PostgreSQL数据库服务已停止响应超过2分钟"

      # Redis连接失败
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Redis服务不可用"
          description: "Redis服务已停止响应超过2分钟"

      # Elasticsearch连接失败
      - alert: ElasticsearchDown
        expr: up{job="elasticsearch"} == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Elasticsearch服务不可用"
          description: "Elasticsearch服务已停止响应超过2分钟"
EOF

    log_success "Prometheus配置完成"
}

# 配置Grafana
setup_grafana() {
    log_info "配置Grafana..."
    
    # 创建Grafana数据源配置
    mkdir -p /opt/spacedata/monitoring/grafana/provisioning/{datasources,dashboards}
    
    cat > /opt/spacedata/monitoring/grafana/provisioning/datasources/prometheus.yml << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
EOF

    # 创建仪表板配置
    cat > /opt/spacedata/monitoring/grafana/provisioning/dashboards/dashboard.yml << EOF
apiVersion: 1

providers:
  - name: 'default'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards
EOF

    # 创建SpaceData仪表板
    cat > /opt/spacedata/monitoring/grafana/provisioning/dashboards/spacedata-dashboard.json << 'EOF'
{
  "dashboard": {
    "id": null,
    "title": "SpaceData监控仪表板",
    "tags": ["spacedata"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "应用状态",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=\"spacedata-app\"}",
            "legendFormat": "应用状态"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "mappings": [
              {
                "options": {
                  "0": {
                    "text": "离线"
                  },
                  "1": {
                    "text": "在线"
                  }
                },
                "type": "value"
              }
            ],
            "thresholds": {
              "steps": [
                {
                  "color": "red",
                  "value": 0
                },
                {
                  "color": "green",
                  "value": 1
                }
              ]
            }
          }
        },
        "gridPos": {
          "h": 8,
          "w": 12,
          "x": 0,
          "y": 0
        }
      },
      {
        "id": 2,
        "title": "CPU使用率",
        "type": "graph",
        "targets": [
          {
            "expr": "100 - (avg by(instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
            "legendFormat": "CPU使用率"
          }
        ],
        "yAxes": [
          {
            "max": 100,
            "min": 0,
            "unit": "percent"
          }
        ],
        "gridPos": {
          "h": 8,
          "w": 12,
          "x": 12,
          "y": 0
        }
      },
      {
        "id": 3,
        "title": "内存使用率",
        "type": "graph",
        "targets": [
          {
            "expr": "(node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100",
            "legendFormat": "内存使用率"
          }
        ],
        "yAxes": [
          {
            "max": 100,
            "min": 0,
            "unit": "percent"
          }
        ],
        "gridPos": {
          "h": 8,
          "w": 12,
          "x": 0,
          "y": 8
        }
      },
      {
        "id": 4,
        "title": "磁盘使用率",
        "type": "graph",
        "targets": [
          {
            "expr": "(node_filesystem_size_bytes - node_filesystem_avail_bytes) / node_filesystem_size_bytes * 100",
            "legendFormat": "磁盘使用率"
          }
        ],
        "yAxes": [
          {
            "max": 100,
            "min": 0,
            "unit": "percent"
          }
        ],
        "gridPos": {
          "h": 8,
          "w": 12,
          "x": 12,
          "y": 8
        }
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
EOF

    log_success "Grafana配置完成"
}

# 安装Node Exporter
install_node_exporter() {
    log_info "安装Node Exporter..."
    
    # 检查是否已安装
    if command -v node_exporter &> /dev/null; then
        log_info "Node Exporter已安装"
        return
    fi
    
    # 下载并安装
    NODE_EXPORTER_VERSION="1.6.1"
    cd /tmp
    wget https://github.com/prometheus/node_exporter/releases/download/v${NODE_EXPORTER_VERSION}/node_exporter-${NODE_EXPORTER_VERSION}.linux-amd64.tar.gz
    tar xvf node_exporter-${NODE_EXPORTER_VERSION}.linux-amd64.tar.gz
    sudo mv node_exporter-${NODE_EXPORTER_VERSION}.linux-amd64/node_exporter /usr/local/bin/
    
    # 创建systemd服务
    sudo tee /etc/systemd/system/node_exporter.service > /dev/null << EOF
[Unit]
Description=Node Exporter
After=network.target

[Service]
User=nobody
Group=nogroup
Type=simple
ExecStart=/usr/local/bin/node_exporter

[Install]
WantedBy=multi-user.target
EOF
    
    # 启动服务
    sudo systemctl daemon-reload
    sudo systemctl start node_exporter
    sudo systemctl enable node_exporter
    
    log_success "Node Exporter安装完成"
}

# 配置日志监控
setup_log_monitoring() {
    log_info "配置日志监控..."
    
    # 创建日志轮转配置
    sudo tee /etc/logrotate.d/spacedata > /dev/null << EOF
/var/log/spacedata/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $USER $USER
    postrotate
        systemctl reload nginx > /dev/null 2>&1 || true
        pm2 reloadLogs > /dev/null 2>&1 || true
    endscript
}
EOF
    
    log_success "日志监控配置完成"
}

# 主函数
main() {
    log_info "开始配置监控..."
    
    create_monitoring_directories
    setup_prometheus
    setup_grafana
    install_node_exporter
    setup_log_monitoring
    
    log_success "监控配置完成!"
    log_info "监控服务:"
    log_info "  Prometheus: http://localhost:9090"
    log_info "  Grafana: http://localhost:3000"
    log_info "  Node Exporter: http://localhost:9100"
}

# 执行主函数
main "$@"
