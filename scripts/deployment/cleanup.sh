#!/bin/bash

# 清理部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 确认清理操作
confirm_cleanup() {
    echo
    log_warning "此操作将清理所有SpaceData相关的部署文件和服务"
    log_warning "包括："
    echo "  - 停止所有服务"
    echo "  - 删除Docker容器和镜像"
    echo "  - 删除应用文件"
    echo "  - 删除配置文件"
    echo "  - 保留数据库数据（可选择删除）"
    echo
    
    read -p "确定要继续吗？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "取消清理操作"
        exit 0
    fi
    
    read -p "是否同时删除数据库数据？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        DELETE_DATA=true
    else
        DELETE_DATA=false
    fi
}

# 停止Docker服务
stop_docker_services() {
    log_info "停止Docker服务..."
    
    if [[ -f "/opt/spacedata/current/docker/compose/production.yml" ]]; then
        cd /opt/spacedata/current
        docker-compose -f docker/compose/production.yml down || true
        log_success "Docker服务已停止"
    else
        log_warning "Docker Compose文件不存在，跳过"
    fi
    
    # 停止并删除SpaceData相关容器
    local containers=$(docker ps -a --filter "name=spacedata" --format "{{.Names}}" 2>/dev/null || true)
    if [[ -n "$containers" ]]; then
        log_info "删除SpaceData容器..."
        echo "$containers" | xargs docker rm -f 2>/dev/null || true
        log_success "容器删除完成"
    fi
    
    # 删除SpaceData镜像
    local images=$(docker images --filter "reference=spacedata*" --format "{{.Repository}}:{{.Tag}}" 2>/dev/null || true)
    if [[ -n "$images" ]]; then
        log_info "删除SpaceData镜像..."
        echo "$images" | xargs docker rmi -f 2>/dev/null || true
        log_success "镜像删除完成"
    fi
    
    # 删除未使用的卷（如果选择删除数据）
    if [[ "$DELETE_DATA" == true ]]; then
        log_info "删除Docker卷..."
        docker volume ls --filter "name=spacedata" --format "{{.Name}}" | xargs docker volume rm 2>/dev/null || true
        log_success "Docker卷删除完成"
    fi
}

# 停止PM2服务
stop_pm2_services() {
    log_info "停止PM2服务..."
    
    if command -v pm2 &> /dev/null; then
        pm2 stop spacedata-app 2>/dev/null || true
        pm2 delete spacedata-app 2>/dev/null || true
        pm2 save 2>/dev/null || true
        log_success "PM2服务已停止"
    else
        log_warning "PM2未安装，跳过"
    fi
}

# 停止systemd服务
stop_systemd_services() {
    log_info "停止systemd服务..."
    
    if systemctl list-unit-files | grep -q spacedata; then
        sudo systemctl stop spacedata 2>/dev/null || true
        sudo systemctl disable spacedata 2>/dev/null || true
        sudo rm -f /etc/systemd/system/spacedata.service
        sudo systemctl daemon-reload
        log_success "systemd服务已停止"
    else
        log_warning "systemd服务不存在，跳过"
    fi
}

# 停止Nginx配置
cleanup_nginx() {
    log_info "清理Nginx配置..."
    
    # 删除站点配置
    if [[ -f "/etc/nginx/sites-enabled/spacedata" ]]; then
        sudo rm -f /etc/nginx/sites-enabled/spacedata
        log_info "删除Nginx站点配置"
    fi
    
    if [[ -f "/etc/nginx/sites-available/spacedata" ]]; then
        sudo rm -f /etc/nginx/sites-available/spacedata
        log_info "删除Nginx可用站点配置"
    fi
    
    # 删除通用配置片段
    if [[ -f "/etc/nginx/snippets/spacedata-common.conf" ]]; then
        sudo rm -f /etc/nginx/snippets/spacedata-common.conf
        log_info "删除Nginx通用配置"
    fi
    
    # 重新加载Nginx配置
    if systemctl is-active --quiet nginx; then
        sudo nginx -t && sudo systemctl reload nginx || true
        log_success "Nginx配置已重新加载"
    fi
}

# 删除应用文件
remove_application_files() {
    log_info "删除应用文件..."
    
    # 删除应用目录
    if [[ -d "/opt/spacedata" ]]; then
        sudo rm -rf /opt/spacedata
        log_success "应用目录已删除"
    fi
    
    # 删除日志文件
    if [[ -d "/var/log/spacedata" ]]; then
        sudo rm -rf /var/log/spacedata
        log_success "日志目录已删除"
    fi
    
    # 删除数据目录（如果选择删除数据）
    if [[ "$DELETE_DATA" == true && -d "/var/lib/spacedata" ]]; then
        sudo rm -rf /var/lib/spacedata
        log_success "数据目录已删除"
    fi
}

# 清理监控服务
cleanup_monitoring() {
    log_info "清理监控服务..."
    
    # 停止Node Exporter
    if systemctl list-unit-files | grep -q node_exporter; then
        sudo systemctl stop node_exporter 2>/dev/null || true
        sudo systemctl disable node_exporter 2>/dev/null || true
        sudo rm -f /etc/systemd/system/node_exporter.service
        sudo systemctl daemon-reload
        log_info "Node Exporter服务已停止"
    fi
    
    # 删除Node Exporter二进制文件
    if [[ -f "/usr/local/bin/node_exporter" ]]; then
        sudo rm -f /usr/local/bin/node_exporter
        log_info "Node Exporter二进制文件已删除"
    fi
    
    # 删除监控配置文件
    if [[ -d "/opt/spacedata/monitoring" ]]; then
        sudo rm -rf /opt/spacedata/monitoring
        log_info "监控配置文件已删除"
    fi
}

# 清理日志轮转配置
cleanup_logrotate() {
    log_info "清理日志轮转配置..."
    
    if [[ -f "/etc/logrotate.d/spacedata" ]]; then
        sudo rm -f /etc/logrotate.d/spacedata
        log_success "日志轮转配置已删除"
    fi
}

# 清理防火墙规则
cleanup_firewall() {
    log_info "清理防火墙规则..."
    
    # 删除应用端口规则
    sudo ufw delete allow 3001 2>/dev/null || true
    
    log_success "防火墙规则已清理"
}

# 清理SSL证书
cleanup_ssl() {
    log_info "清理SSL证书..."
    
    # 注意：这里不删除Let's Encrypt证书，因为可能被其他服务使用
    log_warning "SSL证书保留，如需删除请手动执行: sudo certbot delete"
}

# 清理数据库（可选）
cleanup_databases() {
    if [[ "$DELETE_DATA" != true ]]; then
        log_warning "跳过数据库清理（数据保留）"
        return
    fi
    
    log_info "清理数据库数据..."
    
    # 如果是Docker部署，数据已在Docker卷清理中处理
    # 如果是本地数据库，需要手动清理
    log_warning "本地数据库数据需要手动清理："
    echo "  PostgreSQL: sudo -u postgres dropdb spacedata"
    echo "  MongoDB: mongo --eval 'db.dropDatabase()' spacedata"
    echo "  Redis: redis-cli FLUSHALL"
}

# 显示清理结果
show_cleanup_result() {
    echo
    log_success "清理完成！"
    echo
    log_info "已清理的内容："
    echo "  ✓ 停止所有服务"
    echo "  ✓ 删除Docker容器和镜像"
    echo "  ✓ 删除应用文件"
    echo "  ✓ 删除Nginx配置"
    echo "  ✓ 删除监控服务"
    echo "  ✓ 清理系统配置"
    
    if [[ "$DELETE_DATA" == true ]]; then
        echo "  ✓ 删除数据文件"
    else
        echo "  - 保留数据文件"
    fi
    
    echo
    log_info "保留的内容："
    echo "  - 系统软件包 (Node.js, Docker, Nginx等)"
    echo "  - SSL证书"
    echo "  - 系统用户和权限"
    
    if [[ "$DELETE_DATA" != true ]]; then
        echo "  - 数据库数据"
    fi
    
    echo
    log_info "如需完全重新部署，请运行："
    echo "  ./deploy.sh"
}

# 主函数
main() {
    log_info "SpaceData部署清理工具"
    
    # 确认清理操作
    confirm_cleanup
    
    log_info "开始清理部署..."
    
    # 停止所有服务
    stop_docker_services
    stop_pm2_services
    stop_systemd_services
    
    # 清理配置和文件
    cleanup_nginx
    remove_application_files
    cleanup_monitoring
    cleanup_logrotate
    cleanup_firewall
    cleanup_ssl
    cleanup_databases
    
    # 显示结果
    show_cleanup_result
}

# 执行主函数
main "$@"
