#!/bin/bash

# 环境准备脚本
# 安装所有必要的依赖和服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 更新系统包
update_system() {
    log_info "更新系统包..."
    sudo apt-get update -y
    sudo apt-get upgrade -y
    log_success "系统包更新完成"
}

# 安装基础工具
install_basic_tools() {
    log_info "安装基础工具..."
    sudo apt-get install -y \
        curl \
        wget \
        git \
        vim \
        htop \
        unzip \
        software-properties-common \
        apt-transport-https \
        ca-certificates \
        gnupg \
        lsb-release \
        build-essential \
        python3 \
        python3-pip
    log_success "基础工具安装完成"
}

# 安装Node.js
install_nodejs() {
    log_info "安装Node.js..."
    
    # 检查是否已安装
    if command -v node &> /dev/null; then
        node_version=$(node --version)
        log_info "Node.js已安装，版本: $node_version"
        
        # 检查版本是否满足要求 (>=16)
        major_version=$(echo $node_version | cut -d'v' -f2 | cut -d'.' -f1)
        if [[ $major_version -ge 16 ]]; then
            log_success "Node.js版本满足要求"
            return
        else
            log_warning "Node.js版本过低，需要升级"
        fi
    fi
    
    # 安装NodeSource仓库
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
    
    # 验证安装
    node_version=$(node --version)
    npm_version=$(npm --version)
    log_success "Node.js安装完成，版本: $node_version"
    log_success "npm版本: $npm_version"
}

# 安装Docker
install_docker() {
    log_info "安装Docker..."
    
    # 检查是否已安装
    if command -v docker &> /dev/null; then
        docker_version=$(docker --version)
        log_info "Docker已安装: $docker_version"
        return
    fi
    
    # 添加Docker官方GPG密钥
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # 添加Docker仓库
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # 安装Docker
    sudo apt-get update
    sudo apt-get install -y docker-ce docker-ce-cli containerd.io
    
    # 将当前用户添加到docker组
    sudo usermod -aG docker $USER
    
    # 启动Docker服务
    sudo systemctl start docker
    sudo systemctl enable docker
    
    log_success "Docker安装完成"
    log_warning "请重新登录以使docker组权限生效"
}

# 安装Docker Compose
install_docker_compose() {
    log_info "安装Docker Compose..."
    
    # 检查是否已安装
    if command -v docker-compose &> /dev/null; then
        compose_version=$(docker-compose --version)
        log_info "Docker Compose已安装: $compose_version"
        return
    fi
    
    # 获取最新版本
    COMPOSE_VERSION=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep 'tag_name' | cut -d\" -f4)
    
    # 下载并安装
    sudo curl -L "https://github.com/docker/compose/releases/download/${COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    
    # 创建符号链接
    sudo ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    
    log_success "Docker Compose安装完成，版本: $COMPOSE_VERSION"
}

# 安装Nginx
install_nginx() {
    log_info "安装Nginx..."
    
    # 检查是否已安装
    if command -v nginx &> /dev/null; then
        nginx_version=$(nginx -v 2>&1)
        log_info "Nginx已安装: $nginx_version"
        return
    fi
    
    sudo apt-get install -y nginx
    
    # 启动并启用Nginx
    sudo systemctl start nginx
    sudo systemctl enable nginx
    
    # 配置防火墙
    sudo ufw allow 'Nginx Full' 2>/dev/null || true
    
    log_success "Nginx安装完成"
}

# 安装PM2
install_pm2() {
    log_info "安装PM2..."
    
    # 检查是否已安装
    if command -v pm2 &> /dev/null; then
        pm2_version=$(pm2 --version)
        log_info "PM2已安装，版本: $pm2_version"
        return
    fi
    
    sudo npm install -g pm2
    
    # 配置PM2开机启动
    pm2 startup | grep -E '^sudo' | bash || true
    
    log_success "PM2安装完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    # 启用UFW
    sudo ufw --force enable
    
    # 允许SSH
    sudo ufw allow ssh
    
    # 允许HTTP和HTTPS
    sudo ufw allow 80
    sudo ufw allow 443
    
    # 允许应用端口
    sudo ufw allow 3001
    
    # 允许数据库端口（仅本地）
    sudo ufw allow from 127.0.0.1 to any port 5432  # PostgreSQL
    sudo ufw allow from 127.0.0.1 to any port 27017 # MongoDB
    sudo ufw allow from 127.0.0.1 to any port 6379  # Redis
    sudo ufw allow from 127.0.0.1 to any port 9200  # Elasticsearch
    
    log_success "防火墙配置完成"
}

# 创建应用目录
create_app_directories() {
    log_info "创建应用目录..."
    
    # 创建应用根目录
    sudo mkdir -p /opt/spacedata
    sudo chown $USER:$USER /opt/spacedata
    
    # 创建日志目录
    sudo mkdir -p /var/log/spacedata
    sudo chown $USER:$USER /var/log/spacedata
    
    # 创建数据目录
    sudo mkdir -p /var/lib/spacedata
    sudo chown $USER:$USER /var/lib/spacedata
    
    log_success "应用目录创建完成"
}

# 配置系统限制
configure_system_limits() {
    log_info "配置系统限制..."
    
    # 增加文件描述符限制
    echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
    echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf
    
    # 配置内核参数
    echo "vm.max_map_count=262144" | sudo tee -a /etc/sysctl.conf
    echo "fs.file-max=2097152" | sudo tee -a /etc/sysctl.conf
    
    # 应用配置
    sudo sysctl -p
    
    log_success "系统限制配置完成"
}

# 主函数
main() {
    log_info "开始环境准备..."
    
    update_system
    install_basic_tools
    install_nodejs
    install_docker
    install_docker_compose
    install_nginx
    install_pm2
    configure_firewall
    create_app_directories
    configure_system_limits
    
    log_success "环境准备完成!"
    log_warning "建议重启系统以确保所有配置生效"
}

# 执行主函数
main "$@"
