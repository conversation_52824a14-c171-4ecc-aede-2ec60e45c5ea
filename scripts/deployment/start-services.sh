#!/bin/bash

# 服务启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 解析参数
ENV="production"
DEPLOY_METHOD="docker"

while [[ $# -gt 0 ]]; do
    case $1 in
        --env)
            ENV="$2"
            shift 2
            ;;
        --method)
            DEPLOY_METHOD="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 检查服务状态
check_service_status() {
    local service_name=$1
    local port=$2
    
    log_info "检查 $service_name 服务状态..."
    
    # 检查端口是否监听
    if netstat -tuln | grep -q ":$port "; then
        log_success "$service_name 服务正在运行 (端口: $port)"
        return 0
    else
        log_warning "$service_name 服务未运行 (端口: $port)"
        return 1
    fi
}

# 等待服务启动
wait_for_service() {
    local service_name=$1
    local port=$2
    local max_wait=${3:-60}
    local wait_time=0
    
    log_info "等待 $service_name 服务启动..."
    
    while [[ $wait_time -lt $max_wait ]]; do
        if netstat -tuln | grep -q ":$port "; then
            log_success "$service_name 服务启动成功"
            return 0
        fi
        
        sleep 5
        wait_time=$((wait_time + 5))
        echo -n "."
    done
    
    echo
    log_error "$service_name 服务启动超时"
    return 1
}

# Docker方式启动服务
start_services_docker() {
    log_info "使用Docker启动服务..."
    
    cd /opt/spacedata/current
    
    # 启动所有服务
    docker-compose -f docker/compose/production.yml up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    docker-compose -f docker/compose/production.yml ps
    
    # 验证关键服务
    wait_for_service "PostgreSQL" 5432 120
    wait_for_service "MongoDB" 27017 120
    wait_for_service "Redis" 6379 60
    wait_for_service "Elasticsearch" 9200 180
    wait_for_service "RabbitMQ" 5672 60
    wait_for_service "SpaceData App" 3001 120
    
    log_success "Docker服务启动完成"
}

# PM2方式启动服务
start_services_pm2() {
    log_info "使用PM2启动服务..."
    
    cd /opt/spacedata/current
    
    # 启动应用
    pm2 start ecosystem.config.js
    
    # 显示状态
    pm2 status
    
    # 验证服务
    wait_for_service "SpaceData App" 3001 60
    
    log_success "PM2服务启动完成"
}

# systemd方式启动服务
start_services_systemd() {
    log_info "使用systemd启动服务..."
    
    # 启动应用服务
    sudo systemctl start spacedata
    sudo systemctl enable spacedata
    
    # 检查服务状态
    if sudo systemctl is-active --quiet spacedata; then
        log_success "SpaceData服务启动成功"
    else
        log_error "SpaceData服务启动失败"
        sudo systemctl status spacedata
        exit 1
    fi
    
    # 验证服务
    wait_for_service "SpaceData App" 3001 60
    
    log_success "systemd服务启动完成"
}

# 启动Nginx
start_nginx() {
    log_info "启动Nginx..."
    
    # 检查配置
    if ! sudo nginx -t; then
        log_error "Nginx配置错误"
        exit 1
    fi
    
    # 启动Nginx
    sudo systemctl start nginx
    sudo systemctl enable nginx
    
    if sudo systemctl is-active --quiet nginx; then
        log_success "Nginx启动成功"
    else
        log_error "Nginx启动失败"
        sudo systemctl status nginx
        exit 1
    fi
    
    # 验证Nginx
    wait_for_service "Nginx" 80 30
}

# 启动监控服务
start_monitoring_services() {
    log_info "启动监控服务..."
    
    # 启动Node Exporter
    if systemctl list-unit-files | grep -q node_exporter; then
        sudo systemctl start node_exporter
        sudo systemctl enable node_exporter
        log_success "Node Exporter启动成功"
    fi
    
    # 如果使用Docker，监控服务已经在Docker Compose中启动
    if [[ "$DEPLOY_METHOD" == "docker" ]]; then
        wait_for_service "Prometheus" 9090 60
        wait_for_service "Grafana" 3000 60
    fi
    
    log_success "监控服务启动完成"
}

# 验证所有服务
verify_all_services() {
    log_info "验证所有服务..."
    
    local failed_services=()
    
    # 检查应用服务
    if ! check_service_status "SpaceData App" 3001; then
        failed_services+=("SpaceData App")
    fi
    
    # 检查Nginx
    if ! check_service_status "Nginx" 80; then
        failed_services+=("Nginx")
    fi
    
    # 检查数据库服务（如果使用Docker）
    if [[ "$DEPLOY_METHOD" == "docker" ]]; then
        if ! check_service_status "PostgreSQL" 5432; then
            failed_services+=("PostgreSQL")
        fi
        
        if ! check_service_status "MongoDB" 27017; then
            failed_services+=("MongoDB")
        fi
        
        if ! check_service_status "Redis" 6379; then
            failed_services+=("Redis")
        fi
        
        if ! check_service_status "Elasticsearch" 9200; then
            failed_services+=("Elasticsearch")
        fi
        
        if ! check_service_status "RabbitMQ" 5672; then
            failed_services+=("RabbitMQ")
        fi
    fi
    
    # 检查监控服务
    if ! check_service_status "Node Exporter" 9100; then
        failed_services+=("Node Exporter")
    fi
    
    if [[ "$DEPLOY_METHOD" == "docker" ]]; then
        if ! check_service_status "Prometheus" 9090; then
            failed_services+=("Prometheus")
        fi
        
        if ! check_service_status "Grafana" 3000; then
            failed_services+=("Grafana")
        fi
    fi
    
    # 报告结果
    if [[ ${#failed_services[@]} -eq 0 ]]; then
        log_success "所有服务验证通过"
    else
        log_error "以下服务验证失败:"
        for service in "${failed_services[@]}"; do
            log_error "  - $service"
        done
        exit 1
    fi
}

# 显示服务信息
show_service_info() {
    log_info "服务信息:"
    
    echo
    echo "=== 应用服务 ==="
    echo "SpaceData App: http://localhost:3001"
    echo "API文档: http://localhost:3001/api-docs"
    echo "健康检查: http://localhost:3001/health"
    
    echo
    echo "=== Web服务 ==="
    echo "Nginx: http://localhost:80"
    
    if [[ "$DEPLOY_METHOD" == "docker" ]]; then
        echo
        echo "=== 数据库服务 ==="
        echo "PostgreSQL: localhost:5432"
        echo "MongoDB: localhost:27017"
        echo "Redis: localhost:6379"
        echo "Elasticsearch: http://localhost:9200"
        echo "RabbitMQ: http://localhost:15672"
        
        echo
        echo "=== 监控服务 ==="
        echo "Prometheus: http://localhost:9090"
        echo "Grafana: http://localhost:3000"
    fi
    
    echo "Node Exporter: http://localhost:9100"
    
    echo
    echo "=== 日志位置 ==="
    echo "应用日志: /var/log/spacedata/"
    echo "Nginx日志: /var/log/nginx/"
    
    if [[ "$DEPLOY_METHOD" == "docker" ]]; then
        echo "Docker日志: docker-compose logs"
    elif [[ "$DEPLOY_METHOD" == "pm2" ]]; then
        echo "PM2日志: pm2 logs"
    elif [[ "$DEPLOY_METHOD" == "systemd" ]]; then
        echo "systemd日志: journalctl -u spacedata"
    fi
}

# 主函数
main() {
    log_info "开始启动服务 (方法: $DEPLOY_METHOD, 环境: $ENV)..."
    
    # 根据部署方法启动服务
    case $DEPLOY_METHOD in
        docker)
            start_services_docker
            ;;
        pm2)
            start_services_pm2
            ;;
        systemd)
            start_services_systemd
            ;;
        *)
            log_error "不支持的部署方法: $DEPLOY_METHOD"
            exit 1
            ;;
    esac
    
    # 启动Nginx
    start_nginx
    
    # 启动监控服务
    start_monitoring_services
    
    # 验证所有服务
    verify_all_services
    
    # 显示服务信息
    show_service_info
    
    log_success "所有服务启动完成!"
}

# 执行主函数
main "$@"
